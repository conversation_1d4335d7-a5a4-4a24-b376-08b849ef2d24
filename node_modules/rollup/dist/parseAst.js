/*
  @license
	Rollup.js v4.39.0
	Wed, 02 Apr 2025 04:49:00 GMT - commit 5c001245779063abac3899aa9d25294ab003581b

	https://github.com/rollup/rollup

	Released under the MIT License.
*/
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

require('./native.js');
const parseAst_js = require('./shared/parseAst.js');
require('node:path');



exports.parseAst = parseAst_js.parseAst;
exports.parseAstAsync = parseAst_js.parseAstAsync;
//# sourceMappingURL=parseAst.js.map
