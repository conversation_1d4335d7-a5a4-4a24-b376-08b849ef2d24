{"name": "@zendia/runtime", "version": "1.0.1", "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/zendia-runtime.mjs", "require": "./dist/zendia-runtime.js"}}, "main": "./dist/zendia-runtime.js", "scripts": {"build": "vite build && tsc --project ./tsconfig.build.json", "gen": "tsc -b && node ./dev/gen/index.js", "dev": "vite"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@bufbuild/protobuf": "^2.2.3", "@types/node": "^22.10.5", "jimp": "^1.6.0", "long": "^5.3.2", "lv_font_conv": "^1.5.3"}, "devDependencies": {"vite": "^6.0.10"}}