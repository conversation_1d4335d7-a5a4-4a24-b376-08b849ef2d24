// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/product.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "znd.project.v1";

/** 产品信息 */
export interface Product {
  /** 产品id */
  productIdU32: number;
  /** 产品名称 */
  productName: string;
  /** 产品包含的工程id列表 */
  projectList: ProductProject[];
}

/** 产品包含的工程信息 */
export interface ProductProject {
  /** 工程id */
  projectIdU32: number;
  /** 工程版本id列表 */
  projectVersionIdU32: number[];
  /** 是否最新版本 */
  latestVersionIdU32: number;
}

function createBaseProduct(): Product {
  return { productIdU32: 0, productName: "", projectList: [] };
}

export const Product: MessageFns<Product> = {
  encode(message: Product, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.productIdU32 !== 0) {
      writer.uint32(8).int32(message.productIdU32);
    }
    if (message.productName !== "") {
      writer.uint32(18).string(message.productName);
    }
    for (const v of message.projectList) {
      ProductProject.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Product {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProduct();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.productIdU32 = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.productName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.projectList.push(ProductProject.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Product {
    return {
      productIdU32: isSet(object.productIdU32) ? globalThis.Number(object.productIdU32) : 0,
      productName: isSet(object.productName) ? globalThis.String(object.productName) : "",
      projectList: globalThis.Array.isArray(object?.projectList)
        ? object.projectList.map((e: any) => ProductProject.fromJSON(e))
        : [],
    };
  },

  toJSON(message: Product): unknown {
    const obj: any = {};
    if (message.productIdU32 !== 0) {
      obj.productIdU32 = Math.round(message.productIdU32);
    }
    if (message.productName !== "") {
      obj.productName = message.productName;
    }
    if (message.projectList?.length) {
      obj.projectList = message.projectList.map((e) => ProductProject.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Product>, I>>(base?: I): Product {
    return Product.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Product>, I>>(object: I): Product {
    const message = createBaseProduct();
    message.productIdU32 = object.productIdU32 ?? 0;
    message.productName = object.productName ?? "";
    message.projectList = object.projectList?.map((e) => ProductProject.fromPartial(e)) || [];
    return message;
  },
};

function createBaseProductProject(): ProductProject {
  return { projectIdU32: 0, projectVersionIdU32: [], latestVersionIdU32: 0 };
}

export const ProductProject: MessageFns<ProductProject> = {
  encode(message: ProductProject, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.projectIdU32 !== 0) {
      writer.uint32(8).int32(message.projectIdU32);
    }
    writer.uint32(18).fork();
    for (const v of message.projectVersionIdU32) {
      writer.int32(v);
    }
    writer.join();
    if (message.latestVersionIdU32 !== 0) {
      writer.uint32(24).int32(message.latestVersionIdU32);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProductProject {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProductProject();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.projectIdU32 = reader.int32();
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.projectVersionIdU32.push(reader.int32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.projectVersionIdU32.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.latestVersionIdU32 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProductProject {
    return {
      projectIdU32: isSet(object.projectIdU32) ? globalThis.Number(object.projectIdU32) : 0,
      projectVersionIdU32: globalThis.Array.isArray(object?.projectVersionIdU32)
        ? object.projectVersionIdU32.map((e: any) => globalThis.Number(e))
        : [],
      latestVersionIdU32: isSet(object.latestVersionIdU32) ? globalThis.Number(object.latestVersionIdU32) : 0,
    };
  },

  toJSON(message: ProductProject): unknown {
    const obj: any = {};
    if (message.projectIdU32 !== 0) {
      obj.projectIdU32 = Math.round(message.projectIdU32);
    }
    if (message.projectVersionIdU32?.length) {
      obj.projectVersionIdU32 = message.projectVersionIdU32.map((e) => Math.round(e));
    }
    if (message.latestVersionIdU32 !== 0) {
      obj.latestVersionIdU32 = Math.round(message.latestVersionIdU32);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProductProject>, I>>(base?: I): ProductProject {
    return ProductProject.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProductProject>, I>>(object: I): ProductProject {
    const message = createBaseProductProject();
    message.projectIdU32 = object.projectIdU32 ?? 0;
    message.projectVersionIdU32 = object.projectVersionIdU32?.map((e) => e) || [];
    message.latestVersionIdU32 = object.latestVersionIdU32 ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
