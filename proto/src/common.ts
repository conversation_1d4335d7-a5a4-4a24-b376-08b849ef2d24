// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/common.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "znd.project.v1";

/** 保护类型 */
export enum ProtectType {
  /** PROTECT_TYPE_UNSPECIFIED - 不保护 */
  PROTECT_TYPE_UNSPECIFIED = 0,
  /** PROTECT_TYPE_MODIFY - 修改保护，表示只读 */
  PROTECT_TYPE_MODIFY = 1,
  /** PROTECT_TYPE_OPEN - 打开保护，表示不让打开 */
  PROTECT_TYPE_OPEN = 2,
  UNRECOGNIZED = -1,
}

export function protectTypeFromJSON(object: any): ProtectType {
  switch (object) {
    case 0:
    case "PROTECT_TYPE_UNSPECIFIED":
      return ProtectType.PROTECT_TYPE_UNSPECIFIED;
    case 1:
    case "PROTECT_TYPE_MODIFY":
      return ProtectType.PROTECT_TYPE_MODIFY;
    case 2:
    case "PROTECT_TYPE_OPEN":
      return ProtectType.PROTECT_TYPE_OPEN;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ProtectType.UNRECOGNIZED;
  }
}

export function protectTypeToJSON(object: ProtectType): string {
  switch (object) {
    case ProtectType.PROTECT_TYPE_UNSPECIFIED:
      return "PROTECT_TYPE_UNSPECIFIED";
    case ProtectType.PROTECT_TYPE_MODIFY:
      return "PROTECT_TYPE_MODIFY";
    case ProtectType.PROTECT_TYPE_OPEN:
      return "PROTECT_TYPE_OPEN";
    case ProtectType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 位置类型 */
export enum PositionType {
  POSITION_TYPE_UNSPECIFIED = 0,
  POSITION_TYPE_LEFT_TOP = 1,
  POSITION_TYPE_CENTER_TOP = 2,
  POSITION_TYPE_RIGHT_TOP = 3,
  POSITION_TYPE_LEFT_CENTER = 4,
  POSITION_TYPE_CENTER_CENTER = 5,
  POSITION_TYPE_RIGHT_CENTER = 6,
  POSITION_TYPE_LEFT_BOTTOM = 7,
  POSITION_TYPE_CENTER_BOTTOM = 8,
  POSITION_TYPE_RIGHT_BOTTOM = 9,
  UNRECOGNIZED = -1,
}

export function positionTypeFromJSON(object: any): PositionType {
  switch (object) {
    case 0:
    case "POSITION_TYPE_UNSPECIFIED":
      return PositionType.POSITION_TYPE_UNSPECIFIED;
    case 1:
    case "POSITION_TYPE_LEFT_TOP":
      return PositionType.POSITION_TYPE_LEFT_TOP;
    case 2:
    case "POSITION_TYPE_CENTER_TOP":
      return PositionType.POSITION_TYPE_CENTER_TOP;
    case 3:
    case "POSITION_TYPE_RIGHT_TOP":
      return PositionType.POSITION_TYPE_RIGHT_TOP;
    case 4:
    case "POSITION_TYPE_LEFT_CENTER":
      return PositionType.POSITION_TYPE_LEFT_CENTER;
    case 5:
    case "POSITION_TYPE_CENTER_CENTER":
      return PositionType.POSITION_TYPE_CENTER_CENTER;
    case 6:
    case "POSITION_TYPE_RIGHT_CENTER":
      return PositionType.POSITION_TYPE_RIGHT_CENTER;
    case 7:
    case "POSITION_TYPE_LEFT_BOTTOM":
      return PositionType.POSITION_TYPE_LEFT_BOTTOM;
    case 8:
    case "POSITION_TYPE_CENTER_BOTTOM":
      return PositionType.POSITION_TYPE_CENTER_BOTTOM;
    case 9:
    case "POSITION_TYPE_RIGHT_BOTTOM":
      return PositionType.POSITION_TYPE_RIGHT_BOTTOM;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PositionType.UNRECOGNIZED;
  }
}

export function positionTypeToJSON(object: PositionType): string {
  switch (object) {
    case PositionType.POSITION_TYPE_UNSPECIFIED:
      return "POSITION_TYPE_UNSPECIFIED";
    case PositionType.POSITION_TYPE_LEFT_TOP:
      return "POSITION_TYPE_LEFT_TOP";
    case PositionType.POSITION_TYPE_CENTER_TOP:
      return "POSITION_TYPE_CENTER_TOP";
    case PositionType.POSITION_TYPE_RIGHT_TOP:
      return "POSITION_TYPE_RIGHT_TOP";
    case PositionType.POSITION_TYPE_LEFT_CENTER:
      return "POSITION_TYPE_LEFT_CENTER";
    case PositionType.POSITION_TYPE_CENTER_CENTER:
      return "POSITION_TYPE_CENTER_CENTER";
    case PositionType.POSITION_TYPE_RIGHT_CENTER:
      return "POSITION_TYPE_RIGHT_CENTER";
    case PositionType.POSITION_TYPE_LEFT_BOTTOM:
      return "POSITION_TYPE_LEFT_BOTTOM";
    case PositionType.POSITION_TYPE_CENTER_BOTTOM:
      return "POSITION_TYPE_CENTER_BOTTOM";
    case PositionType.POSITION_TYPE_RIGHT_BOTTOM:
      return "POSITION_TYPE_RIGHT_BOTTOM";
    case PositionType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum AlignType {
  /** ALIGN_TYPE_UNSPECIFIED - 未定义 */
  ALIGN_TYPE_UNSPECIFIED = 0,
  /** ALIGN_TYPE_TOP_LEFT - 左上对齐 */
  ALIGN_TYPE_TOP_LEFT = 1,
  /** ALIGN_TYPE_TOP_MID - 顶部居中 */
  ALIGN_TYPE_TOP_MID = 2,
  /** ALIGN_TYPE_TOP_RIGHT - 右上对齐 */
  ALIGN_TYPE_TOP_RIGHT = 3,
  /** ALIGN_TYPE_BOTTOM_LEFT - 左下 */
  ALIGN_TYPE_BOTTOM_LEFT = 4,
  /** ALIGN_TYPE_BOTTOM_MID - 下边居中 */
  ALIGN_TYPE_BOTTOM_MID = 5,
  /** ALIGN_TYPE_BOTTOM_RIGHT - 右下角 */
  ALIGN_TYPE_BOTTOM_RIGHT = 6,
  /** ALIGN_TYPE_LEFT_MID - 左中对齐 */
  ALIGN_TYPE_LEFT_MID = 7,
  /** ALIGN_TYPE_RIGHT_MID - 右边居中 */
  ALIGN_TYPE_RIGHT_MID = 8,
  /** ALIGN_TYPE_CENTER - 居正中 */
  ALIGN_TYPE_CENTER = 9,
  UNRECOGNIZED = -1,
}

export function alignTypeFromJSON(object: any): AlignType {
  switch (object) {
    case 0:
    case "ALIGN_TYPE_UNSPECIFIED":
      return AlignType.ALIGN_TYPE_UNSPECIFIED;
    case 1:
    case "ALIGN_TYPE_TOP_LEFT":
      return AlignType.ALIGN_TYPE_TOP_LEFT;
    case 2:
    case "ALIGN_TYPE_TOP_MID":
      return AlignType.ALIGN_TYPE_TOP_MID;
    case 3:
    case "ALIGN_TYPE_TOP_RIGHT":
      return AlignType.ALIGN_TYPE_TOP_RIGHT;
    case 4:
    case "ALIGN_TYPE_BOTTOM_LEFT":
      return AlignType.ALIGN_TYPE_BOTTOM_LEFT;
    case 5:
    case "ALIGN_TYPE_BOTTOM_MID":
      return AlignType.ALIGN_TYPE_BOTTOM_MID;
    case 6:
    case "ALIGN_TYPE_BOTTOM_RIGHT":
      return AlignType.ALIGN_TYPE_BOTTOM_RIGHT;
    case 7:
    case "ALIGN_TYPE_LEFT_MID":
      return AlignType.ALIGN_TYPE_LEFT_MID;
    case 8:
    case "ALIGN_TYPE_RIGHT_MID":
      return AlignType.ALIGN_TYPE_RIGHT_MID;
    case 9:
    case "ALIGN_TYPE_CENTER":
      return AlignType.ALIGN_TYPE_CENTER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AlignType.UNRECOGNIZED;
  }
}

export function alignTypeToJSON(object: AlignType): string {
  switch (object) {
    case AlignType.ALIGN_TYPE_UNSPECIFIED:
      return "ALIGN_TYPE_UNSPECIFIED";
    case AlignType.ALIGN_TYPE_TOP_LEFT:
      return "ALIGN_TYPE_TOP_LEFT";
    case AlignType.ALIGN_TYPE_TOP_MID:
      return "ALIGN_TYPE_TOP_MID";
    case AlignType.ALIGN_TYPE_TOP_RIGHT:
      return "ALIGN_TYPE_TOP_RIGHT";
    case AlignType.ALIGN_TYPE_BOTTOM_LEFT:
      return "ALIGN_TYPE_BOTTOM_LEFT";
    case AlignType.ALIGN_TYPE_BOTTOM_MID:
      return "ALIGN_TYPE_BOTTOM_MID";
    case AlignType.ALIGN_TYPE_BOTTOM_RIGHT:
      return "ALIGN_TYPE_BOTTOM_RIGHT";
    case AlignType.ALIGN_TYPE_LEFT_MID:
      return "ALIGN_TYPE_LEFT_MID";
    case AlignType.ALIGN_TYPE_RIGHT_MID:
      return "ALIGN_TYPE_RIGHT_MID";
    case AlignType.ALIGN_TYPE_CENTER:
      return "ALIGN_TYPE_CENTER";
    case AlignType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 保存位置 */
export enum SaveLocation {
  SAVE_LOCATION_UNSPECIFIED = 0,
  SAVE_LOCATION_LOCAL = 1,
  SAVE_LOCATION_USB = 2,
  SAVE_LOCATION_CLOUD = 3,
  UNRECOGNIZED = -1,
}

export function saveLocationFromJSON(object: any): SaveLocation {
  switch (object) {
    case 0:
    case "SAVE_LOCATION_UNSPECIFIED":
      return SaveLocation.SAVE_LOCATION_UNSPECIFIED;
    case 1:
    case "SAVE_LOCATION_LOCAL":
      return SaveLocation.SAVE_LOCATION_LOCAL;
    case 2:
    case "SAVE_LOCATION_USB":
      return SaveLocation.SAVE_LOCATION_USB;
    case 3:
    case "SAVE_LOCATION_CLOUD":
      return SaveLocation.SAVE_LOCATION_CLOUD;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SaveLocation.UNRECOGNIZED;
  }
}

export function saveLocationToJSON(object: SaveLocation): string {
  switch (object) {
    case SaveLocation.SAVE_LOCATION_UNSPECIFIED:
      return "SAVE_LOCATION_UNSPECIFIED";
    case SaveLocation.SAVE_LOCATION_LOCAL:
      return "SAVE_LOCATION_LOCAL";
    case SaveLocation.SAVE_LOCATION_USB:
      return "SAVE_LOCATION_USB";
    case SaveLocation.SAVE_LOCATION_CLOUD:
      return "SAVE_LOCATION_CLOUD";
    case SaveLocation.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 滚动方向 */
export enum ScrollDirection {
  SCROLL_DIRECTION_UNSPECIFIED = 0,
  SCROLL_DIRECTION_LEFT_TO_RIGHT = 1,
  SCROLL_DIRECTION_RIGHT_TO_LEFT = 2,
  SCROLL_DIRECTION_TOP_TO_BOTTOM = 3,
  SCROLL_DIRECTION_BOTTOM_TO_TOP = 4,
  UNRECOGNIZED = -1,
}

export function scrollDirectionFromJSON(object: any): ScrollDirection {
  switch (object) {
    case 0:
    case "SCROLL_DIRECTION_UNSPECIFIED":
      return ScrollDirection.SCROLL_DIRECTION_UNSPECIFIED;
    case 1:
    case "SCROLL_DIRECTION_LEFT_TO_RIGHT":
      return ScrollDirection.SCROLL_DIRECTION_LEFT_TO_RIGHT;
    case 2:
    case "SCROLL_DIRECTION_RIGHT_TO_LEFT":
      return ScrollDirection.SCROLL_DIRECTION_RIGHT_TO_LEFT;
    case 3:
    case "SCROLL_DIRECTION_TOP_TO_BOTTOM":
      return ScrollDirection.SCROLL_DIRECTION_TOP_TO_BOTTOM;
    case 4:
    case "SCROLL_DIRECTION_BOTTOM_TO_TOP":
      return ScrollDirection.SCROLL_DIRECTION_BOTTOM_TO_TOP;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ScrollDirection.UNRECOGNIZED;
  }
}

export function scrollDirectionToJSON(object: ScrollDirection): string {
  switch (object) {
    case ScrollDirection.SCROLL_DIRECTION_UNSPECIFIED:
      return "SCROLL_DIRECTION_UNSPECIFIED";
    case ScrollDirection.SCROLL_DIRECTION_LEFT_TO_RIGHT:
      return "SCROLL_DIRECTION_LEFT_TO_RIGHT";
    case ScrollDirection.SCROLL_DIRECTION_RIGHT_TO_LEFT:
      return "SCROLL_DIRECTION_RIGHT_TO_LEFT";
    case ScrollDirection.SCROLL_DIRECTION_TOP_TO_BOTTOM:
      return "SCROLL_DIRECTION_TOP_TO_BOTTOM";
    case ScrollDirection.SCROLL_DIRECTION_BOTTOM_TO_TOP:
      return "SCROLL_DIRECTION_BOTTOM_TO_TOP";
    case ScrollDirection.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 方向 */
export enum Direction {
  DIRECTION_UNSPECIFIED = 0,
  DIRECTION_LEFT = 1,
  DIRECTION_RIGHT = 2,
  DIRECTION_UP = 3,
  DIRECTION_DOWN = 4,
  UNRECOGNIZED = -1,
}

export function directionFromJSON(object: any): Direction {
  switch (object) {
    case 0:
    case "DIRECTION_UNSPECIFIED":
      return Direction.DIRECTION_UNSPECIFIED;
    case 1:
    case "DIRECTION_LEFT":
      return Direction.DIRECTION_LEFT;
    case 2:
    case "DIRECTION_RIGHT":
      return Direction.DIRECTION_RIGHT;
    case 3:
    case "DIRECTION_UP":
      return Direction.DIRECTION_UP;
    case 4:
    case "DIRECTION_DOWN":
      return Direction.DIRECTION_DOWN;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Direction.UNRECOGNIZED;
  }
}

export function directionToJSON(object: Direction): string {
  switch (object) {
    case Direction.DIRECTION_UNSPECIFIED:
      return "DIRECTION_UNSPECIFIED";
    case Direction.DIRECTION_LEFT:
      return "DIRECTION_LEFT";
    case Direction.DIRECTION_RIGHT:
      return "DIRECTION_RIGHT";
    case Direction.DIRECTION_UP:
      return "DIRECTION_UP";
    case Direction.DIRECTION_DOWN:
      return "DIRECTION_DOWN";
    case Direction.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 时间格式 HMS */
export enum TimeFormatType {
  /** TIME_FORMAT_TYPE_UNSPECIFIED - 未设置 */
  TIME_FORMAT_TYPE_UNSPECIFIED = 0,
  /** TIME_FORMAT_TYPE_HMS - 时间格式 HH:MM:SS */
  TIME_FORMAT_TYPE_HMS = 1,
  /** TIME_FORMAT_TYPE_HM - 时间格式 HH:MM */
  TIME_FORMAT_TYPE_HM = 2,
  /** TIME_FORMAT_TYPE_MS - 时间格式 MM:SS */
  TIME_FORMAT_TYPE_MS = 3,
  UNRECOGNIZED = -1,
}

export function timeFormatTypeFromJSON(object: any): TimeFormatType {
  switch (object) {
    case 0:
    case "TIME_FORMAT_TYPE_UNSPECIFIED":
      return TimeFormatType.TIME_FORMAT_TYPE_UNSPECIFIED;
    case 1:
    case "TIME_FORMAT_TYPE_HMS":
      return TimeFormatType.TIME_FORMAT_TYPE_HMS;
    case 2:
    case "TIME_FORMAT_TYPE_HM":
      return TimeFormatType.TIME_FORMAT_TYPE_HM;
    case 3:
    case "TIME_FORMAT_TYPE_MS":
      return TimeFormatType.TIME_FORMAT_TYPE_MS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TimeFormatType.UNRECOGNIZED;
  }
}

export function timeFormatTypeToJSON(object: TimeFormatType): string {
  switch (object) {
    case TimeFormatType.TIME_FORMAT_TYPE_UNSPECIFIED:
      return "TIME_FORMAT_TYPE_UNSPECIFIED";
    case TimeFormatType.TIME_FORMAT_TYPE_HMS:
      return "TIME_FORMAT_TYPE_HMS";
    case TimeFormatType.TIME_FORMAT_TYPE_HM:
      return "TIME_FORMAT_TYPE_HM";
    case TimeFormatType.TIME_FORMAT_TYPE_MS:
      return "TIME_FORMAT_TYPE_MS";
    case TimeFormatType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 日期格式 YDM */
export enum DateFormatType {
  /** DATE_FORMAT_TYPE_UNSPECIFIED - 未设置 */
  DATE_FORMAT_TYPE_UNSPECIFIED = 0,
  /**
   * DATE_FORMAT_TYPE_MDY - 分隔符 /
   * 日期格式 MM/DD/YYYY
   */
  DATE_FORMAT_TYPE_MDY = 1,
  /** DATE_FORMAT_TYPE_DMY - 日期格式 DD/MM/YYYY */
  DATE_FORMAT_TYPE_DMY = 2,
  /** DATE_FORMAT_TYPE_YMD - 日期格式 YYYY/MM/DD */
  DATE_FORMAT_TYPE_YMD = 3,
  /** DATE_FORMAT_TYPE_MD - 日期格式 MM/DD */
  DATE_FORMAT_TYPE_MD = 4,
  /** DATE_FORMAT_TYPE_DM - 日期格式 DD/MM */
  DATE_FORMAT_TYPE_DM = 5,
  /** DATE_FORMAT_TYPE_D - 日期格式 DD */
  DATE_FORMAT_TYPE_D = 6,
  /**
   * DATE_FORMAT_TYPE_MDY_POINT - / 分隔符 .
   * 日期格式 MM.DD.YYYY
   */
  DATE_FORMAT_TYPE_MDY_POINT = 7,
  /** DATE_FORMAT_TYPE_DMY_POINT - 日期格式 DD.MM.YYYY */
  DATE_FORMAT_TYPE_DMY_POINT = 8,
  /** DATE_FORMAT_TYPE_YMD_POINT - 日期格式 YYYY.MM.DD */
  DATE_FORMAT_TYPE_YMD_POINT = 9,
  /** DATE_FORMAT_TYPE_MD_POINT - 日期格式 MM.DD */
  DATE_FORMAT_TYPE_MD_POINT = 10,
  /** DATE_FORMAT_TYPE_DM_POINT - 日期格式 DD.MM */
  DATE_FORMAT_TYPE_DM_POINT = 11,
  /** DATE_FORMAT_TYPE_D_POINT - 日期格式 DD */
  DATE_FORMAT_TYPE_D_POINT = 12,
  /**
   * DATE_FORMAT_TYPE_MDY_DASH - 分隔符 -
   * 日期格式 MM-DD-YYYY
   */
  DATE_FORMAT_TYPE_MDY_DASH = 13,
  /** DATE_FORMAT_TYPE_DMY_DASH - 日期格式 DD-MM-YYYY */
  DATE_FORMAT_TYPE_DMY_DASH = 14,
  /** DATE_FORMAT_TYPE_YMD_DASH - 日期格式 YYYY-MM-DD */
  DATE_FORMAT_TYPE_YMD_DASH = 15,
  /** DATE_FORMAT_TYPE_MD_DASH - 日期格式 MM-DD */
  DATE_FORMAT_TYPE_MD_DASH = 16,
  /** DATE_FORMAT_TYPE_DM_DASH - 日期格式 DD-MM */
  DATE_FORMAT_TYPE_DM_DASH = 17,
  /** DATE_FORMAT_TYPE_D_DASH - 日期格式 DD */
  DATE_FORMAT_TYPE_D_DASH = 18,
  UNRECOGNIZED = -1,
}

export function dateFormatTypeFromJSON(object: any): DateFormatType {
  switch (object) {
    case 0:
    case "DATE_FORMAT_TYPE_UNSPECIFIED":
      return DateFormatType.DATE_FORMAT_TYPE_UNSPECIFIED;
    case 1:
    case "DATE_FORMAT_TYPE_MDY":
      return DateFormatType.DATE_FORMAT_TYPE_MDY;
    case 2:
    case "DATE_FORMAT_TYPE_DMY":
      return DateFormatType.DATE_FORMAT_TYPE_DMY;
    case 3:
    case "DATE_FORMAT_TYPE_YMD":
      return DateFormatType.DATE_FORMAT_TYPE_YMD;
    case 4:
    case "DATE_FORMAT_TYPE_MD":
      return DateFormatType.DATE_FORMAT_TYPE_MD;
    case 5:
    case "DATE_FORMAT_TYPE_DM":
      return DateFormatType.DATE_FORMAT_TYPE_DM;
    case 6:
    case "DATE_FORMAT_TYPE_D":
      return DateFormatType.DATE_FORMAT_TYPE_D;
    case 7:
    case "DATE_FORMAT_TYPE_MDY_POINT":
      return DateFormatType.DATE_FORMAT_TYPE_MDY_POINT;
    case 8:
    case "DATE_FORMAT_TYPE_DMY_POINT":
      return DateFormatType.DATE_FORMAT_TYPE_DMY_POINT;
    case 9:
    case "DATE_FORMAT_TYPE_YMD_POINT":
      return DateFormatType.DATE_FORMAT_TYPE_YMD_POINT;
    case 10:
    case "DATE_FORMAT_TYPE_MD_POINT":
      return DateFormatType.DATE_FORMAT_TYPE_MD_POINT;
    case 11:
    case "DATE_FORMAT_TYPE_DM_POINT":
      return DateFormatType.DATE_FORMAT_TYPE_DM_POINT;
    case 12:
    case "DATE_FORMAT_TYPE_D_POINT":
      return DateFormatType.DATE_FORMAT_TYPE_D_POINT;
    case 13:
    case "DATE_FORMAT_TYPE_MDY_DASH":
      return DateFormatType.DATE_FORMAT_TYPE_MDY_DASH;
    case 14:
    case "DATE_FORMAT_TYPE_DMY_DASH":
      return DateFormatType.DATE_FORMAT_TYPE_DMY_DASH;
    case 15:
    case "DATE_FORMAT_TYPE_YMD_DASH":
      return DateFormatType.DATE_FORMAT_TYPE_YMD_DASH;
    case 16:
    case "DATE_FORMAT_TYPE_MD_DASH":
      return DateFormatType.DATE_FORMAT_TYPE_MD_DASH;
    case 17:
    case "DATE_FORMAT_TYPE_DM_DASH":
      return DateFormatType.DATE_FORMAT_TYPE_DM_DASH;
    case 18:
    case "DATE_FORMAT_TYPE_D_DASH":
      return DateFormatType.DATE_FORMAT_TYPE_D_DASH;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DateFormatType.UNRECOGNIZED;
  }
}

export function dateFormatTypeToJSON(object: DateFormatType): string {
  switch (object) {
    case DateFormatType.DATE_FORMAT_TYPE_UNSPECIFIED:
      return "DATE_FORMAT_TYPE_UNSPECIFIED";
    case DateFormatType.DATE_FORMAT_TYPE_MDY:
      return "DATE_FORMAT_TYPE_MDY";
    case DateFormatType.DATE_FORMAT_TYPE_DMY:
      return "DATE_FORMAT_TYPE_DMY";
    case DateFormatType.DATE_FORMAT_TYPE_YMD:
      return "DATE_FORMAT_TYPE_YMD";
    case DateFormatType.DATE_FORMAT_TYPE_MD:
      return "DATE_FORMAT_TYPE_MD";
    case DateFormatType.DATE_FORMAT_TYPE_DM:
      return "DATE_FORMAT_TYPE_DM";
    case DateFormatType.DATE_FORMAT_TYPE_D:
      return "DATE_FORMAT_TYPE_D";
    case DateFormatType.DATE_FORMAT_TYPE_MDY_POINT:
      return "DATE_FORMAT_TYPE_MDY_POINT";
    case DateFormatType.DATE_FORMAT_TYPE_DMY_POINT:
      return "DATE_FORMAT_TYPE_DMY_POINT";
    case DateFormatType.DATE_FORMAT_TYPE_YMD_POINT:
      return "DATE_FORMAT_TYPE_YMD_POINT";
    case DateFormatType.DATE_FORMAT_TYPE_MD_POINT:
      return "DATE_FORMAT_TYPE_MD_POINT";
    case DateFormatType.DATE_FORMAT_TYPE_DM_POINT:
      return "DATE_FORMAT_TYPE_DM_POINT";
    case DateFormatType.DATE_FORMAT_TYPE_D_POINT:
      return "DATE_FORMAT_TYPE_D_POINT";
    case DateFormatType.DATE_FORMAT_TYPE_MDY_DASH:
      return "DATE_FORMAT_TYPE_MDY_DASH";
    case DateFormatType.DATE_FORMAT_TYPE_DMY_DASH:
      return "DATE_FORMAT_TYPE_DMY_DASH";
    case DateFormatType.DATE_FORMAT_TYPE_YMD_DASH:
      return "DATE_FORMAT_TYPE_YMD_DASH";
    case DateFormatType.DATE_FORMAT_TYPE_MD_DASH:
      return "DATE_FORMAT_TYPE_MD_DASH";
    case DateFormatType.DATE_FORMAT_TYPE_DM_DASH:
      return "DATE_FORMAT_TYPE_DM_DASH";
    case DateFormatType.DATE_FORMAT_TYPE_D_DASH:
      return "DATE_FORMAT_TYPE_D_DASH";
    case DateFormatType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 数据来源 */
export enum ListDataResourceType {
  /** LIST_DATA_RESOURCE_TYPE_UNSPECIFIED - 自定义 */
  LIST_DATA_RESOURCE_TYPE_UNSPECIFIED = 0,
  /** LIST_DATA_RESOURCE_TYPE_HISTORY_DATE - 历史数据日期 */
  LIST_DATA_RESOURCE_TYPE_HISTORY_DATE = 1,
  /** LIST_DATA_RESOURCE_TYPE_USER_ACCOUNT - 用户账号 */
  LIST_DATA_RESOURCE_TYPE_USER_ACCOUNT = 2,
  UNRECOGNIZED = -1,
}

export function listDataResourceTypeFromJSON(object: any): ListDataResourceType {
  switch (object) {
    case 0:
    case "LIST_DATA_RESOURCE_TYPE_UNSPECIFIED":
      return ListDataResourceType.LIST_DATA_RESOURCE_TYPE_UNSPECIFIED;
    case 1:
    case "LIST_DATA_RESOURCE_TYPE_HISTORY_DATE":
      return ListDataResourceType.LIST_DATA_RESOURCE_TYPE_HISTORY_DATE;
    case 2:
    case "LIST_DATA_RESOURCE_TYPE_USER_ACCOUNT":
      return ListDataResourceType.LIST_DATA_RESOURCE_TYPE_USER_ACCOUNT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ListDataResourceType.UNRECOGNIZED;
  }
}

export function listDataResourceTypeToJSON(object: ListDataResourceType): string {
  switch (object) {
    case ListDataResourceType.LIST_DATA_RESOURCE_TYPE_UNSPECIFIED:
      return "LIST_DATA_RESOURCE_TYPE_UNSPECIFIED";
    case ListDataResourceType.LIST_DATA_RESOURCE_TYPE_HISTORY_DATE:
      return "LIST_DATA_RESOURCE_TYPE_HISTORY_DATE";
    case ListDataResourceType.LIST_DATA_RESOURCE_TYPE_USER_ACCOUNT:
      return "LIST_DATA_RESOURCE_TYPE_USER_ACCOUNT";
    case ListDataResourceType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/**
 * 图标样式
 * buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
 */
export enum SymbolType {
  SYMBOL_TYPE_BULLET = 0,
  SYMBOL_TYPE_AUDIO = 1,
  SYMBOL_TYPE_VIDEO = 2,
  SYMBOL_TYPE_LIST = 3,
  SYMBOL_TYPE_OK = 4,
  SYMBOL_TYPE_CLOSE = 5,
  SYMBOL_TYPE_POWER = 6,
  SYMBOL_TYPE_SETTINGS = 7,
  SYMBOL_TYPE_HOME = 8,
  SYMBOL_TYPE_DOWNLOAD = 9,
  SYMBOL_TYPE_DRIVE = 10,
  SYMBOL_TYPE_REFRESH = 11,
  SYMBOL_TYPE_MUTE = 12,
  SYMBOL_TYPE_VOLUME_MID = 13,
  SYMBOL_TYPE_VOLUME_MAX = 14,
  SYMBOL_TYPE_IMAGE = 15,
  SYMBOL_TYPE_TINT = 16,
  SYMBOL_TYPE_PREV = 17,
  SYMBOL_TYPE_PLAY = 18,
  SYMBOL_TYPE_PAUSE = 19,
  SYMBOL_TYPE_STOP = 20,
  SYMBOL_TYPE_EJECT = 21,
  SYMBOL_TYPE_LEFT = 22,
  SYMBOL_TYPE_RIGHT = 23,
  SYMBOL_TYPE_PLUS = 24,
  SYMBOL_TYPE_MINUS = 25,
  SYMBOL_TYPE_EYE_OPEN = 26,
  SYMBOL_TYPE_EYE_CLOSE = 27,
  SYMBOL_TYPE_WARNING = 28,
  SYMBOL_TYPE_SHUFFLE = 29,
  SYMBOL_TYPE_UP = 30,
  SYMBOL_TYPE_DOWN = 31,
  SYMBOL_TYPE_LOOP = 32,
  SYMBOL_TYPE_DIRECTORY = 33,
  SYMBOL_TYPE_UPLOAD = 34,
  SYMBOL_TYPE_CALL = 35,
  SYMBOL_TYPE_CUT = 36,
  SYMBOL_TYPE_COPY = 37,
  SYMBOL_TYPE_SAVE = 38,
  SYMBOL_TYPE_BARS = 39,
  SYMBOL_TYPE_ENVELOPE = 40,
  SYMBOL_TYPE_CHARGE = 41,
  SYMBOL_TYPE_PASTE = 42,
  SYMBOL_TYPE_BELL = 43,
  SYMBOL_TYPE_KEYBOARD = 44,
  SYMBOL_TYPE_GPS = 45,
  SYMBOL_TYPE_FILE = 46,
  SYMBOL_TYPE_WIFI = 47,
  SYMBOL_TYPE_BATTERY_FULL = 48,
  SYMBOL_TYPE_BATTERY_3 = 49,
  SYMBOL_TYPE_BATTERY_2 = 50,
  SYMBOL_TYPE_BATTERY_1 = 51,
  SYMBOL_TYPE_BATTERY_EMPTY = 52,
  SYMBOL_TYPE_USB = 53,
  SYMBOL_TYPE_BLUETOOTH = 54,
  SYMBOL_TYPE_TRASH = 55,
  SYMBOL_TYPE_EDIT = 56,
  SYMBOL_TYPE_BACKSPACE = 57,
  SYMBOL_TYPE_SD_CARD = 58,
  SYMBOL_TYPE_NEW_LINE = 59,
  SYMBOL_TYPE_DUMMY = 60,
  UNRECOGNIZED = -1,
}

export function symbolTypeFromJSON(object: any): SymbolType {
  switch (object) {
    case 0:
    case "SYMBOL_TYPE_BULLET":
      return SymbolType.SYMBOL_TYPE_BULLET;
    case 1:
    case "SYMBOL_TYPE_AUDIO":
      return SymbolType.SYMBOL_TYPE_AUDIO;
    case 2:
    case "SYMBOL_TYPE_VIDEO":
      return SymbolType.SYMBOL_TYPE_VIDEO;
    case 3:
    case "SYMBOL_TYPE_LIST":
      return SymbolType.SYMBOL_TYPE_LIST;
    case 4:
    case "SYMBOL_TYPE_OK":
      return SymbolType.SYMBOL_TYPE_OK;
    case 5:
    case "SYMBOL_TYPE_CLOSE":
      return SymbolType.SYMBOL_TYPE_CLOSE;
    case 6:
    case "SYMBOL_TYPE_POWER":
      return SymbolType.SYMBOL_TYPE_POWER;
    case 7:
    case "SYMBOL_TYPE_SETTINGS":
      return SymbolType.SYMBOL_TYPE_SETTINGS;
    case 8:
    case "SYMBOL_TYPE_HOME":
      return SymbolType.SYMBOL_TYPE_HOME;
    case 9:
    case "SYMBOL_TYPE_DOWNLOAD":
      return SymbolType.SYMBOL_TYPE_DOWNLOAD;
    case 10:
    case "SYMBOL_TYPE_DRIVE":
      return SymbolType.SYMBOL_TYPE_DRIVE;
    case 11:
    case "SYMBOL_TYPE_REFRESH":
      return SymbolType.SYMBOL_TYPE_REFRESH;
    case 12:
    case "SYMBOL_TYPE_MUTE":
      return SymbolType.SYMBOL_TYPE_MUTE;
    case 13:
    case "SYMBOL_TYPE_VOLUME_MID":
      return SymbolType.SYMBOL_TYPE_VOLUME_MID;
    case 14:
    case "SYMBOL_TYPE_VOLUME_MAX":
      return SymbolType.SYMBOL_TYPE_VOLUME_MAX;
    case 15:
    case "SYMBOL_TYPE_IMAGE":
      return SymbolType.SYMBOL_TYPE_IMAGE;
    case 16:
    case "SYMBOL_TYPE_TINT":
      return SymbolType.SYMBOL_TYPE_TINT;
    case 17:
    case "SYMBOL_TYPE_PREV":
      return SymbolType.SYMBOL_TYPE_PREV;
    case 18:
    case "SYMBOL_TYPE_PLAY":
      return SymbolType.SYMBOL_TYPE_PLAY;
    case 19:
    case "SYMBOL_TYPE_PAUSE":
      return SymbolType.SYMBOL_TYPE_PAUSE;
    case 20:
    case "SYMBOL_TYPE_STOP":
      return SymbolType.SYMBOL_TYPE_STOP;
    case 21:
    case "SYMBOL_TYPE_EJECT":
      return SymbolType.SYMBOL_TYPE_EJECT;
    case 22:
    case "SYMBOL_TYPE_LEFT":
      return SymbolType.SYMBOL_TYPE_LEFT;
    case 23:
    case "SYMBOL_TYPE_RIGHT":
      return SymbolType.SYMBOL_TYPE_RIGHT;
    case 24:
    case "SYMBOL_TYPE_PLUS":
      return SymbolType.SYMBOL_TYPE_PLUS;
    case 25:
    case "SYMBOL_TYPE_MINUS":
      return SymbolType.SYMBOL_TYPE_MINUS;
    case 26:
    case "SYMBOL_TYPE_EYE_OPEN":
      return SymbolType.SYMBOL_TYPE_EYE_OPEN;
    case 27:
    case "SYMBOL_TYPE_EYE_CLOSE":
      return SymbolType.SYMBOL_TYPE_EYE_CLOSE;
    case 28:
    case "SYMBOL_TYPE_WARNING":
      return SymbolType.SYMBOL_TYPE_WARNING;
    case 29:
    case "SYMBOL_TYPE_SHUFFLE":
      return SymbolType.SYMBOL_TYPE_SHUFFLE;
    case 30:
    case "SYMBOL_TYPE_UP":
      return SymbolType.SYMBOL_TYPE_UP;
    case 31:
    case "SYMBOL_TYPE_DOWN":
      return SymbolType.SYMBOL_TYPE_DOWN;
    case 32:
    case "SYMBOL_TYPE_LOOP":
      return SymbolType.SYMBOL_TYPE_LOOP;
    case 33:
    case "SYMBOL_TYPE_DIRECTORY":
      return SymbolType.SYMBOL_TYPE_DIRECTORY;
    case 34:
    case "SYMBOL_TYPE_UPLOAD":
      return SymbolType.SYMBOL_TYPE_UPLOAD;
    case 35:
    case "SYMBOL_TYPE_CALL":
      return SymbolType.SYMBOL_TYPE_CALL;
    case 36:
    case "SYMBOL_TYPE_CUT":
      return SymbolType.SYMBOL_TYPE_CUT;
    case 37:
    case "SYMBOL_TYPE_COPY":
      return SymbolType.SYMBOL_TYPE_COPY;
    case 38:
    case "SYMBOL_TYPE_SAVE":
      return SymbolType.SYMBOL_TYPE_SAVE;
    case 39:
    case "SYMBOL_TYPE_BARS":
      return SymbolType.SYMBOL_TYPE_BARS;
    case 40:
    case "SYMBOL_TYPE_ENVELOPE":
      return SymbolType.SYMBOL_TYPE_ENVELOPE;
    case 41:
    case "SYMBOL_TYPE_CHARGE":
      return SymbolType.SYMBOL_TYPE_CHARGE;
    case 42:
    case "SYMBOL_TYPE_PASTE":
      return SymbolType.SYMBOL_TYPE_PASTE;
    case 43:
    case "SYMBOL_TYPE_BELL":
      return SymbolType.SYMBOL_TYPE_BELL;
    case 44:
    case "SYMBOL_TYPE_KEYBOARD":
      return SymbolType.SYMBOL_TYPE_KEYBOARD;
    case 45:
    case "SYMBOL_TYPE_GPS":
      return SymbolType.SYMBOL_TYPE_GPS;
    case 46:
    case "SYMBOL_TYPE_FILE":
      return SymbolType.SYMBOL_TYPE_FILE;
    case 47:
    case "SYMBOL_TYPE_WIFI":
      return SymbolType.SYMBOL_TYPE_WIFI;
    case 48:
    case "SYMBOL_TYPE_BATTERY_FULL":
      return SymbolType.SYMBOL_TYPE_BATTERY_FULL;
    case 49:
    case "SYMBOL_TYPE_BATTERY_3":
      return SymbolType.SYMBOL_TYPE_BATTERY_3;
    case 50:
    case "SYMBOL_TYPE_BATTERY_2":
      return SymbolType.SYMBOL_TYPE_BATTERY_2;
    case 51:
    case "SYMBOL_TYPE_BATTERY_1":
      return SymbolType.SYMBOL_TYPE_BATTERY_1;
    case 52:
    case "SYMBOL_TYPE_BATTERY_EMPTY":
      return SymbolType.SYMBOL_TYPE_BATTERY_EMPTY;
    case 53:
    case "SYMBOL_TYPE_USB":
      return SymbolType.SYMBOL_TYPE_USB;
    case 54:
    case "SYMBOL_TYPE_BLUETOOTH":
      return SymbolType.SYMBOL_TYPE_BLUETOOTH;
    case 55:
    case "SYMBOL_TYPE_TRASH":
      return SymbolType.SYMBOL_TYPE_TRASH;
    case 56:
    case "SYMBOL_TYPE_EDIT":
      return SymbolType.SYMBOL_TYPE_EDIT;
    case 57:
    case "SYMBOL_TYPE_BACKSPACE":
      return SymbolType.SYMBOL_TYPE_BACKSPACE;
    case 58:
    case "SYMBOL_TYPE_SD_CARD":
      return SymbolType.SYMBOL_TYPE_SD_CARD;
    case 59:
    case "SYMBOL_TYPE_NEW_LINE":
      return SymbolType.SYMBOL_TYPE_NEW_LINE;
    case 60:
    case "SYMBOL_TYPE_DUMMY":
      return SymbolType.SYMBOL_TYPE_DUMMY;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SymbolType.UNRECOGNIZED;
  }
}

export function symbolTypeToJSON(object: SymbolType): string {
  switch (object) {
    case SymbolType.SYMBOL_TYPE_BULLET:
      return "SYMBOL_TYPE_BULLET";
    case SymbolType.SYMBOL_TYPE_AUDIO:
      return "SYMBOL_TYPE_AUDIO";
    case SymbolType.SYMBOL_TYPE_VIDEO:
      return "SYMBOL_TYPE_VIDEO";
    case SymbolType.SYMBOL_TYPE_LIST:
      return "SYMBOL_TYPE_LIST";
    case SymbolType.SYMBOL_TYPE_OK:
      return "SYMBOL_TYPE_OK";
    case SymbolType.SYMBOL_TYPE_CLOSE:
      return "SYMBOL_TYPE_CLOSE";
    case SymbolType.SYMBOL_TYPE_POWER:
      return "SYMBOL_TYPE_POWER";
    case SymbolType.SYMBOL_TYPE_SETTINGS:
      return "SYMBOL_TYPE_SETTINGS";
    case SymbolType.SYMBOL_TYPE_HOME:
      return "SYMBOL_TYPE_HOME";
    case SymbolType.SYMBOL_TYPE_DOWNLOAD:
      return "SYMBOL_TYPE_DOWNLOAD";
    case SymbolType.SYMBOL_TYPE_DRIVE:
      return "SYMBOL_TYPE_DRIVE";
    case SymbolType.SYMBOL_TYPE_REFRESH:
      return "SYMBOL_TYPE_REFRESH";
    case SymbolType.SYMBOL_TYPE_MUTE:
      return "SYMBOL_TYPE_MUTE";
    case SymbolType.SYMBOL_TYPE_VOLUME_MID:
      return "SYMBOL_TYPE_VOLUME_MID";
    case SymbolType.SYMBOL_TYPE_VOLUME_MAX:
      return "SYMBOL_TYPE_VOLUME_MAX";
    case SymbolType.SYMBOL_TYPE_IMAGE:
      return "SYMBOL_TYPE_IMAGE";
    case SymbolType.SYMBOL_TYPE_TINT:
      return "SYMBOL_TYPE_TINT";
    case SymbolType.SYMBOL_TYPE_PREV:
      return "SYMBOL_TYPE_PREV";
    case SymbolType.SYMBOL_TYPE_PLAY:
      return "SYMBOL_TYPE_PLAY";
    case SymbolType.SYMBOL_TYPE_PAUSE:
      return "SYMBOL_TYPE_PAUSE";
    case SymbolType.SYMBOL_TYPE_STOP:
      return "SYMBOL_TYPE_STOP";
    case SymbolType.SYMBOL_TYPE_EJECT:
      return "SYMBOL_TYPE_EJECT";
    case SymbolType.SYMBOL_TYPE_LEFT:
      return "SYMBOL_TYPE_LEFT";
    case SymbolType.SYMBOL_TYPE_RIGHT:
      return "SYMBOL_TYPE_RIGHT";
    case SymbolType.SYMBOL_TYPE_PLUS:
      return "SYMBOL_TYPE_PLUS";
    case SymbolType.SYMBOL_TYPE_MINUS:
      return "SYMBOL_TYPE_MINUS";
    case SymbolType.SYMBOL_TYPE_EYE_OPEN:
      return "SYMBOL_TYPE_EYE_OPEN";
    case SymbolType.SYMBOL_TYPE_EYE_CLOSE:
      return "SYMBOL_TYPE_EYE_CLOSE";
    case SymbolType.SYMBOL_TYPE_WARNING:
      return "SYMBOL_TYPE_WARNING";
    case SymbolType.SYMBOL_TYPE_SHUFFLE:
      return "SYMBOL_TYPE_SHUFFLE";
    case SymbolType.SYMBOL_TYPE_UP:
      return "SYMBOL_TYPE_UP";
    case SymbolType.SYMBOL_TYPE_DOWN:
      return "SYMBOL_TYPE_DOWN";
    case SymbolType.SYMBOL_TYPE_LOOP:
      return "SYMBOL_TYPE_LOOP";
    case SymbolType.SYMBOL_TYPE_DIRECTORY:
      return "SYMBOL_TYPE_DIRECTORY";
    case SymbolType.SYMBOL_TYPE_UPLOAD:
      return "SYMBOL_TYPE_UPLOAD";
    case SymbolType.SYMBOL_TYPE_CALL:
      return "SYMBOL_TYPE_CALL";
    case SymbolType.SYMBOL_TYPE_CUT:
      return "SYMBOL_TYPE_CUT";
    case SymbolType.SYMBOL_TYPE_COPY:
      return "SYMBOL_TYPE_COPY";
    case SymbolType.SYMBOL_TYPE_SAVE:
      return "SYMBOL_TYPE_SAVE";
    case SymbolType.SYMBOL_TYPE_BARS:
      return "SYMBOL_TYPE_BARS";
    case SymbolType.SYMBOL_TYPE_ENVELOPE:
      return "SYMBOL_TYPE_ENVELOPE";
    case SymbolType.SYMBOL_TYPE_CHARGE:
      return "SYMBOL_TYPE_CHARGE";
    case SymbolType.SYMBOL_TYPE_PASTE:
      return "SYMBOL_TYPE_PASTE";
    case SymbolType.SYMBOL_TYPE_BELL:
      return "SYMBOL_TYPE_BELL";
    case SymbolType.SYMBOL_TYPE_KEYBOARD:
      return "SYMBOL_TYPE_KEYBOARD";
    case SymbolType.SYMBOL_TYPE_GPS:
      return "SYMBOL_TYPE_GPS";
    case SymbolType.SYMBOL_TYPE_FILE:
      return "SYMBOL_TYPE_FILE";
    case SymbolType.SYMBOL_TYPE_WIFI:
      return "SYMBOL_TYPE_WIFI";
    case SymbolType.SYMBOL_TYPE_BATTERY_FULL:
      return "SYMBOL_TYPE_BATTERY_FULL";
    case SymbolType.SYMBOL_TYPE_BATTERY_3:
      return "SYMBOL_TYPE_BATTERY_3";
    case SymbolType.SYMBOL_TYPE_BATTERY_2:
      return "SYMBOL_TYPE_BATTERY_2";
    case SymbolType.SYMBOL_TYPE_BATTERY_1:
      return "SYMBOL_TYPE_BATTERY_1";
    case SymbolType.SYMBOL_TYPE_BATTERY_EMPTY:
      return "SYMBOL_TYPE_BATTERY_EMPTY";
    case SymbolType.SYMBOL_TYPE_USB:
      return "SYMBOL_TYPE_USB";
    case SymbolType.SYMBOL_TYPE_BLUETOOTH:
      return "SYMBOL_TYPE_BLUETOOTH";
    case SymbolType.SYMBOL_TYPE_TRASH:
      return "SYMBOL_TYPE_TRASH";
    case SymbolType.SYMBOL_TYPE_EDIT:
      return "SYMBOL_TYPE_EDIT";
    case SymbolType.SYMBOL_TYPE_BACKSPACE:
      return "SYMBOL_TYPE_BACKSPACE";
    case SymbolType.SYMBOL_TYPE_SD_CARD:
      return "SYMBOL_TYPE_SD_CARD";
    case SymbolType.SYMBOL_TYPE_NEW_LINE:
      return "SYMBOL_TYPE_NEW_LINE";
    case SymbolType.SYMBOL_TYPE_DUMMY:
      return "SYMBOL_TYPE_DUMMY";
    case SymbolType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface Location {
  leftI16: number;
  topI16: number;
}

export interface Size {
  widthI16: number;
  heightI16: number;
}

/** 适用于边距之类的，比如margin或者padding */
export interface PositionOffset {
  leftI16: number;
  topI16: number;
  rightI16: number;
  bottomI16: number;
}

/** 保存选项 */
export interface SaveOption {
  /** 保存位置 */
  saveLocation: SaveLocation;
  /** 目录名(含有规则) */
  dirName: string;
  /** 文件名(含有规则) */
  fileName: string;
  /** 保留时长（天） */
  keepDays: number;
  /** 保留条数 */
  keepCount: number;
  /** 超出后不保存，默认清掉旧的 */
  discardWhenFull: boolean;
}

function createBaseLocation(): Location {
  return { leftI16: 0, topI16: 0 };
}

export const Location: MessageFns<Location> = {
  encode(message: Location, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.leftI16 !== 0) {
      writer.uint32(8).int32(message.leftI16);
    }
    if (message.topI16 !== 0) {
      writer.uint32(16).int32(message.topI16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Location {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.leftI16 = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.topI16 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Location {
    return {
      leftI16: isSet(object.leftI16) ? globalThis.Number(object.leftI16) : 0,
      topI16: isSet(object.topI16) ? globalThis.Number(object.topI16) : 0,
    };
  },

  toJSON(message: Location): unknown {
    const obj: any = {};
    if (message.leftI16 !== 0) {
      obj.leftI16 = Math.round(message.leftI16);
    }
    if (message.topI16 !== 0) {
      obj.topI16 = Math.round(message.topI16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Location>, I>>(base?: I): Location {
    return Location.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Location>, I>>(object: I): Location {
    const message = createBaseLocation();
    message.leftI16 = object.leftI16 ?? 0;
    message.topI16 = object.topI16 ?? 0;
    return message;
  },
};

function createBaseSize(): Size {
  return { widthI16: 0, heightI16: 0 };
}

export const Size: MessageFns<Size> = {
  encode(message: Size, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.widthI16 !== 0) {
      writer.uint32(8).int32(message.widthI16);
    }
    if (message.heightI16 !== 0) {
      writer.uint32(16).int32(message.heightI16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Size {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSize();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.widthI16 = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.heightI16 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Size {
    return {
      widthI16: isSet(object.widthI16) ? globalThis.Number(object.widthI16) : 0,
      heightI16: isSet(object.heightI16) ? globalThis.Number(object.heightI16) : 0,
    };
  },

  toJSON(message: Size): unknown {
    const obj: any = {};
    if (message.widthI16 !== 0) {
      obj.widthI16 = Math.round(message.widthI16);
    }
    if (message.heightI16 !== 0) {
      obj.heightI16 = Math.round(message.heightI16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Size>, I>>(base?: I): Size {
    return Size.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Size>, I>>(object: I): Size {
    const message = createBaseSize();
    message.widthI16 = object.widthI16 ?? 0;
    message.heightI16 = object.heightI16 ?? 0;
    return message;
  },
};

function createBasePositionOffset(): PositionOffset {
  return { leftI16: 0, topI16: 0, rightI16: 0, bottomI16: 0 };
}

export const PositionOffset: MessageFns<PositionOffset> = {
  encode(message: PositionOffset, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.leftI16 !== 0) {
      writer.uint32(8).int32(message.leftI16);
    }
    if (message.topI16 !== 0) {
      writer.uint32(16).int32(message.topI16);
    }
    if (message.rightI16 !== 0) {
      writer.uint32(24).int32(message.rightI16);
    }
    if (message.bottomI16 !== 0) {
      writer.uint32(32).int32(message.bottomI16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PositionOffset {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePositionOffset();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.leftI16 = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.topI16 = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.rightI16 = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.bottomI16 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PositionOffset {
    return {
      leftI16: isSet(object.leftI16) ? globalThis.Number(object.leftI16) : 0,
      topI16: isSet(object.topI16) ? globalThis.Number(object.topI16) : 0,
      rightI16: isSet(object.rightI16) ? globalThis.Number(object.rightI16) : 0,
      bottomI16: isSet(object.bottomI16) ? globalThis.Number(object.bottomI16) : 0,
    };
  },

  toJSON(message: PositionOffset): unknown {
    const obj: any = {};
    if (message.leftI16 !== 0) {
      obj.leftI16 = Math.round(message.leftI16);
    }
    if (message.topI16 !== 0) {
      obj.topI16 = Math.round(message.topI16);
    }
    if (message.rightI16 !== 0) {
      obj.rightI16 = Math.round(message.rightI16);
    }
    if (message.bottomI16 !== 0) {
      obj.bottomI16 = Math.round(message.bottomI16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PositionOffset>, I>>(base?: I): PositionOffset {
    return PositionOffset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PositionOffset>, I>>(object: I): PositionOffset {
    const message = createBasePositionOffset();
    message.leftI16 = object.leftI16 ?? 0;
    message.topI16 = object.topI16 ?? 0;
    message.rightI16 = object.rightI16 ?? 0;
    message.bottomI16 = object.bottomI16 ?? 0;
    return message;
  },
};

function createBaseSaveOption(): SaveOption {
  return { saveLocation: 0, dirName: "", fileName: "", keepDays: 0, keepCount: 0, discardWhenFull: false };
}

export const SaveOption: MessageFns<SaveOption> = {
  encode(message: SaveOption, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.saveLocation !== 0) {
      writer.uint32(8).int32(message.saveLocation);
    }
    if (message.dirName !== "") {
      writer.uint32(18).string(message.dirName);
    }
    if (message.fileName !== "") {
      writer.uint32(26).string(message.fileName);
    }
    if (message.keepDays !== 0) {
      writer.uint32(32).int32(message.keepDays);
    }
    if (message.keepCount !== 0) {
      writer.uint32(40).int32(message.keepCount);
    }
    if (message.discardWhenFull !== false) {
      writer.uint32(48).bool(message.discardWhenFull);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SaveOption {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSaveOption();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.saveLocation = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.dirName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fileName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.keepDays = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.keepCount = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.discardWhenFull = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SaveOption {
    return {
      saveLocation: isSet(object.saveLocation) ? saveLocationFromJSON(object.saveLocation) : 0,
      dirName: isSet(object.dirName) ? globalThis.String(object.dirName) : "",
      fileName: isSet(object.fileName) ? globalThis.String(object.fileName) : "",
      keepDays: isSet(object.keepDays) ? globalThis.Number(object.keepDays) : 0,
      keepCount: isSet(object.keepCount) ? globalThis.Number(object.keepCount) : 0,
      discardWhenFull: isSet(object.discardWhenFull) ? globalThis.Boolean(object.discardWhenFull) : false,
    };
  },

  toJSON(message: SaveOption): unknown {
    const obj: any = {};
    if (message.saveLocation !== 0) {
      obj.saveLocation = saveLocationToJSON(message.saveLocation);
    }
    if (message.dirName !== "") {
      obj.dirName = message.dirName;
    }
    if (message.fileName !== "") {
      obj.fileName = message.fileName;
    }
    if (message.keepDays !== 0) {
      obj.keepDays = Math.round(message.keepDays);
    }
    if (message.keepCount !== 0) {
      obj.keepCount = Math.round(message.keepCount);
    }
    if (message.discardWhenFull !== false) {
      obj.discardWhenFull = message.discardWhenFull;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SaveOption>, I>>(base?: I): SaveOption {
    return SaveOption.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveOption>, I>>(object: I): SaveOption {
    const message = createBaseSaveOption();
    message.saveLocation = object.saveLocation ?? 0;
    message.dirName = object.dirName ?? "";
    message.fileName = object.fileName ?? "";
    message.keepDays = object.keepDays ?? 0;
    message.keepCount = object.keepCount ?? 0;
    message.discardWhenFull = object.discardWhenFull ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
