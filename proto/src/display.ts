// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/display.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  Location,
  PositionType,
  positionTypeFromJSON,
  positionTypeToJSON,
  ProtectType,
  protectTypeFromJSON,
  protectTypeToJSON,
  Size,
} from "./common";
import { GraphicReference, StyleProperties } from "./style";

export const protobufPackage = "znd.project.v1";

/** 旋转角度类型 */
export enum RotateType {
  /** ROTATE_TYPE_UNSPECIFIED - 不旋转 */
  ROTATE_TYPE_UNSPECIFIED = 0,
  /** ROTATE_TYPE_90 - 90度 */
  ROTATE_TYPE_90 = 1,
  /** ROTATE_TYPE_180 - 180度 */
  ROTATE_TYPE_180 = 2,
  /** ROTATE_TYPE_270 - 270度 */
  ROTATE_TYPE_270 = 3,
  UNRECOGNIZED = -1,
}

export function rotateTypeFromJSON(object: any): RotateType {
  switch (object) {
    case 0:
    case "ROTATE_TYPE_UNSPECIFIED":
      return RotateType.ROTATE_TYPE_UNSPECIFIED;
    case 1:
    case "ROTATE_TYPE_90":
      return RotateType.ROTATE_TYPE_90;
    case 2:
    case "ROTATE_TYPE_180":
      return RotateType.ROTATE_TYPE_180;
    case 3:
    case "ROTATE_TYPE_270":
      return RotateType.ROTATE_TYPE_270;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RotateType.UNRECOGNIZED;
  }
}

export function rotateTypeToJSON(object: RotateType): string {
  switch (object) {
    case RotateType.ROTATE_TYPE_UNSPECIFIED:
      return "ROTATE_TYPE_UNSPECIFIED";
    case RotateType.ROTATE_TYPE_90:
      return "ROTATE_TYPE_90";
    case RotateType.ROTATE_TYPE_180:
      return "ROTATE_TYPE_180";
    case RotateType.ROTATE_TYPE_270:
      return "ROTATE_TYPE_270";
    case RotateType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 屏幕输出类型 */
export enum DisplayOutputType {
  /** DISPLAY_OUTPUT_TYPE_UNSPECIFIED - 未定义 */
  DISPLAY_OUTPUT_TYPE_UNSPECIFIED = 0,
  /** DISPLAY_OUTPUT_TYPE_LOCAL - 本地 */
  DISPLAY_OUTPUT_TYPE_LOCAL = 1,
  /** DISPLAY_OUTPUT_TYPE_MOBILE - 移动端 */
  DISPLAY_OUTPUT_TYPE_MOBILE = 2,
  /** DISPLAY_OUTPUT_TYPE_PAD - 平板 */
  DISPLAY_OUTPUT_TYPE_PAD = 3,
  /** DISPLAY_OUTPUT_TYPE_TV - 电视 */
  DISPLAY_OUTPUT_TYPE_TV = 4,
  /** DISPLAY_OUTPUT_TYPE_PC - PC */
  DISPLAY_OUTPUT_TYPE_PC = 5,
  /** DISPLAY_OUTPUT_TYPE_TYPEC - TYPEC */
  DISPLAY_OUTPUT_TYPE_TYPEC = 6,
  /** DISPLAY_OUTPUT_TYPE_HDMI1 - HDMI1 */
  DISPLAY_OUTPUT_TYPE_HDMI1 = 7,
  /** DISPLAY_OUTPUT_TYPE_HDMI2 - HDMI2 */
  DISPLAY_OUTPUT_TYPE_HDMI2 = 8,
  UNRECOGNIZED = -1,
}

export function displayOutputTypeFromJSON(object: any): DisplayOutputType {
  switch (object) {
    case 0:
    case "DISPLAY_OUTPUT_TYPE_UNSPECIFIED":
      return DisplayOutputType.DISPLAY_OUTPUT_TYPE_UNSPECIFIED;
    case 1:
    case "DISPLAY_OUTPUT_TYPE_LOCAL":
      return DisplayOutputType.DISPLAY_OUTPUT_TYPE_LOCAL;
    case 2:
    case "DISPLAY_OUTPUT_TYPE_MOBILE":
      return DisplayOutputType.DISPLAY_OUTPUT_TYPE_MOBILE;
    case 3:
    case "DISPLAY_OUTPUT_TYPE_PAD":
      return DisplayOutputType.DISPLAY_OUTPUT_TYPE_PAD;
    case 4:
    case "DISPLAY_OUTPUT_TYPE_TV":
      return DisplayOutputType.DISPLAY_OUTPUT_TYPE_TV;
    case 5:
    case "DISPLAY_OUTPUT_TYPE_PC":
      return DisplayOutputType.DISPLAY_OUTPUT_TYPE_PC;
    case 6:
    case "DISPLAY_OUTPUT_TYPE_TYPEC":
      return DisplayOutputType.DISPLAY_OUTPUT_TYPE_TYPEC;
    case 7:
    case "DISPLAY_OUTPUT_TYPE_HDMI1":
      return DisplayOutputType.DISPLAY_OUTPUT_TYPE_HDMI1;
    case 8:
    case "DISPLAY_OUTPUT_TYPE_HDMI2":
      return DisplayOutputType.DISPLAY_OUTPUT_TYPE_HDMI2;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DisplayOutputType.UNRECOGNIZED;
  }
}

export function displayOutputTypeToJSON(object: DisplayOutputType): string {
  switch (object) {
    case DisplayOutputType.DISPLAY_OUTPUT_TYPE_UNSPECIFIED:
      return "DISPLAY_OUTPUT_TYPE_UNSPECIFIED";
    case DisplayOutputType.DISPLAY_OUTPUT_TYPE_LOCAL:
      return "DISPLAY_OUTPUT_TYPE_LOCAL";
    case DisplayOutputType.DISPLAY_OUTPUT_TYPE_MOBILE:
      return "DISPLAY_OUTPUT_TYPE_MOBILE";
    case DisplayOutputType.DISPLAY_OUTPUT_TYPE_PAD:
      return "DISPLAY_OUTPUT_TYPE_PAD";
    case DisplayOutputType.DISPLAY_OUTPUT_TYPE_TV:
      return "DISPLAY_OUTPUT_TYPE_TV";
    case DisplayOutputType.DISPLAY_OUTPUT_TYPE_PC:
      return "DISPLAY_OUTPUT_TYPE_PC";
    case DisplayOutputType.DISPLAY_OUTPUT_TYPE_TYPEC:
      return "DISPLAY_OUTPUT_TYPE_TYPEC";
    case DisplayOutputType.DISPLAY_OUTPUT_TYPE_HDMI1:
      return "DISPLAY_OUTPUT_TYPE_HDMI1";
    case DisplayOutputType.DISPLAY_OUTPUT_TYPE_HDMI2:
      return "DISPLAY_OUTPUT_TYPE_HDMI2";
    case DisplayOutputType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 页面自动大小类型 */
export enum PageSizeType {
  /** PAGE_SIZE_TYPE_UNSPECIFIED - 未定义（自由尺寸） */
  PAGE_SIZE_TYPE_UNSPECIFIED = 0,
  /** PAGE_SIZE_TYPE_FULL - 全屏 */
  PAGE_SIZE_TYPE_FULL = 1,
  /** PAGE_SIZE_TYPE_LARGE - 大尺寸 */
  PAGE_SIZE_TYPE_LARGE = 2,
  /** PAGE_SIZE_TYPE_MEDIUM - 中尺寸 */
  PAGE_SIZE_TYPE_MEDIUM = 3,
  /** PAGE_SIZE_TYPE_SMALL - 小尺寸 */
  PAGE_SIZE_TYPE_SMALL = 4,
  UNRECOGNIZED = -1,
}

export function pageSizeTypeFromJSON(object: any): PageSizeType {
  switch (object) {
    case 0:
    case "PAGE_SIZE_TYPE_UNSPECIFIED":
      return PageSizeType.PAGE_SIZE_TYPE_UNSPECIFIED;
    case 1:
    case "PAGE_SIZE_TYPE_FULL":
      return PageSizeType.PAGE_SIZE_TYPE_FULL;
    case 2:
    case "PAGE_SIZE_TYPE_LARGE":
      return PageSizeType.PAGE_SIZE_TYPE_LARGE;
    case 3:
    case "PAGE_SIZE_TYPE_MEDIUM":
      return PageSizeType.PAGE_SIZE_TYPE_MEDIUM;
    case 4:
    case "PAGE_SIZE_TYPE_SMALL":
      return PageSizeType.PAGE_SIZE_TYPE_SMALL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PageSizeType.UNRECOGNIZED;
  }
}

export function pageSizeTypeToJSON(object: PageSizeType): string {
  switch (object) {
    case PageSizeType.PAGE_SIZE_TYPE_UNSPECIFIED:
      return "PAGE_SIZE_TYPE_UNSPECIFIED";
    case PageSizeType.PAGE_SIZE_TYPE_FULL:
      return "PAGE_SIZE_TYPE_FULL";
    case PageSizeType.PAGE_SIZE_TYPE_LARGE:
      return "PAGE_SIZE_TYPE_LARGE";
    case PageSizeType.PAGE_SIZE_TYPE_MEDIUM:
      return "PAGE_SIZE_TYPE_MEDIUM";
    case PageSizeType.PAGE_SIZE_TYPE_SMALL:
      return "PAGE_SIZE_TYPE_SMALL";
    case PageSizeType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 页面布局类型 */
export enum LayoutType {
  /** LAYOUT_TYPE_UNSPECIFIED - 自由布局 */
  LAYOUT_TYPE_UNSPECIFIED = 0,
  LAYOUT_TYPE_FLEX = 1,
  LAYOUT_TYPE_GRID = 2,
  UNRECOGNIZED = -1,
}

export function layoutTypeFromJSON(object: any): LayoutType {
  switch (object) {
    case 0:
    case "LAYOUT_TYPE_UNSPECIFIED":
      return LayoutType.LAYOUT_TYPE_UNSPECIFIED;
    case 1:
    case "LAYOUT_TYPE_FLEX":
      return LayoutType.LAYOUT_TYPE_FLEX;
    case 2:
    case "LAYOUT_TYPE_GRID":
      return LayoutType.LAYOUT_TYPE_GRID;
    case -1:
    case "UNRECOGNIZED":
    default:
      return LayoutType.UNRECOGNIZED;
  }
}

export function layoutTypeToJSON(object: LayoutType): string {
  switch (object) {
    case LayoutType.LAYOUT_TYPE_UNSPECIFIED:
      return "LAYOUT_TYPE_UNSPECIFIED";
    case LayoutType.LAYOUT_TYPE_FLEX:
      return "LAYOUT_TYPE_FLEX";
    case LayoutType.LAYOUT_TYPE_GRID:
      return "LAYOUT_TYPE_GRID";
    case LayoutType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/**
 * 页面类型（界面上主要分为基本窗口和功能窗口，基本窗口一般是主操作窗口，功能窗口不叫系统窗口的原因是，客户会增加和自定义）
 * 更细区分类型的原因，主要是为了用户在界面上选择时可以根据场景找到相应的窗口
 * 窗口逻辑：HMI的窗口主要分为基本窗口和公共窗口，基本窗口在HMI上只会有一个，进行切换，公共窗口同理
 * 画面跳转就分为基本窗口跳转和公共窗口跳转，基本窗口有且只有一个，公共窗口可以跳到0，也就是可以没有公共窗口
 */
export enum PageType {
  /** PAGE_TYPE_UNSPECIFIED - 未定义 */
  PAGE_TYPE_UNSPECIFIED = 0,
  /** PAGE_TYPE_NORMAL - 基本窗口，或者叫普通窗口 */
  PAGE_TYPE_NORMAL = 1,
  /** PAGE_TYPE_COMMON - 公共窗口 */
  PAGE_TYPE_COMMON = 2,
  /**
   * PAGE_TYPE_KEYBOARD - 功能窗口中的键盘窗口
   * 是为了图元中选择输入键盘可以少一些
   */
  PAGE_TYPE_KEYBOARD = 3,
  /**
   * PAGE_TYPE_MESSAGE - 功能窗口中的信息窗口，信息提示、确认、询问类，如权限不足提醒，执行确认、通讯报错等
   * 是为了图元中执行确认的选择可以少一些
   */
  PAGE_TYPE_MESSAGE = 4,
  /** PAGE_TYPE_FUNCTION - 功能窗口中的其他窗口，如用户登录、权限配置、配方操作、网络设置、后台设置等 */
  PAGE_TYPE_FUNCTION = 5,
  UNRECOGNIZED = -1,
}

export function pageTypeFromJSON(object: any): PageType {
  switch (object) {
    case 0:
    case "PAGE_TYPE_UNSPECIFIED":
      return PageType.PAGE_TYPE_UNSPECIFIED;
    case 1:
    case "PAGE_TYPE_NORMAL":
      return PageType.PAGE_TYPE_NORMAL;
    case 2:
    case "PAGE_TYPE_COMMON":
      return PageType.PAGE_TYPE_COMMON;
    case 3:
    case "PAGE_TYPE_KEYBOARD":
      return PageType.PAGE_TYPE_KEYBOARD;
    case 4:
    case "PAGE_TYPE_MESSAGE":
      return PageType.PAGE_TYPE_MESSAGE;
    case 5:
    case "PAGE_TYPE_FUNCTION":
      return PageType.PAGE_TYPE_FUNCTION;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PageType.UNRECOGNIZED;
  }
}

export function pageTypeToJSON(object: PageType): string {
  switch (object) {
    case PageType.PAGE_TYPE_UNSPECIFIED:
      return "PAGE_TYPE_UNSPECIFIED";
    case PageType.PAGE_TYPE_NORMAL:
      return "PAGE_TYPE_NORMAL";
    case PageType.PAGE_TYPE_COMMON:
      return "PAGE_TYPE_COMMON";
    case PageType.PAGE_TYPE_KEYBOARD:
      return "PAGE_TYPE_KEYBOARD";
    case PageType.PAGE_TYPE_MESSAGE:
      return "PAGE_TYPE_MESSAGE";
    case PageType.PAGE_TYPE_FUNCTION:
      return "PAGE_TYPE_FUNCTION";
    case PageType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 弹出窗口方式 */
export enum PagePopupType {
  /** PAGE_POPUP_TYPE_UNSPECIFIED - 非弹出窗口 */
  PAGE_POPUP_TYPE_UNSPECIFIED = 0,
  /** PAGE_POPUP_TYPE_HALF_MODEL - 半垄断式弹出(半模态窗口，指点击外部可关闭，默认) */
  PAGE_POPUP_TYPE_HALF_MODEL = 1,
  /** PAGE_POPUP_TYPE_MODEL - 垄断式弹出(模态窗口) */
  PAGE_POPUP_TYPE_MODEL = 2,
  /** PAGE_POPUP_TYPE_FLOATING - 浮窗式弹出(非模态窗口) */
  PAGE_POPUP_TYPE_FLOATING = 3,
  UNRECOGNIZED = -1,
}

export function pagePopupTypeFromJSON(object: any): PagePopupType {
  switch (object) {
    case 0:
    case "PAGE_POPUP_TYPE_UNSPECIFIED":
      return PagePopupType.PAGE_POPUP_TYPE_UNSPECIFIED;
    case 1:
    case "PAGE_POPUP_TYPE_HALF_MODEL":
      return PagePopupType.PAGE_POPUP_TYPE_HALF_MODEL;
    case 2:
    case "PAGE_POPUP_TYPE_MODEL":
      return PagePopupType.PAGE_POPUP_TYPE_MODEL;
    case 3:
    case "PAGE_POPUP_TYPE_FLOATING":
      return PagePopupType.PAGE_POPUP_TYPE_FLOATING;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PagePopupType.UNRECOGNIZED;
  }
}

export function pagePopupTypeToJSON(object: PagePopupType): string {
  switch (object) {
    case PagePopupType.PAGE_POPUP_TYPE_UNSPECIFIED:
      return "PAGE_POPUP_TYPE_UNSPECIFIED";
    case PagePopupType.PAGE_POPUP_TYPE_HALF_MODEL:
      return "PAGE_POPUP_TYPE_HALF_MODEL";
    case PagePopupType.PAGE_POPUP_TYPE_MODEL:
      return "PAGE_POPUP_TYPE_MODEL";
    case PagePopupType.PAGE_POPUP_TYPE_FLOATING:
      return "PAGE_POPUP_TYPE_FLOATING";
    case PagePopupType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface DisplayList {
  /** key为屏幕ID，用u8 */
  displays: { [key: number]: Display };
  /** key为页面ID，用u16(pageId为全局唯一) */
  pageInfos: { [key: number]: PageInfo };
  /** 各端的display入口，key是DisplayOutputType，value为display的id，用u8，如果该端没有，则使用默认display */
  displayEntries: { [key: number]: number };
  /** 默认display，用u8 */
  defaultDisplayIdU8: number;
}

export interface DisplayList_DisplaysEntry {
  key: number;
  value: Display | undefined;
}

export interface DisplayList_PageInfosEntry {
  key: number;
  value: PageInfo | undefined;
}

export interface DisplayList_DisplayEntriesEntry {
  key: number;
  value: number;
}

export interface Display {
  /** 屏幕名称 */
  name: string;
  /** 屏幕尺寸，单位像素 */
  size:
    | Size
    | undefined;
  /** 旋转角度 */
  rotate: RotateType;
  /** 页面分类列表 */
  pageClassifies: PageClassify[];
  /** 主窗口ID */
  mainPageIdU16: number;
  /** 窗口列表 */
  pageIdsU16: number[];
  /** 默认公共窗口(确实可能没有公共窗口) */
  commonPageIdU16?:
    | number
    | undefined;
  /** 公共窗口是否在基本窗口的上方，否则就是在下方 */
  commonPageOnTop: boolean;
  /** 样式 */
  style?:
    | StyleProperties
    | undefined;
  /** 始终缓存的页面 */
  alwaysCachePageIdsU16: number[];
  /** 缓存页面最大数量 */
  cachePageMaxCountU16: number;
  /** 输出类型,支持多种输出，数组为空表示都支持 */
  outputTypes: DisplayOutputType[];
}

/** 页面信息(元件列表移到了widget.proto中的PageWidgets) */
export interface PageInfo {
  name: string;
  pageType: PageType;
  /** 画面号，只在display内部唯一，优先在display内部找，如果找不到，则去找default display中的画面号 */
  pageNoU16?:
    | number
    | undefined;
  /** 页面分类ID */
  pageClassifyIdU16: number;
  /** 布局类型 */
  layout: LayoutType;
  /** 是否保护，分为只读和不让打开 */
  protectType: ProtectType;
  /** 保护密码，如果为空，则取工程参数中的公共保护密码 */
  protectPasswd?:
    | string
    | undefined;
  /**
   * 层叠窗口，如果为空，则不层叠
   * 如果有则按顺序先渲染层叠的，最后自己
   */
  stackPageIds: number[];
  /** 样式 */
  style?:
    | StyleProperties
    | undefined;
  /** 背景图 */
  backgroundGraphic?:
    | GraphicReference
    | undefined;
  /** 位置，一般是0不用设置，除非弹出时指定位置 */
  location?:
    | Location
    | undefined;
  /** 大小，未设置则是跟随display的尺寸或者自动大小 */
  size?:
    | Size
    | undefined;
  /** 自动大小类型(如果为空，则不自动大小)，尺寸的优先遵守原则是：size>auto_size_type>display.size */
  autoSizeType: PageSizeType;
  /** 弹出窗口方式 */
  popupType?:
    | PagePopupType
    | undefined;
  /** 弹出窗口位置(如果未定义，则取location的值) */
  popupPosition?:
    | PositionType
    | undefined;
  /** 键盘信息 */
  keyboardInfo?:
    | KeyboardInfo
    | undefined;
  /** 可跳转窗口列表,用于运行时预加载 */
  jumpPageIds: number[];
}

/** 键盘窗口信息 */
export interface KeyboardInfo {
  /** 最大值或最长长度元件id */
  maxValueWidgetId?:
    | number
    | undefined;
  /** 最小值或最短长度元件id */
  minValueWidgetId?:
    | number
    | undefined;
  /** 当前值元件id */
  currentValueWidgetId?:
    | number
    | undefined;
  /** 候选字区域坐标(相对键盘窗口坐标) */
  candidateLocation?:
    | Location
    | undefined;
  /** 候选字区域大小(相对键盘窗口坐标) */
  candidateSize?: Size | undefined;
}

/** 页面分类 */
export interface PageClassify {
  /** id */
  idU16: number;
  /** 名称 */
  name: string;
  /** 父分类id，没父类的为空 */
  parentIdU16?: number | undefined;
}

function createBaseDisplayList(): DisplayList {
  return { displays: {}, pageInfos: {}, displayEntries: {}, defaultDisplayIdU8: 0 };
}

export const DisplayList: MessageFns<DisplayList> = {
  encode(message: DisplayList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.displays).forEach(([key, value]) => {
      DisplayList_DisplaysEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    Object.entries(message.pageInfos).forEach(([key, value]) => {
      DisplayList_PageInfosEntry.encode({ key: key as any, value }, writer.uint32(58).fork()).join();
    });
    Object.entries(message.displayEntries).forEach(([key, value]) => {
      DisplayList_DisplayEntriesEntry.encode({ key: key as any, value }, writer.uint32(66).fork()).join();
    });
    if (message.defaultDisplayIdU8 !== 0) {
      writer.uint32(72).uint32(message.defaultDisplayIdU8);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DisplayList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDisplayList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = DisplayList_DisplaysEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.displays[entry1.key] = entry1.value;
          }
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          const entry7 = DisplayList_PageInfosEntry.decode(reader, reader.uint32());
          if (entry7.value !== undefined) {
            message.pageInfos[entry7.key] = entry7.value;
          }
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          const entry8 = DisplayList_DisplayEntriesEntry.decode(reader, reader.uint32());
          if (entry8.value !== undefined) {
            message.displayEntries[entry8.key] = entry8.value;
          }
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.defaultDisplayIdU8 = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DisplayList {
    return {
      displays: isObject(object.displays)
        ? Object.entries(object.displays).reduce<{ [key: number]: Display }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = Display.fromJSON(value);
          return acc;
        }, {})
        : {},
      pageInfos: isObject(object.pageInfos)
        ? Object.entries(object.pageInfos).reduce<{ [key: number]: PageInfo }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = PageInfo.fromJSON(value);
          return acc;
        }, {})
        : {},
      displayEntries: isObject(object.displayEntries)
        ? Object.entries(object.displayEntries).reduce<{ [key: number]: number }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = Number(value);
          return acc;
        }, {})
        : {},
      defaultDisplayIdU8: isSet(object.defaultDisplayIdU8) ? globalThis.Number(object.defaultDisplayIdU8) : 0,
    };
  },

  toJSON(message: DisplayList): unknown {
    const obj: any = {};
    if (message.displays) {
      const entries = Object.entries(message.displays);
      if (entries.length > 0) {
        obj.displays = {};
        entries.forEach(([k, v]) => {
          obj.displays[k] = Display.toJSON(v);
        });
      }
    }
    if (message.pageInfos) {
      const entries = Object.entries(message.pageInfos);
      if (entries.length > 0) {
        obj.pageInfos = {};
        entries.forEach(([k, v]) => {
          obj.pageInfos[k] = PageInfo.toJSON(v);
        });
      }
    }
    if (message.displayEntries) {
      const entries = Object.entries(message.displayEntries);
      if (entries.length > 0) {
        obj.displayEntries = {};
        entries.forEach(([k, v]) => {
          obj.displayEntries[k] = Math.round(v);
        });
      }
    }
    if (message.defaultDisplayIdU8 !== 0) {
      obj.defaultDisplayIdU8 = Math.round(message.defaultDisplayIdU8);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DisplayList>, I>>(base?: I): DisplayList {
    return DisplayList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DisplayList>, I>>(object: I): DisplayList {
    const message = createBaseDisplayList();
    message.displays = Object.entries(object.displays ?? {}).reduce<{ [key: number]: Display }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = Display.fromPartial(value);
      }
      return acc;
    }, {});
    message.pageInfos = Object.entries(object.pageInfos ?? {}).reduce<{ [key: number]: PageInfo }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = PageInfo.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.displayEntries = Object.entries(object.displayEntries ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.defaultDisplayIdU8 = object.defaultDisplayIdU8 ?? 0;
    return message;
  },
};

function createBaseDisplayList_DisplaysEntry(): DisplayList_DisplaysEntry {
  return { key: 0, value: undefined };
}

export const DisplayList_DisplaysEntry: MessageFns<DisplayList_DisplaysEntry> = {
  encode(message: DisplayList_DisplaysEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      Display.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DisplayList_DisplaysEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDisplayList_DisplaysEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = Display.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DisplayList_DisplaysEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? Display.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: DisplayList_DisplaysEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = Display.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DisplayList_DisplaysEntry>, I>>(base?: I): DisplayList_DisplaysEntry {
    return DisplayList_DisplaysEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DisplayList_DisplaysEntry>, I>>(object: I): DisplayList_DisplaysEntry {
    const message = createBaseDisplayList_DisplaysEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? Display.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseDisplayList_PageInfosEntry(): DisplayList_PageInfosEntry {
  return { key: 0, value: undefined };
}

export const DisplayList_PageInfosEntry: MessageFns<DisplayList_PageInfosEntry> = {
  encode(message: DisplayList_PageInfosEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      PageInfo.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DisplayList_PageInfosEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDisplayList_PageInfosEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = PageInfo.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DisplayList_PageInfosEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? PageInfo.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: DisplayList_PageInfosEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = PageInfo.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DisplayList_PageInfosEntry>, I>>(base?: I): DisplayList_PageInfosEntry {
    return DisplayList_PageInfosEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DisplayList_PageInfosEntry>, I>>(object: I): DisplayList_PageInfosEntry {
    const message = createBaseDisplayList_PageInfosEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? PageInfo.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseDisplayList_DisplayEntriesEntry(): DisplayList_DisplayEntriesEntry {
  return { key: 0, value: 0 };
}

export const DisplayList_DisplayEntriesEntry: MessageFns<DisplayList_DisplayEntriesEntry> = {
  encode(message: DisplayList_DisplayEntriesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).uint32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DisplayList_DisplayEntriesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDisplayList_DisplayEntriesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DisplayList_DisplayEntriesEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
    };
  },

  toJSON(message: DisplayList_DisplayEntriesEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== 0) {
      obj.value = Math.round(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DisplayList_DisplayEntriesEntry>, I>>(base?: I): DisplayList_DisplayEntriesEntry {
    return DisplayList_DisplayEntriesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DisplayList_DisplayEntriesEntry>, I>>(
    object: I,
  ): DisplayList_DisplayEntriesEntry {
    const message = createBaseDisplayList_DisplayEntriesEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseDisplay(): Display {
  return {
    name: "",
    size: undefined,
    rotate: 0,
    pageClassifies: [],
    mainPageIdU16: 0,
    pageIdsU16: [],
    commonPageIdU16: undefined,
    commonPageOnTop: false,
    style: undefined,
    alwaysCachePageIdsU16: [],
    cachePageMaxCountU16: 0,
    outputTypes: [],
  };
}

export const Display: MessageFns<Display> = {
  encode(message: Display, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.size !== undefined) {
      Size.encode(message.size, writer.uint32(26).fork()).join();
    }
    if (message.rotate !== 0) {
      writer.uint32(32).int32(message.rotate);
    }
    for (const v of message.pageClassifies) {
      PageClassify.encode(v!, writer.uint32(42).fork()).join();
    }
    if (message.mainPageIdU16 !== 0) {
      writer.uint32(48).uint32(message.mainPageIdU16);
    }
    writer.uint32(58).fork();
    for (const v of message.pageIdsU16) {
      writer.uint32(v);
    }
    writer.join();
    if (message.commonPageIdU16 !== undefined) {
      writer.uint32(64).uint32(message.commonPageIdU16);
    }
    if (message.commonPageOnTop !== false) {
      writer.uint32(72).bool(message.commonPageOnTop);
    }
    if (message.style !== undefined) {
      StyleProperties.encode(message.style, writer.uint32(82).fork()).join();
    }
    writer.uint32(90).fork();
    for (const v of message.alwaysCachePageIdsU16) {
      writer.uint32(v);
    }
    writer.join();
    if (message.cachePageMaxCountU16 !== 0) {
      writer.uint32(96).uint32(message.cachePageMaxCountU16);
    }
    writer.uint32(106).fork();
    for (const v of message.outputTypes) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Display {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDisplay();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.size = Size.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.rotate = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.pageClassifies.push(PageClassify.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.mainPageIdU16 = reader.uint32();
          continue;
        }
        case 7: {
          if (tag === 56) {
            message.pageIdsU16.push(reader.uint32());

            continue;
          }

          if (tag === 58) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.pageIdsU16.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.commonPageIdU16 = reader.uint32();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.commonPageOnTop = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.style = StyleProperties.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag === 88) {
            message.alwaysCachePageIdsU16.push(reader.uint32());

            continue;
          }

          if (tag === 90) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.alwaysCachePageIdsU16.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.cachePageMaxCountU16 = reader.uint32();
          continue;
        }
        case 13: {
          if (tag === 104) {
            message.outputTypes.push(reader.int32() as any);

            continue;
          }

          if (tag === 106) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.outputTypes.push(reader.int32() as any);
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Display {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      size: isSet(object.size) ? Size.fromJSON(object.size) : undefined,
      rotate: isSet(object.rotate) ? rotateTypeFromJSON(object.rotate) : 0,
      pageClassifies: globalThis.Array.isArray(object?.pageClassifies)
        ? object.pageClassifies.map((e: any) => PageClassify.fromJSON(e))
        : [],
      mainPageIdU16: isSet(object.mainPageIdU16) ? globalThis.Number(object.mainPageIdU16) : 0,
      pageIdsU16: globalThis.Array.isArray(object?.pageIdsU16)
        ? object.pageIdsU16.map((e: any) => globalThis.Number(e))
        : [],
      commonPageIdU16: isSet(object.commonPageIdU16) ? globalThis.Number(object.commonPageIdU16) : undefined,
      commonPageOnTop: isSet(object.commonPageOnTop) ? globalThis.Boolean(object.commonPageOnTop) : false,
      style: isSet(object.style) ? StyleProperties.fromJSON(object.style) : undefined,
      alwaysCachePageIdsU16: globalThis.Array.isArray(object?.alwaysCachePageIdsU16)
        ? object.alwaysCachePageIdsU16.map((e: any) => globalThis.Number(e))
        : [],
      cachePageMaxCountU16: isSet(object.cachePageMaxCountU16) ? globalThis.Number(object.cachePageMaxCountU16) : 0,
      outputTypes: globalThis.Array.isArray(object?.outputTypes)
        ? object.outputTypes.map((e: any) => displayOutputTypeFromJSON(e))
        : [],
    };
  },

  toJSON(message: Display): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.size !== undefined) {
      obj.size = Size.toJSON(message.size);
    }
    if (message.rotate !== 0) {
      obj.rotate = rotateTypeToJSON(message.rotate);
    }
    if (message.pageClassifies?.length) {
      obj.pageClassifies = message.pageClassifies.map((e) => PageClassify.toJSON(e));
    }
    if (message.mainPageIdU16 !== 0) {
      obj.mainPageIdU16 = Math.round(message.mainPageIdU16);
    }
    if (message.pageIdsU16?.length) {
      obj.pageIdsU16 = message.pageIdsU16.map((e) => Math.round(e));
    }
    if (message.commonPageIdU16 !== undefined) {
      obj.commonPageIdU16 = Math.round(message.commonPageIdU16);
    }
    if (message.commonPageOnTop !== false) {
      obj.commonPageOnTop = message.commonPageOnTop;
    }
    if (message.style !== undefined) {
      obj.style = StyleProperties.toJSON(message.style);
    }
    if (message.alwaysCachePageIdsU16?.length) {
      obj.alwaysCachePageIdsU16 = message.alwaysCachePageIdsU16.map((e) => Math.round(e));
    }
    if (message.cachePageMaxCountU16 !== 0) {
      obj.cachePageMaxCountU16 = Math.round(message.cachePageMaxCountU16);
    }
    if (message.outputTypes?.length) {
      obj.outputTypes = message.outputTypes.map((e) => displayOutputTypeToJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Display>, I>>(base?: I): Display {
    return Display.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Display>, I>>(object: I): Display {
    const message = createBaseDisplay();
    message.name = object.name ?? "";
    message.size = (object.size !== undefined && object.size !== null) ? Size.fromPartial(object.size) : undefined;
    message.rotate = object.rotate ?? 0;
    message.pageClassifies = object.pageClassifies?.map((e) => PageClassify.fromPartial(e)) || [];
    message.mainPageIdU16 = object.mainPageIdU16 ?? 0;
    message.pageIdsU16 = object.pageIdsU16?.map((e) => e) || [];
    message.commonPageIdU16 = object.commonPageIdU16 ?? undefined;
    message.commonPageOnTop = object.commonPageOnTop ?? false;
    message.style = (object.style !== undefined && object.style !== null)
      ? StyleProperties.fromPartial(object.style)
      : undefined;
    message.alwaysCachePageIdsU16 = object.alwaysCachePageIdsU16?.map((e) => e) || [];
    message.cachePageMaxCountU16 = object.cachePageMaxCountU16 ?? 0;
    message.outputTypes = object.outputTypes?.map((e) => e) || [];
    return message;
  },
};

function createBasePageInfo(): PageInfo {
  return {
    name: "",
    pageType: 0,
    pageNoU16: undefined,
    pageClassifyIdU16: 0,
    layout: 0,
    protectType: 0,
    protectPasswd: undefined,
    stackPageIds: [],
    style: undefined,
    backgroundGraphic: undefined,
    location: undefined,
    size: undefined,
    autoSizeType: 0,
    popupType: undefined,
    popupPosition: undefined,
    keyboardInfo: undefined,
    jumpPageIds: [],
  };
}

export const PageInfo: MessageFns<PageInfo> = {
  encode(message: PageInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.pageType !== 0) {
      writer.uint32(24).int32(message.pageType);
    }
    if (message.pageNoU16 !== undefined) {
      writer.uint32(32).uint32(message.pageNoU16);
    }
    if (message.pageClassifyIdU16 !== 0) {
      writer.uint32(40).uint32(message.pageClassifyIdU16);
    }
    if (message.layout !== 0) {
      writer.uint32(48).int32(message.layout);
    }
    if (message.protectType !== 0) {
      writer.uint32(56).int32(message.protectType);
    }
    if (message.protectPasswd !== undefined) {
      writer.uint32(66).string(message.protectPasswd);
    }
    writer.uint32(82).fork();
    for (const v of message.stackPageIds) {
      writer.uint32(v);
    }
    writer.join();
    if (message.style !== undefined) {
      StyleProperties.encode(message.style, writer.uint32(90).fork()).join();
    }
    if (message.backgroundGraphic !== undefined) {
      GraphicReference.encode(message.backgroundGraphic, writer.uint32(98).fork()).join();
    }
    if (message.location !== undefined) {
      Location.encode(message.location, writer.uint32(106).fork()).join();
    }
    if (message.size !== undefined) {
      Size.encode(message.size, writer.uint32(114).fork()).join();
    }
    if (message.autoSizeType !== 0) {
      writer.uint32(120).int32(message.autoSizeType);
    }
    if (message.popupType !== undefined) {
      writer.uint32(128).int32(message.popupType);
    }
    if (message.popupPosition !== undefined) {
      writer.uint32(136).int32(message.popupPosition);
    }
    if (message.keyboardInfo !== undefined) {
      KeyboardInfo.encode(message.keyboardInfo, writer.uint32(154).fork()).join();
    }
    writer.uint32(146).fork();
    for (const v of message.jumpPageIds) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PageInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePageInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.pageType = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.pageNoU16 = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.pageClassifyIdU16 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.layout = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.protectType = reader.int32() as any;
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.protectPasswd = reader.string();
          continue;
        }
        case 10: {
          if (tag === 80) {
            message.stackPageIds.push(reader.uint32());

            continue;
          }

          if (tag === 82) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.stackPageIds.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.style = StyleProperties.decode(reader, reader.uint32());
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.backgroundGraphic = GraphicReference.decode(reader, reader.uint32());
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.location = Location.decode(reader, reader.uint32());
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.size = Size.decode(reader, reader.uint32());
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.autoSizeType = reader.int32() as any;
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.popupType = reader.int32() as any;
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.popupPosition = reader.int32() as any;
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.keyboardInfo = KeyboardInfo.decode(reader, reader.uint32());
          continue;
        }
        case 18: {
          if (tag === 144) {
            message.jumpPageIds.push(reader.int32());

            continue;
          }

          if (tag === 146) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.jumpPageIds.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PageInfo {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      pageType: isSet(object.pageType) ? pageTypeFromJSON(object.pageType) : 0,
      pageNoU16: isSet(object.pageNoU16) ? globalThis.Number(object.pageNoU16) : undefined,
      pageClassifyIdU16: isSet(object.pageClassifyIdU16) ? globalThis.Number(object.pageClassifyIdU16) : 0,
      layout: isSet(object.layout) ? layoutTypeFromJSON(object.layout) : 0,
      protectType: isSet(object.protectType) ? protectTypeFromJSON(object.protectType) : 0,
      protectPasswd: isSet(object.protectPasswd) ? globalThis.String(object.protectPasswd) : undefined,
      stackPageIds: globalThis.Array.isArray(object?.stackPageIds)
        ? object.stackPageIds.map((e: any) => globalThis.Number(e))
        : [],
      style: isSet(object.style) ? StyleProperties.fromJSON(object.style) : undefined,
      backgroundGraphic: isSet(object.backgroundGraphic)
        ? GraphicReference.fromJSON(object.backgroundGraphic)
        : undefined,
      location: isSet(object.location) ? Location.fromJSON(object.location) : undefined,
      size: isSet(object.size) ? Size.fromJSON(object.size) : undefined,
      autoSizeType: isSet(object.autoSizeType) ? pageSizeTypeFromJSON(object.autoSizeType) : 0,
      popupType: isSet(object.popupType) ? pagePopupTypeFromJSON(object.popupType) : undefined,
      popupPosition: isSet(object.popupPosition) ? positionTypeFromJSON(object.popupPosition) : undefined,
      keyboardInfo: isSet(object.keyboardInfo) ? KeyboardInfo.fromJSON(object.keyboardInfo) : undefined,
      jumpPageIds: globalThis.Array.isArray(object?.jumpPageIds)
        ? object.jumpPageIds.map((e: any) => globalThis.Number(e))
        : [],
    };
  },

  toJSON(message: PageInfo): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.pageType !== 0) {
      obj.pageType = pageTypeToJSON(message.pageType);
    }
    if (message.pageNoU16 !== undefined) {
      obj.pageNoU16 = Math.round(message.pageNoU16);
    }
    if (message.pageClassifyIdU16 !== 0) {
      obj.pageClassifyIdU16 = Math.round(message.pageClassifyIdU16);
    }
    if (message.layout !== 0) {
      obj.layout = layoutTypeToJSON(message.layout);
    }
    if (message.protectType !== 0) {
      obj.protectType = protectTypeToJSON(message.protectType);
    }
    if (message.protectPasswd !== undefined) {
      obj.protectPasswd = message.protectPasswd;
    }
    if (message.stackPageIds?.length) {
      obj.stackPageIds = message.stackPageIds.map((e) => Math.round(e));
    }
    if (message.style !== undefined) {
      obj.style = StyleProperties.toJSON(message.style);
    }
    if (message.backgroundGraphic !== undefined) {
      obj.backgroundGraphic = GraphicReference.toJSON(message.backgroundGraphic);
    }
    if (message.location !== undefined) {
      obj.location = Location.toJSON(message.location);
    }
    if (message.size !== undefined) {
      obj.size = Size.toJSON(message.size);
    }
    if (message.autoSizeType !== 0) {
      obj.autoSizeType = pageSizeTypeToJSON(message.autoSizeType);
    }
    if (message.popupType !== undefined) {
      obj.popupType = pagePopupTypeToJSON(message.popupType);
    }
    if (message.popupPosition !== undefined) {
      obj.popupPosition = positionTypeToJSON(message.popupPosition);
    }
    if (message.keyboardInfo !== undefined) {
      obj.keyboardInfo = KeyboardInfo.toJSON(message.keyboardInfo);
    }
    if (message.jumpPageIds?.length) {
      obj.jumpPageIds = message.jumpPageIds.map((e) => Math.round(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PageInfo>, I>>(base?: I): PageInfo {
    return PageInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageInfo>, I>>(object: I): PageInfo {
    const message = createBasePageInfo();
    message.name = object.name ?? "";
    message.pageType = object.pageType ?? 0;
    message.pageNoU16 = object.pageNoU16 ?? undefined;
    message.pageClassifyIdU16 = object.pageClassifyIdU16 ?? 0;
    message.layout = object.layout ?? 0;
    message.protectType = object.protectType ?? 0;
    message.protectPasswd = object.protectPasswd ?? undefined;
    message.stackPageIds = object.stackPageIds?.map((e) => e) || [];
    message.style = (object.style !== undefined && object.style !== null)
      ? StyleProperties.fromPartial(object.style)
      : undefined;
    message.backgroundGraphic = (object.backgroundGraphic !== undefined && object.backgroundGraphic !== null)
      ? GraphicReference.fromPartial(object.backgroundGraphic)
      : undefined;
    message.location = (object.location !== undefined && object.location !== null)
      ? Location.fromPartial(object.location)
      : undefined;
    message.size = (object.size !== undefined && object.size !== null) ? Size.fromPartial(object.size) : undefined;
    message.autoSizeType = object.autoSizeType ?? 0;
    message.popupType = object.popupType ?? undefined;
    message.popupPosition = object.popupPosition ?? undefined;
    message.keyboardInfo = (object.keyboardInfo !== undefined && object.keyboardInfo !== null)
      ? KeyboardInfo.fromPartial(object.keyboardInfo)
      : undefined;
    message.jumpPageIds = object.jumpPageIds?.map((e) => e) || [];
    return message;
  },
};

function createBaseKeyboardInfo(): KeyboardInfo {
  return {
    maxValueWidgetId: undefined,
    minValueWidgetId: undefined,
    currentValueWidgetId: undefined,
    candidateLocation: undefined,
    candidateSize: undefined,
  };
}

export const KeyboardInfo: MessageFns<KeyboardInfo> = {
  encode(message: KeyboardInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.maxValueWidgetId !== undefined) {
      writer.uint32(8).int32(message.maxValueWidgetId);
    }
    if (message.minValueWidgetId !== undefined) {
      writer.uint32(16).int32(message.minValueWidgetId);
    }
    if (message.currentValueWidgetId !== undefined) {
      writer.uint32(24).int32(message.currentValueWidgetId);
    }
    if (message.candidateLocation !== undefined) {
      Location.encode(message.candidateLocation, writer.uint32(34).fork()).join();
    }
    if (message.candidateSize !== undefined) {
      Size.encode(message.candidateSize, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): KeyboardInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseKeyboardInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.maxValueWidgetId = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.minValueWidgetId = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.currentValueWidgetId = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.candidateLocation = Location.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.candidateSize = Size.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): KeyboardInfo {
    return {
      maxValueWidgetId: isSet(object.maxValueWidgetId) ? globalThis.Number(object.maxValueWidgetId) : undefined,
      minValueWidgetId: isSet(object.minValueWidgetId) ? globalThis.Number(object.minValueWidgetId) : undefined,
      currentValueWidgetId: isSet(object.currentValueWidgetId)
        ? globalThis.Number(object.currentValueWidgetId)
        : undefined,
      candidateLocation: isSet(object.candidateLocation) ? Location.fromJSON(object.candidateLocation) : undefined,
      candidateSize: isSet(object.candidateSize) ? Size.fromJSON(object.candidateSize) : undefined,
    };
  },

  toJSON(message: KeyboardInfo): unknown {
    const obj: any = {};
    if (message.maxValueWidgetId !== undefined) {
      obj.maxValueWidgetId = Math.round(message.maxValueWidgetId);
    }
    if (message.minValueWidgetId !== undefined) {
      obj.minValueWidgetId = Math.round(message.minValueWidgetId);
    }
    if (message.currentValueWidgetId !== undefined) {
      obj.currentValueWidgetId = Math.round(message.currentValueWidgetId);
    }
    if (message.candidateLocation !== undefined) {
      obj.candidateLocation = Location.toJSON(message.candidateLocation);
    }
    if (message.candidateSize !== undefined) {
      obj.candidateSize = Size.toJSON(message.candidateSize);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<KeyboardInfo>, I>>(base?: I): KeyboardInfo {
    return KeyboardInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KeyboardInfo>, I>>(object: I): KeyboardInfo {
    const message = createBaseKeyboardInfo();
    message.maxValueWidgetId = object.maxValueWidgetId ?? undefined;
    message.minValueWidgetId = object.minValueWidgetId ?? undefined;
    message.currentValueWidgetId = object.currentValueWidgetId ?? undefined;
    message.candidateLocation = (object.candidateLocation !== undefined && object.candidateLocation !== null)
      ? Location.fromPartial(object.candidateLocation)
      : undefined;
    message.candidateSize = (object.candidateSize !== undefined && object.candidateSize !== null)
      ? Size.fromPartial(object.candidateSize)
      : undefined;
    return message;
  },
};

function createBasePageClassify(): PageClassify {
  return { idU16: 0, name: "", parentIdU16: undefined };
}

export const PageClassify: MessageFns<PageClassify> = {
  encode(message: PageClassify, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.idU16 !== 0) {
      writer.uint32(8).uint32(message.idU16);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.parentIdU16 !== undefined) {
      writer.uint32(24).uint32(message.parentIdU16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PageClassify {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePageClassify();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.idU16 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.parentIdU16 = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PageClassify {
    return {
      idU16: isSet(object.idU16) ? globalThis.Number(object.idU16) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      parentIdU16: isSet(object.parentIdU16) ? globalThis.Number(object.parentIdU16) : undefined,
    };
  },

  toJSON(message: PageClassify): unknown {
    const obj: any = {};
    if (message.idU16 !== 0) {
      obj.idU16 = Math.round(message.idU16);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.parentIdU16 !== undefined) {
      obj.parentIdU16 = Math.round(message.parentIdU16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PageClassify>, I>>(base?: I): PageClassify {
    return PageClassify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageClassify>, I>>(object: I): PageClassify {
    const message = createBasePageClassify();
    message.idU16 = object.idU16 ?? 0;
    message.name = object.name ?? "";
    message.parentIdU16 = object.parentIdU16 ?? undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
