// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/text.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "znd.project.v1";

/** 文本标签库，只有用户建的，objectId为0 */
export interface TextTagLibrary {
  /** key用u16 */
  tags: { [key: number]: TextTag };
  /** key用u16 */
  classify: { [key: number]: TextTagClassify };
}

export interface TextTagLibrary_TagsEntry {
  key: number;
  value: TextTag | undefined;
}

export interface TextTagLibrary_ClassifyEntry {
  key: number;
  value: TextTagClassify | undefined;
}

/** 文本标签引用 */
export interface TextReference {
  /** 文本内容 */
  from?:
    | //
    /** 文本标签id */
    { $case: "tagIdU16"; value: number }
    | //
    /** 输入的文本 */
    { $case: "input"; value: TextTag }
    | undefined;
}

/**
 * 字体及使用到的文字(单独一个对象类型,ID是字体定义ID)
 * 后续优化时，可能可以只从这边读，而不从元件属性中读取，以节省空间占用，但要考虑怎么提升性能
 */
export interface FontDefinitionText {
  /**
   * 使用到的文字
   * key是uint32,是页面ID+元件ID(如果是报警和日志的元件，则根据元件上的字体，找到报警记录中的配置文本)
   */
  widgetUse: { [key: number]: FontDefinitionText_WidgetUse };
}

export interface FontDefinitionText_WidgetUseEntry {
  key: number;
  value: FontDefinitionText_WidgetUse | undefined;
}

/** 字体定义使用到的内容 */
export interface FontDefinitionText_WidgetUse {
  /** key是uint8,是属性ID */
  text: { [key: number]: FontDefinitionText_WidgetUse_TextReferenceArray };
  /** 使用的预置类型 */
  preset: FontDefinitionText_WidgetUse_PresetType[];
  /** 使用的来源 */
  source: FontDefinitionText_WidgetUse_Source[];
}

/** 预置类型 */
export enum FontDefinitionText_WidgetUse_PresetType {
  /** PRESET_TYPE_UNSPECIFIED - 未定义 */
  PRESET_TYPE_UNSPECIFIED = 0,
  /** PRESET_TYPE_NUMBER - 数字 */
  PRESET_TYPE_NUMBER = 1,
  /** PRESET_TYPE_LETTER - 字母 */
  PRESET_TYPE_LETTER = 2,
  /** PRESET_TYPE_SPECIAL - 特殊字符 */
  PRESET_TYPE_SPECIAL = 3,
  /** PRESET_TYPE_CHINESE_MINI - 中文精简 */
  PRESET_TYPE_CHINESE_MINI = 4,
  /** PRESET_TYPE_CHINESE_FULL - 中文全面 */
  PRESET_TYPE_CHINESE_FULL = 5,
  UNRECOGNIZED = -1,
}

export function fontDefinitionText_WidgetUse_PresetTypeFromJSON(object: any): FontDefinitionText_WidgetUse_PresetType {
  switch (object) {
    case 0:
    case "PRESET_TYPE_UNSPECIFIED":
      return FontDefinitionText_WidgetUse_PresetType.PRESET_TYPE_UNSPECIFIED;
    case 1:
    case "PRESET_TYPE_NUMBER":
      return FontDefinitionText_WidgetUse_PresetType.PRESET_TYPE_NUMBER;
    case 2:
    case "PRESET_TYPE_LETTER":
      return FontDefinitionText_WidgetUse_PresetType.PRESET_TYPE_LETTER;
    case 3:
    case "PRESET_TYPE_SPECIAL":
      return FontDefinitionText_WidgetUse_PresetType.PRESET_TYPE_SPECIAL;
    case 4:
    case "PRESET_TYPE_CHINESE_MINI":
      return FontDefinitionText_WidgetUse_PresetType.PRESET_TYPE_CHINESE_MINI;
    case 5:
    case "PRESET_TYPE_CHINESE_FULL":
      return FontDefinitionText_WidgetUse_PresetType.PRESET_TYPE_CHINESE_FULL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return FontDefinitionText_WidgetUse_PresetType.UNRECOGNIZED;
  }
}

export function fontDefinitionText_WidgetUse_PresetTypeToJSON(object: FontDefinitionText_WidgetUse_PresetType): string {
  switch (object) {
    case FontDefinitionText_WidgetUse_PresetType.PRESET_TYPE_UNSPECIFIED:
      return "PRESET_TYPE_UNSPECIFIED";
    case FontDefinitionText_WidgetUse_PresetType.PRESET_TYPE_NUMBER:
      return "PRESET_TYPE_NUMBER";
    case FontDefinitionText_WidgetUse_PresetType.PRESET_TYPE_LETTER:
      return "PRESET_TYPE_LETTER";
    case FontDefinitionText_WidgetUse_PresetType.PRESET_TYPE_SPECIAL:
      return "PRESET_TYPE_SPECIAL";
    case FontDefinitionText_WidgetUse_PresetType.PRESET_TYPE_CHINESE_MINI:
      return "PRESET_TYPE_CHINESE_MINI";
    case FontDefinitionText_WidgetUse_PresetType.PRESET_TYPE_CHINESE_FULL:
      return "PRESET_TYPE_CHINESE_FULL";
    case FontDefinitionText_WidgetUse_PresetType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 来源类型 */
export enum FontDefinitionText_WidgetUse_SourceType {
  /** SOURCE_TYPE_UNSPECIFIED - 未定义 */
  SOURCE_TYPE_UNSPECIFIED = 0,
  /** SOURCE_TYPE_VARIABLE - 变量（根据变量类型：数字、字母、特殊字符、中文精简、中文全面） */
  SOURCE_TYPE_VARIABLE = 1,
  /** SOURCE_TYPE_TEXT_TAG - 文本标签 */
  SOURCE_TYPE_TEXT_TAG = 2,
  /** SOURCE_TYPE_ALARM - 报警 */
  SOURCE_TYPE_ALARM = 3,
  /** SOURCE_TYPE_OPERATION - 操作记录 */
  SOURCE_TYPE_OPERATION = 4,
  /** SOURCE_TYPE_SAMPLING - 数据采样 */
  SOURCE_TYPE_SAMPLING = 5,
  UNRECOGNIZED = -1,
}

export function fontDefinitionText_WidgetUse_SourceTypeFromJSON(object: any): FontDefinitionText_WidgetUse_SourceType {
  switch (object) {
    case 0:
    case "SOURCE_TYPE_UNSPECIFIED":
      return FontDefinitionText_WidgetUse_SourceType.SOURCE_TYPE_UNSPECIFIED;
    case 1:
    case "SOURCE_TYPE_VARIABLE":
      return FontDefinitionText_WidgetUse_SourceType.SOURCE_TYPE_VARIABLE;
    case 2:
    case "SOURCE_TYPE_TEXT_TAG":
      return FontDefinitionText_WidgetUse_SourceType.SOURCE_TYPE_TEXT_TAG;
    case 3:
    case "SOURCE_TYPE_ALARM":
      return FontDefinitionText_WidgetUse_SourceType.SOURCE_TYPE_ALARM;
    case 4:
    case "SOURCE_TYPE_OPERATION":
      return FontDefinitionText_WidgetUse_SourceType.SOURCE_TYPE_OPERATION;
    case 5:
    case "SOURCE_TYPE_SAMPLING":
      return FontDefinitionText_WidgetUse_SourceType.SOURCE_TYPE_SAMPLING;
    case -1:
    case "UNRECOGNIZED":
    default:
      return FontDefinitionText_WidgetUse_SourceType.UNRECOGNIZED;
  }
}

export function fontDefinitionText_WidgetUse_SourceTypeToJSON(object: FontDefinitionText_WidgetUse_SourceType): string {
  switch (object) {
    case FontDefinitionText_WidgetUse_SourceType.SOURCE_TYPE_UNSPECIFIED:
      return "SOURCE_TYPE_UNSPECIFIED";
    case FontDefinitionText_WidgetUse_SourceType.SOURCE_TYPE_VARIABLE:
      return "SOURCE_TYPE_VARIABLE";
    case FontDefinitionText_WidgetUse_SourceType.SOURCE_TYPE_TEXT_TAG:
      return "SOURCE_TYPE_TEXT_TAG";
    case FontDefinitionText_WidgetUse_SourceType.SOURCE_TYPE_ALARM:
      return "SOURCE_TYPE_ALARM";
    case FontDefinitionText_WidgetUse_SourceType.SOURCE_TYPE_OPERATION:
      return "SOURCE_TYPE_OPERATION";
    case FontDefinitionText_WidgetUse_SourceType.SOURCE_TYPE_SAMPLING:
      return "SOURCE_TYPE_SAMPLING";
    case FontDefinitionText_WidgetUse_SourceType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface FontDefinitionText_WidgetUse_TextEntry {
  key: number;
  value: FontDefinitionText_WidgetUse_TextReferenceArray | undefined;
}

/** 引用的文本，如果属性值不是数组，则只取第一个 */
export interface FontDefinitionText_WidgetUse_TextReferenceArray {
  text: TextReference[];
}

/** 数据来源 */
export interface FontDefinitionText_WidgetUse_Source {
  type: FontDefinitionText_WidgetUse_SourceType;
  /** key是u16，是各种对象的ID */
  id: number;
}

/** 文本标签分类 */
export interface TextTagClassify {
  /** int32 id = 1; */
  name: string;
  /** 父分类id */
  parentId?: number | undefined;
}

/** 多语言文本 */
export interface MultiLanguageText {
  /** key是LanguageType枚举值, uint8 */
  content: { [key: number]: string };
}

export interface MultiLanguageText_ContentEntry {
  key: number;
  value: string;
}

/** 文本标签 */
export interface TextTag {
  from?:
    | //
    /** 多语言 */
    { $case: "multiLang"; value: MultiLanguageText }
    | //
    /** 单语言 */
    { $case: "singleLang"; value: string }
    | undefined;
  /** 多行文本 */
  multiLine: boolean;
  /** 所属分类id(设计端使用) */
  classifyIdU16: number;
}

function createBaseTextTagLibrary(): TextTagLibrary {
  return { tags: {}, classify: {} };
}

export const TextTagLibrary: MessageFns<TextTagLibrary> = {
  encode(message: TextTagLibrary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.tags).forEach(([key, value]) => {
      TextTagLibrary_TagsEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    Object.entries(message.classify).forEach(([key, value]) => {
      TextTagLibrary_ClassifyEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TextTagLibrary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTextTagLibrary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = TextTagLibrary_TagsEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.tags[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = TextTagLibrary_ClassifyEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.classify[entry2.key] = entry2.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TextTagLibrary {
    return {
      tags: isObject(object.tags)
        ? Object.entries(object.tags).reduce<{ [key: number]: TextTag }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = TextTag.fromJSON(value);
          return acc;
        }, {})
        : {},
      classify: isObject(object.classify)
        ? Object.entries(object.classify).reduce<{ [key: number]: TextTagClassify }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = TextTagClassify.fromJSON(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: TextTagLibrary): unknown {
    const obj: any = {};
    if (message.tags) {
      const entries = Object.entries(message.tags);
      if (entries.length > 0) {
        obj.tags = {};
        entries.forEach(([k, v]) => {
          obj.tags[k] = TextTag.toJSON(v);
        });
      }
    }
    if (message.classify) {
      const entries = Object.entries(message.classify);
      if (entries.length > 0) {
        obj.classify = {};
        entries.forEach(([k, v]) => {
          obj.classify[k] = TextTagClassify.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TextTagLibrary>, I>>(base?: I): TextTagLibrary {
    return TextTagLibrary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TextTagLibrary>, I>>(object: I): TextTagLibrary {
    const message = createBaseTextTagLibrary();
    message.tags = Object.entries(object.tags ?? {}).reduce<{ [key: number]: TextTag }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = TextTag.fromPartial(value);
      }
      return acc;
    }, {});
    message.classify = Object.entries(object.classify ?? {}).reduce<{ [key: number]: TextTagClassify }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = TextTagClassify.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseTextTagLibrary_TagsEntry(): TextTagLibrary_TagsEntry {
  return { key: 0, value: undefined };
}

export const TextTagLibrary_TagsEntry: MessageFns<TextTagLibrary_TagsEntry> = {
  encode(message: TextTagLibrary_TagsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== undefined) {
      TextTag.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TextTagLibrary_TagsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTextTagLibrary_TagsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = TextTag.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TextTagLibrary_TagsEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? TextTag.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: TextTagLibrary_TagsEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = TextTag.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TextTagLibrary_TagsEntry>, I>>(base?: I): TextTagLibrary_TagsEntry {
    return TextTagLibrary_TagsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TextTagLibrary_TagsEntry>, I>>(object: I): TextTagLibrary_TagsEntry {
    const message = createBaseTextTagLibrary_TagsEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? TextTag.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseTextTagLibrary_ClassifyEntry(): TextTagLibrary_ClassifyEntry {
  return { key: 0, value: undefined };
}

export const TextTagLibrary_ClassifyEntry: MessageFns<TextTagLibrary_ClassifyEntry> = {
  encode(message: TextTagLibrary_ClassifyEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== undefined) {
      TextTagClassify.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TextTagLibrary_ClassifyEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTextTagLibrary_ClassifyEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = TextTagClassify.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TextTagLibrary_ClassifyEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? TextTagClassify.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: TextTagLibrary_ClassifyEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = TextTagClassify.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TextTagLibrary_ClassifyEntry>, I>>(base?: I): TextTagLibrary_ClassifyEntry {
    return TextTagLibrary_ClassifyEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TextTagLibrary_ClassifyEntry>, I>>(object: I): TextTagLibrary_ClassifyEntry {
    const message = createBaseTextTagLibrary_ClassifyEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? TextTagClassify.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseTextReference(): TextReference {
  return { from: undefined };
}

export const TextReference: MessageFns<TextReference> = {
  encode(message: TextReference, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "tagIdU16":
        writer.uint32(8).uint32(message.from.value);
        break;
      case "input":
        TextTag.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TextReference {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTextReference();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.from = { $case: "tagIdU16", value: reader.uint32() };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "input", value: TextTag.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TextReference {
    return {
      from: isSet(object.tagIdU16)
        ? { $case: "tagIdU16", value: globalThis.Number(object.tagIdU16) }
        : isSet(object.input)
        ? { $case: "input", value: TextTag.fromJSON(object.input) }
        : undefined,
    };
  },

  toJSON(message: TextReference): unknown {
    const obj: any = {};
    if (message.from?.$case === "tagIdU16") {
      obj.tagIdU16 = Math.round(message.from.value);
    } else if (message.from?.$case === "input") {
      obj.input = TextTag.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TextReference>, I>>(base?: I): TextReference {
    return TextReference.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TextReference>, I>>(object: I): TextReference {
    const message = createBaseTextReference();
    switch (object.from?.$case) {
      case "tagIdU16": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "tagIdU16", value: object.from.value };
        }
        break;
      }
      case "input": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "input", value: TextTag.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseFontDefinitionText(): FontDefinitionText {
  return { widgetUse: {} };
}

export const FontDefinitionText: MessageFns<FontDefinitionText> = {
  encode(message: FontDefinitionText, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.widgetUse).forEach(([key, value]) => {
      FontDefinitionText_WidgetUseEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FontDefinitionText {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFontDefinitionText();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = FontDefinitionText_WidgetUseEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.widgetUse[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FontDefinitionText {
    return {
      widgetUse: isObject(object.widgetUse)
        ? Object.entries(object.widgetUse).reduce<{ [key: number]: FontDefinitionText_WidgetUse }>(
          (acc, [key, value]) => {
            acc[globalThis.Number(key)] = FontDefinitionText_WidgetUse.fromJSON(value);
            return acc;
          },
          {},
        )
        : {},
    };
  },

  toJSON(message: FontDefinitionText): unknown {
    const obj: any = {};
    if (message.widgetUse) {
      const entries = Object.entries(message.widgetUse);
      if (entries.length > 0) {
        obj.widgetUse = {};
        entries.forEach(([k, v]) => {
          obj.widgetUse[k] = FontDefinitionText_WidgetUse.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FontDefinitionText>, I>>(base?: I): FontDefinitionText {
    return FontDefinitionText.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FontDefinitionText>, I>>(object: I): FontDefinitionText {
    const message = createBaseFontDefinitionText();
    message.widgetUse = Object.entries(object.widgetUse ?? {}).reduce<{ [key: number]: FontDefinitionText_WidgetUse }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = FontDefinitionText_WidgetUse.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseFontDefinitionText_WidgetUseEntry(): FontDefinitionText_WidgetUseEntry {
  return { key: 0, value: undefined };
}

export const FontDefinitionText_WidgetUseEntry: MessageFns<FontDefinitionText_WidgetUseEntry> = {
  encode(message: FontDefinitionText_WidgetUseEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      FontDefinitionText_WidgetUse.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FontDefinitionText_WidgetUseEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFontDefinitionText_WidgetUseEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = FontDefinitionText_WidgetUse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FontDefinitionText_WidgetUseEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? FontDefinitionText_WidgetUse.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: FontDefinitionText_WidgetUseEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = FontDefinitionText_WidgetUse.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FontDefinitionText_WidgetUseEntry>, I>>(
    base?: I,
  ): FontDefinitionText_WidgetUseEntry {
    return FontDefinitionText_WidgetUseEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FontDefinitionText_WidgetUseEntry>, I>>(
    object: I,
  ): FontDefinitionText_WidgetUseEntry {
    const message = createBaseFontDefinitionText_WidgetUseEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? FontDefinitionText_WidgetUse.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseFontDefinitionText_WidgetUse(): FontDefinitionText_WidgetUse {
  return { text: {}, preset: [], source: [] };
}

export const FontDefinitionText_WidgetUse: MessageFns<FontDefinitionText_WidgetUse> = {
  encode(message: FontDefinitionText_WidgetUse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.text).forEach(([key, value]) => {
      FontDefinitionText_WidgetUse_TextEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    writer.uint32(18).fork();
    for (const v of message.preset) {
      writer.int32(v);
    }
    writer.join();
    for (const v of message.source) {
      FontDefinitionText_WidgetUse_Source.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FontDefinitionText_WidgetUse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFontDefinitionText_WidgetUse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = FontDefinitionText_WidgetUse_TextEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.text[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.preset.push(reader.int32() as any);

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.preset.push(reader.int32() as any);
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.source.push(FontDefinitionText_WidgetUse_Source.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FontDefinitionText_WidgetUse {
    return {
      text: isObject(object.text)
        ? Object.entries(object.text).reduce<{ [key: number]: FontDefinitionText_WidgetUse_TextReferenceArray }>(
          (acc, [key, value]) => {
            acc[globalThis.Number(key)] = FontDefinitionText_WidgetUse_TextReferenceArray.fromJSON(value);
            return acc;
          },
          {},
        )
        : {},
      preset: globalThis.Array.isArray(object?.preset)
        ? object.preset.map((e: any) => fontDefinitionText_WidgetUse_PresetTypeFromJSON(e))
        : [],
      source: globalThis.Array.isArray(object?.source)
        ? object.source.map((e: any) => FontDefinitionText_WidgetUse_Source.fromJSON(e))
        : [],
    };
  },

  toJSON(message: FontDefinitionText_WidgetUse): unknown {
    const obj: any = {};
    if (message.text) {
      const entries = Object.entries(message.text);
      if (entries.length > 0) {
        obj.text = {};
        entries.forEach(([k, v]) => {
          obj.text[k] = FontDefinitionText_WidgetUse_TextReferenceArray.toJSON(v);
        });
      }
    }
    if (message.preset?.length) {
      obj.preset = message.preset.map((e) => fontDefinitionText_WidgetUse_PresetTypeToJSON(e));
    }
    if (message.source?.length) {
      obj.source = message.source.map((e) => FontDefinitionText_WidgetUse_Source.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FontDefinitionText_WidgetUse>, I>>(base?: I): FontDefinitionText_WidgetUse {
    return FontDefinitionText_WidgetUse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FontDefinitionText_WidgetUse>, I>>(object: I): FontDefinitionText_WidgetUse {
    const message = createBaseFontDefinitionText_WidgetUse();
    message.text = Object.entries(object.text ?? {}).reduce<
      { [key: number]: FontDefinitionText_WidgetUse_TextReferenceArray }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = FontDefinitionText_WidgetUse_TextReferenceArray.fromPartial(value);
      }
      return acc;
    }, {});
    message.preset = object.preset?.map((e) => e) || [];
    message.source = object.source?.map((e) => FontDefinitionText_WidgetUse_Source.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFontDefinitionText_WidgetUse_TextEntry(): FontDefinitionText_WidgetUse_TextEntry {
  return { key: 0, value: undefined };
}

export const FontDefinitionText_WidgetUse_TextEntry: MessageFns<FontDefinitionText_WidgetUse_TextEntry> = {
  encode(message: FontDefinitionText_WidgetUse_TextEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      FontDefinitionText_WidgetUse_TextReferenceArray.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FontDefinitionText_WidgetUse_TextEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFontDefinitionText_WidgetUse_TextEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = FontDefinitionText_WidgetUse_TextReferenceArray.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FontDefinitionText_WidgetUse_TextEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? FontDefinitionText_WidgetUse_TextReferenceArray.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: FontDefinitionText_WidgetUse_TextEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = FontDefinitionText_WidgetUse_TextReferenceArray.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FontDefinitionText_WidgetUse_TextEntry>, I>>(
    base?: I,
  ): FontDefinitionText_WidgetUse_TextEntry {
    return FontDefinitionText_WidgetUse_TextEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FontDefinitionText_WidgetUse_TextEntry>, I>>(
    object: I,
  ): FontDefinitionText_WidgetUse_TextEntry {
    const message = createBaseFontDefinitionText_WidgetUse_TextEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? FontDefinitionText_WidgetUse_TextReferenceArray.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseFontDefinitionText_WidgetUse_TextReferenceArray(): FontDefinitionText_WidgetUse_TextReferenceArray {
  return { text: [] };
}

export const FontDefinitionText_WidgetUse_TextReferenceArray: MessageFns<
  FontDefinitionText_WidgetUse_TextReferenceArray
> = {
  encode(
    message: FontDefinitionText_WidgetUse_TextReferenceArray,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    for (const v of message.text) {
      TextReference.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FontDefinitionText_WidgetUse_TextReferenceArray {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFontDefinitionText_WidgetUse_TextReferenceArray();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.text.push(TextReference.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FontDefinitionText_WidgetUse_TextReferenceArray {
    return {
      text: globalThis.Array.isArray(object?.text) ? object.text.map((e: any) => TextReference.fromJSON(e)) : [],
    };
  },

  toJSON(message: FontDefinitionText_WidgetUse_TextReferenceArray): unknown {
    const obj: any = {};
    if (message.text?.length) {
      obj.text = message.text.map((e) => TextReference.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FontDefinitionText_WidgetUse_TextReferenceArray>, I>>(
    base?: I,
  ): FontDefinitionText_WidgetUse_TextReferenceArray {
    return FontDefinitionText_WidgetUse_TextReferenceArray.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FontDefinitionText_WidgetUse_TextReferenceArray>, I>>(
    object: I,
  ): FontDefinitionText_WidgetUse_TextReferenceArray {
    const message = createBaseFontDefinitionText_WidgetUse_TextReferenceArray();
    message.text = object.text?.map((e) => TextReference.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFontDefinitionText_WidgetUse_Source(): FontDefinitionText_WidgetUse_Source {
  return { type: 0, id: 0 };
}

export const FontDefinitionText_WidgetUse_Source: MessageFns<FontDefinitionText_WidgetUse_Source> = {
  encode(message: FontDefinitionText_WidgetUse_Source, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.type !== 0) {
      writer.uint32(8).int32(message.type);
    }
    if (message.id !== 0) {
      writer.uint32(16).uint32(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FontDefinitionText_WidgetUse_Source {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFontDefinitionText_WidgetUse_Source();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.id = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FontDefinitionText_WidgetUse_Source {
    return {
      type: isSet(object.type) ? fontDefinitionText_WidgetUse_SourceTypeFromJSON(object.type) : 0,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
    };
  },

  toJSON(message: FontDefinitionText_WidgetUse_Source): unknown {
    const obj: any = {};
    if (message.type !== 0) {
      obj.type = fontDefinitionText_WidgetUse_SourceTypeToJSON(message.type);
    }
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FontDefinitionText_WidgetUse_Source>, I>>(
    base?: I,
  ): FontDefinitionText_WidgetUse_Source {
    return FontDefinitionText_WidgetUse_Source.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FontDefinitionText_WidgetUse_Source>, I>>(
    object: I,
  ): FontDefinitionText_WidgetUse_Source {
    const message = createBaseFontDefinitionText_WidgetUse_Source();
    message.type = object.type ?? 0;
    message.id = object.id ?? 0;
    return message;
  },
};

function createBaseTextTagClassify(): TextTagClassify {
  return { name: "", parentId: undefined };
}

export const TextTagClassify: MessageFns<TextTagClassify> = {
  encode(message: TextTagClassify, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.parentId !== undefined) {
      writer.uint32(24).int32(message.parentId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TextTagClassify {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTextTagClassify();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.parentId = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TextTagClassify {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      parentId: isSet(object.parentId) ? globalThis.Number(object.parentId) : undefined,
    };
  },

  toJSON(message: TextTagClassify): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.parentId !== undefined) {
      obj.parentId = Math.round(message.parentId);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TextTagClassify>, I>>(base?: I): TextTagClassify {
    return TextTagClassify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TextTagClassify>, I>>(object: I): TextTagClassify {
    const message = createBaseTextTagClassify();
    message.name = object.name ?? "";
    message.parentId = object.parentId ?? undefined;
    return message;
  },
};

function createBaseMultiLanguageText(): MultiLanguageText {
  return { content: {} };
}

export const MultiLanguageText: MessageFns<MultiLanguageText> = {
  encode(message: MultiLanguageText, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.content).forEach(([key, value]) => {
      MultiLanguageText_ContentEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MultiLanguageText {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMultiLanguageText();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = MultiLanguageText_ContentEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.content[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MultiLanguageText {
    return {
      content: isObject(object.content)
        ? Object.entries(object.content).reduce<{ [key: number]: string }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = String(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: MultiLanguageText): unknown {
    const obj: any = {};
    if (message.content) {
      const entries = Object.entries(message.content);
      if (entries.length > 0) {
        obj.content = {};
        entries.forEach(([k, v]) => {
          obj.content[k] = v;
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MultiLanguageText>, I>>(base?: I): MultiLanguageText {
    return MultiLanguageText.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MultiLanguageText>, I>>(object: I): MultiLanguageText {
    const message = createBaseMultiLanguageText();
    message.content = Object.entries(object.content ?? {}).reduce<{ [key: number]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseMultiLanguageText_ContentEntry(): MultiLanguageText_ContentEntry {
  return { key: 0, value: "" };
}

export const MultiLanguageText_ContentEntry: MessageFns<MultiLanguageText_ContentEntry> = {
  encode(message: MultiLanguageText_ContentEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MultiLanguageText_ContentEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMultiLanguageText_ContentEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MultiLanguageText_ContentEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: MultiLanguageText_ContentEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MultiLanguageText_ContentEntry>, I>>(base?: I): MultiLanguageText_ContentEntry {
    return MultiLanguageText_ContentEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MultiLanguageText_ContentEntry>, I>>(
    object: I,
  ): MultiLanguageText_ContentEntry {
    const message = createBaseMultiLanguageText_ContentEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseTextTag(): TextTag {
  return { from: undefined, multiLine: false, classifyIdU16: 0 };
}

export const TextTag: MessageFns<TextTag> = {
  encode(message: TextTag, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "multiLang":
        MultiLanguageText.encode(message.from.value, writer.uint32(10).fork()).join();
        break;
      case "singleLang":
        writer.uint32(18).string(message.from.value);
        break;
    }
    if (message.multiLine !== false) {
      writer.uint32(24).bool(message.multiLine);
    }
    if (message.classifyIdU16 !== 0) {
      writer.uint32(32).uint32(message.classifyIdU16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TextTag {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTextTag();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.from = { $case: "multiLang", value: MultiLanguageText.decode(reader, reader.uint32()) };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "singleLang", value: reader.string() };
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.multiLine = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.classifyIdU16 = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TextTag {
    return {
      from: isSet(object.multiLang)
        ? { $case: "multiLang", value: MultiLanguageText.fromJSON(object.multiLang) }
        : isSet(object.singleLang)
        ? { $case: "singleLang", value: globalThis.String(object.singleLang) }
        : undefined,
      multiLine: isSet(object.multiLine) ? globalThis.Boolean(object.multiLine) : false,
      classifyIdU16: isSet(object.classifyIdU16) ? globalThis.Number(object.classifyIdU16) : 0,
    };
  },

  toJSON(message: TextTag): unknown {
    const obj: any = {};
    if (message.from?.$case === "multiLang") {
      obj.multiLang = MultiLanguageText.toJSON(message.from.value);
    } else if (message.from?.$case === "singleLang") {
      obj.singleLang = message.from.value;
    }
    if (message.multiLine !== false) {
      obj.multiLine = message.multiLine;
    }
    if (message.classifyIdU16 !== 0) {
      obj.classifyIdU16 = Math.round(message.classifyIdU16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TextTag>, I>>(base?: I): TextTag {
    return TextTag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TextTag>, I>>(object: I): TextTag {
    const message = createBaseTextTag();
    switch (object.from?.$case) {
      case "multiLang": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "multiLang", value: MultiLanguageText.fromPartial(object.from.value) };
        }
        break;
      }
      case "singleLang": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "singleLang", value: object.from.value };
        }
        break;
      }
    }
    message.multiLine = object.multiLine ?? false;
    message.classifyIdU16 = object.classifyIdU16 ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
