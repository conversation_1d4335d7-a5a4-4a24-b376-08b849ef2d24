// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/project.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "znd.project.v1";

/** 工程对象类型 */
export enum ProjectObjectType {
  /** PROJECT_OBJECT_TYPE_UNSPECIFIED - 未设置 */
  PROJECT_OBJECT_TYPE_UNSPECIFIED = 0,
  /** PROJECT_OBJECT_TYPE_PROJECT_INFO - 工程信息（唯一） */
  PROJECT_OBJECT_TYPE_PROJECT_INFO = 1,
  /** PROJECT_OBJECT_TYPE_DISPLAY_LIST - 显示列表 */
  PROJECT_OBJECT_TYPE_DISPLAY_LIST = 2,
  /** PROJECT_OBJECT_TYPE_PAGE_WIDGETS - 页面元件（一个页面一个文件） */
  PROJECT_OBJECT_TYPE_PAGE_WIDGETS = 3,
  /** PROJECT_OBJECT_TYPE_TUNNEL_DEVICE_LIST - 设备列表 */
  PROJECT_OBJECT_TYPE_TUNNEL_DEVICE_LIST = 4,
  /** PROJECT_OBJECT_TYPE_VARIABLE - 变量定义（一个设备一个文件,0表示公用，比如动态变量和表达式） */
  PROJECT_OBJECT_TYPE_VARIABLE = 5,
  /** PROJECT_OBJECT_TYPE_STYLE_CONFIG - 样式配置文件（唯一） */
  PROJECT_OBJECT_TYPE_STYLE_CONFIG = 6,
  /** PROJECT_OBJECT_TYPE_TEXT_TAG_LIBRARY - 文本标签库（0是用户建的，其他用pageId保存） */
  PROJECT_OBJECT_TYPE_TEXT_TAG_LIBRARY = 7,
  /** PROJECT_OBJECT_TYPE_FONT_FILE - 字体文件（一个字体一个文件） */
  PROJECT_OBJECT_TYPE_FONT_FILE = 8,
  /** PROJECT_OBJECT_TYPE_IMAGE_LIST - 图片列表 */
  PROJECT_OBJECT_TYPE_IMAGE_LIST = 9,
  /** PROJECT_OBJECT_TYPE_IMAGE_FILE - 图片转换后文件（一个图片一个文件） */
  PROJECT_OBJECT_TYPE_IMAGE_FILE = 10,
  /** PROJECT_OBJECT_TYPE_PROJECT_GALLERY_LIST - 工程图库列表(这个还是会下载到设备端,后续上载时可以从云端根据md5读源文件) */
  PROJECT_OBJECT_TYPE_PROJECT_GALLERY_LIST = 11,
  /** PROJECT_OBJECT_TYPE_PROJECT_GALLERY_FILE - 图片源文件（工程图库，一个图片一个文件，这个不会下载到设备端） */
  PROJECT_OBJECT_TYPE_PROJECT_GALLERY_FILE = 12,
  /** PROJECT_OBJECT_TYPE_DATA_CONFIG - 数据配置（数据采样、报警、操作记录、数据库） */
  PROJECT_OBJECT_TYPE_DATA_CONFIG = 13,
  /** PROJECT_OBJECT_TYPE_RECIPE_CONFIG - 配方配置 */
  PROJECT_OBJECT_TYPE_RECIPE_CONFIG = 14,
  /** PROJECT_OBJECT_TYPE_PROJECT_EDIT_STATUS - 工程上次编辑的状态 */
  PROJECT_OBJECT_TYPE_PROJECT_EDIT_STATUS = 15,
  /** PROJECT_OBJECT_TYPE_PAGE_THUMBNAIL - 页面的缩略图 */
  PROJECT_OBJECT_TYPE_PAGE_THUMBNAIL = 16,
  /** PROJECT_OBJECT_TYPE_FONT_DEFINITION_TEXT - 字体使用文本(ID是字体定义ID,如果不用上载，则不用下载到设备端) */
  PROJECT_OBJECT_TYPE_FONT_DEFINITION_TEXT = 17,
  UNRECOGNIZED = -1,
}

export function projectObjectTypeFromJSON(object: any): ProjectObjectType {
  switch (object) {
    case 0:
    case "PROJECT_OBJECT_TYPE_UNSPECIFIED":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_UNSPECIFIED;
    case 1:
    case "PROJECT_OBJECT_TYPE_PROJECT_INFO":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_PROJECT_INFO;
    case 2:
    case "PROJECT_OBJECT_TYPE_DISPLAY_LIST":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_DISPLAY_LIST;
    case 3:
    case "PROJECT_OBJECT_TYPE_PAGE_WIDGETS":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_PAGE_WIDGETS;
    case 4:
    case "PROJECT_OBJECT_TYPE_TUNNEL_DEVICE_LIST":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_TUNNEL_DEVICE_LIST;
    case 5:
    case "PROJECT_OBJECT_TYPE_VARIABLE":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_VARIABLE;
    case 6:
    case "PROJECT_OBJECT_TYPE_STYLE_CONFIG":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_STYLE_CONFIG;
    case 7:
    case "PROJECT_OBJECT_TYPE_TEXT_TAG_LIBRARY":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_TEXT_TAG_LIBRARY;
    case 8:
    case "PROJECT_OBJECT_TYPE_FONT_FILE":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_FONT_FILE;
    case 9:
    case "PROJECT_OBJECT_TYPE_IMAGE_LIST":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_IMAGE_LIST;
    case 10:
    case "PROJECT_OBJECT_TYPE_IMAGE_FILE":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_IMAGE_FILE;
    case 11:
    case "PROJECT_OBJECT_TYPE_PROJECT_GALLERY_LIST":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_PROJECT_GALLERY_LIST;
    case 12:
    case "PROJECT_OBJECT_TYPE_PROJECT_GALLERY_FILE":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_PROJECT_GALLERY_FILE;
    case 13:
    case "PROJECT_OBJECT_TYPE_DATA_CONFIG":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_DATA_CONFIG;
    case 14:
    case "PROJECT_OBJECT_TYPE_RECIPE_CONFIG":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_RECIPE_CONFIG;
    case 15:
    case "PROJECT_OBJECT_TYPE_PROJECT_EDIT_STATUS":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_PROJECT_EDIT_STATUS;
    case 16:
    case "PROJECT_OBJECT_TYPE_PAGE_THUMBNAIL":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_PAGE_THUMBNAIL;
    case 17:
    case "PROJECT_OBJECT_TYPE_FONT_DEFINITION_TEXT":
      return ProjectObjectType.PROJECT_OBJECT_TYPE_FONT_DEFINITION_TEXT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ProjectObjectType.UNRECOGNIZED;
  }
}

export function projectObjectTypeToJSON(object: ProjectObjectType): string {
  switch (object) {
    case ProjectObjectType.PROJECT_OBJECT_TYPE_UNSPECIFIED:
      return "PROJECT_OBJECT_TYPE_UNSPECIFIED";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_PROJECT_INFO:
      return "PROJECT_OBJECT_TYPE_PROJECT_INFO";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_DISPLAY_LIST:
      return "PROJECT_OBJECT_TYPE_DISPLAY_LIST";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_PAGE_WIDGETS:
      return "PROJECT_OBJECT_TYPE_PAGE_WIDGETS";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_TUNNEL_DEVICE_LIST:
      return "PROJECT_OBJECT_TYPE_TUNNEL_DEVICE_LIST";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_VARIABLE:
      return "PROJECT_OBJECT_TYPE_VARIABLE";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_STYLE_CONFIG:
      return "PROJECT_OBJECT_TYPE_STYLE_CONFIG";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_TEXT_TAG_LIBRARY:
      return "PROJECT_OBJECT_TYPE_TEXT_TAG_LIBRARY";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_FONT_FILE:
      return "PROJECT_OBJECT_TYPE_FONT_FILE";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_IMAGE_LIST:
      return "PROJECT_OBJECT_TYPE_IMAGE_LIST";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_IMAGE_FILE:
      return "PROJECT_OBJECT_TYPE_IMAGE_FILE";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_PROJECT_GALLERY_LIST:
      return "PROJECT_OBJECT_TYPE_PROJECT_GALLERY_LIST";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_PROJECT_GALLERY_FILE:
      return "PROJECT_OBJECT_TYPE_PROJECT_GALLERY_FILE";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_DATA_CONFIG:
      return "PROJECT_OBJECT_TYPE_DATA_CONFIG";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_RECIPE_CONFIG:
      return "PROJECT_OBJECT_TYPE_RECIPE_CONFIG";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_PROJECT_EDIT_STATUS:
      return "PROJECT_OBJECT_TYPE_PROJECT_EDIT_STATUS";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_PAGE_THUMBNAIL:
      return "PROJECT_OBJECT_TYPE_PAGE_THUMBNAIL";
    case ProjectObjectType.PROJECT_OBJECT_TYPE_FONT_DEFINITION_TEXT:
      return "PROJECT_OBJECT_TYPE_FONT_DEFINITION_TEXT";
    case ProjectObjectType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum HardwareType {
  HARDWARE_TYPE_UNSPECIFIED = 0,
  HARDWARE_TYPE_HMI = 1,
  HARDWARE_TYPE_BOX = 2,
  HARDWARE_TYPE_GATEWAY = 3,
  HARDWARE_TYPE_PC = 10,
  UNRECOGNIZED = -1,
}

export function hardwareTypeFromJSON(object: any): HardwareType {
  switch (object) {
    case 0:
    case "HARDWARE_TYPE_UNSPECIFIED":
      return HardwareType.HARDWARE_TYPE_UNSPECIFIED;
    case 1:
    case "HARDWARE_TYPE_HMI":
      return HardwareType.HARDWARE_TYPE_HMI;
    case 2:
    case "HARDWARE_TYPE_BOX":
      return HardwareType.HARDWARE_TYPE_BOX;
    case 3:
    case "HARDWARE_TYPE_GATEWAY":
      return HardwareType.HARDWARE_TYPE_GATEWAY;
    case 10:
    case "HARDWARE_TYPE_PC":
      return HardwareType.HARDWARE_TYPE_PC;
    case -1:
    case "UNRECOGNIZED":
    default:
      return HardwareType.UNRECOGNIZED;
  }
}

export function hardwareTypeToJSON(object: HardwareType): string {
  switch (object) {
    case HardwareType.HARDWARE_TYPE_UNSPECIFIED:
      return "HARDWARE_TYPE_UNSPECIFIED";
    case HardwareType.HARDWARE_TYPE_HMI:
      return "HARDWARE_TYPE_HMI";
    case HardwareType.HARDWARE_TYPE_BOX:
      return "HARDWARE_TYPE_BOX";
    case HardwareType.HARDWARE_TYPE_GATEWAY:
      return "HARDWARE_TYPE_GATEWAY";
    case HardwareType.HARDWARE_TYPE_PC:
      return "HARDWARE_TYPE_PC";
    case HardwareType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 版本号 */
export interface Version {
  majorU8: number;
  minorU8: number;
  patchU16: number;
  buildU16: number;
}

export interface ProjectInfo {
  /** HMI类型 */
  hardwareType: HardwareType;
  /** 创建的组态版本 */
  createBuilderVersion:
    | Version
    | undefined;
  /** 更新时的组态版本 */
  updateBuilderVersion:
    | Version
    | undefined;
  /** 最低固件版本号 */
  minFirmwareVersion:
    | Version
    | undefined;
  /** 设计打开密码 */
  designPassword?:
    | string
    | undefined;
  /** 上载密码，为空不允许上载 */
  uploadPassword?:
    | string
    | undefined;
  /** 下载密码(这个应该改到HMI硬件中去设置？) */
  downloadPassword?:
    | string
    | undefined;
  /** 反编译密码，为空不允许反编译 */
  uncompilePassword?:
    | string
    | undefined;
  /** 内容保护密码 */
  protectPassword?:
    | string
    | undefined;
  /** HMI硬件绑定密码（逻辑待梳理） */
  hardwareBindPassword?: string | undefined;
}

/** 工程上次编辑的状态 */
export interface ProjectEditStatus {
  /** bool changed = 1;//是否发生变更 */
  editorView: number;
  /** 当前工程配置选中项 */
  projectConfigIndex: number;
  /** 当前IOT配置选中项 */
  iotConfigView: string;
  /** 当前编辑的页面 */
  activePageId: number;
  /** 当前缩放比例 */
  zoom: number;
  /** 当前页面状态值 */
  pageStateValue: number;
  /** 当前页面语言值 */
  pageLanguageNo: number;
  /** 当前页面主题值 */
  pageThemeNo: number;
}

function createBaseVersion(): Version {
  return { majorU8: 0, minorU8: 0, patchU16: 0, buildU16: 0 };
}

export const Version: MessageFns<Version> = {
  encode(message: Version, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.majorU8 !== 0) {
      writer.uint32(8).uint32(message.majorU8);
    }
    if (message.minorU8 !== 0) {
      writer.uint32(16).uint32(message.minorU8);
    }
    if (message.patchU16 !== 0) {
      writer.uint32(24).uint32(message.patchU16);
    }
    if (message.buildU16 !== 0) {
      writer.uint32(32).uint32(message.buildU16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Version {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVersion();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.majorU8 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.minorU8 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.patchU16 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.buildU16 = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Version {
    return {
      majorU8: isSet(object.majorU8) ? globalThis.Number(object.majorU8) : 0,
      minorU8: isSet(object.minorU8) ? globalThis.Number(object.minorU8) : 0,
      patchU16: isSet(object.patchU16) ? globalThis.Number(object.patchU16) : 0,
      buildU16: isSet(object.buildU16) ? globalThis.Number(object.buildU16) : 0,
    };
  },

  toJSON(message: Version): unknown {
    const obj: any = {};
    if (message.majorU8 !== 0) {
      obj.majorU8 = Math.round(message.majorU8);
    }
    if (message.minorU8 !== 0) {
      obj.minorU8 = Math.round(message.minorU8);
    }
    if (message.patchU16 !== 0) {
      obj.patchU16 = Math.round(message.patchU16);
    }
    if (message.buildU16 !== 0) {
      obj.buildU16 = Math.round(message.buildU16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Version>, I>>(base?: I): Version {
    return Version.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Version>, I>>(object: I): Version {
    const message = createBaseVersion();
    message.majorU8 = object.majorU8 ?? 0;
    message.minorU8 = object.minorU8 ?? 0;
    message.patchU16 = object.patchU16 ?? 0;
    message.buildU16 = object.buildU16 ?? 0;
    return message;
  },
};

function createBaseProjectInfo(): ProjectInfo {
  return {
    hardwareType: 0,
    createBuilderVersion: undefined,
    updateBuilderVersion: undefined,
    minFirmwareVersion: undefined,
    designPassword: undefined,
    uploadPassword: undefined,
    downloadPassword: undefined,
    uncompilePassword: undefined,
    protectPassword: undefined,
    hardwareBindPassword: undefined,
  };
}

export const ProjectInfo: MessageFns<ProjectInfo> = {
  encode(message: ProjectInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.hardwareType !== 0) {
      writer.uint32(8).int32(message.hardwareType);
    }
    if (message.createBuilderVersion !== undefined) {
      Version.encode(message.createBuilderVersion, writer.uint32(18).fork()).join();
    }
    if (message.updateBuilderVersion !== undefined) {
      Version.encode(message.updateBuilderVersion, writer.uint32(26).fork()).join();
    }
    if (message.minFirmwareVersion !== undefined) {
      Version.encode(message.minFirmwareVersion, writer.uint32(34).fork()).join();
    }
    if (message.designPassword !== undefined) {
      writer.uint32(42).string(message.designPassword);
    }
    if (message.uploadPassword !== undefined) {
      writer.uint32(50).string(message.uploadPassword);
    }
    if (message.downloadPassword !== undefined) {
      writer.uint32(58).string(message.downloadPassword);
    }
    if (message.uncompilePassword !== undefined) {
      writer.uint32(66).string(message.uncompilePassword);
    }
    if (message.protectPassword !== undefined) {
      writer.uint32(74).string(message.protectPassword);
    }
    if (message.hardwareBindPassword !== undefined) {
      writer.uint32(82).string(message.hardwareBindPassword);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProjectInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProjectInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.hardwareType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.createBuilderVersion = Version.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.updateBuilderVersion = Version.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.minFirmwareVersion = Version.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.designPassword = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.uploadPassword = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.downloadPassword = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.uncompilePassword = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.protectPassword = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.hardwareBindPassword = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProjectInfo {
    return {
      hardwareType: isSet(object.hardwareType) ? hardwareTypeFromJSON(object.hardwareType) : 0,
      createBuilderVersion: isSet(object.createBuilderVersion)
        ? Version.fromJSON(object.createBuilderVersion)
        : undefined,
      updateBuilderVersion: isSet(object.updateBuilderVersion)
        ? Version.fromJSON(object.updateBuilderVersion)
        : undefined,
      minFirmwareVersion: isSet(object.minFirmwareVersion) ? Version.fromJSON(object.minFirmwareVersion) : undefined,
      designPassword: isSet(object.designPassword) ? globalThis.String(object.designPassword) : undefined,
      uploadPassword: isSet(object.uploadPassword) ? globalThis.String(object.uploadPassword) : undefined,
      downloadPassword: isSet(object.downloadPassword) ? globalThis.String(object.downloadPassword) : undefined,
      uncompilePassword: isSet(object.uncompilePassword) ? globalThis.String(object.uncompilePassword) : undefined,
      protectPassword: isSet(object.protectPassword) ? globalThis.String(object.protectPassword) : undefined,
      hardwareBindPassword: isSet(object.hardwareBindPassword)
        ? globalThis.String(object.hardwareBindPassword)
        : undefined,
    };
  },

  toJSON(message: ProjectInfo): unknown {
    const obj: any = {};
    if (message.hardwareType !== 0) {
      obj.hardwareType = hardwareTypeToJSON(message.hardwareType);
    }
    if (message.createBuilderVersion !== undefined) {
      obj.createBuilderVersion = Version.toJSON(message.createBuilderVersion);
    }
    if (message.updateBuilderVersion !== undefined) {
      obj.updateBuilderVersion = Version.toJSON(message.updateBuilderVersion);
    }
    if (message.minFirmwareVersion !== undefined) {
      obj.minFirmwareVersion = Version.toJSON(message.minFirmwareVersion);
    }
    if (message.designPassword !== undefined) {
      obj.designPassword = message.designPassword;
    }
    if (message.uploadPassword !== undefined) {
      obj.uploadPassword = message.uploadPassword;
    }
    if (message.downloadPassword !== undefined) {
      obj.downloadPassword = message.downloadPassword;
    }
    if (message.uncompilePassword !== undefined) {
      obj.uncompilePassword = message.uncompilePassword;
    }
    if (message.protectPassword !== undefined) {
      obj.protectPassword = message.protectPassword;
    }
    if (message.hardwareBindPassword !== undefined) {
      obj.hardwareBindPassword = message.hardwareBindPassword;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProjectInfo>, I>>(base?: I): ProjectInfo {
    return ProjectInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProjectInfo>, I>>(object: I): ProjectInfo {
    const message = createBaseProjectInfo();
    message.hardwareType = object.hardwareType ?? 0;
    message.createBuilderVersion = (object.createBuilderVersion !== undefined && object.createBuilderVersion !== null)
      ? Version.fromPartial(object.createBuilderVersion)
      : undefined;
    message.updateBuilderVersion = (object.updateBuilderVersion !== undefined && object.updateBuilderVersion !== null)
      ? Version.fromPartial(object.updateBuilderVersion)
      : undefined;
    message.minFirmwareVersion = (object.minFirmwareVersion !== undefined && object.minFirmwareVersion !== null)
      ? Version.fromPartial(object.minFirmwareVersion)
      : undefined;
    message.designPassword = object.designPassword ?? undefined;
    message.uploadPassword = object.uploadPassword ?? undefined;
    message.downloadPassword = object.downloadPassword ?? undefined;
    message.uncompilePassword = object.uncompilePassword ?? undefined;
    message.protectPassword = object.protectPassword ?? undefined;
    message.hardwareBindPassword = object.hardwareBindPassword ?? undefined;
    return message;
  },
};

function createBaseProjectEditStatus(): ProjectEditStatus {
  return {
    editorView: 0,
    projectConfigIndex: 0,
    iotConfigView: "",
    activePageId: 0,
    zoom: 0,
    pageStateValue: 0,
    pageLanguageNo: 0,
    pageThemeNo: 0,
  };
}

export const ProjectEditStatus: MessageFns<ProjectEditStatus> = {
  encode(message: ProjectEditStatus, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.editorView !== 0) {
      writer.uint32(16).int32(message.editorView);
    }
    if (message.projectConfigIndex !== 0) {
      writer.uint32(24).int32(message.projectConfigIndex);
    }
    if (message.iotConfigView !== "") {
      writer.uint32(34).string(message.iotConfigView);
    }
    if (message.activePageId !== 0) {
      writer.uint32(40).int32(message.activePageId);
    }
    if (message.zoom !== 0) {
      writer.uint32(48).int32(message.zoom);
    }
    if (message.pageStateValue !== 0) {
      writer.uint32(56).int32(message.pageStateValue);
    }
    if (message.pageLanguageNo !== 0) {
      writer.uint32(64).int32(message.pageLanguageNo);
    }
    if (message.pageThemeNo !== 0) {
      writer.uint32(72).int32(message.pageThemeNo);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProjectEditStatus {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProjectEditStatus();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.editorView = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.projectConfigIndex = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.iotConfigView = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.activePageId = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.zoom = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.pageStateValue = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.pageLanguageNo = reader.int32();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.pageThemeNo = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProjectEditStatus {
    return {
      editorView: isSet(object.editorView) ? globalThis.Number(object.editorView) : 0,
      projectConfigIndex: isSet(object.projectConfigIndex) ? globalThis.Number(object.projectConfigIndex) : 0,
      iotConfigView: isSet(object.iotConfigView) ? globalThis.String(object.iotConfigView) : "",
      activePageId: isSet(object.activePageId) ? globalThis.Number(object.activePageId) : 0,
      zoom: isSet(object.zoom) ? globalThis.Number(object.zoom) : 0,
      pageStateValue: isSet(object.pageStateValue) ? globalThis.Number(object.pageStateValue) : 0,
      pageLanguageNo: isSet(object.pageLanguageNo) ? globalThis.Number(object.pageLanguageNo) : 0,
      pageThemeNo: isSet(object.pageThemeNo) ? globalThis.Number(object.pageThemeNo) : 0,
    };
  },

  toJSON(message: ProjectEditStatus): unknown {
    const obj: any = {};
    if (message.editorView !== 0) {
      obj.editorView = Math.round(message.editorView);
    }
    if (message.projectConfigIndex !== 0) {
      obj.projectConfigIndex = Math.round(message.projectConfigIndex);
    }
    if (message.iotConfigView !== "") {
      obj.iotConfigView = message.iotConfigView;
    }
    if (message.activePageId !== 0) {
      obj.activePageId = Math.round(message.activePageId);
    }
    if (message.zoom !== 0) {
      obj.zoom = Math.round(message.zoom);
    }
    if (message.pageStateValue !== 0) {
      obj.pageStateValue = Math.round(message.pageStateValue);
    }
    if (message.pageLanguageNo !== 0) {
      obj.pageLanguageNo = Math.round(message.pageLanguageNo);
    }
    if (message.pageThemeNo !== 0) {
      obj.pageThemeNo = Math.round(message.pageThemeNo);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProjectEditStatus>, I>>(base?: I): ProjectEditStatus {
    return ProjectEditStatus.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProjectEditStatus>, I>>(object: I): ProjectEditStatus {
    const message = createBaseProjectEditStatus();
    message.editorView = object.editorView ?? 0;
    message.projectConfigIndex = object.projectConfigIndex ?? 0;
    message.iotConfigView = object.iotConfigView ?? "";
    message.activePageId = object.activePageId ?? 0;
    message.zoom = object.zoom ?? 0;
    message.pageStateValue = object.pageStateValue ?? 0;
    message.pageLanguageNo = object.pageLanguageNo ?? 0;
    message.pageThemeNo = object.pageThemeNo ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
