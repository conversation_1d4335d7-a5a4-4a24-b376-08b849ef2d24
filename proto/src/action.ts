// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/action.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { TextTag } from "./text";
import {
  DataReference,
  DataReferenceBool,
  DataReferenceInt16,
  DataReferenceNumber,
  DataReferenceUInt16,
  DataReferenceUInt8,
  VariableReference,
} from "./variable";

export const protobufPackage = "znd.project.v1";

/** 执行时机 */
export enum ActionTiming {
  /** ACTION_TIMING_UNSPECIFIED - 未设置 */
  ACTION_TIMING_UNSPECIFIED = 0,
  /** ACTION_TIMING_BEFORE - 执行前 */
  ACTION_TIMING_BEFORE = 1,
  /** ACTION_TIMING_PRESS - 按下时 */
  ACTION_TIMING_PRESS = 2,
  /** ACTION_TIMING_RELEASE - 抬起时 */
  ACTION_TIMING_RELEASE = 3,
  /** ACTION_TIMING_INPUT - 输入时 */
  ACTION_TIMING_INPUT = 4,
  /** ACTION_TIMING_BATCH_INPUT - 批量输入时 */
  ACTION_TIMING_BATCH_INPUT = 5,
  /** ACTION_TIMING_OPEN_PAGE - 画面打开时(以本动作所属的pageId为对比) */
  ACTION_TIMING_OPEN_PAGE = 6,
  /** ACTION_TIMING_CLOSE_PAGE - 画面关闭时(以本动作所属的pageId为对比) */
  ACTION_TIMING_CLOSE_PAGE = 7,
  /** ACTION_TIMING_SCREEN_BLACK - 屏幕熄屏时(此类任务的pageId=0) */
  ACTION_TIMING_SCREEN_BLACK = 8,
  /** ACTION_TIMING_SCREEN_LIGHT - 屏幕亮屏时(此类任务的pageId=0) */
  ACTION_TIMING_SCREEN_LIGHT = 9,
  /** ACTION_TIMING_APP_START - 应用启动时(此类任务的pageId=0) */
  ACTION_TIMING_APP_START = 10,
  /** ACTION_TIMING_APP_EXIT - 应用退出时(仅PC端，或者HMI端点击重启时，同样此类任务的pageId=0) */
  ACTION_TIMING_APP_EXIT = 11,
  /** ACTION_TIMING_AFTER_SUCCESS - 执行成功后 */
  ACTION_TIMING_AFTER_SUCCESS = 12,
  /** ACTION_TIMING_AFTER_FAILURE - 执行失败后 */
  ACTION_TIMING_AFTER_FAILURE = 13,
  /** ACTION_TIMING_AFTER - 执行后 */
  ACTION_TIMING_AFTER = 14,
  UNRECOGNIZED = -1,
}

export function actionTimingFromJSON(object: any): ActionTiming {
  switch (object) {
    case 0:
    case "ACTION_TIMING_UNSPECIFIED":
      return ActionTiming.ACTION_TIMING_UNSPECIFIED;
    case 1:
    case "ACTION_TIMING_BEFORE":
      return ActionTiming.ACTION_TIMING_BEFORE;
    case 2:
    case "ACTION_TIMING_PRESS":
      return ActionTiming.ACTION_TIMING_PRESS;
    case 3:
    case "ACTION_TIMING_RELEASE":
      return ActionTiming.ACTION_TIMING_RELEASE;
    case 4:
    case "ACTION_TIMING_INPUT":
      return ActionTiming.ACTION_TIMING_INPUT;
    case 5:
    case "ACTION_TIMING_BATCH_INPUT":
      return ActionTiming.ACTION_TIMING_BATCH_INPUT;
    case 6:
    case "ACTION_TIMING_OPEN_PAGE":
      return ActionTiming.ACTION_TIMING_OPEN_PAGE;
    case 7:
    case "ACTION_TIMING_CLOSE_PAGE":
      return ActionTiming.ACTION_TIMING_CLOSE_PAGE;
    case 8:
    case "ACTION_TIMING_SCREEN_BLACK":
      return ActionTiming.ACTION_TIMING_SCREEN_BLACK;
    case 9:
    case "ACTION_TIMING_SCREEN_LIGHT":
      return ActionTiming.ACTION_TIMING_SCREEN_LIGHT;
    case 10:
    case "ACTION_TIMING_APP_START":
      return ActionTiming.ACTION_TIMING_APP_START;
    case 11:
    case "ACTION_TIMING_APP_EXIT":
      return ActionTiming.ACTION_TIMING_APP_EXIT;
    case 12:
    case "ACTION_TIMING_AFTER_SUCCESS":
      return ActionTiming.ACTION_TIMING_AFTER_SUCCESS;
    case 13:
    case "ACTION_TIMING_AFTER_FAILURE":
      return ActionTiming.ACTION_TIMING_AFTER_FAILURE;
    case 14:
    case "ACTION_TIMING_AFTER":
      return ActionTiming.ACTION_TIMING_AFTER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ActionTiming.UNRECOGNIZED;
  }
}

export function actionTimingToJSON(object: ActionTiming): string {
  switch (object) {
    case ActionTiming.ACTION_TIMING_UNSPECIFIED:
      return "ACTION_TIMING_UNSPECIFIED";
    case ActionTiming.ACTION_TIMING_BEFORE:
      return "ACTION_TIMING_BEFORE";
    case ActionTiming.ACTION_TIMING_PRESS:
      return "ACTION_TIMING_PRESS";
    case ActionTiming.ACTION_TIMING_RELEASE:
      return "ACTION_TIMING_RELEASE";
    case ActionTiming.ACTION_TIMING_INPUT:
      return "ACTION_TIMING_INPUT";
    case ActionTiming.ACTION_TIMING_BATCH_INPUT:
      return "ACTION_TIMING_BATCH_INPUT";
    case ActionTiming.ACTION_TIMING_OPEN_PAGE:
      return "ACTION_TIMING_OPEN_PAGE";
    case ActionTiming.ACTION_TIMING_CLOSE_PAGE:
      return "ACTION_TIMING_CLOSE_PAGE";
    case ActionTiming.ACTION_TIMING_SCREEN_BLACK:
      return "ACTION_TIMING_SCREEN_BLACK";
    case ActionTiming.ACTION_TIMING_SCREEN_LIGHT:
      return "ACTION_TIMING_SCREEN_LIGHT";
    case ActionTiming.ACTION_TIMING_APP_START:
      return "ACTION_TIMING_APP_START";
    case ActionTiming.ACTION_TIMING_APP_EXIT:
      return "ACTION_TIMING_APP_EXIT";
    case ActionTiming.ACTION_TIMING_AFTER_SUCCESS:
      return "ACTION_TIMING_AFTER_SUCCESS";
    case ActionTiming.ACTION_TIMING_AFTER_FAILURE:
      return "ACTION_TIMING_AFTER_FAILURE";
    case ActionTiming.ACTION_TIMING_AFTER:
      return "ACTION_TIMING_AFTER";
    case ActionTiming.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 页面跳转类型 */
export enum PageActionType {
  PAGE_ACTION_TYPE_UNSPECIFIED = 0,
  /** PAGE_ACTION_TYPE_BASE_CHANGE - 切换基本窗口 */
  PAGE_ACTION_TYPE_BASE_CHANGE = 1,
  /** PAGE_ACTION_TYPE_BASE_HOME - 返回主页 */
  PAGE_ACTION_TYPE_BASE_HOME = 2,
  /** PAGE_ACTION_TYPE_BASE_BACK - 返回上一页 */
  PAGE_ACTION_TYPE_BASE_BACK = 3,
  /** PAGE_ACTION_TYPE_BASE_FORWARD - 前进到刚才的页面 */
  PAGE_ACTION_TYPE_BASE_FORWARD = 4,
  /** PAGE_ACTION_TYPE_BASE_NEXT - 跳到下个窗口号 */
  PAGE_ACTION_TYPE_BASE_NEXT = 5,
  /** PAGE_ACTION_TYPE_BASE_PREV - 跳到上个窗口号 */
  PAGE_ACTION_TYPE_BASE_PREV = 6,
  /** PAGE_ACTION_TYPE_COMMON_CHANGE - 切换公共窗口 */
  PAGE_ACTION_TYPE_COMMON_CHANGE = 7,
  /** PAGE_ACTION_TYPE_POPUP_SHOW - 弹出窗口 */
  PAGE_ACTION_TYPE_POPUP_SHOW = 8,
  /** PAGE_ACTION_TYPE_POPUP_CLOSE_LAST - 关闭最后一个弹出窗口 */
  PAGE_ACTION_TYPE_POPUP_CLOSE_LAST = 9,
  /** PAGE_ACTION_TYPE_POPUP_CLOSE_ALL - 关闭所有弹出窗口 */
  PAGE_ACTION_TYPE_POPUP_CLOSE_ALL = 10,
  UNRECOGNIZED = -1,
}

export function pageActionTypeFromJSON(object: any): PageActionType {
  switch (object) {
    case 0:
    case "PAGE_ACTION_TYPE_UNSPECIFIED":
      return PageActionType.PAGE_ACTION_TYPE_UNSPECIFIED;
    case 1:
    case "PAGE_ACTION_TYPE_BASE_CHANGE":
      return PageActionType.PAGE_ACTION_TYPE_BASE_CHANGE;
    case 2:
    case "PAGE_ACTION_TYPE_BASE_HOME":
      return PageActionType.PAGE_ACTION_TYPE_BASE_HOME;
    case 3:
    case "PAGE_ACTION_TYPE_BASE_BACK":
      return PageActionType.PAGE_ACTION_TYPE_BASE_BACK;
    case 4:
    case "PAGE_ACTION_TYPE_BASE_FORWARD":
      return PageActionType.PAGE_ACTION_TYPE_BASE_FORWARD;
    case 5:
    case "PAGE_ACTION_TYPE_BASE_NEXT":
      return PageActionType.PAGE_ACTION_TYPE_BASE_NEXT;
    case 6:
    case "PAGE_ACTION_TYPE_BASE_PREV":
      return PageActionType.PAGE_ACTION_TYPE_BASE_PREV;
    case 7:
    case "PAGE_ACTION_TYPE_COMMON_CHANGE":
      return PageActionType.PAGE_ACTION_TYPE_COMMON_CHANGE;
    case 8:
    case "PAGE_ACTION_TYPE_POPUP_SHOW":
      return PageActionType.PAGE_ACTION_TYPE_POPUP_SHOW;
    case 9:
    case "PAGE_ACTION_TYPE_POPUP_CLOSE_LAST":
      return PageActionType.PAGE_ACTION_TYPE_POPUP_CLOSE_LAST;
    case 10:
    case "PAGE_ACTION_TYPE_POPUP_CLOSE_ALL":
      return PageActionType.PAGE_ACTION_TYPE_POPUP_CLOSE_ALL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PageActionType.UNRECOGNIZED;
  }
}

export function pageActionTypeToJSON(object: PageActionType): string {
  switch (object) {
    case PageActionType.PAGE_ACTION_TYPE_UNSPECIFIED:
      return "PAGE_ACTION_TYPE_UNSPECIFIED";
    case PageActionType.PAGE_ACTION_TYPE_BASE_CHANGE:
      return "PAGE_ACTION_TYPE_BASE_CHANGE";
    case PageActionType.PAGE_ACTION_TYPE_BASE_HOME:
      return "PAGE_ACTION_TYPE_BASE_HOME";
    case PageActionType.PAGE_ACTION_TYPE_BASE_BACK:
      return "PAGE_ACTION_TYPE_BASE_BACK";
    case PageActionType.PAGE_ACTION_TYPE_BASE_FORWARD:
      return "PAGE_ACTION_TYPE_BASE_FORWARD";
    case PageActionType.PAGE_ACTION_TYPE_BASE_NEXT:
      return "PAGE_ACTION_TYPE_BASE_NEXT";
    case PageActionType.PAGE_ACTION_TYPE_BASE_PREV:
      return "PAGE_ACTION_TYPE_BASE_PREV";
    case PageActionType.PAGE_ACTION_TYPE_COMMON_CHANGE:
      return "PAGE_ACTION_TYPE_COMMON_CHANGE";
    case PageActionType.PAGE_ACTION_TYPE_POPUP_SHOW:
      return "PAGE_ACTION_TYPE_POPUP_SHOW";
    case PageActionType.PAGE_ACTION_TYPE_POPUP_CLOSE_LAST:
      return "PAGE_ACTION_TYPE_POPUP_CLOSE_LAST";
    case PageActionType.PAGE_ACTION_TYPE_POPUP_CLOSE_ALL:
      return "PAGE_ACTION_TYPE_POPUP_CLOSE_ALL";
    case PageActionType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 位操作模式 */
export enum BitSetMode {
  /** BIT_SET_MODE_UNSPECIFIED - 未设置 */
  BIT_SET_MODE_UNSPECIFIED = 0,
  /** BIT_SET_MODE_SET_ON - 设置ON按钮 */
  BIT_SET_MODE_SET_ON = 1,
  /** BIT_SET_MODE_SET_OFF - 设置OFF按钮 */
  BIT_SET_MODE_SET_OFF = 2,
  /** BIT_SET_MODE_TOGGLE - 切换按钮 */
  BIT_SET_MODE_TOGGLE = 3,
  /** BIT_SET_MODE_PRESS_ON_RELEASE_OFF - 按下时设ON，释放时设OFF */
  BIT_SET_MODE_PRESS_ON_RELEASE_OFF = 4,
  /** BIT_SET_MODE_PRESS_OFF_RELEASE_ON - 按下时设OFF，释放时设ON */
  BIT_SET_MODE_PRESS_OFF_RELEASE_ON = 5,
  /** BIT_SET_MODE_SET_ON_PULSE - 设置ON脉冲 */
  BIT_SET_MODE_SET_ON_PULSE = 6,
  /** BIT_SET_MODE_SET_OFF_PULSE - 设置OFF脉冲 */
  BIT_SET_MODE_SET_OFF_PULSE = 7,
  UNRECOGNIZED = -1,
}

export function bitSetModeFromJSON(object: any): BitSetMode {
  switch (object) {
    case 0:
    case "BIT_SET_MODE_UNSPECIFIED":
      return BitSetMode.BIT_SET_MODE_UNSPECIFIED;
    case 1:
    case "BIT_SET_MODE_SET_ON":
      return BitSetMode.BIT_SET_MODE_SET_ON;
    case 2:
    case "BIT_SET_MODE_SET_OFF":
      return BitSetMode.BIT_SET_MODE_SET_OFF;
    case 3:
    case "BIT_SET_MODE_TOGGLE":
      return BitSetMode.BIT_SET_MODE_TOGGLE;
    case 4:
    case "BIT_SET_MODE_PRESS_ON_RELEASE_OFF":
      return BitSetMode.BIT_SET_MODE_PRESS_ON_RELEASE_OFF;
    case 5:
    case "BIT_SET_MODE_PRESS_OFF_RELEASE_ON":
      return BitSetMode.BIT_SET_MODE_PRESS_OFF_RELEASE_ON;
    case 6:
    case "BIT_SET_MODE_SET_ON_PULSE":
      return BitSetMode.BIT_SET_MODE_SET_ON_PULSE;
    case 7:
    case "BIT_SET_MODE_SET_OFF_PULSE":
      return BitSetMode.BIT_SET_MODE_SET_OFF_PULSE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BitSetMode.UNRECOGNIZED;
  }
}

export function bitSetModeToJSON(object: BitSetMode): string {
  switch (object) {
    case BitSetMode.BIT_SET_MODE_UNSPECIFIED:
      return "BIT_SET_MODE_UNSPECIFIED";
    case BitSetMode.BIT_SET_MODE_SET_ON:
      return "BIT_SET_MODE_SET_ON";
    case BitSetMode.BIT_SET_MODE_SET_OFF:
      return "BIT_SET_MODE_SET_OFF";
    case BitSetMode.BIT_SET_MODE_TOGGLE:
      return "BIT_SET_MODE_TOGGLE";
    case BitSetMode.BIT_SET_MODE_PRESS_ON_RELEASE_OFF:
      return "BIT_SET_MODE_PRESS_ON_RELEASE_OFF";
    case BitSetMode.BIT_SET_MODE_PRESS_OFF_RELEASE_ON:
      return "BIT_SET_MODE_PRESS_OFF_RELEASE_ON";
    case BitSetMode.BIT_SET_MODE_SET_ON_PULSE:
      return "BIT_SET_MODE_SET_ON_PULSE";
    case BitSetMode.BIT_SET_MODE_SET_OFF_PULSE:
      return "BIT_SET_MODE_SET_OFF_PULSE";
    case BitSetMode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 系统动作类型 */
export enum SystemActionType {
  SYSTEM_ACTION_TYPE_UNSPECIFIED = 0,
  /** SYSTEM_ACTION_TYPE_OPEN_SETTING - 打开设置后台 */
  SYSTEM_ACTION_TYPE_OPEN_SETTING = 1,
  /** SYSTEM_ACTION_TYPE_REBOOT - 重启 */
  SYSTEM_ACTION_TYPE_REBOOT = 2,
  /** SYSTEM_ACTION_TYPE_CLEAR_BOM - 清空配方 */
  SYSTEM_ACTION_TYPE_CLEAR_BOM = 3,
  /** SYSTEM_ACTION_TYPE_CLEAR_HISTORY - 清空历史记录 */
  SYSTEM_ACTION_TYPE_CLEAR_HISTORY = 4,
  /** SYSTEM_ACTION_TYPE_CLEAR_ALARM - 清空报警 */
  SYSTEM_ACTION_TYPE_CLEAR_ALARM = 5,
  /** SYSTEM_ACTION_TYPE_CONFIRM_ALARM - 确认所有报警 */
  SYSTEM_ACTION_TYPE_CONFIRM_ALARM = 6,
  /** SYSTEM_ACTION_TYPE_SCREEN_LIGHT - 亮屏 */
  SYSTEM_ACTION_TYPE_SCREEN_LIGHT = 7,
  /** SYSTEM_ACTION_TYPE_SCREEN_SET_BRIGHTNESS - 设置亮度 */
  SYSTEM_ACTION_TYPE_SCREEN_SET_BRIGHTNESS = 8,
  /** SYSTEM_ACTION_TYPE_SCREEN_BLACK - 立即熄屏 */
  SYSTEM_ACTION_TYPE_SCREEN_BLACK = 9,
  /** SYSTEM_ACTION_TYPE_RESTORE_FACTORY - 恢复出厂设置 */
  SYSTEM_ACTION_TYPE_RESTORE_FACTORY = 10,
  /** SYSTEM_ACTION_TYPE_TOUCH_CALIBRATION - 触摸校准 */
  SYSTEM_ACTION_TYPE_TOUCH_CALIBRATION = 11,
  UNRECOGNIZED = -1,
}

export function systemActionTypeFromJSON(object: any): SystemActionType {
  switch (object) {
    case 0:
    case "SYSTEM_ACTION_TYPE_UNSPECIFIED":
      return SystemActionType.SYSTEM_ACTION_TYPE_UNSPECIFIED;
    case 1:
    case "SYSTEM_ACTION_TYPE_OPEN_SETTING":
      return SystemActionType.SYSTEM_ACTION_TYPE_OPEN_SETTING;
    case 2:
    case "SYSTEM_ACTION_TYPE_REBOOT":
      return SystemActionType.SYSTEM_ACTION_TYPE_REBOOT;
    case 3:
    case "SYSTEM_ACTION_TYPE_CLEAR_BOM":
      return SystemActionType.SYSTEM_ACTION_TYPE_CLEAR_BOM;
    case 4:
    case "SYSTEM_ACTION_TYPE_CLEAR_HISTORY":
      return SystemActionType.SYSTEM_ACTION_TYPE_CLEAR_HISTORY;
    case 5:
    case "SYSTEM_ACTION_TYPE_CLEAR_ALARM":
      return SystemActionType.SYSTEM_ACTION_TYPE_CLEAR_ALARM;
    case 6:
    case "SYSTEM_ACTION_TYPE_CONFIRM_ALARM":
      return SystemActionType.SYSTEM_ACTION_TYPE_CONFIRM_ALARM;
    case 7:
    case "SYSTEM_ACTION_TYPE_SCREEN_LIGHT":
      return SystemActionType.SYSTEM_ACTION_TYPE_SCREEN_LIGHT;
    case 8:
    case "SYSTEM_ACTION_TYPE_SCREEN_SET_BRIGHTNESS":
      return SystemActionType.SYSTEM_ACTION_TYPE_SCREEN_SET_BRIGHTNESS;
    case 9:
    case "SYSTEM_ACTION_TYPE_SCREEN_BLACK":
      return SystemActionType.SYSTEM_ACTION_TYPE_SCREEN_BLACK;
    case 10:
    case "SYSTEM_ACTION_TYPE_RESTORE_FACTORY":
      return SystemActionType.SYSTEM_ACTION_TYPE_RESTORE_FACTORY;
    case 11:
    case "SYSTEM_ACTION_TYPE_TOUCH_CALIBRATION":
      return SystemActionType.SYSTEM_ACTION_TYPE_TOUCH_CALIBRATION;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SystemActionType.UNRECOGNIZED;
  }
}

export function systemActionTypeToJSON(object: SystemActionType): string {
  switch (object) {
    case SystemActionType.SYSTEM_ACTION_TYPE_UNSPECIFIED:
      return "SYSTEM_ACTION_TYPE_UNSPECIFIED";
    case SystemActionType.SYSTEM_ACTION_TYPE_OPEN_SETTING:
      return "SYSTEM_ACTION_TYPE_OPEN_SETTING";
    case SystemActionType.SYSTEM_ACTION_TYPE_REBOOT:
      return "SYSTEM_ACTION_TYPE_REBOOT";
    case SystemActionType.SYSTEM_ACTION_TYPE_CLEAR_BOM:
      return "SYSTEM_ACTION_TYPE_CLEAR_BOM";
    case SystemActionType.SYSTEM_ACTION_TYPE_CLEAR_HISTORY:
      return "SYSTEM_ACTION_TYPE_CLEAR_HISTORY";
    case SystemActionType.SYSTEM_ACTION_TYPE_CLEAR_ALARM:
      return "SYSTEM_ACTION_TYPE_CLEAR_ALARM";
    case SystemActionType.SYSTEM_ACTION_TYPE_CONFIRM_ALARM:
      return "SYSTEM_ACTION_TYPE_CONFIRM_ALARM";
    case SystemActionType.SYSTEM_ACTION_TYPE_SCREEN_LIGHT:
      return "SYSTEM_ACTION_TYPE_SCREEN_LIGHT";
    case SystemActionType.SYSTEM_ACTION_TYPE_SCREEN_SET_BRIGHTNESS:
      return "SYSTEM_ACTION_TYPE_SCREEN_SET_BRIGHTNESS";
    case SystemActionType.SYSTEM_ACTION_TYPE_SCREEN_BLACK:
      return "SYSTEM_ACTION_TYPE_SCREEN_BLACK";
    case SystemActionType.SYSTEM_ACTION_TYPE_RESTORE_FACTORY:
      return "SYSTEM_ACTION_TYPE_RESTORE_FACTORY";
    case SystemActionType.SYSTEM_ACTION_TYPE_TOUCH_CALIBRATION:
      return "SYSTEM_ACTION_TYPE_TOUCH_CALIBRATION";
    case SystemActionType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum HttpMethod {
  HTTP_METHOD_UNSPECIFIED = 0,
  HTTP_METHOD_GET = 1,
  HTTP_METHOD_POST = 2,
  HTTP_METHOD_PUT = 3,
  HTTP_METHOD_DELETE = 4,
  UNRECOGNIZED = -1,
}

export function httpMethodFromJSON(object: any): HttpMethod {
  switch (object) {
    case 0:
    case "HTTP_METHOD_UNSPECIFIED":
      return HttpMethod.HTTP_METHOD_UNSPECIFIED;
    case 1:
    case "HTTP_METHOD_GET":
      return HttpMethod.HTTP_METHOD_GET;
    case 2:
    case "HTTP_METHOD_POST":
      return HttpMethod.HTTP_METHOD_POST;
    case 3:
    case "HTTP_METHOD_PUT":
      return HttpMethod.HTTP_METHOD_PUT;
    case 4:
    case "HTTP_METHOD_DELETE":
      return HttpMethod.HTTP_METHOD_DELETE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return HttpMethod.UNRECOGNIZED;
  }
}

export function httpMethodToJSON(object: HttpMethod): string {
  switch (object) {
    case HttpMethod.HTTP_METHOD_UNSPECIFIED:
      return "HTTP_METHOD_UNSPECIFIED";
    case HttpMethod.HTTP_METHOD_GET:
      return "HTTP_METHOD_GET";
    case HttpMethod.HTTP_METHOD_POST:
      return "HTTP_METHOD_POST";
    case HttpMethod.HTTP_METHOD_PUT:
      return "HTTP_METHOD_PUT";
    case HttpMethod.HTTP_METHOD_DELETE:
      return "HTTP_METHOD_DELETE";
    case HttpMethod.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum HttpContentType {
  HTTP_CONTENT_TYPE_UNSPECIFIED = 0,
  HTTP_CONTENT_TYPE_FORM_DATA = 1,
  HTTP_CONTENT_TYPE_URLENCODED = 2,
  HTTP_CONTENT_TYPE_BINARY = 3,
  HTTP_CONTENT_TYPE_MSG_PACK = 4,
  HTTP_CONTENT_TYPE_RAW = 5,
  UNRECOGNIZED = -1,
}

export function httpContentTypeFromJSON(object: any): HttpContentType {
  switch (object) {
    case 0:
    case "HTTP_CONTENT_TYPE_UNSPECIFIED":
      return HttpContentType.HTTP_CONTENT_TYPE_UNSPECIFIED;
    case 1:
    case "HTTP_CONTENT_TYPE_FORM_DATA":
      return HttpContentType.HTTP_CONTENT_TYPE_FORM_DATA;
    case 2:
    case "HTTP_CONTENT_TYPE_URLENCODED":
      return HttpContentType.HTTP_CONTENT_TYPE_URLENCODED;
    case 3:
    case "HTTP_CONTENT_TYPE_BINARY":
      return HttpContentType.HTTP_CONTENT_TYPE_BINARY;
    case 4:
    case "HTTP_CONTENT_TYPE_MSG_PACK":
      return HttpContentType.HTTP_CONTENT_TYPE_MSG_PACK;
    case 5:
    case "HTTP_CONTENT_TYPE_RAW":
      return HttpContentType.HTTP_CONTENT_TYPE_RAW;
    case -1:
    case "UNRECOGNIZED":
    default:
      return HttpContentType.UNRECOGNIZED;
  }
}

export function httpContentTypeToJSON(object: HttpContentType): string {
  switch (object) {
    case HttpContentType.HTTP_CONTENT_TYPE_UNSPECIFIED:
      return "HTTP_CONTENT_TYPE_UNSPECIFIED";
    case HttpContentType.HTTP_CONTENT_TYPE_FORM_DATA:
      return "HTTP_CONTENT_TYPE_FORM_DATA";
    case HttpContentType.HTTP_CONTENT_TYPE_URLENCODED:
      return "HTTP_CONTENT_TYPE_URLENCODED";
    case HttpContentType.HTTP_CONTENT_TYPE_BINARY:
      return "HTTP_CONTENT_TYPE_BINARY";
    case HttpContentType.HTTP_CONTENT_TYPE_MSG_PACK:
      return "HTTP_CONTENT_TYPE_MSG_PACK";
    case HttpContentType.HTTP_CONTENT_TYPE_RAW:
      return "HTTP_CONTENT_TYPE_RAW";
    case HttpContentType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 动作 */
export interface Action {
  /** 执行时机 */
  actionTiming: ActionTiming;
  /** 匹配id，客户端会传来matchId，如果进行对比（比如子元件的ID或者高级图元的子事件ID） */
  matchIdU8?:
    | number
    | undefined;
  /** 是否等待执行结果 */
  waitResult: boolean;
  /** 具体动作 */
  action?:
    | { $case: "page"; value: ActionPage }
    | { $case: "bitOpera"; value: ActionBitOpera }
    | { $case: "wordWriteValue"; value: ActionWordWriteValue }
    | { $case: "wordAddValue"; value: ActionWordAddValue }
    | { $case: "wordSubValue"; value: ActionWordSubValue }
    | { $case: "wordKeepAddValue"; value: ActionWordKeepAddValue }
    | { $case: "wordKeepSubValue"; value: ActionWordKeepSubValue }
    | { $case: "wordAutoAddSubValue"; value: ActionWordAutoAddSubValue }
    | { $case: "wordAddSubValue"; value: ActionWordAddSubValue }
    | { $case: "wordKeepAddSubValue"; value: ActionWordKeepAddSubValue }
    | { $case: "input"; value: ActionInput }
    | { $case: "script"; value: ActionScript }
    | { $case: "system"; value: ActionSystem }
    | { $case: "copyData"; value: ActionCopyDataParam }
    | { $case: "playSound"; value: ActionPlaySound }
    | { $case: "operationLog"; value: ActionOperationLog }
    | { $case: "pushMqtt"; value: ActionPushMqtt }
    | { $case: "http"; value: ActionHttp }
    | { $case: "delay"; value: ActionDelay }
    | undefined;
}

/** 输入内容 */
export interface ActionInput {
  /** 写入数据地址 */
  writeVariable: VariableReference[];
  /** 是否多地址写入，是则一个地址写入一个值，否则只把第1个地址当起始地址写入 */
  multiAddress: boolean;
}

/** 延时 */
export interface ActionDelay {
  /** 延时毫秒(范围：50-10000) */
  delayMillisecondsI16: DataReferenceUInt16 | undefined;
}

/** 操作记录 */
export interface ActionOperationLog {
  /** 记录的文本内容 */
  content?:
    | //
    /** 文本标签id */
    { $case: "tagIdU16"; value: number }
    | //
    /** 输入的文本 */
    { $case: "input"; value: TextTag }
    | undefined;
}

/** 窗口跳转动作 */
export interface ActionPage {
  /** 动作类型 */
  actionType: PageActionType;
  /** 指定要操作的窗口ID（有些类型不需要） */
  pageId?: DataReferenceUInt16 | undefined;
}

/** 执行脚本 */
export interface ActionScript {
  /** 执行程序ID */
  programIdI16: number;
}

/** 位操作,写入地址从active.proto中获取 */
export interface ActionBitOpera {
  /** 位写入模式 */
  bitWriteMode: BitSetMode;
  /** 写入地址 */
  writeVariable:
    | VariableReference
    | undefined;
  /** 脉冲宽度（几个100ms，理论最大255，实际限制最大15），仅在脉冲情况才有此参数 */
  pulseWidth?: DataReferenceUInt8 | undefined;
}

/** 字写值 */
export interface ActionWordWriteValue {
  /** 写入地址 */
  writeVariable:
    | VariableReference
    | undefined;
  /** 写入值(如果这里为空，表示前端会传过来值和类型用于写入) */
  writeValue?: DataReference | undefined;
}

/** 字加值 */
export interface ActionWordAddValue {
  /** 写入地址 */
  writeVariable:
    | VariableReference
    | undefined;
  /** 读取地址 */
  readVariable?:
    | VariableReference
    | undefined;
  /** 加值 */
  addValue:
    | DataReferenceNumber
    | undefined;
  /** 最大值 */
  maxValue: DataReferenceNumber | undefined;
}

/**
 * 字持续递加
 * 当按压时间超过触发时间时，以递加间隔递加值，松开停止
 */
export interface ActionWordKeepAddValue {
  /** 写入地址 */
  writeVariable:
    | VariableReference
    | undefined;
  /** 读取地址 */
  readVariable?:
    | VariableReference
    | undefined;
  /** 加值 */
  addValue:
    | DataReferenceNumber
    | undefined;
  /** 最大值 */
  maxValue:
    | DataReferenceNumber
    | undefined;
  /** 触发时间(单位100ms) */
  triggerTimeU8: number;
  /** 递加间隔(单位100ms) */
  addIntervalU8: number;
}

/** 字减值，写入地址从active.proto中获取 */
export interface ActionWordSubValue {
  /** 写入地址 */
  writeVariable:
    | VariableReference
    | undefined;
  /** 读取地址 */
  readVariable?:
    | VariableReference
    | undefined;
  /** 减值 */
  subValue:
    | DataReferenceNumber
    | undefined;
  /** 最小值 */
  minValue: DataReferenceNumber | undefined;
}

/**
 * 字持续递减
 * 当按压时间超过触发时间时，以递减间隔递减值，松开停止
 */
export interface ActionWordKeepSubValue {
  /** 写入地址 */
  writeVariable:
    | VariableReference
    | undefined;
  /** 读取地址 */
  readVariable?:
    | VariableReference
    | undefined;
  /** 减值 */
  subValue:
    | DataReferenceNumber
    | undefined;
  /** 最小值 */
  minValue:
    | DataReferenceNumber
    | undefined;
  /** 触发时间(单位100ms) */
  triggerTimeU8: number;
  /** 递减间隔(单位100ms) */
  subIntervalU8: number;
}

/** 字加减值,写入地址从active.proto中获取 */
export interface ActionWordAddSubValue {
  /** 加减值方式 */
  mode: ActionWordAddSubValue_Mode;
  /** 写入地址 */
  writeVariable:
    | VariableReference
    | undefined;
  /** 读取地址 */
  readVariable?:
    | VariableReference
    | undefined;
  /** 加减值 */
  addSubValue:
    | DataReferenceNumber
    | undefined;
  /** 最大值 */
  maxValue:
    | DataReferenceNumber
    | undefined;
  /** 最小值 */
  minValue: DataReferenceNumber | undefined;
}

/** 加减值方式 */
export enum ActionWordAddSubValue_Mode {
  /** MODE_UNSPECIFIED - 未设置 */
  MODE_UNSPECIFIED = 0,
  /** MODE_ADD_LOOP - 先加到最大值后回到最小值 */
  MODE_ADD_LOOP = 1,
  /** MODE_SUB_LOOP - 先减到最小值后回到最大值 */
  MODE_SUB_LOOP = 2,
  /** MODE_ADD_OSCILLATE - 先加，到限制后减，再到最小值后再加，循环往复 */
  MODE_ADD_OSCILLATE = 3,
  /** MODE_SUB_OSCILLATE - 先减，到限制后加，再到最大值后再减，循环往复 */
  MODE_SUB_OSCILLATE = 4,
  UNRECOGNIZED = -1,
}

export function actionWordAddSubValue_ModeFromJSON(object: any): ActionWordAddSubValue_Mode {
  switch (object) {
    case 0:
    case "MODE_UNSPECIFIED":
      return ActionWordAddSubValue_Mode.MODE_UNSPECIFIED;
    case 1:
    case "MODE_ADD_LOOP":
      return ActionWordAddSubValue_Mode.MODE_ADD_LOOP;
    case 2:
    case "MODE_SUB_LOOP":
      return ActionWordAddSubValue_Mode.MODE_SUB_LOOP;
    case 3:
    case "MODE_ADD_OSCILLATE":
      return ActionWordAddSubValue_Mode.MODE_ADD_OSCILLATE;
    case 4:
    case "MODE_SUB_OSCILLATE":
      return ActionWordAddSubValue_Mode.MODE_SUB_OSCILLATE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ActionWordAddSubValue_Mode.UNRECOGNIZED;
  }
}

export function actionWordAddSubValue_ModeToJSON(object: ActionWordAddSubValue_Mode): string {
  switch (object) {
    case ActionWordAddSubValue_Mode.MODE_UNSPECIFIED:
      return "MODE_UNSPECIFIED";
    case ActionWordAddSubValue_Mode.MODE_ADD_LOOP:
      return "MODE_ADD_LOOP";
    case ActionWordAddSubValue_Mode.MODE_SUB_LOOP:
      return "MODE_SUB_LOOP";
    case ActionWordAddSubValue_Mode.MODE_ADD_OSCILLATE:
      return "MODE_ADD_OSCILLATE";
    case ActionWordAddSubValue_Mode.MODE_SUB_OSCILLATE:
      return "MODE_SUB_OSCILLATE";
    case ActionWordAddSubValue_Mode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/**
 * 字持续加减值
 * 当按压时间超过触发时间时，以递减值递减值，松开停止
 */
export interface ActionWordKeepAddSubValue {
  /** 加减值方式 */
  mode: ActionWordKeepAddSubValue_Mode;
  /** 写入地址 */
  writeVariable:
    | VariableReference
    | undefined;
  /** 读取地址 */
  readVariable?:
    | VariableReference
    | undefined;
  /** 加减值 */
  addSubValue:
    | DataReferenceNumber
    | undefined;
  /** 最大值 */
  maxValue:
    | DataReferenceNumber
    | undefined;
  /** 最小值 */
  minValue:
    | DataReferenceNumber
    | undefined;
  /** 触发时间(单位100ms) */
  triggerTimeU8: number;
  /** 间隔时间(单位100ms) */
  intervalU8: number;
}

/** 加减值方式 */
export enum ActionWordKeepAddSubValue_Mode {
  /** MODE_UNSPECIFIED - 未设置 */
  MODE_UNSPECIFIED = 0,
  /** MODE_ADD_LOOP - 先加到最大值后回到最小值 */
  MODE_ADD_LOOP = 1,
  /** MODE_SUB_LOOP - 先减到最小值后回到最大值 */
  MODE_SUB_LOOP = 2,
  /** MODE_ADD_OSCILLATE - 先加，到限制后减，再到最小值后再加，循环往复 */
  MODE_ADD_OSCILLATE = 3,
  /** MODE_SUB_OSCILLATE - 先减，到限制后加，再到最大值后再减，循环往复 */
  MODE_SUB_OSCILLATE = 4,
  UNRECOGNIZED = -1,
}

export function actionWordKeepAddSubValue_ModeFromJSON(object: any): ActionWordKeepAddSubValue_Mode {
  switch (object) {
    case 0:
    case "MODE_UNSPECIFIED":
      return ActionWordKeepAddSubValue_Mode.MODE_UNSPECIFIED;
    case 1:
    case "MODE_ADD_LOOP":
      return ActionWordKeepAddSubValue_Mode.MODE_ADD_LOOP;
    case 2:
    case "MODE_SUB_LOOP":
      return ActionWordKeepAddSubValue_Mode.MODE_SUB_LOOP;
    case 3:
    case "MODE_ADD_OSCILLATE":
      return ActionWordKeepAddSubValue_Mode.MODE_ADD_OSCILLATE;
    case 4:
    case "MODE_SUB_OSCILLATE":
      return ActionWordKeepAddSubValue_Mode.MODE_SUB_OSCILLATE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ActionWordKeepAddSubValue_Mode.UNRECOGNIZED;
  }
}

export function actionWordKeepAddSubValue_ModeToJSON(object: ActionWordKeepAddSubValue_Mode): string {
  switch (object) {
    case ActionWordKeepAddSubValue_Mode.MODE_UNSPECIFIED:
      return "MODE_UNSPECIFIED";
    case ActionWordKeepAddSubValue_Mode.MODE_ADD_LOOP:
      return "MODE_ADD_LOOP";
    case ActionWordKeepAddSubValue_Mode.MODE_SUB_LOOP:
      return "MODE_SUB_LOOP";
    case ActionWordKeepAddSubValue_Mode.MODE_ADD_OSCILLATE:
      return "MODE_ADD_OSCILLATE";
    case ActionWordKeepAddSubValue_Mode.MODE_SUB_OSCILLATE:
      return "MODE_SUB_OSCILLATE";
    case ActionWordKeepAddSubValue_Mode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/**
 * 字自动加减值
 * 当按压时间超过触发时间时，以递减值递减值，松开停止
 */
export interface ActionWordAutoAddSubValue {
  /** 加减值方式 */
  mode: ActionWordAutoAddSubValue_Mode;
  /** 写入地址 */
  writeVariable:
    | VariableReference
    | undefined;
  /** 读取地址 */
  readVariable?:
    | VariableReference
    | undefined;
  /** 加减值 */
  addSubValue:
    | DataReferenceNumber
    | undefined;
  /** 最大值 */
  maxValue:
    | DataReferenceNumber
    | undefined;
  /** 最小值 */
  minValue:
    | DataReferenceNumber
    | undefined;
  /** 间隔时间(单位100ms) */
  intervalU8: number;
}

/** 加减值方式 */
export enum ActionWordAutoAddSubValue_Mode {
  /** MODE_UNSPECIFIED - 未设置 */
  MODE_UNSPECIFIED = 0,
  /** MODE_ADD_TO_MAX - 加到最大值 */
  MODE_ADD_TO_MAX = 1,
  /** MODE_SUB_TO_MIN - 减到最小值 */
  MODE_SUB_TO_MIN = 2,
  /** MODE_ADD_LOOP - 先加到最大值后回到最小值 */
  MODE_ADD_LOOP = 3,
  /** MODE_SUB_LOOP - 先减到最小值后回到最大值 */
  MODE_SUB_LOOP = 4,
  /** MODE_ADD_OSCILLATE - 先加，到限制后减，再到最小值后再加，循环往复 */
  MODE_ADD_OSCILLATE = 5,
  /** MODE_SUB_OSCILLATE - 先减，到限制后加，再到最大值后再减，循环往复 */
  MODE_SUB_OSCILLATE = 6,
  UNRECOGNIZED = -1,
}

export function actionWordAutoAddSubValue_ModeFromJSON(object: any): ActionWordAutoAddSubValue_Mode {
  switch (object) {
    case 0:
    case "MODE_UNSPECIFIED":
      return ActionWordAutoAddSubValue_Mode.MODE_UNSPECIFIED;
    case 1:
    case "MODE_ADD_TO_MAX":
      return ActionWordAutoAddSubValue_Mode.MODE_ADD_TO_MAX;
    case 2:
    case "MODE_SUB_TO_MIN":
      return ActionWordAutoAddSubValue_Mode.MODE_SUB_TO_MIN;
    case 3:
    case "MODE_ADD_LOOP":
      return ActionWordAutoAddSubValue_Mode.MODE_ADD_LOOP;
    case 4:
    case "MODE_SUB_LOOP":
      return ActionWordAutoAddSubValue_Mode.MODE_SUB_LOOP;
    case 5:
    case "MODE_ADD_OSCILLATE":
      return ActionWordAutoAddSubValue_Mode.MODE_ADD_OSCILLATE;
    case 6:
    case "MODE_SUB_OSCILLATE":
      return ActionWordAutoAddSubValue_Mode.MODE_SUB_OSCILLATE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ActionWordAutoAddSubValue_Mode.UNRECOGNIZED;
  }
}

export function actionWordAutoAddSubValue_ModeToJSON(object: ActionWordAutoAddSubValue_Mode): string {
  switch (object) {
    case ActionWordAutoAddSubValue_Mode.MODE_UNSPECIFIED:
      return "MODE_UNSPECIFIED";
    case ActionWordAutoAddSubValue_Mode.MODE_ADD_TO_MAX:
      return "MODE_ADD_TO_MAX";
    case ActionWordAutoAddSubValue_Mode.MODE_SUB_TO_MIN:
      return "MODE_SUB_TO_MIN";
    case ActionWordAutoAddSubValue_Mode.MODE_ADD_LOOP:
      return "MODE_ADD_LOOP";
    case ActionWordAutoAddSubValue_Mode.MODE_SUB_LOOP:
      return "MODE_SUB_LOOP";
    case ActionWordAutoAddSubValue_Mode.MODE_ADD_OSCILLATE:
      return "MODE_ADD_OSCILLATE";
    case ActionWordAutoAddSubValue_Mode.MODE_SUB_OSCILLATE:
      return "MODE_SUB_OSCILLATE";
    case ActionWordAutoAddSubValue_Mode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 系统动作 */
export interface ActionSystem {
  systemActionType: SystemActionType;
  /** 部分系统动作的值，比如屏幕亮度 */
  paramValue?: DataReferenceInt16 | undefined;
}

/** 复制数据参数（源地址和目标地址从active.proto中获取） */
export interface ActionCopyDataParam {
  /** 源起始地址 */
  sourceVariable:
    | VariableReference
    | undefined;
  /** 目标起始地址 */
  targetVariable:
    | VariableReference
    | undefined;
  /** 数据长度 */
  length: number;
  /** 间隔时间(单位100ms)，如果有值，则是自动间隔执行，如果无值，则只执行一次 */
  interval?:
    | DataReferenceUInt8
    | undefined;
  /** 是否执行 */
  isExecute?: DataReferenceBool | undefined;
}

/** 播放声音 */
export interface ActionPlaySound {
  /** 声音ID(0-蜂鸣器) */
  soundId:
    | DataReferenceUInt16
    | undefined;
  /** 持续时长(未设置表示播放到完毕) */
  duration?: DataReferenceUInt16 | undefined;
}

/** MQTT推送 */
export interface ActionPushMqtt {
  /** MQTT主题(支持插入地址或标签) */
  topic: string;
  /** MQTT消息(支持插入地址或标签) */
  message: string;
}

/** HTTP调用 */
export interface ActionHttp {
  /** HTTP方法 */
  httpMethod: HttpMethod;
  /** API地址(要加上HTTP服务器前缀，支持插入地址或标签) */
  api: string;
  /** HTTP头 */
  header: HttpKeyValue[];
  /** HTTP体 */
  body: HttpKeyValue[];
  /** BODY类型 */
  bodyType?: HttpContentType | undefined;
}

export interface HttpKeyValue {
  key: string;
  type: string;
  value: string;
}

function createBaseAction(): Action {
  return { actionTiming: 0, matchIdU8: undefined, waitResult: false, action: undefined };
}

export const Action: MessageFns<Action> = {
  encode(message: Action, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.actionTiming !== 0) {
      writer.uint32(8).int32(message.actionTiming);
    }
    if (message.matchIdU8 !== undefined) {
      writer.uint32(16).int32(message.matchIdU8);
    }
    if (message.waitResult !== false) {
      writer.uint32(24).bool(message.waitResult);
    }
    switch (message.action?.$case) {
      case "page":
        ActionPage.encode(message.action.value, writer.uint32(34).fork()).join();
        break;
      case "bitOpera":
        ActionBitOpera.encode(message.action.value, writer.uint32(42).fork()).join();
        break;
      case "wordWriteValue":
        ActionWordWriteValue.encode(message.action.value, writer.uint32(50).fork()).join();
        break;
      case "wordAddValue":
        ActionWordAddValue.encode(message.action.value, writer.uint32(58).fork()).join();
        break;
      case "wordSubValue":
        ActionWordSubValue.encode(message.action.value, writer.uint32(66).fork()).join();
        break;
      case "wordKeepAddValue":
        ActionWordKeepAddValue.encode(message.action.value, writer.uint32(74).fork()).join();
        break;
      case "wordKeepSubValue":
        ActionWordKeepSubValue.encode(message.action.value, writer.uint32(82).fork()).join();
        break;
      case "wordAutoAddSubValue":
        ActionWordAutoAddSubValue.encode(message.action.value, writer.uint32(90).fork()).join();
        break;
      case "wordAddSubValue":
        ActionWordAddSubValue.encode(message.action.value, writer.uint32(98).fork()).join();
        break;
      case "wordKeepAddSubValue":
        ActionWordKeepAddSubValue.encode(message.action.value, writer.uint32(106).fork()).join();
        break;
      case "input":
        ActionInput.encode(message.action.value, writer.uint32(114).fork()).join();
        break;
      case "script":
        ActionScript.encode(message.action.value, writer.uint32(130).fork()).join();
        break;
      case "system":
        ActionSystem.encode(message.action.value, writer.uint32(138).fork()).join();
        break;
      case "copyData":
        ActionCopyDataParam.encode(message.action.value, writer.uint32(146).fork()).join();
        break;
      case "playSound":
        ActionPlaySound.encode(message.action.value, writer.uint32(154).fork()).join();
        break;
      case "operationLog":
        ActionOperationLog.encode(message.action.value, writer.uint32(162).fork()).join();
        break;
      case "pushMqtt":
        ActionPushMqtt.encode(message.action.value, writer.uint32(170).fork()).join();
        break;
      case "http":
        ActionHttp.encode(message.action.value, writer.uint32(178).fork()).join();
        break;
      case "delay":
        ActionDelay.encode(message.action.value, writer.uint32(186).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Action {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.actionTiming = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.matchIdU8 = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.waitResult = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.action = { $case: "page", value: ActionPage.decode(reader, reader.uint32()) };
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.action = { $case: "bitOpera", value: ActionBitOpera.decode(reader, reader.uint32()) };
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.action = { $case: "wordWriteValue", value: ActionWordWriteValue.decode(reader, reader.uint32()) };
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.action = { $case: "wordAddValue", value: ActionWordAddValue.decode(reader, reader.uint32()) };
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.action = { $case: "wordSubValue", value: ActionWordSubValue.decode(reader, reader.uint32()) };
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.action = { $case: "wordKeepAddValue", value: ActionWordKeepAddValue.decode(reader, reader.uint32()) };
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.action = { $case: "wordKeepSubValue", value: ActionWordKeepSubValue.decode(reader, reader.uint32()) };
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.action = {
            $case: "wordAutoAddSubValue",
            value: ActionWordAutoAddSubValue.decode(reader, reader.uint32()),
          };
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.action = { $case: "wordAddSubValue", value: ActionWordAddSubValue.decode(reader, reader.uint32()) };
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.action = {
            $case: "wordKeepAddSubValue",
            value: ActionWordKeepAddSubValue.decode(reader, reader.uint32()),
          };
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.action = { $case: "input", value: ActionInput.decode(reader, reader.uint32()) };
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.action = { $case: "script", value: ActionScript.decode(reader, reader.uint32()) };
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.action = { $case: "system", value: ActionSystem.decode(reader, reader.uint32()) };
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.action = { $case: "copyData", value: ActionCopyDataParam.decode(reader, reader.uint32()) };
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.action = { $case: "playSound", value: ActionPlaySound.decode(reader, reader.uint32()) };
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.action = { $case: "operationLog", value: ActionOperationLog.decode(reader, reader.uint32()) };
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.action = { $case: "pushMqtt", value: ActionPushMqtt.decode(reader, reader.uint32()) };
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.action = { $case: "http", value: ActionHttp.decode(reader, reader.uint32()) };
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.action = { $case: "delay", value: ActionDelay.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Action {
    return {
      actionTiming: isSet(object.actionTiming) ? actionTimingFromJSON(object.actionTiming) : 0,
      matchIdU8: isSet(object.matchIdU8) ? globalThis.Number(object.matchIdU8) : undefined,
      waitResult: isSet(object.waitResult) ? globalThis.Boolean(object.waitResult) : false,
      action: isSet(object.page)
        ? { $case: "page", value: ActionPage.fromJSON(object.page) }
        : isSet(object.bitOpera)
        ? { $case: "bitOpera", value: ActionBitOpera.fromJSON(object.bitOpera) }
        : isSet(object.wordWriteValue)
        ? { $case: "wordWriteValue", value: ActionWordWriteValue.fromJSON(object.wordWriteValue) }
        : isSet(object.wordAddValue)
        ? { $case: "wordAddValue", value: ActionWordAddValue.fromJSON(object.wordAddValue) }
        : isSet(object.wordSubValue)
        ? { $case: "wordSubValue", value: ActionWordSubValue.fromJSON(object.wordSubValue) }
        : isSet(object.wordKeepAddValue)
        ? { $case: "wordKeepAddValue", value: ActionWordKeepAddValue.fromJSON(object.wordKeepAddValue) }
        : isSet(object.wordKeepSubValue)
        ? { $case: "wordKeepSubValue", value: ActionWordKeepSubValue.fromJSON(object.wordKeepSubValue) }
        : isSet(object.wordAutoAddSubValue)
        ? { $case: "wordAutoAddSubValue", value: ActionWordAutoAddSubValue.fromJSON(object.wordAutoAddSubValue) }
        : isSet(object.wordAddSubValue)
        ? { $case: "wordAddSubValue", value: ActionWordAddSubValue.fromJSON(object.wordAddSubValue) }
        : isSet(object.wordKeepAddSubValue)
        ? { $case: "wordKeepAddSubValue", value: ActionWordKeepAddSubValue.fromJSON(object.wordKeepAddSubValue) }
        : isSet(object.input)
        ? { $case: "input", value: ActionInput.fromJSON(object.input) }
        : isSet(object.script)
        ? { $case: "script", value: ActionScript.fromJSON(object.script) }
        : isSet(object.system)
        ? { $case: "system", value: ActionSystem.fromJSON(object.system) }
        : isSet(object.copyData)
        ? { $case: "copyData", value: ActionCopyDataParam.fromJSON(object.copyData) }
        : isSet(object.playSound)
        ? { $case: "playSound", value: ActionPlaySound.fromJSON(object.playSound) }
        : isSet(object.operationLog)
        ? { $case: "operationLog", value: ActionOperationLog.fromJSON(object.operationLog) }
        : isSet(object.pushMqtt)
        ? { $case: "pushMqtt", value: ActionPushMqtt.fromJSON(object.pushMqtt) }
        : isSet(object.http)
        ? { $case: "http", value: ActionHttp.fromJSON(object.http) }
        : isSet(object.delay)
        ? { $case: "delay", value: ActionDelay.fromJSON(object.delay) }
        : undefined,
    };
  },

  toJSON(message: Action): unknown {
    const obj: any = {};
    if (message.actionTiming !== 0) {
      obj.actionTiming = actionTimingToJSON(message.actionTiming);
    }
    if (message.matchIdU8 !== undefined) {
      obj.matchIdU8 = Math.round(message.matchIdU8);
    }
    if (message.waitResult !== false) {
      obj.waitResult = message.waitResult;
    }
    if (message.action?.$case === "page") {
      obj.page = ActionPage.toJSON(message.action.value);
    } else if (message.action?.$case === "bitOpera") {
      obj.bitOpera = ActionBitOpera.toJSON(message.action.value);
    } else if (message.action?.$case === "wordWriteValue") {
      obj.wordWriteValue = ActionWordWriteValue.toJSON(message.action.value);
    } else if (message.action?.$case === "wordAddValue") {
      obj.wordAddValue = ActionWordAddValue.toJSON(message.action.value);
    } else if (message.action?.$case === "wordSubValue") {
      obj.wordSubValue = ActionWordSubValue.toJSON(message.action.value);
    } else if (message.action?.$case === "wordKeepAddValue") {
      obj.wordKeepAddValue = ActionWordKeepAddValue.toJSON(message.action.value);
    } else if (message.action?.$case === "wordKeepSubValue") {
      obj.wordKeepSubValue = ActionWordKeepSubValue.toJSON(message.action.value);
    } else if (message.action?.$case === "wordAutoAddSubValue") {
      obj.wordAutoAddSubValue = ActionWordAutoAddSubValue.toJSON(message.action.value);
    } else if (message.action?.$case === "wordAddSubValue") {
      obj.wordAddSubValue = ActionWordAddSubValue.toJSON(message.action.value);
    } else if (message.action?.$case === "wordKeepAddSubValue") {
      obj.wordKeepAddSubValue = ActionWordKeepAddSubValue.toJSON(message.action.value);
    } else if (message.action?.$case === "input") {
      obj.input = ActionInput.toJSON(message.action.value);
    } else if (message.action?.$case === "script") {
      obj.script = ActionScript.toJSON(message.action.value);
    } else if (message.action?.$case === "system") {
      obj.system = ActionSystem.toJSON(message.action.value);
    } else if (message.action?.$case === "copyData") {
      obj.copyData = ActionCopyDataParam.toJSON(message.action.value);
    } else if (message.action?.$case === "playSound") {
      obj.playSound = ActionPlaySound.toJSON(message.action.value);
    } else if (message.action?.$case === "operationLog") {
      obj.operationLog = ActionOperationLog.toJSON(message.action.value);
    } else if (message.action?.$case === "pushMqtt") {
      obj.pushMqtt = ActionPushMqtt.toJSON(message.action.value);
    } else if (message.action?.$case === "http") {
      obj.http = ActionHttp.toJSON(message.action.value);
    } else if (message.action?.$case === "delay") {
      obj.delay = ActionDelay.toJSON(message.action.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Action>, I>>(base?: I): Action {
    return Action.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Action>, I>>(object: I): Action {
    const message = createBaseAction();
    message.actionTiming = object.actionTiming ?? 0;
    message.matchIdU8 = object.matchIdU8 ?? undefined;
    message.waitResult = object.waitResult ?? false;
    switch (object.action?.$case) {
      case "page": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "page", value: ActionPage.fromPartial(object.action.value) };
        }
        break;
      }
      case "bitOpera": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "bitOpera", value: ActionBitOpera.fromPartial(object.action.value) };
        }
        break;
      }
      case "wordWriteValue": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "wordWriteValue", value: ActionWordWriteValue.fromPartial(object.action.value) };
        }
        break;
      }
      case "wordAddValue": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "wordAddValue", value: ActionWordAddValue.fromPartial(object.action.value) };
        }
        break;
      }
      case "wordSubValue": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "wordSubValue", value: ActionWordSubValue.fromPartial(object.action.value) };
        }
        break;
      }
      case "wordKeepAddValue": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = {
            $case: "wordKeepAddValue",
            value: ActionWordKeepAddValue.fromPartial(object.action.value),
          };
        }
        break;
      }
      case "wordKeepSubValue": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = {
            $case: "wordKeepSubValue",
            value: ActionWordKeepSubValue.fromPartial(object.action.value),
          };
        }
        break;
      }
      case "wordAutoAddSubValue": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = {
            $case: "wordAutoAddSubValue",
            value: ActionWordAutoAddSubValue.fromPartial(object.action.value),
          };
        }
        break;
      }
      case "wordAddSubValue": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "wordAddSubValue", value: ActionWordAddSubValue.fromPartial(object.action.value) };
        }
        break;
      }
      case "wordKeepAddSubValue": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = {
            $case: "wordKeepAddSubValue",
            value: ActionWordKeepAddSubValue.fromPartial(object.action.value),
          };
        }
        break;
      }
      case "input": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "input", value: ActionInput.fromPartial(object.action.value) };
        }
        break;
      }
      case "script": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "script", value: ActionScript.fromPartial(object.action.value) };
        }
        break;
      }
      case "system": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "system", value: ActionSystem.fromPartial(object.action.value) };
        }
        break;
      }
      case "copyData": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "copyData", value: ActionCopyDataParam.fromPartial(object.action.value) };
        }
        break;
      }
      case "playSound": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "playSound", value: ActionPlaySound.fromPartial(object.action.value) };
        }
        break;
      }
      case "operationLog": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "operationLog", value: ActionOperationLog.fromPartial(object.action.value) };
        }
        break;
      }
      case "pushMqtt": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "pushMqtt", value: ActionPushMqtt.fromPartial(object.action.value) };
        }
        break;
      }
      case "http": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "http", value: ActionHttp.fromPartial(object.action.value) };
        }
        break;
      }
      case "delay": {
        if (object.action?.value !== undefined && object.action?.value !== null) {
          message.action = { $case: "delay", value: ActionDelay.fromPartial(object.action.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseActionInput(): ActionInput {
  return { writeVariable: [], multiAddress: false };
}

export const ActionInput: MessageFns<ActionInput> = {
  encode(message: ActionInput, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.writeVariable) {
      VariableReference.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.multiAddress !== false) {
      writer.uint32(16).bool(message.multiAddress);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionInput {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.writeVariable.push(VariableReference.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.multiAddress = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionInput {
    return {
      writeVariable: globalThis.Array.isArray(object?.writeVariable)
        ? object.writeVariable.map((e: any) => VariableReference.fromJSON(e))
        : [],
      multiAddress: isSet(object.multiAddress) ? globalThis.Boolean(object.multiAddress) : false,
    };
  },

  toJSON(message: ActionInput): unknown {
    const obj: any = {};
    if (message.writeVariable?.length) {
      obj.writeVariable = message.writeVariable.map((e) => VariableReference.toJSON(e));
    }
    if (message.multiAddress !== false) {
      obj.multiAddress = message.multiAddress;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionInput>, I>>(base?: I): ActionInput {
    return ActionInput.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionInput>, I>>(object: I): ActionInput {
    const message = createBaseActionInput();
    message.writeVariable = object.writeVariable?.map((e) => VariableReference.fromPartial(e)) || [];
    message.multiAddress = object.multiAddress ?? false;
    return message;
  },
};

function createBaseActionDelay(): ActionDelay {
  return { delayMillisecondsI16: undefined };
}

export const ActionDelay: MessageFns<ActionDelay> = {
  encode(message: ActionDelay, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.delayMillisecondsI16 !== undefined) {
      DataReferenceUInt16.encode(message.delayMillisecondsI16, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionDelay {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionDelay();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.delayMillisecondsI16 = DataReferenceUInt16.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionDelay {
    return {
      delayMillisecondsI16: isSet(object.delayMillisecondsI16)
        ? DataReferenceUInt16.fromJSON(object.delayMillisecondsI16)
        : undefined,
    };
  },

  toJSON(message: ActionDelay): unknown {
    const obj: any = {};
    if (message.delayMillisecondsI16 !== undefined) {
      obj.delayMillisecondsI16 = DataReferenceUInt16.toJSON(message.delayMillisecondsI16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionDelay>, I>>(base?: I): ActionDelay {
    return ActionDelay.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionDelay>, I>>(object: I): ActionDelay {
    const message = createBaseActionDelay();
    message.delayMillisecondsI16 = (object.delayMillisecondsI16 !== undefined && object.delayMillisecondsI16 !== null)
      ? DataReferenceUInt16.fromPartial(object.delayMillisecondsI16)
      : undefined;
    return message;
  },
};

function createBaseActionOperationLog(): ActionOperationLog {
  return { content: undefined };
}

export const ActionOperationLog: MessageFns<ActionOperationLog> = {
  encode(message: ActionOperationLog, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.content?.$case) {
      case "tagIdU16":
        writer.uint32(8).uint32(message.content.value);
        break;
      case "input":
        TextTag.encode(message.content.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionOperationLog {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionOperationLog();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.content = { $case: "tagIdU16", value: reader.uint32() };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.content = { $case: "input", value: TextTag.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionOperationLog {
    return {
      content: isSet(object.tagIdU16)
        ? { $case: "tagIdU16", value: globalThis.Number(object.tagIdU16) }
        : isSet(object.input)
        ? { $case: "input", value: TextTag.fromJSON(object.input) }
        : undefined,
    };
  },

  toJSON(message: ActionOperationLog): unknown {
    const obj: any = {};
    if (message.content?.$case === "tagIdU16") {
      obj.tagIdU16 = Math.round(message.content.value);
    } else if (message.content?.$case === "input") {
      obj.input = TextTag.toJSON(message.content.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionOperationLog>, I>>(base?: I): ActionOperationLog {
    return ActionOperationLog.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionOperationLog>, I>>(object: I): ActionOperationLog {
    const message = createBaseActionOperationLog();
    switch (object.content?.$case) {
      case "tagIdU16": {
        if (object.content?.value !== undefined && object.content?.value !== null) {
          message.content = { $case: "tagIdU16", value: object.content.value };
        }
        break;
      }
      case "input": {
        if (object.content?.value !== undefined && object.content?.value !== null) {
          message.content = { $case: "input", value: TextTag.fromPartial(object.content.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseActionPage(): ActionPage {
  return { actionType: 0, pageId: undefined };
}

export const ActionPage: MessageFns<ActionPage> = {
  encode(message: ActionPage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.actionType !== 0) {
      writer.uint32(8).int32(message.actionType);
    }
    if (message.pageId !== undefined) {
      DataReferenceUInt16.encode(message.pageId, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionPage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionPage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.actionType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pageId = DataReferenceUInt16.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionPage {
    return {
      actionType: isSet(object.actionType) ? pageActionTypeFromJSON(object.actionType) : 0,
      pageId: isSet(object.pageId) ? DataReferenceUInt16.fromJSON(object.pageId) : undefined,
    };
  },

  toJSON(message: ActionPage): unknown {
    const obj: any = {};
    if (message.actionType !== 0) {
      obj.actionType = pageActionTypeToJSON(message.actionType);
    }
    if (message.pageId !== undefined) {
      obj.pageId = DataReferenceUInt16.toJSON(message.pageId);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionPage>, I>>(base?: I): ActionPage {
    return ActionPage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionPage>, I>>(object: I): ActionPage {
    const message = createBaseActionPage();
    message.actionType = object.actionType ?? 0;
    message.pageId = (object.pageId !== undefined && object.pageId !== null)
      ? DataReferenceUInt16.fromPartial(object.pageId)
      : undefined;
    return message;
  },
};

function createBaseActionScript(): ActionScript {
  return { programIdI16: 0 };
}

export const ActionScript: MessageFns<ActionScript> = {
  encode(message: ActionScript, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.programIdI16 !== 0) {
      writer.uint32(8).int32(message.programIdI16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionScript {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionScript();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.programIdI16 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionScript {
    return { programIdI16: isSet(object.programIdI16) ? globalThis.Number(object.programIdI16) : 0 };
  },

  toJSON(message: ActionScript): unknown {
    const obj: any = {};
    if (message.programIdI16 !== 0) {
      obj.programIdI16 = Math.round(message.programIdI16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionScript>, I>>(base?: I): ActionScript {
    return ActionScript.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionScript>, I>>(object: I): ActionScript {
    const message = createBaseActionScript();
    message.programIdI16 = object.programIdI16 ?? 0;
    return message;
  },
};

function createBaseActionBitOpera(): ActionBitOpera {
  return { bitWriteMode: 0, writeVariable: undefined, pulseWidth: undefined };
}

export const ActionBitOpera: MessageFns<ActionBitOpera> = {
  encode(message: ActionBitOpera, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bitWriteMode !== 0) {
      writer.uint32(8).int32(message.bitWriteMode);
    }
    if (message.writeVariable !== undefined) {
      VariableReference.encode(message.writeVariable, writer.uint32(18).fork()).join();
    }
    if (message.pulseWidth !== undefined) {
      DataReferenceUInt8.encode(message.pulseWidth, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionBitOpera {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionBitOpera();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.bitWriteMode = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.writeVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pulseWidth = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionBitOpera {
    return {
      bitWriteMode: isSet(object.bitWriteMode) ? bitSetModeFromJSON(object.bitWriteMode) : 0,
      writeVariable: isSet(object.writeVariable) ? VariableReference.fromJSON(object.writeVariable) : undefined,
      pulseWidth: isSet(object.pulseWidth) ? DataReferenceUInt8.fromJSON(object.pulseWidth) : undefined,
    };
  },

  toJSON(message: ActionBitOpera): unknown {
    const obj: any = {};
    if (message.bitWriteMode !== 0) {
      obj.bitWriteMode = bitSetModeToJSON(message.bitWriteMode);
    }
    if (message.writeVariable !== undefined) {
      obj.writeVariable = VariableReference.toJSON(message.writeVariable);
    }
    if (message.pulseWidth !== undefined) {
      obj.pulseWidth = DataReferenceUInt8.toJSON(message.pulseWidth);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionBitOpera>, I>>(base?: I): ActionBitOpera {
    return ActionBitOpera.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionBitOpera>, I>>(object: I): ActionBitOpera {
    const message = createBaseActionBitOpera();
    message.bitWriteMode = object.bitWriteMode ?? 0;
    message.writeVariable = (object.writeVariable !== undefined && object.writeVariable !== null)
      ? VariableReference.fromPartial(object.writeVariable)
      : undefined;
    message.pulseWidth = (object.pulseWidth !== undefined && object.pulseWidth !== null)
      ? DataReferenceUInt8.fromPartial(object.pulseWidth)
      : undefined;
    return message;
  },
};

function createBaseActionWordWriteValue(): ActionWordWriteValue {
  return { writeVariable: undefined, writeValue: undefined };
}

export const ActionWordWriteValue: MessageFns<ActionWordWriteValue> = {
  encode(message: ActionWordWriteValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.writeVariable !== undefined) {
      VariableReference.encode(message.writeVariable, writer.uint32(10).fork()).join();
    }
    if (message.writeValue !== undefined) {
      DataReference.encode(message.writeValue, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionWordWriteValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionWordWriteValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.writeVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.writeValue = DataReference.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionWordWriteValue {
    return {
      writeVariable: isSet(object.writeVariable) ? VariableReference.fromJSON(object.writeVariable) : undefined,
      writeValue: isSet(object.writeValue) ? DataReference.fromJSON(object.writeValue) : undefined,
    };
  },

  toJSON(message: ActionWordWriteValue): unknown {
    const obj: any = {};
    if (message.writeVariable !== undefined) {
      obj.writeVariable = VariableReference.toJSON(message.writeVariable);
    }
    if (message.writeValue !== undefined) {
      obj.writeValue = DataReference.toJSON(message.writeValue);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionWordWriteValue>, I>>(base?: I): ActionWordWriteValue {
    return ActionWordWriteValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionWordWriteValue>, I>>(object: I): ActionWordWriteValue {
    const message = createBaseActionWordWriteValue();
    message.writeVariable = (object.writeVariable !== undefined && object.writeVariable !== null)
      ? VariableReference.fromPartial(object.writeVariable)
      : undefined;
    message.writeValue = (object.writeValue !== undefined && object.writeValue !== null)
      ? DataReference.fromPartial(object.writeValue)
      : undefined;
    return message;
  },
};

function createBaseActionWordAddValue(): ActionWordAddValue {
  return { writeVariable: undefined, readVariable: undefined, addValue: undefined, maxValue: undefined };
}

export const ActionWordAddValue: MessageFns<ActionWordAddValue> = {
  encode(message: ActionWordAddValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.writeVariable !== undefined) {
      VariableReference.encode(message.writeVariable, writer.uint32(10).fork()).join();
    }
    if (message.readVariable !== undefined) {
      VariableReference.encode(message.readVariable, writer.uint32(18).fork()).join();
    }
    if (message.addValue !== undefined) {
      DataReferenceNumber.encode(message.addValue, writer.uint32(26).fork()).join();
    }
    if (message.maxValue !== undefined) {
      DataReferenceNumber.encode(message.maxValue, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionWordAddValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionWordAddValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.writeVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.readVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.addValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.maxValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionWordAddValue {
    return {
      writeVariable: isSet(object.writeVariable) ? VariableReference.fromJSON(object.writeVariable) : undefined,
      readVariable: isSet(object.readVariable) ? VariableReference.fromJSON(object.readVariable) : undefined,
      addValue: isSet(object.addValue) ? DataReferenceNumber.fromJSON(object.addValue) : undefined,
      maxValue: isSet(object.maxValue) ? DataReferenceNumber.fromJSON(object.maxValue) : undefined,
    };
  },

  toJSON(message: ActionWordAddValue): unknown {
    const obj: any = {};
    if (message.writeVariable !== undefined) {
      obj.writeVariable = VariableReference.toJSON(message.writeVariable);
    }
    if (message.readVariable !== undefined) {
      obj.readVariable = VariableReference.toJSON(message.readVariable);
    }
    if (message.addValue !== undefined) {
      obj.addValue = DataReferenceNumber.toJSON(message.addValue);
    }
    if (message.maxValue !== undefined) {
      obj.maxValue = DataReferenceNumber.toJSON(message.maxValue);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionWordAddValue>, I>>(base?: I): ActionWordAddValue {
    return ActionWordAddValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionWordAddValue>, I>>(object: I): ActionWordAddValue {
    const message = createBaseActionWordAddValue();
    message.writeVariable = (object.writeVariable !== undefined && object.writeVariable !== null)
      ? VariableReference.fromPartial(object.writeVariable)
      : undefined;
    message.readVariable = (object.readVariable !== undefined && object.readVariable !== null)
      ? VariableReference.fromPartial(object.readVariable)
      : undefined;
    message.addValue = (object.addValue !== undefined && object.addValue !== null)
      ? DataReferenceNumber.fromPartial(object.addValue)
      : undefined;
    message.maxValue = (object.maxValue !== undefined && object.maxValue !== null)
      ? DataReferenceNumber.fromPartial(object.maxValue)
      : undefined;
    return message;
  },
};

function createBaseActionWordKeepAddValue(): ActionWordKeepAddValue {
  return {
    writeVariable: undefined,
    readVariable: undefined,
    addValue: undefined,
    maxValue: undefined,
    triggerTimeU8: 0,
    addIntervalU8: 0,
  };
}

export const ActionWordKeepAddValue: MessageFns<ActionWordKeepAddValue> = {
  encode(message: ActionWordKeepAddValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.writeVariable !== undefined) {
      VariableReference.encode(message.writeVariable, writer.uint32(10).fork()).join();
    }
    if (message.readVariable !== undefined) {
      VariableReference.encode(message.readVariable, writer.uint32(18).fork()).join();
    }
    if (message.addValue !== undefined) {
      DataReferenceNumber.encode(message.addValue, writer.uint32(26).fork()).join();
    }
    if (message.maxValue !== undefined) {
      DataReferenceNumber.encode(message.maxValue, writer.uint32(34).fork()).join();
    }
    if (message.triggerTimeU8 !== 0) {
      writer.uint32(40).int32(message.triggerTimeU8);
    }
    if (message.addIntervalU8 !== 0) {
      writer.uint32(48).int32(message.addIntervalU8);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionWordKeepAddValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionWordKeepAddValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.writeVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.readVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.addValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.maxValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.triggerTimeU8 = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.addIntervalU8 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionWordKeepAddValue {
    return {
      writeVariable: isSet(object.writeVariable) ? VariableReference.fromJSON(object.writeVariable) : undefined,
      readVariable: isSet(object.readVariable) ? VariableReference.fromJSON(object.readVariable) : undefined,
      addValue: isSet(object.addValue) ? DataReferenceNumber.fromJSON(object.addValue) : undefined,
      maxValue: isSet(object.maxValue) ? DataReferenceNumber.fromJSON(object.maxValue) : undefined,
      triggerTimeU8: isSet(object.triggerTimeU8) ? globalThis.Number(object.triggerTimeU8) : 0,
      addIntervalU8: isSet(object.addIntervalU8) ? globalThis.Number(object.addIntervalU8) : 0,
    };
  },

  toJSON(message: ActionWordKeepAddValue): unknown {
    const obj: any = {};
    if (message.writeVariable !== undefined) {
      obj.writeVariable = VariableReference.toJSON(message.writeVariable);
    }
    if (message.readVariable !== undefined) {
      obj.readVariable = VariableReference.toJSON(message.readVariable);
    }
    if (message.addValue !== undefined) {
      obj.addValue = DataReferenceNumber.toJSON(message.addValue);
    }
    if (message.maxValue !== undefined) {
      obj.maxValue = DataReferenceNumber.toJSON(message.maxValue);
    }
    if (message.triggerTimeU8 !== 0) {
      obj.triggerTimeU8 = Math.round(message.triggerTimeU8);
    }
    if (message.addIntervalU8 !== 0) {
      obj.addIntervalU8 = Math.round(message.addIntervalU8);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionWordKeepAddValue>, I>>(base?: I): ActionWordKeepAddValue {
    return ActionWordKeepAddValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionWordKeepAddValue>, I>>(object: I): ActionWordKeepAddValue {
    const message = createBaseActionWordKeepAddValue();
    message.writeVariable = (object.writeVariable !== undefined && object.writeVariable !== null)
      ? VariableReference.fromPartial(object.writeVariable)
      : undefined;
    message.readVariable = (object.readVariable !== undefined && object.readVariable !== null)
      ? VariableReference.fromPartial(object.readVariable)
      : undefined;
    message.addValue = (object.addValue !== undefined && object.addValue !== null)
      ? DataReferenceNumber.fromPartial(object.addValue)
      : undefined;
    message.maxValue = (object.maxValue !== undefined && object.maxValue !== null)
      ? DataReferenceNumber.fromPartial(object.maxValue)
      : undefined;
    message.triggerTimeU8 = object.triggerTimeU8 ?? 0;
    message.addIntervalU8 = object.addIntervalU8 ?? 0;
    return message;
  },
};

function createBaseActionWordSubValue(): ActionWordSubValue {
  return { writeVariable: undefined, readVariable: undefined, subValue: undefined, minValue: undefined };
}

export const ActionWordSubValue: MessageFns<ActionWordSubValue> = {
  encode(message: ActionWordSubValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.writeVariable !== undefined) {
      VariableReference.encode(message.writeVariable, writer.uint32(10).fork()).join();
    }
    if (message.readVariable !== undefined) {
      VariableReference.encode(message.readVariable, writer.uint32(18).fork()).join();
    }
    if (message.subValue !== undefined) {
      DataReferenceNumber.encode(message.subValue, writer.uint32(26).fork()).join();
    }
    if (message.minValue !== undefined) {
      DataReferenceNumber.encode(message.minValue, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionWordSubValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionWordSubValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.writeVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.readVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.minValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionWordSubValue {
    return {
      writeVariable: isSet(object.writeVariable) ? VariableReference.fromJSON(object.writeVariable) : undefined,
      readVariable: isSet(object.readVariable) ? VariableReference.fromJSON(object.readVariable) : undefined,
      subValue: isSet(object.subValue) ? DataReferenceNumber.fromJSON(object.subValue) : undefined,
      minValue: isSet(object.minValue) ? DataReferenceNumber.fromJSON(object.minValue) : undefined,
    };
  },

  toJSON(message: ActionWordSubValue): unknown {
    const obj: any = {};
    if (message.writeVariable !== undefined) {
      obj.writeVariable = VariableReference.toJSON(message.writeVariable);
    }
    if (message.readVariable !== undefined) {
      obj.readVariable = VariableReference.toJSON(message.readVariable);
    }
    if (message.subValue !== undefined) {
      obj.subValue = DataReferenceNumber.toJSON(message.subValue);
    }
    if (message.minValue !== undefined) {
      obj.minValue = DataReferenceNumber.toJSON(message.minValue);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionWordSubValue>, I>>(base?: I): ActionWordSubValue {
    return ActionWordSubValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionWordSubValue>, I>>(object: I): ActionWordSubValue {
    const message = createBaseActionWordSubValue();
    message.writeVariable = (object.writeVariable !== undefined && object.writeVariable !== null)
      ? VariableReference.fromPartial(object.writeVariable)
      : undefined;
    message.readVariable = (object.readVariable !== undefined && object.readVariable !== null)
      ? VariableReference.fromPartial(object.readVariable)
      : undefined;
    message.subValue = (object.subValue !== undefined && object.subValue !== null)
      ? DataReferenceNumber.fromPartial(object.subValue)
      : undefined;
    message.minValue = (object.minValue !== undefined && object.minValue !== null)
      ? DataReferenceNumber.fromPartial(object.minValue)
      : undefined;
    return message;
  },
};

function createBaseActionWordKeepSubValue(): ActionWordKeepSubValue {
  return {
    writeVariable: undefined,
    readVariable: undefined,
    subValue: undefined,
    minValue: undefined,
    triggerTimeU8: 0,
    subIntervalU8: 0,
  };
}

export const ActionWordKeepSubValue: MessageFns<ActionWordKeepSubValue> = {
  encode(message: ActionWordKeepSubValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.writeVariable !== undefined) {
      VariableReference.encode(message.writeVariable, writer.uint32(10).fork()).join();
    }
    if (message.readVariable !== undefined) {
      VariableReference.encode(message.readVariable, writer.uint32(18).fork()).join();
    }
    if (message.subValue !== undefined) {
      DataReferenceNumber.encode(message.subValue, writer.uint32(26).fork()).join();
    }
    if (message.minValue !== undefined) {
      DataReferenceNumber.encode(message.minValue, writer.uint32(34).fork()).join();
    }
    if (message.triggerTimeU8 !== 0) {
      writer.uint32(40).int32(message.triggerTimeU8);
    }
    if (message.subIntervalU8 !== 0) {
      writer.uint32(48).int32(message.subIntervalU8);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionWordKeepSubValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionWordKeepSubValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.writeVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.readVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.minValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.triggerTimeU8 = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.subIntervalU8 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionWordKeepSubValue {
    return {
      writeVariable: isSet(object.writeVariable) ? VariableReference.fromJSON(object.writeVariable) : undefined,
      readVariable: isSet(object.readVariable) ? VariableReference.fromJSON(object.readVariable) : undefined,
      subValue: isSet(object.subValue) ? DataReferenceNumber.fromJSON(object.subValue) : undefined,
      minValue: isSet(object.minValue) ? DataReferenceNumber.fromJSON(object.minValue) : undefined,
      triggerTimeU8: isSet(object.triggerTimeU8) ? globalThis.Number(object.triggerTimeU8) : 0,
      subIntervalU8: isSet(object.subIntervalU8) ? globalThis.Number(object.subIntervalU8) : 0,
    };
  },

  toJSON(message: ActionWordKeepSubValue): unknown {
    const obj: any = {};
    if (message.writeVariable !== undefined) {
      obj.writeVariable = VariableReference.toJSON(message.writeVariable);
    }
    if (message.readVariable !== undefined) {
      obj.readVariable = VariableReference.toJSON(message.readVariable);
    }
    if (message.subValue !== undefined) {
      obj.subValue = DataReferenceNumber.toJSON(message.subValue);
    }
    if (message.minValue !== undefined) {
      obj.minValue = DataReferenceNumber.toJSON(message.minValue);
    }
    if (message.triggerTimeU8 !== 0) {
      obj.triggerTimeU8 = Math.round(message.triggerTimeU8);
    }
    if (message.subIntervalU8 !== 0) {
      obj.subIntervalU8 = Math.round(message.subIntervalU8);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionWordKeepSubValue>, I>>(base?: I): ActionWordKeepSubValue {
    return ActionWordKeepSubValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionWordKeepSubValue>, I>>(object: I): ActionWordKeepSubValue {
    const message = createBaseActionWordKeepSubValue();
    message.writeVariable = (object.writeVariable !== undefined && object.writeVariable !== null)
      ? VariableReference.fromPartial(object.writeVariable)
      : undefined;
    message.readVariable = (object.readVariable !== undefined && object.readVariable !== null)
      ? VariableReference.fromPartial(object.readVariable)
      : undefined;
    message.subValue = (object.subValue !== undefined && object.subValue !== null)
      ? DataReferenceNumber.fromPartial(object.subValue)
      : undefined;
    message.minValue = (object.minValue !== undefined && object.minValue !== null)
      ? DataReferenceNumber.fromPartial(object.minValue)
      : undefined;
    message.triggerTimeU8 = object.triggerTimeU8 ?? 0;
    message.subIntervalU8 = object.subIntervalU8 ?? 0;
    return message;
  },
};

function createBaseActionWordAddSubValue(): ActionWordAddSubValue {
  return {
    mode: 0,
    writeVariable: undefined,
    readVariable: undefined,
    addSubValue: undefined,
    maxValue: undefined,
    minValue: undefined,
  };
}

export const ActionWordAddSubValue: MessageFns<ActionWordAddSubValue> = {
  encode(message: ActionWordAddSubValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.mode !== 0) {
      writer.uint32(8).int32(message.mode);
    }
    if (message.writeVariable !== undefined) {
      VariableReference.encode(message.writeVariable, writer.uint32(18).fork()).join();
    }
    if (message.readVariable !== undefined) {
      VariableReference.encode(message.readVariable, writer.uint32(26).fork()).join();
    }
    if (message.addSubValue !== undefined) {
      DataReferenceNumber.encode(message.addSubValue, writer.uint32(34).fork()).join();
    }
    if (message.maxValue !== undefined) {
      DataReferenceNumber.encode(message.maxValue, writer.uint32(42).fork()).join();
    }
    if (message.minValue !== undefined) {
      DataReferenceNumber.encode(message.minValue, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionWordAddSubValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionWordAddSubValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.mode = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.writeVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.readVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.addSubValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.maxValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.minValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionWordAddSubValue {
    return {
      mode: isSet(object.mode) ? actionWordAddSubValue_ModeFromJSON(object.mode) : 0,
      writeVariable: isSet(object.writeVariable) ? VariableReference.fromJSON(object.writeVariable) : undefined,
      readVariable: isSet(object.readVariable) ? VariableReference.fromJSON(object.readVariable) : undefined,
      addSubValue: isSet(object.addSubValue) ? DataReferenceNumber.fromJSON(object.addSubValue) : undefined,
      maxValue: isSet(object.maxValue) ? DataReferenceNumber.fromJSON(object.maxValue) : undefined,
      minValue: isSet(object.minValue) ? DataReferenceNumber.fromJSON(object.minValue) : undefined,
    };
  },

  toJSON(message: ActionWordAddSubValue): unknown {
    const obj: any = {};
    if (message.mode !== 0) {
      obj.mode = actionWordAddSubValue_ModeToJSON(message.mode);
    }
    if (message.writeVariable !== undefined) {
      obj.writeVariable = VariableReference.toJSON(message.writeVariable);
    }
    if (message.readVariable !== undefined) {
      obj.readVariable = VariableReference.toJSON(message.readVariable);
    }
    if (message.addSubValue !== undefined) {
      obj.addSubValue = DataReferenceNumber.toJSON(message.addSubValue);
    }
    if (message.maxValue !== undefined) {
      obj.maxValue = DataReferenceNumber.toJSON(message.maxValue);
    }
    if (message.minValue !== undefined) {
      obj.minValue = DataReferenceNumber.toJSON(message.minValue);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionWordAddSubValue>, I>>(base?: I): ActionWordAddSubValue {
    return ActionWordAddSubValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionWordAddSubValue>, I>>(object: I): ActionWordAddSubValue {
    const message = createBaseActionWordAddSubValue();
    message.mode = object.mode ?? 0;
    message.writeVariable = (object.writeVariable !== undefined && object.writeVariable !== null)
      ? VariableReference.fromPartial(object.writeVariable)
      : undefined;
    message.readVariable = (object.readVariable !== undefined && object.readVariable !== null)
      ? VariableReference.fromPartial(object.readVariable)
      : undefined;
    message.addSubValue = (object.addSubValue !== undefined && object.addSubValue !== null)
      ? DataReferenceNumber.fromPartial(object.addSubValue)
      : undefined;
    message.maxValue = (object.maxValue !== undefined && object.maxValue !== null)
      ? DataReferenceNumber.fromPartial(object.maxValue)
      : undefined;
    message.minValue = (object.minValue !== undefined && object.minValue !== null)
      ? DataReferenceNumber.fromPartial(object.minValue)
      : undefined;
    return message;
  },
};

function createBaseActionWordKeepAddSubValue(): ActionWordKeepAddSubValue {
  return {
    mode: 0,
    writeVariable: undefined,
    readVariable: undefined,
    addSubValue: undefined,
    maxValue: undefined,
    minValue: undefined,
    triggerTimeU8: 0,
    intervalU8: 0,
  };
}

export const ActionWordKeepAddSubValue: MessageFns<ActionWordKeepAddSubValue> = {
  encode(message: ActionWordKeepAddSubValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.mode !== 0) {
      writer.uint32(8).int32(message.mode);
    }
    if (message.writeVariable !== undefined) {
      VariableReference.encode(message.writeVariable, writer.uint32(18).fork()).join();
    }
    if (message.readVariable !== undefined) {
      VariableReference.encode(message.readVariable, writer.uint32(26).fork()).join();
    }
    if (message.addSubValue !== undefined) {
      DataReferenceNumber.encode(message.addSubValue, writer.uint32(34).fork()).join();
    }
    if (message.maxValue !== undefined) {
      DataReferenceNumber.encode(message.maxValue, writer.uint32(42).fork()).join();
    }
    if (message.minValue !== undefined) {
      DataReferenceNumber.encode(message.minValue, writer.uint32(50).fork()).join();
    }
    if (message.triggerTimeU8 !== 0) {
      writer.uint32(56).int32(message.triggerTimeU8);
    }
    if (message.intervalU8 !== 0) {
      writer.uint32(64).int32(message.intervalU8);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionWordKeepAddSubValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionWordKeepAddSubValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.mode = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.writeVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.readVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.addSubValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.maxValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.minValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.triggerTimeU8 = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.intervalU8 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionWordKeepAddSubValue {
    return {
      mode: isSet(object.mode) ? actionWordKeepAddSubValue_ModeFromJSON(object.mode) : 0,
      writeVariable: isSet(object.writeVariable) ? VariableReference.fromJSON(object.writeVariable) : undefined,
      readVariable: isSet(object.readVariable) ? VariableReference.fromJSON(object.readVariable) : undefined,
      addSubValue: isSet(object.addSubValue) ? DataReferenceNumber.fromJSON(object.addSubValue) : undefined,
      maxValue: isSet(object.maxValue) ? DataReferenceNumber.fromJSON(object.maxValue) : undefined,
      minValue: isSet(object.minValue) ? DataReferenceNumber.fromJSON(object.minValue) : undefined,
      triggerTimeU8: isSet(object.triggerTimeU8) ? globalThis.Number(object.triggerTimeU8) : 0,
      intervalU8: isSet(object.intervalU8) ? globalThis.Number(object.intervalU8) : 0,
    };
  },

  toJSON(message: ActionWordKeepAddSubValue): unknown {
    const obj: any = {};
    if (message.mode !== 0) {
      obj.mode = actionWordKeepAddSubValue_ModeToJSON(message.mode);
    }
    if (message.writeVariable !== undefined) {
      obj.writeVariable = VariableReference.toJSON(message.writeVariable);
    }
    if (message.readVariable !== undefined) {
      obj.readVariable = VariableReference.toJSON(message.readVariable);
    }
    if (message.addSubValue !== undefined) {
      obj.addSubValue = DataReferenceNumber.toJSON(message.addSubValue);
    }
    if (message.maxValue !== undefined) {
      obj.maxValue = DataReferenceNumber.toJSON(message.maxValue);
    }
    if (message.minValue !== undefined) {
      obj.minValue = DataReferenceNumber.toJSON(message.minValue);
    }
    if (message.triggerTimeU8 !== 0) {
      obj.triggerTimeU8 = Math.round(message.triggerTimeU8);
    }
    if (message.intervalU8 !== 0) {
      obj.intervalU8 = Math.round(message.intervalU8);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionWordKeepAddSubValue>, I>>(base?: I): ActionWordKeepAddSubValue {
    return ActionWordKeepAddSubValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionWordKeepAddSubValue>, I>>(object: I): ActionWordKeepAddSubValue {
    const message = createBaseActionWordKeepAddSubValue();
    message.mode = object.mode ?? 0;
    message.writeVariable = (object.writeVariable !== undefined && object.writeVariable !== null)
      ? VariableReference.fromPartial(object.writeVariable)
      : undefined;
    message.readVariable = (object.readVariable !== undefined && object.readVariable !== null)
      ? VariableReference.fromPartial(object.readVariable)
      : undefined;
    message.addSubValue = (object.addSubValue !== undefined && object.addSubValue !== null)
      ? DataReferenceNumber.fromPartial(object.addSubValue)
      : undefined;
    message.maxValue = (object.maxValue !== undefined && object.maxValue !== null)
      ? DataReferenceNumber.fromPartial(object.maxValue)
      : undefined;
    message.minValue = (object.minValue !== undefined && object.minValue !== null)
      ? DataReferenceNumber.fromPartial(object.minValue)
      : undefined;
    message.triggerTimeU8 = object.triggerTimeU8 ?? 0;
    message.intervalU8 = object.intervalU8 ?? 0;
    return message;
  },
};

function createBaseActionWordAutoAddSubValue(): ActionWordAutoAddSubValue {
  return {
    mode: 0,
    writeVariable: undefined,
    readVariable: undefined,
    addSubValue: undefined,
    maxValue: undefined,
    minValue: undefined,
    intervalU8: 0,
  };
}

export const ActionWordAutoAddSubValue: MessageFns<ActionWordAutoAddSubValue> = {
  encode(message: ActionWordAutoAddSubValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.mode !== 0) {
      writer.uint32(8).int32(message.mode);
    }
    if (message.writeVariable !== undefined) {
      VariableReference.encode(message.writeVariable, writer.uint32(18).fork()).join();
    }
    if (message.readVariable !== undefined) {
      VariableReference.encode(message.readVariable, writer.uint32(26).fork()).join();
    }
    if (message.addSubValue !== undefined) {
      DataReferenceNumber.encode(message.addSubValue, writer.uint32(34).fork()).join();
    }
    if (message.maxValue !== undefined) {
      DataReferenceNumber.encode(message.maxValue, writer.uint32(42).fork()).join();
    }
    if (message.minValue !== undefined) {
      DataReferenceNumber.encode(message.minValue, writer.uint32(50).fork()).join();
    }
    if (message.intervalU8 !== 0) {
      writer.uint32(56).int32(message.intervalU8);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionWordAutoAddSubValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionWordAutoAddSubValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.mode = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.writeVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.readVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.addSubValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.maxValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.minValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.intervalU8 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionWordAutoAddSubValue {
    return {
      mode: isSet(object.mode) ? actionWordAutoAddSubValue_ModeFromJSON(object.mode) : 0,
      writeVariable: isSet(object.writeVariable) ? VariableReference.fromJSON(object.writeVariable) : undefined,
      readVariable: isSet(object.readVariable) ? VariableReference.fromJSON(object.readVariable) : undefined,
      addSubValue: isSet(object.addSubValue) ? DataReferenceNumber.fromJSON(object.addSubValue) : undefined,
      maxValue: isSet(object.maxValue) ? DataReferenceNumber.fromJSON(object.maxValue) : undefined,
      minValue: isSet(object.minValue) ? DataReferenceNumber.fromJSON(object.minValue) : undefined,
      intervalU8: isSet(object.intervalU8) ? globalThis.Number(object.intervalU8) : 0,
    };
  },

  toJSON(message: ActionWordAutoAddSubValue): unknown {
    const obj: any = {};
    if (message.mode !== 0) {
      obj.mode = actionWordAutoAddSubValue_ModeToJSON(message.mode);
    }
    if (message.writeVariable !== undefined) {
      obj.writeVariable = VariableReference.toJSON(message.writeVariable);
    }
    if (message.readVariable !== undefined) {
      obj.readVariable = VariableReference.toJSON(message.readVariable);
    }
    if (message.addSubValue !== undefined) {
      obj.addSubValue = DataReferenceNumber.toJSON(message.addSubValue);
    }
    if (message.maxValue !== undefined) {
      obj.maxValue = DataReferenceNumber.toJSON(message.maxValue);
    }
    if (message.minValue !== undefined) {
      obj.minValue = DataReferenceNumber.toJSON(message.minValue);
    }
    if (message.intervalU8 !== 0) {
      obj.intervalU8 = Math.round(message.intervalU8);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionWordAutoAddSubValue>, I>>(base?: I): ActionWordAutoAddSubValue {
    return ActionWordAutoAddSubValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionWordAutoAddSubValue>, I>>(object: I): ActionWordAutoAddSubValue {
    const message = createBaseActionWordAutoAddSubValue();
    message.mode = object.mode ?? 0;
    message.writeVariable = (object.writeVariable !== undefined && object.writeVariable !== null)
      ? VariableReference.fromPartial(object.writeVariable)
      : undefined;
    message.readVariable = (object.readVariable !== undefined && object.readVariable !== null)
      ? VariableReference.fromPartial(object.readVariable)
      : undefined;
    message.addSubValue = (object.addSubValue !== undefined && object.addSubValue !== null)
      ? DataReferenceNumber.fromPartial(object.addSubValue)
      : undefined;
    message.maxValue = (object.maxValue !== undefined && object.maxValue !== null)
      ? DataReferenceNumber.fromPartial(object.maxValue)
      : undefined;
    message.minValue = (object.minValue !== undefined && object.minValue !== null)
      ? DataReferenceNumber.fromPartial(object.minValue)
      : undefined;
    message.intervalU8 = object.intervalU8 ?? 0;
    return message;
  },
};

function createBaseActionSystem(): ActionSystem {
  return { systemActionType: 0, paramValue: undefined };
}

export const ActionSystem: MessageFns<ActionSystem> = {
  encode(message: ActionSystem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.systemActionType !== 0) {
      writer.uint32(8).int32(message.systemActionType);
    }
    if (message.paramValue !== undefined) {
      DataReferenceInt16.encode(message.paramValue, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionSystem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionSystem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.systemActionType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.paramValue = DataReferenceInt16.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionSystem {
    return {
      systemActionType: isSet(object.systemActionType) ? systemActionTypeFromJSON(object.systemActionType) : 0,
      paramValue: isSet(object.paramValue) ? DataReferenceInt16.fromJSON(object.paramValue) : undefined,
    };
  },

  toJSON(message: ActionSystem): unknown {
    const obj: any = {};
    if (message.systemActionType !== 0) {
      obj.systemActionType = systemActionTypeToJSON(message.systemActionType);
    }
    if (message.paramValue !== undefined) {
      obj.paramValue = DataReferenceInt16.toJSON(message.paramValue);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionSystem>, I>>(base?: I): ActionSystem {
    return ActionSystem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionSystem>, I>>(object: I): ActionSystem {
    const message = createBaseActionSystem();
    message.systemActionType = object.systemActionType ?? 0;
    message.paramValue = (object.paramValue !== undefined && object.paramValue !== null)
      ? DataReferenceInt16.fromPartial(object.paramValue)
      : undefined;
    return message;
  },
};

function createBaseActionCopyDataParam(): ActionCopyDataParam {
  return { sourceVariable: undefined, targetVariable: undefined, length: 0, interval: undefined, isExecute: undefined };
}

export const ActionCopyDataParam: MessageFns<ActionCopyDataParam> = {
  encode(message: ActionCopyDataParam, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.sourceVariable !== undefined) {
      VariableReference.encode(message.sourceVariable, writer.uint32(10).fork()).join();
    }
    if (message.targetVariable !== undefined) {
      VariableReference.encode(message.targetVariable, writer.uint32(18).fork()).join();
    }
    if (message.length !== 0) {
      writer.uint32(24).int32(message.length);
    }
    if (message.interval !== undefined) {
      DataReferenceUInt8.encode(message.interval, writer.uint32(34).fork()).join();
    }
    if (message.isExecute !== undefined) {
      DataReferenceBool.encode(message.isExecute, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionCopyDataParam {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionCopyDataParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.sourceVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.targetVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.length = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.interval = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.isExecute = DataReferenceBool.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionCopyDataParam {
    return {
      sourceVariable: isSet(object.sourceVariable) ? VariableReference.fromJSON(object.sourceVariable) : undefined,
      targetVariable: isSet(object.targetVariable) ? VariableReference.fromJSON(object.targetVariable) : undefined,
      length: isSet(object.length) ? globalThis.Number(object.length) : 0,
      interval: isSet(object.interval) ? DataReferenceUInt8.fromJSON(object.interval) : undefined,
      isExecute: isSet(object.isExecute) ? DataReferenceBool.fromJSON(object.isExecute) : undefined,
    };
  },

  toJSON(message: ActionCopyDataParam): unknown {
    const obj: any = {};
    if (message.sourceVariable !== undefined) {
      obj.sourceVariable = VariableReference.toJSON(message.sourceVariable);
    }
    if (message.targetVariable !== undefined) {
      obj.targetVariable = VariableReference.toJSON(message.targetVariable);
    }
    if (message.length !== 0) {
      obj.length = Math.round(message.length);
    }
    if (message.interval !== undefined) {
      obj.interval = DataReferenceUInt8.toJSON(message.interval);
    }
    if (message.isExecute !== undefined) {
      obj.isExecute = DataReferenceBool.toJSON(message.isExecute);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionCopyDataParam>, I>>(base?: I): ActionCopyDataParam {
    return ActionCopyDataParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionCopyDataParam>, I>>(object: I): ActionCopyDataParam {
    const message = createBaseActionCopyDataParam();
    message.sourceVariable = (object.sourceVariable !== undefined && object.sourceVariable !== null)
      ? VariableReference.fromPartial(object.sourceVariable)
      : undefined;
    message.targetVariable = (object.targetVariable !== undefined && object.targetVariable !== null)
      ? VariableReference.fromPartial(object.targetVariable)
      : undefined;
    message.length = object.length ?? 0;
    message.interval = (object.interval !== undefined && object.interval !== null)
      ? DataReferenceUInt8.fromPartial(object.interval)
      : undefined;
    message.isExecute = (object.isExecute !== undefined && object.isExecute !== null)
      ? DataReferenceBool.fromPartial(object.isExecute)
      : undefined;
    return message;
  },
};

function createBaseActionPlaySound(): ActionPlaySound {
  return { soundId: undefined, duration: undefined };
}

export const ActionPlaySound: MessageFns<ActionPlaySound> = {
  encode(message: ActionPlaySound, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.soundId !== undefined) {
      DataReferenceUInt16.encode(message.soundId, writer.uint32(10).fork()).join();
    }
    if (message.duration !== undefined) {
      DataReferenceUInt16.encode(message.duration, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionPlaySound {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionPlaySound();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.soundId = DataReferenceUInt16.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.duration = DataReferenceUInt16.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionPlaySound {
    return {
      soundId: isSet(object.soundId) ? DataReferenceUInt16.fromJSON(object.soundId) : undefined,
      duration: isSet(object.duration) ? DataReferenceUInt16.fromJSON(object.duration) : undefined,
    };
  },

  toJSON(message: ActionPlaySound): unknown {
    const obj: any = {};
    if (message.soundId !== undefined) {
      obj.soundId = DataReferenceUInt16.toJSON(message.soundId);
    }
    if (message.duration !== undefined) {
      obj.duration = DataReferenceUInt16.toJSON(message.duration);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionPlaySound>, I>>(base?: I): ActionPlaySound {
    return ActionPlaySound.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionPlaySound>, I>>(object: I): ActionPlaySound {
    const message = createBaseActionPlaySound();
    message.soundId = (object.soundId !== undefined && object.soundId !== null)
      ? DataReferenceUInt16.fromPartial(object.soundId)
      : undefined;
    message.duration = (object.duration !== undefined && object.duration !== null)
      ? DataReferenceUInt16.fromPartial(object.duration)
      : undefined;
    return message;
  },
};

function createBaseActionPushMqtt(): ActionPushMqtt {
  return { topic: "", message: "" };
}

export const ActionPushMqtt: MessageFns<ActionPushMqtt> = {
  encode(message: ActionPushMqtt, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.topic !== "") {
      writer.uint32(10).string(message.topic);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionPushMqtt {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionPushMqtt();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.topic = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionPushMqtt {
    return {
      topic: isSet(object.topic) ? globalThis.String(object.topic) : "",
      message: isSet(object.message) ? globalThis.String(object.message) : "",
    };
  },

  toJSON(message: ActionPushMqtt): unknown {
    const obj: any = {};
    if (message.topic !== "") {
      obj.topic = message.topic;
    }
    if (message.message !== "") {
      obj.message = message.message;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionPushMqtt>, I>>(base?: I): ActionPushMqtt {
    return ActionPushMqtt.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionPushMqtt>, I>>(object: I): ActionPushMqtt {
    const message = createBaseActionPushMqtt();
    message.topic = object.topic ?? "";
    message.message = object.message ?? "";
    return message;
  },
};

function createBaseActionHttp(): ActionHttp {
  return { httpMethod: 0, api: "", header: [], body: [], bodyType: undefined };
}

export const ActionHttp: MessageFns<ActionHttp> = {
  encode(message: ActionHttp, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.httpMethod !== 0) {
      writer.uint32(8).int32(message.httpMethod);
    }
    if (message.api !== "") {
      writer.uint32(18).string(message.api);
    }
    for (const v of message.header) {
      HttpKeyValue.encode(v!, writer.uint32(26).fork()).join();
    }
    for (const v of message.body) {
      HttpKeyValue.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.bodyType !== undefined) {
      writer.uint32(40).int32(message.bodyType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionHttp {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionHttp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.httpMethod = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.api = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.header.push(HttpKeyValue.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.body.push(HttpKeyValue.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.bodyType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionHttp {
    return {
      httpMethod: isSet(object.httpMethod) ? httpMethodFromJSON(object.httpMethod) : 0,
      api: isSet(object.api) ? globalThis.String(object.api) : "",
      header: globalThis.Array.isArray(object?.header) ? object.header.map((e: any) => HttpKeyValue.fromJSON(e)) : [],
      body: globalThis.Array.isArray(object?.body) ? object.body.map((e: any) => HttpKeyValue.fromJSON(e)) : [],
      bodyType: isSet(object.bodyType) ? httpContentTypeFromJSON(object.bodyType) : undefined,
    };
  },

  toJSON(message: ActionHttp): unknown {
    const obj: any = {};
    if (message.httpMethod !== 0) {
      obj.httpMethod = httpMethodToJSON(message.httpMethod);
    }
    if (message.api !== "") {
      obj.api = message.api;
    }
    if (message.header?.length) {
      obj.header = message.header.map((e) => HttpKeyValue.toJSON(e));
    }
    if (message.body?.length) {
      obj.body = message.body.map((e) => HttpKeyValue.toJSON(e));
    }
    if (message.bodyType !== undefined) {
      obj.bodyType = httpContentTypeToJSON(message.bodyType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionHttp>, I>>(base?: I): ActionHttp {
    return ActionHttp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionHttp>, I>>(object: I): ActionHttp {
    const message = createBaseActionHttp();
    message.httpMethod = object.httpMethod ?? 0;
    message.api = object.api ?? "";
    message.header = object.header?.map((e) => HttpKeyValue.fromPartial(e)) || [];
    message.body = object.body?.map((e) => HttpKeyValue.fromPartial(e)) || [];
    message.bodyType = object.bodyType ?? undefined;
    return message;
  },
};

function createBaseHttpKeyValue(): HttpKeyValue {
  return { key: "", type: "", value: "" };
}

export const HttpKeyValue: MessageFns<HttpKeyValue> = {
  encode(message: HttpKeyValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.type !== "") {
      writer.uint32(18).string(message.type);
    }
    if (message.value !== "") {
      writer.uint32(26).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HttpKeyValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHttpKeyValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): HttpKeyValue {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: HttpKeyValue): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<HttpKeyValue>, I>>(base?: I): HttpKeyValue {
    return HttpKeyValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HttpKeyValue>, I>>(object: I): HttpKeyValue {
    const message = createBaseHttpKeyValue();
    message.key = object.key ?? "";
    message.type = object.type ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
