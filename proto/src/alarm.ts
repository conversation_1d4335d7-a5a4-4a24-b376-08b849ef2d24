// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/alarm.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Action } from "./action";
import { SaveOption } from "./common";
import { TextTag } from "./text";
import { VariableReference } from "./variable";

export const protobufPackage = "znd.project.v1";

/** 报警等级 */
export enum AlarmLevel {
  /** ALARM_LEVEL_UNSPECIFIED - 未定义 */
  ALARM_LEVEL_UNSPECIFIED = 0,
  /** ALARM_LEVEL_LOW - 低 */
  ALARM_LEVEL_LOW = 1,
  /** ALARM_LEVEL_MIDDLE - 中 */
  ALARM_LEVEL_MIDDLE = 2,
  /** ALARM_LEVEL_HIGH - 高 */
  ALARM_LEVEL_HIGH = 3,
  /** ALARM_LEVEL_EMERGENCY - 紧急 */
  ALARM_LEVEL_EMERGENCY = 4,
  UNRECOGNIZED = -1,
}

export function alarmLevelFromJSON(object: any): AlarmLevel {
  switch (object) {
    case 0:
    case "ALARM_LEVEL_UNSPECIFIED":
      return AlarmLevel.ALARM_LEVEL_UNSPECIFIED;
    case 1:
    case "ALARM_LEVEL_LOW":
      return AlarmLevel.ALARM_LEVEL_LOW;
    case 2:
    case "ALARM_LEVEL_MIDDLE":
      return AlarmLevel.ALARM_LEVEL_MIDDLE;
    case 3:
    case "ALARM_LEVEL_HIGH":
      return AlarmLevel.ALARM_LEVEL_HIGH;
    case 4:
    case "ALARM_LEVEL_EMERGENCY":
      return AlarmLevel.ALARM_LEVEL_EMERGENCY;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AlarmLevel.UNRECOGNIZED;
  }
}

export function alarmLevelToJSON(object: AlarmLevel): string {
  switch (object) {
    case AlarmLevel.ALARM_LEVEL_UNSPECIFIED:
      return "ALARM_LEVEL_UNSPECIFIED";
    case AlarmLevel.ALARM_LEVEL_LOW:
      return "ALARM_LEVEL_LOW";
    case AlarmLevel.ALARM_LEVEL_MIDDLE:
      return "ALARM_LEVEL_MIDDLE";
    case AlarmLevel.ALARM_LEVEL_HIGH:
      return "ALARM_LEVEL_HIGH";
    case AlarmLevel.ALARM_LEVEL_EMERGENCY:
      return "ALARM_LEVEL_EMERGENCY";
    case AlarmLevel.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 报警配置 */
export interface AlarmConfig {
  /** 报警组名称(下标表示组号，0-255，如果下标没到255的，显示默认名称GroupN) */
  alarmGroupName: string[];
  /** 报警项 */
  alarmItems: AlarmItem[];
  /** 报警保存选项（数组是表示多个位置存放） */
  saveOption: SaveOption[];
}

/** 报警 */
export interface AlarmItem {
  /** 报警id */
  id: number;
  /** 报警名称 */
  name: string;
  /** 报警组 */
  group: number;
  /** 报警等级 */
  level: AlarmLevel;
  /** 触发条件 */
  triggerConditions:
    | VariableReference
    | undefined;
  /** 根据条件是否启用(暂不实现) */
  enableConditions:
    | VariableReference
    | undefined;
  /** 报警文本 */
  alarmText?:
    | //
    /** 文本标签id */
    { $case: "tagIdU16"; value: number }
    | //
    /** 输入的文本 */
    { $case: "input"; value: TextTag }
    | undefined;
  /** 触发时动作 */
  triggerActions: Action[];
  /** 恢复时动作 */
  recoverActions: Action[];
  /** 确认时动作 */
  confirmActions: Action[];
  /** 内容前景色 */
  textColor?:
    | number
    | undefined;
  /** 内容背景色 */
  bgColor?:
    | number
    | undefined;
  /** 内容字体 */
  fontId?: number | undefined;
}

function createBaseAlarmConfig(): AlarmConfig {
  return { alarmGroupName: [], alarmItems: [], saveOption: [] };
}

export const AlarmConfig: MessageFns<AlarmConfig> = {
  encode(message: AlarmConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.alarmGroupName) {
      writer.uint32(10).string(v!);
    }
    for (const v of message.alarmItems) {
      AlarmItem.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.saveOption) {
      SaveOption.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AlarmConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAlarmConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.alarmGroupName.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.alarmItems.push(AlarmItem.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.saveOption.push(SaveOption.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AlarmConfig {
    return {
      alarmGroupName: globalThis.Array.isArray(object?.alarmGroupName)
        ? object.alarmGroupName.map((e: any) => globalThis.String(e))
        : [],
      alarmItems: globalThis.Array.isArray(object?.alarmItems)
        ? object.alarmItems.map((e: any) => AlarmItem.fromJSON(e))
        : [],
      saveOption: globalThis.Array.isArray(object?.saveOption)
        ? object.saveOption.map((e: any) => SaveOption.fromJSON(e))
        : [],
    };
  },

  toJSON(message: AlarmConfig): unknown {
    const obj: any = {};
    if (message.alarmGroupName?.length) {
      obj.alarmGroupName = message.alarmGroupName;
    }
    if (message.alarmItems?.length) {
      obj.alarmItems = message.alarmItems.map((e) => AlarmItem.toJSON(e));
    }
    if (message.saveOption?.length) {
      obj.saveOption = message.saveOption.map((e) => SaveOption.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AlarmConfig>, I>>(base?: I): AlarmConfig {
    return AlarmConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AlarmConfig>, I>>(object: I): AlarmConfig {
    const message = createBaseAlarmConfig();
    message.alarmGroupName = object.alarmGroupName?.map((e) => e) || [];
    message.alarmItems = object.alarmItems?.map((e) => AlarmItem.fromPartial(e)) || [];
    message.saveOption = object.saveOption?.map((e) => SaveOption.fromPartial(e)) || [];
    return message;
  },
};

function createBaseAlarmItem(): AlarmItem {
  return {
    id: 0,
    name: "",
    group: 0,
    level: 0,
    triggerConditions: undefined,
    enableConditions: undefined,
    alarmText: undefined,
    triggerActions: [],
    recoverActions: [],
    confirmActions: [],
    textColor: undefined,
    bgColor: undefined,
    fontId: undefined,
  };
}

export const AlarmItem: MessageFns<AlarmItem> = {
  encode(message: AlarmItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int32(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.group !== 0) {
      writer.uint32(24).int32(message.group);
    }
    if (message.level !== 0) {
      writer.uint32(32).int32(message.level);
    }
    if (message.triggerConditions !== undefined) {
      VariableReference.encode(message.triggerConditions, writer.uint32(42).fork()).join();
    }
    if (message.enableConditions !== undefined) {
      VariableReference.encode(message.enableConditions, writer.uint32(50).fork()).join();
    }
    switch (message.alarmText?.$case) {
      case "tagIdU16":
        writer.uint32(56).uint32(message.alarmText.value);
        break;
      case "input":
        TextTag.encode(message.alarmText.value, writer.uint32(66).fork()).join();
        break;
    }
    for (const v of message.triggerActions) {
      Action.encode(v!, writer.uint32(74).fork()).join();
    }
    for (const v of message.recoverActions) {
      Action.encode(v!, writer.uint32(82).fork()).join();
    }
    for (const v of message.confirmActions) {
      Action.encode(v!, writer.uint32(90).fork()).join();
    }
    if (message.textColor !== undefined) {
      writer.uint32(96).uint32(message.textColor);
    }
    if (message.bgColor !== undefined) {
      writer.uint32(104).uint32(message.bgColor);
    }
    if (message.fontId !== undefined) {
      writer.uint32(112).int32(message.fontId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AlarmItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAlarmItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.group = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.level = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.triggerConditions = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.enableConditions = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.alarmText = { $case: "tagIdU16", value: reader.uint32() };
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.alarmText = { $case: "input", value: TextTag.decode(reader, reader.uint32()) };
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.triggerActions.push(Action.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.recoverActions.push(Action.decode(reader, reader.uint32()));
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.confirmActions.push(Action.decode(reader, reader.uint32()));
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.textColor = reader.uint32();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.bgColor = reader.uint32();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.fontId = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AlarmItem {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      group: isSet(object.group) ? globalThis.Number(object.group) : 0,
      level: isSet(object.level) ? alarmLevelFromJSON(object.level) : 0,
      triggerConditions: isSet(object.triggerConditions)
        ? VariableReference.fromJSON(object.triggerConditions)
        : undefined,
      enableConditions: isSet(object.enableConditions)
        ? VariableReference.fromJSON(object.enableConditions)
        : undefined,
      alarmText: isSet(object.tagIdU16)
        ? { $case: "tagIdU16", value: globalThis.Number(object.tagIdU16) }
        : isSet(object.input)
        ? { $case: "input", value: TextTag.fromJSON(object.input) }
        : undefined,
      triggerActions: globalThis.Array.isArray(object?.triggerActions)
        ? object.triggerActions.map((e: any) => Action.fromJSON(e))
        : [],
      recoverActions: globalThis.Array.isArray(object?.recoverActions)
        ? object.recoverActions.map((e: any) => Action.fromJSON(e))
        : [],
      confirmActions: globalThis.Array.isArray(object?.confirmActions)
        ? object.confirmActions.map((e: any) => Action.fromJSON(e))
        : [],
      textColor: isSet(object.textColor) ? globalThis.Number(object.textColor) : undefined,
      bgColor: isSet(object.bgColor) ? globalThis.Number(object.bgColor) : undefined,
      fontId: isSet(object.fontId) ? globalThis.Number(object.fontId) : undefined,
    };
  },

  toJSON(message: AlarmItem): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.group !== 0) {
      obj.group = Math.round(message.group);
    }
    if (message.level !== 0) {
      obj.level = alarmLevelToJSON(message.level);
    }
    if (message.triggerConditions !== undefined) {
      obj.triggerConditions = VariableReference.toJSON(message.triggerConditions);
    }
    if (message.enableConditions !== undefined) {
      obj.enableConditions = VariableReference.toJSON(message.enableConditions);
    }
    if (message.alarmText?.$case === "tagIdU16") {
      obj.tagIdU16 = Math.round(message.alarmText.value);
    } else if (message.alarmText?.$case === "input") {
      obj.input = TextTag.toJSON(message.alarmText.value);
    }
    if (message.triggerActions?.length) {
      obj.triggerActions = message.triggerActions.map((e) => Action.toJSON(e));
    }
    if (message.recoverActions?.length) {
      obj.recoverActions = message.recoverActions.map((e) => Action.toJSON(e));
    }
    if (message.confirmActions?.length) {
      obj.confirmActions = message.confirmActions.map((e) => Action.toJSON(e));
    }
    if (message.textColor !== undefined) {
      obj.textColor = Math.round(message.textColor);
    }
    if (message.bgColor !== undefined) {
      obj.bgColor = Math.round(message.bgColor);
    }
    if (message.fontId !== undefined) {
      obj.fontId = Math.round(message.fontId);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AlarmItem>, I>>(base?: I): AlarmItem {
    return AlarmItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AlarmItem>, I>>(object: I): AlarmItem {
    const message = createBaseAlarmItem();
    message.id = object.id ?? 0;
    message.name = object.name ?? "";
    message.group = object.group ?? 0;
    message.level = object.level ?? 0;
    message.triggerConditions = (object.triggerConditions !== undefined && object.triggerConditions !== null)
      ? VariableReference.fromPartial(object.triggerConditions)
      : undefined;
    message.enableConditions = (object.enableConditions !== undefined && object.enableConditions !== null)
      ? VariableReference.fromPartial(object.enableConditions)
      : undefined;
    switch (object.alarmText?.$case) {
      case "tagIdU16": {
        if (object.alarmText?.value !== undefined && object.alarmText?.value !== null) {
          message.alarmText = { $case: "tagIdU16", value: object.alarmText.value };
        }
        break;
      }
      case "input": {
        if (object.alarmText?.value !== undefined && object.alarmText?.value !== null) {
          message.alarmText = { $case: "input", value: TextTag.fromPartial(object.alarmText.value) };
        }
        break;
      }
    }
    message.triggerActions = object.triggerActions?.map((e) => Action.fromPartial(e)) || [];
    message.recoverActions = object.recoverActions?.map((e) => Action.fromPartial(e)) || [];
    message.confirmActions = object.confirmActions?.map((e) => Action.fromPartial(e)) || [];
    message.textColor = object.textColor ?? undefined;
    message.bgColor = object.bgColor ?? undefined;
    message.fontId = object.fontId ?? undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
