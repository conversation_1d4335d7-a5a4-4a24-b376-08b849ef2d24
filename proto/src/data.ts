// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/data.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { DataTransform, VariableReference, VariableTrigger } from "./variable";

export const protobufPackage = "znd.project.v1";

/** 存储包括数据采样、报警、配方、操作记录、数据库等配置 */
export interface DataConfig {
  /** key为采样ID，用u16 */
  dataSamplings: { [key: number]: DataSampling };
}

export interface DataConfig_DataSamplingsEntry {
  key: number;
  value: DataSampling | undefined;
}

/** 数据采样 */
export interface DataSampling {
  /** 标识 */
  key: string;
  /** 名称 */
  name: string;
  /** 采集方式 */
  samplingType?:
    | //
    /** 触发采样 */
    { $case: "variableTrigger"; value: VariableTrigger }
    | //
    /** 定时采样 */
    { $case: "interval"; value: DataSampling_Interval }
    | undefined;
  /** key为字段ID，U8类型（数据库字段名用这个ID） */
  samplingFields: { [key: number]: DataSampling_Field };
  /** 暂停控制 */
  pauseControl?:
    | VariableReference
    | undefined;
  /** 清除控制（为1时清除内存中的记录） */
  clearControl?:
    | VariableReference
    | undefined;
  /** 是否优先采样(性能优先，占用显示性能) */
  prioritySampling: boolean;
  /** 保存参数，支持同时保存到多个地方 */
  saveParams: DataSampling_SaveParam[];
  /** 需要通知的元件(包括趋势图、数据表) */
  notifyWidgets: DataSamplingWidgetReference[];
}

export enum DataSampling_FieldFormat {
  FIELD_FORMAT_UNSPECIFIED = 0,
  /** FIELD_FORMAT_AUTO - 自动（SQLITE的NUMERIC格式） */
  FIELD_FORMAT_AUTO = 1,
  /** FIELD_FORMAT_INT - 整数 */
  FIELD_FORMAT_INT = 2,
  /** FIELD_FORMAT_REAL - 实数 */
  FIELD_FORMAT_REAL = 3,
  /** FIELD_FORMAT_TEXT - 文本 */
  FIELD_FORMAT_TEXT = 4,
  UNRECOGNIZED = -1,
}

export function dataSampling_FieldFormatFromJSON(object: any): DataSampling_FieldFormat {
  switch (object) {
    case 0:
    case "FIELD_FORMAT_UNSPECIFIED":
      return DataSampling_FieldFormat.FIELD_FORMAT_UNSPECIFIED;
    case 1:
    case "FIELD_FORMAT_AUTO":
      return DataSampling_FieldFormat.FIELD_FORMAT_AUTO;
    case 2:
    case "FIELD_FORMAT_INT":
      return DataSampling_FieldFormat.FIELD_FORMAT_INT;
    case 3:
    case "FIELD_FORMAT_REAL":
      return DataSampling_FieldFormat.FIELD_FORMAT_REAL;
    case 4:
    case "FIELD_FORMAT_TEXT":
      return DataSampling_FieldFormat.FIELD_FORMAT_TEXT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DataSampling_FieldFormat.UNRECOGNIZED;
  }
}

export function dataSampling_FieldFormatToJSON(object: DataSampling_FieldFormat): string {
  switch (object) {
    case DataSampling_FieldFormat.FIELD_FORMAT_UNSPECIFIED:
      return "FIELD_FORMAT_UNSPECIFIED";
    case DataSampling_FieldFormat.FIELD_FORMAT_AUTO:
      return "FIELD_FORMAT_AUTO";
    case DataSampling_FieldFormat.FIELD_FORMAT_INT:
      return "FIELD_FORMAT_INT";
    case DataSampling_FieldFormat.FIELD_FORMAT_REAL:
      return "FIELD_FORMAT_REAL";
    case DataSampling_FieldFormat.FIELD_FORMAT_TEXT:
      return "FIELD_FORMAT_TEXT";
    case DataSampling_FieldFormat.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 定时采样 */
export interface DataSampling_Interval {
  /** 时间间隔（单位100毫秒） */
  timeIntervalU16: number;
  /**
   * 是否准点(意思是，除了启动时的第一次采样外，其他尽量在整点和整分采样)
   * 打勾这个的时候，设计端会确保间隔时间符合准点要求
   */
  isOnTime: boolean;
}

/** 采样字段，也就是数据库字段 */
export interface DataSampling_Field {
  /** 字段标识（用于匹配数据库字段或者MQTT字段或API字段） */
  key: string;
  /** 字段名称 */
  name: string;
  /** 存储数据格式 */
  fieldFormat: DataSampling_FieldFormat;
  /** 字段变量 */
  variable:
    | VariableReference
    | undefined;
  /** 描述 */
  memo: string;
  /** 数据转换(为什么加在这，两个原因，一是方便不建变量使用，二是为了差值换算，如果变量自身有转换，为了性能和逻辑清晰，设计端会禁用这边转换) */
  dataTransform: DataTransform[];
}

export interface DataSampling_SamplingFieldsEntry {
  key: number;
  value: DataSampling_Field | undefined;
}

/** 保存参数 */
export interface DataSampling_SaveParam {
  /**
   * 保存位置：{yyyy}/{MM}/{dd}/{hh}/{mm}/{ss}
   * 1. 内部：hmi:path
   * 2. U盘：usb:path
   */
  savePath: string;
  /** 保存时长（天），超出表示保存不下 */
  saveDaysU8: number;
  /** 最大保存条数，超出表示保存不下 */
  maxSaveCountU32: number;
  /** 最大文件大小（MB）,超出则分隔为新文件 */
  maxFileSizeU32: number;
  /** 最大存储大小，超出表示保存不下 */
  maxStorageSizeU32: number;
  /** 最小剩余空间（MB），小于这个表示保存不下 */
  minFreeSpaceU32: number;
  /** 保存不下是否丢弃新数据，默认为否，也就是保存不下时，适当删除旧数据 */
  isLimitDropNew: boolean;
  /** 是否用csv格式保存，否则就是sqlite */
  isCsv: boolean;
  /** 清除控制变量 */
  clearControl?: VariableReference | undefined;
}

/** 数据采样需要通知的元件 */
export interface DataSamplingWidgetReference {
  /** 页面ID */
  pageIdU16: number;
  /** 元件ID */
  widgetIdU16: number;
  /** 通知哪些数据(如果是空数组，则按采样到的数据数组直接推，如果非空值，则本数组值是采样数据的下标，只把这些下标的数组按顺序组成数组推) */
  notifyVariableIndexU8: number[];
}

function createBaseDataConfig(): DataConfig {
  return { dataSamplings: {} };
}

export const DataConfig: MessageFns<DataConfig> = {
  encode(message: DataConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.dataSamplings).forEach(([key, value]) => {
      DataConfig_DataSamplingsEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = DataConfig_DataSamplingsEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.dataSamplings[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataConfig {
    return {
      dataSamplings: isObject(object.dataSamplings)
        ? Object.entries(object.dataSamplings).reduce<{ [key: number]: DataSampling }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = DataSampling.fromJSON(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: DataConfig): unknown {
    const obj: any = {};
    if (message.dataSamplings) {
      const entries = Object.entries(message.dataSamplings);
      if (entries.length > 0) {
        obj.dataSamplings = {};
        entries.forEach(([k, v]) => {
          obj.dataSamplings[k] = DataSampling.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataConfig>, I>>(base?: I): DataConfig {
    return DataConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataConfig>, I>>(object: I): DataConfig {
    const message = createBaseDataConfig();
    message.dataSamplings = Object.entries(object.dataSamplings ?? {}).reduce<{ [key: number]: DataSampling }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = DataSampling.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseDataConfig_DataSamplingsEntry(): DataConfig_DataSamplingsEntry {
  return { key: 0, value: undefined };
}

export const DataConfig_DataSamplingsEntry: MessageFns<DataConfig_DataSamplingsEntry> = {
  encode(message: DataConfig_DataSamplingsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      DataSampling.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataConfig_DataSamplingsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataConfig_DataSamplingsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = DataSampling.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataConfig_DataSamplingsEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? DataSampling.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: DataConfig_DataSamplingsEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = DataSampling.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataConfig_DataSamplingsEntry>, I>>(base?: I): DataConfig_DataSamplingsEntry {
    return DataConfig_DataSamplingsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataConfig_DataSamplingsEntry>, I>>(
    object: I,
  ): DataConfig_DataSamplingsEntry {
    const message = createBaseDataConfig_DataSamplingsEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? DataSampling.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseDataSampling(): DataSampling {
  return {
    key: "",
    name: "",
    samplingType: undefined,
    samplingFields: {},
    pauseControl: undefined,
    clearControl: undefined,
    prioritySampling: false,
    saveParams: [],
    notifyWidgets: [],
  };
}

export const DataSampling: MessageFns<DataSampling> = {
  encode(message: DataSampling, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    switch (message.samplingType?.$case) {
      case "variableTrigger":
        VariableTrigger.encode(message.samplingType.value, writer.uint32(26).fork()).join();
        break;
      case "interval":
        DataSampling_Interval.encode(message.samplingType.value, writer.uint32(34).fork()).join();
        break;
    }
    Object.entries(message.samplingFields).forEach(([key, value]) => {
      DataSampling_SamplingFieldsEntry.encode({ key: key as any, value }, writer.uint32(50).fork()).join();
    });
    if (message.pauseControl !== undefined) {
      VariableReference.encode(message.pauseControl, writer.uint32(58).fork()).join();
    }
    if (message.clearControl !== undefined) {
      VariableReference.encode(message.clearControl, writer.uint32(66).fork()).join();
    }
    if (message.prioritySampling !== false) {
      writer.uint32(72).bool(message.prioritySampling);
    }
    for (const v of message.saveParams) {
      DataSampling_SaveParam.encode(v!, writer.uint32(82).fork()).join();
    }
    for (const v of message.notifyWidgets) {
      DataSamplingWidgetReference.encode(v!, writer.uint32(90).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataSampling {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataSampling();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.samplingType = { $case: "variableTrigger", value: VariableTrigger.decode(reader, reader.uint32()) };
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.samplingType = { $case: "interval", value: DataSampling_Interval.decode(reader, reader.uint32()) };
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          const entry6 = DataSampling_SamplingFieldsEntry.decode(reader, reader.uint32());
          if (entry6.value !== undefined) {
            message.samplingFields[entry6.key] = entry6.value;
          }
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.pauseControl = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.clearControl = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.prioritySampling = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.saveParams.push(DataSampling_SaveParam.decode(reader, reader.uint32()));
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.notifyWidgets.push(DataSamplingWidgetReference.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataSampling {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      samplingType: isSet(object.variableTrigger)
        ? { $case: "variableTrigger", value: VariableTrigger.fromJSON(object.variableTrigger) }
        : isSet(object.interval)
        ? { $case: "interval", value: DataSampling_Interval.fromJSON(object.interval) }
        : undefined,
      samplingFields: isObject(object.samplingFields)
        ? Object.entries(object.samplingFields).reduce<{ [key: number]: DataSampling_Field }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = DataSampling_Field.fromJSON(value);
          return acc;
        }, {})
        : {},
      pauseControl: isSet(object.pauseControl) ? VariableReference.fromJSON(object.pauseControl) : undefined,
      clearControl: isSet(object.clearControl) ? VariableReference.fromJSON(object.clearControl) : undefined,
      prioritySampling: isSet(object.prioritySampling) ? globalThis.Boolean(object.prioritySampling) : false,
      saveParams: globalThis.Array.isArray(object?.saveParams)
        ? object.saveParams.map((e: any) => DataSampling_SaveParam.fromJSON(e))
        : [],
      notifyWidgets: globalThis.Array.isArray(object?.notifyWidgets)
        ? object.notifyWidgets.map((e: any) => DataSamplingWidgetReference.fromJSON(e))
        : [],
    };
  },

  toJSON(message: DataSampling): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.samplingType?.$case === "variableTrigger") {
      obj.variableTrigger = VariableTrigger.toJSON(message.samplingType.value);
    } else if (message.samplingType?.$case === "interval") {
      obj.interval = DataSampling_Interval.toJSON(message.samplingType.value);
    }
    if (message.samplingFields) {
      const entries = Object.entries(message.samplingFields);
      if (entries.length > 0) {
        obj.samplingFields = {};
        entries.forEach(([k, v]) => {
          obj.samplingFields[k] = DataSampling_Field.toJSON(v);
        });
      }
    }
    if (message.pauseControl !== undefined) {
      obj.pauseControl = VariableReference.toJSON(message.pauseControl);
    }
    if (message.clearControl !== undefined) {
      obj.clearControl = VariableReference.toJSON(message.clearControl);
    }
    if (message.prioritySampling !== false) {
      obj.prioritySampling = message.prioritySampling;
    }
    if (message.saveParams?.length) {
      obj.saveParams = message.saveParams.map((e) => DataSampling_SaveParam.toJSON(e));
    }
    if (message.notifyWidgets?.length) {
      obj.notifyWidgets = message.notifyWidgets.map((e) => DataSamplingWidgetReference.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataSampling>, I>>(base?: I): DataSampling {
    return DataSampling.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataSampling>, I>>(object: I): DataSampling {
    const message = createBaseDataSampling();
    message.key = object.key ?? "";
    message.name = object.name ?? "";
    switch (object.samplingType?.$case) {
      case "variableTrigger": {
        if (object.samplingType?.value !== undefined && object.samplingType?.value !== null) {
          message.samplingType = {
            $case: "variableTrigger",
            value: VariableTrigger.fromPartial(object.samplingType.value),
          };
        }
        break;
      }
      case "interval": {
        if (object.samplingType?.value !== undefined && object.samplingType?.value !== null) {
          message.samplingType = {
            $case: "interval",
            value: DataSampling_Interval.fromPartial(object.samplingType.value),
          };
        }
        break;
      }
    }
    message.samplingFields = Object.entries(object.samplingFields ?? {}).reduce<{ [key: number]: DataSampling_Field }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = DataSampling_Field.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.pauseControl = (object.pauseControl !== undefined && object.pauseControl !== null)
      ? VariableReference.fromPartial(object.pauseControl)
      : undefined;
    message.clearControl = (object.clearControl !== undefined && object.clearControl !== null)
      ? VariableReference.fromPartial(object.clearControl)
      : undefined;
    message.prioritySampling = object.prioritySampling ?? false;
    message.saveParams = object.saveParams?.map((e) => DataSampling_SaveParam.fromPartial(e)) || [];
    message.notifyWidgets = object.notifyWidgets?.map((e) => DataSamplingWidgetReference.fromPartial(e)) || [];
    return message;
  },
};

function createBaseDataSampling_Interval(): DataSampling_Interval {
  return { timeIntervalU16: 0, isOnTime: false };
}

export const DataSampling_Interval: MessageFns<DataSampling_Interval> = {
  encode(message: DataSampling_Interval, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.timeIntervalU16 !== 0) {
      writer.uint32(8).uint32(message.timeIntervalU16);
    }
    if (message.isOnTime !== false) {
      writer.uint32(16).bool(message.isOnTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataSampling_Interval {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataSampling_Interval();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.timeIntervalU16 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isOnTime = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataSampling_Interval {
    return {
      timeIntervalU16: isSet(object.timeIntervalU16) ? globalThis.Number(object.timeIntervalU16) : 0,
      isOnTime: isSet(object.isOnTime) ? globalThis.Boolean(object.isOnTime) : false,
    };
  },

  toJSON(message: DataSampling_Interval): unknown {
    const obj: any = {};
    if (message.timeIntervalU16 !== 0) {
      obj.timeIntervalU16 = Math.round(message.timeIntervalU16);
    }
    if (message.isOnTime !== false) {
      obj.isOnTime = message.isOnTime;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataSampling_Interval>, I>>(base?: I): DataSampling_Interval {
    return DataSampling_Interval.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataSampling_Interval>, I>>(object: I): DataSampling_Interval {
    const message = createBaseDataSampling_Interval();
    message.timeIntervalU16 = object.timeIntervalU16 ?? 0;
    message.isOnTime = object.isOnTime ?? false;
    return message;
  },
};

function createBaseDataSampling_Field(): DataSampling_Field {
  return { key: "", name: "", fieldFormat: 0, variable: undefined, memo: "", dataTransform: [] };
}

export const DataSampling_Field: MessageFns<DataSampling_Field> = {
  encode(message: DataSampling_Field, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.fieldFormat !== 0) {
      writer.uint32(24).int32(message.fieldFormat);
    }
    if (message.variable !== undefined) {
      VariableReference.encode(message.variable, writer.uint32(34).fork()).join();
    }
    if (message.memo !== "") {
      writer.uint32(42).string(message.memo);
    }
    for (const v of message.dataTransform) {
      DataTransform.encode(v!, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataSampling_Field {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataSampling_Field();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.fieldFormat = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.variable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.dataTransform.push(DataTransform.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataSampling_Field {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      fieldFormat: isSet(object.fieldFormat) ? dataSampling_FieldFormatFromJSON(object.fieldFormat) : 0,
      variable: isSet(object.variable) ? VariableReference.fromJSON(object.variable) : undefined,
      memo: isSet(object.memo) ? globalThis.String(object.memo) : "",
      dataTransform: globalThis.Array.isArray(object?.dataTransform)
        ? object.dataTransform.map((e: any) => DataTransform.fromJSON(e))
        : [],
    };
  },

  toJSON(message: DataSampling_Field): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.fieldFormat !== 0) {
      obj.fieldFormat = dataSampling_FieldFormatToJSON(message.fieldFormat);
    }
    if (message.variable !== undefined) {
      obj.variable = VariableReference.toJSON(message.variable);
    }
    if (message.memo !== "") {
      obj.memo = message.memo;
    }
    if (message.dataTransform?.length) {
      obj.dataTransform = message.dataTransform.map((e) => DataTransform.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataSampling_Field>, I>>(base?: I): DataSampling_Field {
    return DataSampling_Field.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataSampling_Field>, I>>(object: I): DataSampling_Field {
    const message = createBaseDataSampling_Field();
    message.key = object.key ?? "";
    message.name = object.name ?? "";
    message.fieldFormat = object.fieldFormat ?? 0;
    message.variable = (object.variable !== undefined && object.variable !== null)
      ? VariableReference.fromPartial(object.variable)
      : undefined;
    message.memo = object.memo ?? "";
    message.dataTransform = object.dataTransform?.map((e) => DataTransform.fromPartial(e)) || [];
    return message;
  },
};

function createBaseDataSampling_SamplingFieldsEntry(): DataSampling_SamplingFieldsEntry {
  return { key: 0, value: undefined };
}

export const DataSampling_SamplingFieldsEntry: MessageFns<DataSampling_SamplingFieldsEntry> = {
  encode(message: DataSampling_SamplingFieldsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      DataSampling_Field.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataSampling_SamplingFieldsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataSampling_SamplingFieldsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = DataSampling_Field.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataSampling_SamplingFieldsEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? DataSampling_Field.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: DataSampling_SamplingFieldsEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = DataSampling_Field.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataSampling_SamplingFieldsEntry>, I>>(
    base?: I,
  ): DataSampling_SamplingFieldsEntry {
    return DataSampling_SamplingFieldsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataSampling_SamplingFieldsEntry>, I>>(
    object: I,
  ): DataSampling_SamplingFieldsEntry {
    const message = createBaseDataSampling_SamplingFieldsEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? DataSampling_Field.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseDataSampling_SaveParam(): DataSampling_SaveParam {
  return {
    savePath: "",
    saveDaysU8: 0,
    maxSaveCountU32: 0,
    maxFileSizeU32: 0,
    maxStorageSizeU32: 0,
    minFreeSpaceU32: 0,
    isLimitDropNew: false,
    isCsv: false,
    clearControl: undefined,
  };
}

export const DataSampling_SaveParam: MessageFns<DataSampling_SaveParam> = {
  encode(message: DataSampling_SaveParam, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.savePath !== "") {
      writer.uint32(10).string(message.savePath);
    }
    if (message.saveDaysU8 !== 0) {
      writer.uint32(16).uint32(message.saveDaysU8);
    }
    if (message.maxSaveCountU32 !== 0) {
      writer.uint32(24).uint32(message.maxSaveCountU32);
    }
    if (message.maxFileSizeU32 !== 0) {
      writer.uint32(32).uint32(message.maxFileSizeU32);
    }
    if (message.maxStorageSizeU32 !== 0) {
      writer.uint32(40).uint32(message.maxStorageSizeU32);
    }
    if (message.minFreeSpaceU32 !== 0) {
      writer.uint32(48).uint32(message.minFreeSpaceU32);
    }
    if (message.isLimitDropNew !== false) {
      writer.uint32(56).bool(message.isLimitDropNew);
    }
    if (message.isCsv !== false) {
      writer.uint32(64).bool(message.isCsv);
    }
    if (message.clearControl !== undefined) {
      VariableReference.encode(message.clearControl, writer.uint32(74).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataSampling_SaveParam {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataSampling_SaveParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.savePath = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.saveDaysU8 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.maxSaveCountU32 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.maxFileSizeU32 = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.maxStorageSizeU32 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.minFreeSpaceU32 = reader.uint32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.isLimitDropNew = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.isCsv = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.clearControl = VariableReference.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataSampling_SaveParam {
    return {
      savePath: isSet(object.savePath) ? globalThis.String(object.savePath) : "",
      saveDaysU8: isSet(object.saveDaysU8) ? globalThis.Number(object.saveDaysU8) : 0,
      maxSaveCountU32: isSet(object.maxSaveCountU32) ? globalThis.Number(object.maxSaveCountU32) : 0,
      maxFileSizeU32: isSet(object.maxFileSizeU32) ? globalThis.Number(object.maxFileSizeU32) : 0,
      maxStorageSizeU32: isSet(object.maxStorageSizeU32) ? globalThis.Number(object.maxStorageSizeU32) : 0,
      minFreeSpaceU32: isSet(object.minFreeSpaceU32) ? globalThis.Number(object.minFreeSpaceU32) : 0,
      isLimitDropNew: isSet(object.isLimitDropNew) ? globalThis.Boolean(object.isLimitDropNew) : false,
      isCsv: isSet(object.isCsv) ? globalThis.Boolean(object.isCsv) : false,
      clearControl: isSet(object.clearControl) ? VariableReference.fromJSON(object.clearControl) : undefined,
    };
  },

  toJSON(message: DataSampling_SaveParam): unknown {
    const obj: any = {};
    if (message.savePath !== "") {
      obj.savePath = message.savePath;
    }
    if (message.saveDaysU8 !== 0) {
      obj.saveDaysU8 = Math.round(message.saveDaysU8);
    }
    if (message.maxSaveCountU32 !== 0) {
      obj.maxSaveCountU32 = Math.round(message.maxSaveCountU32);
    }
    if (message.maxFileSizeU32 !== 0) {
      obj.maxFileSizeU32 = Math.round(message.maxFileSizeU32);
    }
    if (message.maxStorageSizeU32 !== 0) {
      obj.maxStorageSizeU32 = Math.round(message.maxStorageSizeU32);
    }
    if (message.minFreeSpaceU32 !== 0) {
      obj.minFreeSpaceU32 = Math.round(message.minFreeSpaceU32);
    }
    if (message.isLimitDropNew !== false) {
      obj.isLimitDropNew = message.isLimitDropNew;
    }
    if (message.isCsv !== false) {
      obj.isCsv = message.isCsv;
    }
    if (message.clearControl !== undefined) {
      obj.clearControl = VariableReference.toJSON(message.clearControl);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataSampling_SaveParam>, I>>(base?: I): DataSampling_SaveParam {
    return DataSampling_SaveParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataSampling_SaveParam>, I>>(object: I): DataSampling_SaveParam {
    const message = createBaseDataSampling_SaveParam();
    message.savePath = object.savePath ?? "";
    message.saveDaysU8 = object.saveDaysU8 ?? 0;
    message.maxSaveCountU32 = object.maxSaveCountU32 ?? 0;
    message.maxFileSizeU32 = object.maxFileSizeU32 ?? 0;
    message.maxStorageSizeU32 = object.maxStorageSizeU32 ?? 0;
    message.minFreeSpaceU32 = object.minFreeSpaceU32 ?? 0;
    message.isLimitDropNew = object.isLimitDropNew ?? false;
    message.isCsv = object.isCsv ?? false;
    message.clearControl = (object.clearControl !== undefined && object.clearControl !== null)
      ? VariableReference.fromPartial(object.clearControl)
      : undefined;
    return message;
  },
};

function createBaseDataSamplingWidgetReference(): DataSamplingWidgetReference {
  return { pageIdU16: 0, widgetIdU16: 0, notifyVariableIndexU8: [] };
}

export const DataSamplingWidgetReference: MessageFns<DataSamplingWidgetReference> = {
  encode(message: DataSamplingWidgetReference, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageIdU16 !== 0) {
      writer.uint32(8).uint32(message.pageIdU16);
    }
    if (message.widgetIdU16 !== 0) {
      writer.uint32(16).uint32(message.widgetIdU16);
    }
    writer.uint32(26).fork();
    for (const v of message.notifyVariableIndexU8) {
      writer.uint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataSamplingWidgetReference {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataSamplingWidgetReference();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pageIdU16 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.widgetIdU16 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.notifyVariableIndexU8.push(reader.uint32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.notifyVariableIndexU8.push(reader.uint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataSamplingWidgetReference {
    return {
      pageIdU16: isSet(object.pageIdU16) ? globalThis.Number(object.pageIdU16) : 0,
      widgetIdU16: isSet(object.widgetIdU16) ? globalThis.Number(object.widgetIdU16) : 0,
      notifyVariableIndexU8: globalThis.Array.isArray(object?.notifyVariableIndexU8)
        ? object.notifyVariableIndexU8.map((e: any) => globalThis.Number(e))
        : [],
    };
  },

  toJSON(message: DataSamplingWidgetReference): unknown {
    const obj: any = {};
    if (message.pageIdU16 !== 0) {
      obj.pageIdU16 = Math.round(message.pageIdU16);
    }
    if (message.widgetIdU16 !== 0) {
      obj.widgetIdU16 = Math.round(message.widgetIdU16);
    }
    if (message.notifyVariableIndexU8?.length) {
      obj.notifyVariableIndexU8 = message.notifyVariableIndexU8.map((e) => Math.round(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataSamplingWidgetReference>, I>>(base?: I): DataSamplingWidgetReference {
    return DataSamplingWidgetReference.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataSamplingWidgetReference>, I>>(object: I): DataSamplingWidgetReference {
    const message = createBaseDataSamplingWidgetReference();
    message.pageIdU16 = object.pageIdU16 ?? 0;
    message.widgetIdU16 = object.widgetIdU16 ?? 0;
    message.notifyVariableIndexU8 = object.notifyVariableIndexU8?.map((e) => e) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
