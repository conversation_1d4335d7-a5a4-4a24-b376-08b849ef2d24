// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/active.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Action } from "./action";

export const protobufPackage = "znd.project.v1";

/** 事件id */
export enum EventEnum {
  EVENT_ENUM_UNSPECIFIED = 0,
  /** EVENT_ENUM_WIDGET_PRESS - 元件按下 */
  EVENT_ENUM_WIDGET_PRESS = 1,
  /** EVENT_ENUM_WIDGET_RELEASE - 元件松开 */
  EVENT_ENUM_WIDGET_RELEASE = 2,
  /** EVENT_ENUM_WIDGET_CLICK - 元件点击（点击事件表示同时触发按下和松开，实际动作列表没有对应的执行时机） */
  EVENT_ENUM_WIDGET_CLICK = 3,
  /**
   * EVENT_ENUM_WIDGET_INPUT - 元件输入
   * 协议：TLV格式
   * 执行InputAction，根据其中的multi_address执行两种写入逻辑
   */
  EVENT_ENUM_WIDGET_INPUT = 4,
  UNRECOGNIZED = -1,
}

export function eventEnumFromJSON(object: any): EventEnum {
  switch (object) {
    case 0:
    case "EVENT_ENUM_UNSPECIFIED":
      return EventEnum.EVENT_ENUM_UNSPECIFIED;
    case 1:
    case "EVENT_ENUM_WIDGET_PRESS":
      return EventEnum.EVENT_ENUM_WIDGET_PRESS;
    case 2:
    case "EVENT_ENUM_WIDGET_RELEASE":
      return EventEnum.EVENT_ENUM_WIDGET_RELEASE;
    case 3:
    case "EVENT_ENUM_WIDGET_CLICK":
      return EventEnum.EVENT_ENUM_WIDGET_CLICK;
    case 4:
    case "EVENT_ENUM_WIDGET_INPUT":
      return EventEnum.EVENT_ENUM_WIDGET_INPUT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return EventEnum.UNRECOGNIZED;
  }
}

export function eventEnumToJSON(object: EventEnum): string {
  switch (object) {
    case EventEnum.EVENT_ENUM_UNSPECIFIED:
      return "EVENT_ENUM_UNSPECIFIED";
    case EventEnum.EVENT_ENUM_WIDGET_PRESS:
      return "EVENT_ENUM_WIDGET_PRESS";
    case EventEnum.EVENT_ENUM_WIDGET_RELEASE:
      return "EVENT_ENUM_WIDGET_RELEASE";
    case EventEnum.EVENT_ENUM_WIDGET_CLICK:
      return "EVENT_ENUM_WIDGET_CLICK";
    case EventEnum.EVENT_ENUM_WIDGET_INPUT:
      return "EVENT_ENUM_WIDGET_INPUT";
    case EventEnum.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/**
 * 考虑不止是属性，还有事件或者动作
 * 执行动作时，前端只发送动作ID，后端根据动作ID，在actions.proto中找到对应的动作
 * 然后根据动作的参数，如果需要变量的值，由后端自行提前采集，执行动作
 * 元件属性ID
 */
export enum WidgetPropertyEnum {
  WIDGET_PROPERTY_ENUM_UNSPECIFIED = 0,
  /** WIDGET_PROPERTY_ENUM_LOCATION_LEFT - 位置LEFT */
  WIDGET_PROPERTY_ENUM_LOCATION_LEFT = 1,
  /** WIDGET_PROPERTY_ENUM_LOCATION_TOP - 位置TOP */
  WIDGET_PROPERTY_ENUM_LOCATION_TOP = 2,
  /** WIDGET_PROPERTY_ENUM_SIZE_WIDTH - 宽 */
  WIDGET_PROPERTY_ENUM_SIZE_WIDTH = 3,
  /** WIDGET_PROPERTY_ENUM_SIZE_HEIGHT - 高 */
  WIDGET_PROPERTY_ENUM_SIZE_HEIGHT = 4,
  /** WIDGET_PROPERTY_ENUM_ENABLE_CONTROL_CONDITION - 控制能否使用的条件运算结果(TRUE/FALSE) */
  WIDGET_PROPERTY_ENUM_ENABLE_CONTROL_CONDITION = 5,
  /** WIDGET_PROPERTY_ENUM_STATE_VALUE - 当前状态值 */
  WIDGET_PROPERTY_ENUM_STATE_VALUE = 8,
  /** WIDGET_PROPERTY_ENUM_DISPLAY_VALUE - 当前显示值 */
  WIDGET_PROPERTY_ENUM_DISPLAY_VALUE = 9,
  /** WIDGET_PROPERTY_ENUM_ACTION_PAGE_ID - 动作中的页面跳转ID */
  WIDGET_PROPERTY_ENUM_ACTION_PAGE_ID = 10,
  /** WIDGET_PROPERTY_ENUM_ACTION_BIT_WRITE_ADDRESS - 动作中位操作写入地址（如果位动作是切换，则这个地址需要纳入采集，否则只是存入位写入动作） */
  WIDGET_PROPERTY_ENUM_ACTION_BIT_WRITE_ADDRESS = 11,
  /** WIDGET_PROPERTY_ENUM_ACTION_BIT_PULSE_WIDTH - 动作中位操作脉冲宽度 */
  WIDGET_PROPERTY_ENUM_ACTION_BIT_PULSE_WIDTH = 12,
  /** WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_ADDRESS - 动作中字操作写入地址（如果字动作需要原值，则这个地址需要纳入采集，否则只是存入字写入动作） */
  WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_ADDRESS = 13,
  /** WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_VALUE - 动作中字操作写入值或加减值(根据dataFormat写到NumberValue中不同的值) */
  WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_VALUE = 14,
  /** WIDGET_PROPERTY_ENUM_ACTION_WORD_MAX_VALUE - 动作中字操作最大值 */
  WIDGET_PROPERTY_ENUM_ACTION_WORD_MAX_VALUE = 15,
  /** WIDGET_PROPERTY_ENUM_ACTION_WORD_MIN_VALUE - 动作中字操作最小值 */
  WIDGET_PROPERTY_ENUM_ACTION_WORD_MIN_VALUE = 16,
  /** WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_PAGE_ID - 截屏动作中的页面ID */
  WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_PAGE_ID = 19,
  /** WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_SAVE_PATH - 截屏动作中的保存路径 */
  WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_SAVE_PATH = 20,
  /** WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_FILE_NAME - 截屏动作中的文件名 */
  WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_FILE_NAME = 21,
  /** WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_SOURCE_ADDRESS - 复制数据动作中的源地址 */
  WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_SOURCE_ADDRESS = 22,
  /** WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_TARGET_ADDRESS - 复制数据动作中的目标地址 */
  WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_TARGET_ADDRESS = 23,
  /** WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_LENGTH - 复制数据动作中的数据长度 */
  WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_LENGTH = 24,
  /** WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_SOUND_ID - 播放声音中的声音ID */
  WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_SOUND_ID = 25,
  /** WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_DURATION - 播放声音中的持续时长 */
  WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_DURATION = 26,
  /** WIDGET_PROPERTY_ENUM_ACTION_DELAY_MILLISECONDS - 延时动作中的延时时长 */
  WIDGET_PROPERTY_ENUM_ACTION_DELAY_MILLISECONDS = 27,
  /** WIDGET_PROPERTY_ENUM_ACTION_LOG_TEXT - 操作记录中的文本(多语言要用JSON) */
  WIDGET_PROPERTY_ENUM_ACTION_LOG_TEXT = 28,
  /** WIDGET_PROPERTY_ENUM_ACTION_INPUT_WRITE_ADDRESS - 输入动作中的写入地址 */
  WIDGET_PROPERTY_ENUM_ACTION_INPUT_WRITE_ADDRESS = 29,
  /** WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_VALUE - 输入动作中的最大值 */
  WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_VALUE = 30,
  /** WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_VALUE - 输入动作中的最小值 */
  WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_VALUE = 31,
  /** WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_LENGTH - 输入动作中的最短长度 */
  WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_LENGTH = 32,
  /** WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_LENGTH - 输入动作中的最长长度 */
  WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_LENGTH = 33,
  /** WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_TOPIC - MQTT推送中的主题 */
  WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_TOPIC = 34,
  /** WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_MESSAGE - MQTT推送中的消息 */
  WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_MESSAGE = 35,
  /** WIDGET_PROPERTY_ENUM_ACTION_HTTP_API - HTTP调用中的API */
  WIDGET_PROPERTY_ENUM_ACTION_HTTP_API = 36,
  /** WIDGET_PROPERTY_ENUM_CONFIRM_PAGE_ID - 确认弹窗中的页面ID */
  WIDGET_PROPERTY_ENUM_CONFIRM_PAGE_ID = 37,
  /** WIDGET_PROPERTY_ENUM_PRESS_SOUND_ID - 按下时的声音反馈 */
  WIDGET_PROPERTY_ENUM_PRESS_SOUND_ID = 38,
  /** WIDGET_PROPERTY_ENUM_PRESS_SOUND_DURATION - 按下时的声音反馈持续时长 */
  WIDGET_PROPERTY_ENUM_PRESS_SOUND_DURATION = 39,
  /** WIDGET_PROPERTY_ENUM_STYLE_BACKGROUND_COLOR - 样式背景色（通过这个一定是颜色值，不会是主题颜色） */
  WIDGET_PROPERTY_ENUM_STYLE_BACKGROUND_COLOR = 40,
  /** WIDGET_PROPERTY_ENUM_INVALID_INPUT - 非法输入 */
  WIDGET_PROPERTY_ENUM_INVALID_INPUT = 41,
  /** WIDGET_PROPERTY_ENUM_TEXT - 文本 */
  WIDGET_PROPERTY_ENUM_TEXT = 42,
  UNRECOGNIZED = -1,
}

export function widgetPropertyEnumFromJSON(object: any): WidgetPropertyEnum {
  switch (object) {
    case 0:
    case "WIDGET_PROPERTY_ENUM_UNSPECIFIED":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_UNSPECIFIED;
    case 1:
    case "WIDGET_PROPERTY_ENUM_LOCATION_LEFT":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_LOCATION_LEFT;
    case 2:
    case "WIDGET_PROPERTY_ENUM_LOCATION_TOP":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_LOCATION_TOP;
    case 3:
    case "WIDGET_PROPERTY_ENUM_SIZE_WIDTH":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_SIZE_WIDTH;
    case 4:
    case "WIDGET_PROPERTY_ENUM_SIZE_HEIGHT":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_SIZE_HEIGHT;
    case 5:
    case "WIDGET_PROPERTY_ENUM_ENABLE_CONTROL_CONDITION":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ENABLE_CONTROL_CONDITION;
    case 8:
    case "WIDGET_PROPERTY_ENUM_STATE_VALUE":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_STATE_VALUE;
    case 9:
    case "WIDGET_PROPERTY_ENUM_DISPLAY_VALUE":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_DISPLAY_VALUE;
    case 10:
    case "WIDGET_PROPERTY_ENUM_ACTION_PAGE_ID":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_PAGE_ID;
    case 11:
    case "WIDGET_PROPERTY_ENUM_ACTION_BIT_WRITE_ADDRESS":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_BIT_WRITE_ADDRESS;
    case 12:
    case "WIDGET_PROPERTY_ENUM_ACTION_BIT_PULSE_WIDTH":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_BIT_PULSE_WIDTH;
    case 13:
    case "WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_ADDRESS":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_ADDRESS;
    case 14:
    case "WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_VALUE":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_VALUE;
    case 15:
    case "WIDGET_PROPERTY_ENUM_ACTION_WORD_MAX_VALUE":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_WORD_MAX_VALUE;
    case 16:
    case "WIDGET_PROPERTY_ENUM_ACTION_WORD_MIN_VALUE":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_WORD_MIN_VALUE;
    case 19:
    case "WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_PAGE_ID":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_PAGE_ID;
    case 20:
    case "WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_SAVE_PATH":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_SAVE_PATH;
    case 21:
    case "WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_FILE_NAME":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_FILE_NAME;
    case 22:
    case "WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_SOURCE_ADDRESS":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_SOURCE_ADDRESS;
    case 23:
    case "WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_TARGET_ADDRESS":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_TARGET_ADDRESS;
    case 24:
    case "WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_LENGTH":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_LENGTH;
    case 25:
    case "WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_SOUND_ID":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_SOUND_ID;
    case 26:
    case "WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_DURATION":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_DURATION;
    case 27:
    case "WIDGET_PROPERTY_ENUM_ACTION_DELAY_MILLISECONDS":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_DELAY_MILLISECONDS;
    case 28:
    case "WIDGET_PROPERTY_ENUM_ACTION_LOG_TEXT":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_LOG_TEXT;
    case 29:
    case "WIDGET_PROPERTY_ENUM_ACTION_INPUT_WRITE_ADDRESS":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_INPUT_WRITE_ADDRESS;
    case 30:
    case "WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_VALUE":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_VALUE;
    case 31:
    case "WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_VALUE":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_VALUE;
    case 32:
    case "WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_LENGTH":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_LENGTH;
    case 33:
    case "WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_LENGTH":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_LENGTH;
    case 34:
    case "WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_TOPIC":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_TOPIC;
    case 35:
    case "WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_MESSAGE":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_MESSAGE;
    case 36:
    case "WIDGET_PROPERTY_ENUM_ACTION_HTTP_API":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_HTTP_API;
    case 37:
    case "WIDGET_PROPERTY_ENUM_CONFIRM_PAGE_ID":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_CONFIRM_PAGE_ID;
    case 38:
    case "WIDGET_PROPERTY_ENUM_PRESS_SOUND_ID":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_PRESS_SOUND_ID;
    case 39:
    case "WIDGET_PROPERTY_ENUM_PRESS_SOUND_DURATION":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_PRESS_SOUND_DURATION;
    case 40:
    case "WIDGET_PROPERTY_ENUM_STYLE_BACKGROUND_COLOR":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_STYLE_BACKGROUND_COLOR;
    case 41:
    case "WIDGET_PROPERTY_ENUM_INVALID_INPUT":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_INVALID_INPUT;
    case 42:
    case "WIDGET_PROPERTY_ENUM_TEXT":
      return WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_TEXT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetPropertyEnum.UNRECOGNIZED;
  }
}

export function widgetPropertyEnumToJSON(object: WidgetPropertyEnum): string {
  switch (object) {
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_UNSPECIFIED:
      return "WIDGET_PROPERTY_ENUM_UNSPECIFIED";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_LOCATION_LEFT:
      return "WIDGET_PROPERTY_ENUM_LOCATION_LEFT";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_LOCATION_TOP:
      return "WIDGET_PROPERTY_ENUM_LOCATION_TOP";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_SIZE_WIDTH:
      return "WIDGET_PROPERTY_ENUM_SIZE_WIDTH";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_SIZE_HEIGHT:
      return "WIDGET_PROPERTY_ENUM_SIZE_HEIGHT";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ENABLE_CONTROL_CONDITION:
      return "WIDGET_PROPERTY_ENUM_ENABLE_CONTROL_CONDITION";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_STATE_VALUE:
      return "WIDGET_PROPERTY_ENUM_STATE_VALUE";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_DISPLAY_VALUE:
      return "WIDGET_PROPERTY_ENUM_DISPLAY_VALUE";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_PAGE_ID:
      return "WIDGET_PROPERTY_ENUM_ACTION_PAGE_ID";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_BIT_WRITE_ADDRESS:
      return "WIDGET_PROPERTY_ENUM_ACTION_BIT_WRITE_ADDRESS";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_BIT_PULSE_WIDTH:
      return "WIDGET_PROPERTY_ENUM_ACTION_BIT_PULSE_WIDTH";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_ADDRESS:
      return "WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_ADDRESS";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_VALUE:
      return "WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_VALUE";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_WORD_MAX_VALUE:
      return "WIDGET_PROPERTY_ENUM_ACTION_WORD_MAX_VALUE";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_WORD_MIN_VALUE:
      return "WIDGET_PROPERTY_ENUM_ACTION_WORD_MIN_VALUE";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_PAGE_ID:
      return "WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_PAGE_ID";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_SAVE_PATH:
      return "WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_SAVE_PATH";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_FILE_NAME:
      return "WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_FILE_NAME";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_SOURCE_ADDRESS:
      return "WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_SOURCE_ADDRESS";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_TARGET_ADDRESS:
      return "WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_TARGET_ADDRESS";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_LENGTH:
      return "WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_LENGTH";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_SOUND_ID:
      return "WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_SOUND_ID";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_DURATION:
      return "WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_DURATION";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_DELAY_MILLISECONDS:
      return "WIDGET_PROPERTY_ENUM_ACTION_DELAY_MILLISECONDS";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_LOG_TEXT:
      return "WIDGET_PROPERTY_ENUM_ACTION_LOG_TEXT";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_INPUT_WRITE_ADDRESS:
      return "WIDGET_PROPERTY_ENUM_ACTION_INPUT_WRITE_ADDRESS";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_VALUE:
      return "WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_VALUE";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_VALUE:
      return "WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_VALUE";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_LENGTH:
      return "WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_LENGTH";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_LENGTH:
      return "WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_LENGTH";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_TOPIC:
      return "WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_TOPIC";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_MESSAGE:
      return "WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_MESSAGE";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_HTTP_API:
      return "WIDGET_PROPERTY_ENUM_ACTION_HTTP_API";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_CONFIRM_PAGE_ID:
      return "WIDGET_PROPERTY_ENUM_CONFIRM_PAGE_ID";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_PRESS_SOUND_ID:
      return "WIDGET_PROPERTY_ENUM_PRESS_SOUND_ID";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_PRESS_SOUND_DURATION:
      return "WIDGET_PROPERTY_ENUM_PRESS_SOUND_DURATION";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_STYLE_BACKGROUND_COLOR:
      return "WIDGET_PROPERTY_ENUM_STYLE_BACKGROUND_COLOR";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_INVALID_INPUT:
      return "WIDGET_PROPERTY_ENUM_INVALID_INPUT";
    case WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_TEXT:
      return "WIDGET_PROPERTY_ENUM_TEXT";
    case WidgetPropertyEnum.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 图元属性从150开始，前面留给公用属性 */
export enum WidgetTextPropertyEnum {
  WIDGET_TEXT_PROPERTY_ENUM_UNSPECIFIED = 0,
  /** WIDGET_TEXT_PROPERTY_ENUM_TEXT - 文本内容(通过这个发送的，如果是字符串，直接显示，如果要多语言，需要JSON格式的字符串) */
  WIDGET_TEXT_PROPERTY_ENUM_TEXT = 150,
  /** WIDGET_TEXT_PROPERTY_ENUM_TEXT_COLOR - 文本颜色 */
  WIDGET_TEXT_PROPERTY_ENUM_TEXT_COLOR = 151,
  UNRECOGNIZED = -1,
}

export function widgetTextPropertyEnumFromJSON(object: any): WidgetTextPropertyEnum {
  switch (object) {
    case 0:
    case "WIDGET_TEXT_PROPERTY_ENUM_UNSPECIFIED":
      return WidgetTextPropertyEnum.WIDGET_TEXT_PROPERTY_ENUM_UNSPECIFIED;
    case 150:
    case "WIDGET_TEXT_PROPERTY_ENUM_TEXT":
      return WidgetTextPropertyEnum.WIDGET_TEXT_PROPERTY_ENUM_TEXT;
    case 151:
    case "WIDGET_TEXT_PROPERTY_ENUM_TEXT_COLOR":
      return WidgetTextPropertyEnum.WIDGET_TEXT_PROPERTY_ENUM_TEXT_COLOR;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetTextPropertyEnum.UNRECOGNIZED;
  }
}

export function widgetTextPropertyEnumToJSON(object: WidgetTextPropertyEnum): string {
  switch (object) {
    case WidgetTextPropertyEnum.WIDGET_TEXT_PROPERTY_ENUM_UNSPECIFIED:
      return "WIDGET_TEXT_PROPERTY_ENUM_UNSPECIFIED";
    case WidgetTextPropertyEnum.WIDGET_TEXT_PROPERTY_ENUM_TEXT:
      return "WIDGET_TEXT_PROPERTY_ENUM_TEXT";
    case WidgetTextPropertyEnum.WIDGET_TEXT_PROPERTY_ENUM_TEXT_COLOR:
      return "WIDGET_TEXT_PROPERTY_ENUM_TEXT_COLOR";
    case WidgetTextPropertyEnum.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 图元属性从150开始，前面留给公用属性 */
export enum WidgetMatrixButtonPropertyEnum {
  WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_UNSPECIFIED = 0,
  /** WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S0 - 状态0（松开时）的按钮文本 */
  WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S0 = 150,
  /** WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S1 - 状态1（按下时）的按钮文本 */
  WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S1 = 151,
  UNRECOGNIZED = -1,
}

export function widgetMatrixButtonPropertyEnumFromJSON(object: any): WidgetMatrixButtonPropertyEnum {
  switch (object) {
    case 0:
    case "WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_UNSPECIFIED":
      return WidgetMatrixButtonPropertyEnum.WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_UNSPECIFIED;
    case 150:
    case "WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S0":
      return WidgetMatrixButtonPropertyEnum.WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S0;
    case 151:
    case "WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S1":
      return WidgetMatrixButtonPropertyEnum.WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S1;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetMatrixButtonPropertyEnum.UNRECOGNIZED;
  }
}

export function widgetMatrixButtonPropertyEnumToJSON(object: WidgetMatrixButtonPropertyEnum): string {
  switch (object) {
    case WidgetMatrixButtonPropertyEnum.WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_UNSPECIFIED:
      return "WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_UNSPECIFIED";
    case WidgetMatrixButtonPropertyEnum.WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S0:
      return "WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S0";
    case WidgetMatrixButtonPropertyEnum.WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S1:
      return "WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S1";
    case WidgetMatrixButtonPropertyEnum.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 图元属性从150开始，前面留给公用属性 */
export enum WidgetBitPropertyEnum {
  WIDGET_BIT_PROPERTY_ENUM_UNSPECIFIED = 0,
  /** WIDGET_BIT_PROPERTY_ENUM_TEXT - 文本内容，下标是状态(通过这个发送的，如果是字符串，直接显示，如果要多语言，需要JSON格式的字符串) */
  WIDGET_BIT_PROPERTY_ENUM_TEXT = 150,
  /** WIDGET_BIT_PROPERTY_ENUM_TEXT_COLOR - 文本颜色（不同下标表示不同状态） */
  WIDGET_BIT_PROPERTY_ENUM_TEXT_COLOR = 151,
  UNRECOGNIZED = -1,
}

export function widgetBitPropertyEnumFromJSON(object: any): WidgetBitPropertyEnum {
  switch (object) {
    case 0:
    case "WIDGET_BIT_PROPERTY_ENUM_UNSPECIFIED":
      return WidgetBitPropertyEnum.WIDGET_BIT_PROPERTY_ENUM_UNSPECIFIED;
    case 150:
    case "WIDGET_BIT_PROPERTY_ENUM_TEXT":
      return WidgetBitPropertyEnum.WIDGET_BIT_PROPERTY_ENUM_TEXT;
    case 151:
    case "WIDGET_BIT_PROPERTY_ENUM_TEXT_COLOR":
      return WidgetBitPropertyEnum.WIDGET_BIT_PROPERTY_ENUM_TEXT_COLOR;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetBitPropertyEnum.UNRECOGNIZED;
  }
}

export function widgetBitPropertyEnumToJSON(object: WidgetBitPropertyEnum): string {
  switch (object) {
    case WidgetBitPropertyEnum.WIDGET_BIT_PROPERTY_ENUM_UNSPECIFIED:
      return "WIDGET_BIT_PROPERTY_ENUM_UNSPECIFIED";
    case WidgetBitPropertyEnum.WIDGET_BIT_PROPERTY_ENUM_TEXT:
      return "WIDGET_BIT_PROPERTY_ENUM_TEXT";
    case WidgetBitPropertyEnum.WIDGET_BIT_PROPERTY_ENUM_TEXT_COLOR:
      return "WIDGET_BIT_PROPERTY_ENUM_TEXT_COLOR";
    case WidgetBitPropertyEnum.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 图元属性从150开始，前面留给公用属性 */
export enum WidgetWordPropertyEnum {
  WIDGET_WORD_PROPERTY_ENUM_UNSPECIFIED = 0,
  /** WIDGET_WORD_PROPERTY_ENUM_TEXT - 文本内容，下标是状态(通过这个发送的，如果是字符串，直接显示，如果要多语言，需要JSON格式的字符串) */
  WIDGET_WORD_PROPERTY_ENUM_TEXT = 150,
  /** WIDGET_WORD_PROPERTY_ENUM_TEXT_COLOR - 文本颜色（不同下标表示不同状态） */
  WIDGET_WORD_PROPERTY_ENUM_TEXT_COLOR = 151,
  UNRECOGNIZED = -1,
}

export function widgetWordPropertyEnumFromJSON(object: any): WidgetWordPropertyEnum {
  switch (object) {
    case 0:
    case "WIDGET_WORD_PROPERTY_ENUM_UNSPECIFIED":
      return WidgetWordPropertyEnum.WIDGET_WORD_PROPERTY_ENUM_UNSPECIFIED;
    case 150:
    case "WIDGET_WORD_PROPERTY_ENUM_TEXT":
      return WidgetWordPropertyEnum.WIDGET_WORD_PROPERTY_ENUM_TEXT;
    case 151:
    case "WIDGET_WORD_PROPERTY_ENUM_TEXT_COLOR":
      return WidgetWordPropertyEnum.WIDGET_WORD_PROPERTY_ENUM_TEXT_COLOR;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetWordPropertyEnum.UNRECOGNIZED;
  }
}

export function widgetWordPropertyEnumToJSON(object: WidgetWordPropertyEnum): string {
  switch (object) {
    case WidgetWordPropertyEnum.WIDGET_WORD_PROPERTY_ENUM_UNSPECIFIED:
      return "WIDGET_WORD_PROPERTY_ENUM_UNSPECIFIED";
    case WidgetWordPropertyEnum.WIDGET_WORD_PROPERTY_ENUM_TEXT:
      return "WIDGET_WORD_PROPERTY_ENUM_TEXT";
    case WidgetWordPropertyEnum.WIDGET_WORD_PROPERTY_ENUM_TEXT_COLOR:
      return "WIDGET_WORD_PROPERTY_ENUM_TEXT_COLOR";
    case WidgetWordPropertyEnum.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 图元属性从150开始，前面留给公用属性 */
export enum WidgetNumberPropertyEnum {
  WIDGET_NUMBER_PROPERTY_ENUM_UNSPECIFIED = 0,
  /** WIDGET_NUMBER_PROPERTY_ENUM_VALUE - 显示数值 */
  WIDGET_NUMBER_PROPERTY_ENUM_VALUE = 150,
  /** WIDGET_NUMBER_PROPERTY_ENUM_TEXT_COLOR - 文本颜色 */
  WIDGET_NUMBER_PROPERTY_ENUM_TEXT_COLOR = 151,
  UNRECOGNIZED = -1,
}

export function widgetNumberPropertyEnumFromJSON(object: any): WidgetNumberPropertyEnum {
  switch (object) {
    case 0:
    case "WIDGET_NUMBER_PROPERTY_ENUM_UNSPECIFIED":
      return WidgetNumberPropertyEnum.WIDGET_NUMBER_PROPERTY_ENUM_UNSPECIFIED;
    case 150:
    case "WIDGET_NUMBER_PROPERTY_ENUM_VALUE":
      return WidgetNumberPropertyEnum.WIDGET_NUMBER_PROPERTY_ENUM_VALUE;
    case 151:
    case "WIDGET_NUMBER_PROPERTY_ENUM_TEXT_COLOR":
      return WidgetNumberPropertyEnum.WIDGET_NUMBER_PROPERTY_ENUM_TEXT_COLOR;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetNumberPropertyEnum.UNRECOGNIZED;
  }
}

export function widgetNumberPropertyEnumToJSON(object: WidgetNumberPropertyEnum): string {
  switch (object) {
    case WidgetNumberPropertyEnum.WIDGET_NUMBER_PROPERTY_ENUM_UNSPECIFIED:
      return "WIDGET_NUMBER_PROPERTY_ENUM_UNSPECIFIED";
    case WidgetNumberPropertyEnum.WIDGET_NUMBER_PROPERTY_ENUM_VALUE:
      return "WIDGET_NUMBER_PROPERTY_ENUM_VALUE";
    case WidgetNumberPropertyEnum.WIDGET_NUMBER_PROPERTY_ENUM_TEXT_COLOR:
      return "WIDGET_NUMBER_PROPERTY_ENUM_TEXT_COLOR";
    case WidgetNumberPropertyEnum.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 图元属性从150开始，前面留给公用属性 */
export enum WidgetStringPropertyEnum {
  WIDGET_STRING_PROPERTY_ENUM_UNSPECIFIED = 0,
  /** WIDGET_STRING_PROPERTY_ENUM_VALUE - 文本内容 */
  WIDGET_STRING_PROPERTY_ENUM_VALUE = 150,
  /** WIDGET_STRING_PROPERTY_ENUM_TEXT_COLOR - 文本颜色 */
  WIDGET_STRING_PROPERTY_ENUM_TEXT_COLOR = 151,
  UNRECOGNIZED = -1,
}

export function widgetStringPropertyEnumFromJSON(object: any): WidgetStringPropertyEnum {
  switch (object) {
    case 0:
    case "WIDGET_STRING_PROPERTY_ENUM_UNSPECIFIED":
      return WidgetStringPropertyEnum.WIDGET_STRING_PROPERTY_ENUM_UNSPECIFIED;
    case 150:
    case "WIDGET_STRING_PROPERTY_ENUM_VALUE":
      return WidgetStringPropertyEnum.WIDGET_STRING_PROPERTY_ENUM_VALUE;
    case 151:
    case "WIDGET_STRING_PROPERTY_ENUM_TEXT_COLOR":
      return WidgetStringPropertyEnum.WIDGET_STRING_PROPERTY_ENUM_TEXT_COLOR;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetStringPropertyEnum.UNRECOGNIZED;
  }
}

export function widgetStringPropertyEnumToJSON(object: WidgetStringPropertyEnum): string {
  switch (object) {
    case WidgetStringPropertyEnum.WIDGET_STRING_PROPERTY_ENUM_UNSPECIFIED:
      return "WIDGET_STRING_PROPERTY_ENUM_UNSPECIFIED";
    case WidgetStringPropertyEnum.WIDGET_STRING_PROPERTY_ENUM_VALUE:
      return "WIDGET_STRING_PROPERTY_ENUM_VALUE";
    case WidgetStringPropertyEnum.WIDGET_STRING_PROPERTY_ENUM_TEXT_COLOR:
      return "WIDGET_STRING_PROPERTY_ENUM_TEXT_COLOR";
    case WidgetStringPropertyEnum.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 曲线 */
export enum WidgetCurvePropertyEnum {
  WIDGET_CURVE_PROPERTY_ENUM_UNSPECIFIED = 0,
  /** WIDGET_CURVE_PROPERTY_ENUM_Y_MIN_VALUE - 曲线Y轴最小值 */
  WIDGET_CURVE_PROPERTY_ENUM_Y_MIN_VALUE = 150,
  /** WIDGET_CURVE_PROPERTY_ENUM_Y_MAX_VALUE - 曲线Y轴最大值 */
  WIDGET_CURVE_PROPERTY_ENUM_Y_MAX_VALUE = 151,
  /** WIDGET_CURVE_PROPERTY_ENUM_X_POINT_DISTANCE - 采样点像素距离 */
  WIDGET_CURVE_PROPERTY_ENUM_X_POINT_DISTANCE = 152,
  /** WIDGET_CURVE_PROPERTY_ENUM_X_POINT_COUNT - 曲线上的一屏显示的点数 */
  WIDGET_CURVE_PROPERTY_ENUM_X_POINT_COUNT = 153,
  /** WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_SECOND - 一屏的时间范围（秒数） */
  WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_SECOND = 154,
  /** WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_MINUTE - 一屏的时间范围（分钟） */
  WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_MINUTE = 155,
  /** WIDGET_CURVE_PROPERTY_ENUM_CURSOR_VISIBLE - 游标是否显示 */
  WIDGET_CURVE_PROPERTY_ENUM_CURSOR_VISIBLE = 156,
  /**
   * WIDGET_CURVE_PROPERTY_ENUM_SERIES_WIDTH - 数据序列的动态配置
   * 曲线宽度（下标是数据序列的序号）
   */
  WIDGET_CURVE_PROPERTY_ENUM_SERIES_WIDTH = 157,
  /** WIDGET_CURVE_PROPERTY_ENUM_SERIES_VISIBLE - 曲线是否显示（下标是数据序列的序号） */
  WIDGET_CURVE_PROPERTY_ENUM_SERIES_VISIBLE = 158,
  /** WIDGET_CURVE_PROPERTY_ENUM_SERIES_MIN_VALUE - 曲线最小值（下标是数据序列的序号） */
  WIDGET_CURVE_PROPERTY_ENUM_SERIES_MIN_VALUE = 159,
  /** WIDGET_CURVE_PROPERTY_ENUM_SERIES_MAX_VALUE - 曲线最大值（下标是数据序列的序号） */
  WIDGET_CURVE_PROPERTY_ENUM_SERIES_MAX_VALUE = 160,
  UNRECOGNIZED = -1,
}

export function widgetCurvePropertyEnumFromJSON(object: any): WidgetCurvePropertyEnum {
  switch (object) {
    case 0:
    case "WIDGET_CURVE_PROPERTY_ENUM_UNSPECIFIED":
      return WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_UNSPECIFIED;
    case 150:
    case "WIDGET_CURVE_PROPERTY_ENUM_Y_MIN_VALUE":
      return WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_Y_MIN_VALUE;
    case 151:
    case "WIDGET_CURVE_PROPERTY_ENUM_Y_MAX_VALUE":
      return WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_Y_MAX_VALUE;
    case 152:
    case "WIDGET_CURVE_PROPERTY_ENUM_X_POINT_DISTANCE":
      return WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_X_POINT_DISTANCE;
    case 153:
    case "WIDGET_CURVE_PROPERTY_ENUM_X_POINT_COUNT":
      return WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_X_POINT_COUNT;
    case 154:
    case "WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_SECOND":
      return WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_SECOND;
    case 155:
    case "WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_MINUTE":
      return WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_MINUTE;
    case 156:
    case "WIDGET_CURVE_PROPERTY_ENUM_CURSOR_VISIBLE":
      return WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_CURSOR_VISIBLE;
    case 157:
    case "WIDGET_CURVE_PROPERTY_ENUM_SERIES_WIDTH":
      return WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_SERIES_WIDTH;
    case 158:
    case "WIDGET_CURVE_PROPERTY_ENUM_SERIES_VISIBLE":
      return WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_SERIES_VISIBLE;
    case 159:
    case "WIDGET_CURVE_PROPERTY_ENUM_SERIES_MIN_VALUE":
      return WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_SERIES_MIN_VALUE;
    case 160:
    case "WIDGET_CURVE_PROPERTY_ENUM_SERIES_MAX_VALUE":
      return WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_SERIES_MAX_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetCurvePropertyEnum.UNRECOGNIZED;
  }
}

export function widgetCurvePropertyEnumToJSON(object: WidgetCurvePropertyEnum): string {
  switch (object) {
    case WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_UNSPECIFIED:
      return "WIDGET_CURVE_PROPERTY_ENUM_UNSPECIFIED";
    case WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_Y_MIN_VALUE:
      return "WIDGET_CURVE_PROPERTY_ENUM_Y_MIN_VALUE";
    case WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_Y_MAX_VALUE:
      return "WIDGET_CURVE_PROPERTY_ENUM_Y_MAX_VALUE";
    case WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_X_POINT_DISTANCE:
      return "WIDGET_CURVE_PROPERTY_ENUM_X_POINT_DISTANCE";
    case WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_X_POINT_COUNT:
      return "WIDGET_CURVE_PROPERTY_ENUM_X_POINT_COUNT";
    case WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_SECOND:
      return "WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_SECOND";
    case WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_MINUTE:
      return "WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_MINUTE";
    case WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_CURSOR_VISIBLE:
      return "WIDGET_CURVE_PROPERTY_ENUM_CURSOR_VISIBLE";
    case WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_SERIES_WIDTH:
      return "WIDGET_CURVE_PROPERTY_ENUM_SERIES_WIDTH";
    case WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_SERIES_VISIBLE:
      return "WIDGET_CURVE_PROPERTY_ENUM_SERIES_VISIBLE";
    case WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_SERIES_MIN_VALUE:
      return "WIDGET_CURVE_PROPERTY_ENUM_SERIES_MIN_VALUE";
    case WidgetCurvePropertyEnum.WIDGET_CURVE_PROPERTY_ENUM_SERIES_MAX_VALUE:
      return "WIDGET_CURVE_PROPERTY_ENUM_SERIES_MAX_VALUE";
    case WidgetCurvePropertyEnum.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum WidgetRollerPropertyEnum {
  WIDGET_ROLLER_PROPERTY_ENUM_UNSPECIFIED = 0,
  /** WIDGET_ROLLER_PROPERTY_ENUM_ROLLER_MODE - 滚动模式 */
  WIDGET_ROLLER_PROPERTY_ENUM_ROLLER_MODE = 150,
  UNRECOGNIZED = -1,
}

export function widgetRollerPropertyEnumFromJSON(object: any): WidgetRollerPropertyEnum {
  switch (object) {
    case 0:
    case "WIDGET_ROLLER_PROPERTY_ENUM_UNSPECIFIED":
      return WidgetRollerPropertyEnum.WIDGET_ROLLER_PROPERTY_ENUM_UNSPECIFIED;
    case 150:
    case "WIDGET_ROLLER_PROPERTY_ENUM_ROLLER_MODE":
      return WidgetRollerPropertyEnum.WIDGET_ROLLER_PROPERTY_ENUM_ROLLER_MODE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetRollerPropertyEnum.UNRECOGNIZED;
  }
}

export function widgetRollerPropertyEnumToJSON(object: WidgetRollerPropertyEnum): string {
  switch (object) {
    case WidgetRollerPropertyEnum.WIDGET_ROLLER_PROPERTY_ENUM_UNSPECIFIED:
      return "WIDGET_ROLLER_PROPERTY_ENUM_UNSPECIFIED";
    case WidgetRollerPropertyEnum.WIDGET_ROLLER_PROPERTY_ENUM_ROLLER_MODE:
      return "WIDGET_ROLLER_PROPERTY_ENUM_ROLLER_MODE";
    case WidgetRollerPropertyEnum.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum WidgetDropListPropertyEnum {
  WIDGET_DROP_LIST_PROPERTY_ENUM_UNSPECIFIED = 0,
  /** WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_DIRECTION - 下拉方向 */
  WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_DIRECTION = 150,
  /** WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_SYMBOL - 下拉图标 */
  WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_SYMBOL = 151,
  /** WIDGET_DROP_LIST_PROPERTY_ENUM_ALWAYS_OPEN - 是否常开 */
  WIDGET_DROP_LIST_PROPERTY_ENUM_ALWAYS_OPEN = 152,
  UNRECOGNIZED = -1,
}

export function widgetDropListPropertyEnumFromJSON(object: any): WidgetDropListPropertyEnum {
  switch (object) {
    case 0:
    case "WIDGET_DROP_LIST_PROPERTY_ENUM_UNSPECIFIED":
      return WidgetDropListPropertyEnum.WIDGET_DROP_LIST_PROPERTY_ENUM_UNSPECIFIED;
    case 150:
    case "WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_DIRECTION":
      return WidgetDropListPropertyEnum.WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_DIRECTION;
    case 151:
    case "WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_SYMBOL":
      return WidgetDropListPropertyEnum.WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_SYMBOL;
    case 152:
    case "WIDGET_DROP_LIST_PROPERTY_ENUM_ALWAYS_OPEN":
      return WidgetDropListPropertyEnum.WIDGET_DROP_LIST_PROPERTY_ENUM_ALWAYS_OPEN;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetDropListPropertyEnum.UNRECOGNIZED;
  }
}

export function widgetDropListPropertyEnumToJSON(object: WidgetDropListPropertyEnum): string {
  switch (object) {
    case WidgetDropListPropertyEnum.WIDGET_DROP_LIST_PROPERTY_ENUM_UNSPECIFIED:
      return "WIDGET_DROP_LIST_PROPERTY_ENUM_UNSPECIFIED";
    case WidgetDropListPropertyEnum.WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_DIRECTION:
      return "WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_DIRECTION";
    case WidgetDropListPropertyEnum.WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_SYMBOL:
      return "WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_SYMBOL";
    case WidgetDropListPropertyEnum.WIDGET_DROP_LIST_PROPERTY_ENUM_ALWAYS_OPEN:
      return "WIDGET_DROP_LIST_PROPERTY_ENUM_ALWAYS_OPEN";
    case WidgetDropListPropertyEnum.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum WidgetMeterPropertyEnum {
  WIDGET_METER_PROPERTY_ENUM_UNSPECIFIED = 0,
  /** WIDGET_METER_PROPERTY_ENUM_UPPER_LIMIT - 上限值 */
  WIDGET_METER_PROPERTY_ENUM_UPPER_LIMIT = 150,
  /** WIDGET_METER_PROPERTY_ENUM_LOWER_LIMIT - 下限值 */
  WIDGET_METER_PROPERTY_ENUM_LOWER_LIMIT = 151,
  UNRECOGNIZED = -1,
}

export function widgetMeterPropertyEnumFromJSON(object: any): WidgetMeterPropertyEnum {
  switch (object) {
    case 0:
    case "WIDGET_METER_PROPERTY_ENUM_UNSPECIFIED":
      return WidgetMeterPropertyEnum.WIDGET_METER_PROPERTY_ENUM_UNSPECIFIED;
    case 150:
    case "WIDGET_METER_PROPERTY_ENUM_UPPER_LIMIT":
      return WidgetMeterPropertyEnum.WIDGET_METER_PROPERTY_ENUM_UPPER_LIMIT;
    case 151:
    case "WIDGET_METER_PROPERTY_ENUM_LOWER_LIMIT":
      return WidgetMeterPropertyEnum.WIDGET_METER_PROPERTY_ENUM_LOWER_LIMIT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetMeterPropertyEnum.UNRECOGNIZED;
  }
}

export function widgetMeterPropertyEnumToJSON(object: WidgetMeterPropertyEnum): string {
  switch (object) {
    case WidgetMeterPropertyEnum.WIDGET_METER_PROPERTY_ENUM_UNSPECIFIED:
      return "WIDGET_METER_PROPERTY_ENUM_UNSPECIFIED";
    case WidgetMeterPropertyEnum.WIDGET_METER_PROPERTY_ENUM_UPPER_LIMIT:
      return "WIDGET_METER_PROPERTY_ENUM_UPPER_LIMIT";
    case WidgetMeterPropertyEnum.WIDGET_METER_PROPERTY_ENUM_LOWER_LIMIT:
      return "WIDGET_METER_PROPERTY_ENUM_LOWER_LIMIT";
    case WidgetMeterPropertyEnum.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 动作列表 */
export interface ActionList {
  actions: Action[];
}

function createBaseActionList(): ActionList {
  return { actions: [] };
}

export const ActionList: MessageFns<ActionList> = {
  encode(message: ActionList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.actions) {
      Action.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActionList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActionList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.actions.push(Action.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ActionList {
    return {
      actions: globalThis.Array.isArray(object?.actions) ? object.actions.map((e: any) => Action.fromJSON(e)) : [],
    };
  },

  toJSON(message: ActionList): unknown {
    const obj: any = {};
    if (message.actions?.length) {
      obj.actions = message.actions.map((e) => Action.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ActionList>, I>>(base?: I): ActionList {
    return ActionList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActionList>, I>>(object: I): ActionList {
    const message = createBaseActionList();
    message.actions = object.actions?.map((e) => Action.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
