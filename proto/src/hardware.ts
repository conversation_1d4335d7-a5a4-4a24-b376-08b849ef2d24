// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/hardware.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "znd.project.v1";

export interface HardwareSetting {
  /** repeated SerialConfig serial_configs = 1;   //串口参数移到设备中定义，没有设备不可能定义串口 */
  ethernet1Config?:
    | EthernetConfig
    | undefined;
  /** 以太网2参数 */
  ethernet2Config?:
    | EthernetConfig
    | undefined;
  /** WIFI参数 */
  wifiConfig?:
    | EthernetConfig
    | undefined;
  /** 4G参数 */
  mobileConfig?:
    | EthernetConfig
    | undefined;
  /** 屏幕亮度 */
  screenBrightness?:
    | number
    | undefined;
  /** 屏保时间 */
  screensaverTime?:
    | number
    | undefined;
  /** 熄屏时间 */
  screenOffTime?:
    | number
    | undefined;
  /** 开机密码 */
  bootPassword?:
    | string
    | undefined;
  /** 后台密码 */
  adminPassword?:
    | string
    | undefined;
  /** 下载密码 */
  downloadPassword?:
    | string
    | undefined;
  /** 蜂鸣器是否启用 */
  buzzerEnabled?:
    | boolean
    | undefined;
  /** 是否启用网络对时 */
  ntpEnabled?:
    | boolean
    | undefined;
  /** 对时服务器地址 */
  ntpSyncServer?:
    | string
    | undefined;
  /** 时区 */
  timezone?: string | undefined;
}

/** 以太网口配置 */
export interface EthernetConfig {
  /** IP类型 */
  ipType: EthernetConfig_IpDnsType;
  ip?: string | undefined;
  subnetMask?: string | undefined;
  gateway?:
    | string
    | undefined;
  /** DNS类型 */
  dnsType: EthernetConfig_IpDnsType;
  dns1?: string | undefined;
  dns2?: string | undefined;
  disable?:
    | boolean
    | undefined;
  /** WIFI记住 */
  wifiRemembers: WifiRemember[];
  /** APN(4G才有用) */
  apn?: ApnConfig | undefined;
}

export enum EthernetConfig_IpDnsType {
  IP_DNS_TYPE_UNSPECIFIED = 0,
  IP_DNS_TYPE_STATIC = 1,
  IP_DNS_TYPE_DHCP = 2,
  UNRECOGNIZED = -1,
}

export function ethernetConfig_IpDnsTypeFromJSON(object: any): EthernetConfig_IpDnsType {
  switch (object) {
    case 0:
    case "IP_DNS_TYPE_UNSPECIFIED":
      return EthernetConfig_IpDnsType.IP_DNS_TYPE_UNSPECIFIED;
    case 1:
    case "IP_DNS_TYPE_STATIC":
      return EthernetConfig_IpDnsType.IP_DNS_TYPE_STATIC;
    case 2:
    case "IP_DNS_TYPE_DHCP":
      return EthernetConfig_IpDnsType.IP_DNS_TYPE_DHCP;
    case -1:
    case "UNRECOGNIZED":
    default:
      return EthernetConfig_IpDnsType.UNRECOGNIZED;
  }
}

export function ethernetConfig_IpDnsTypeToJSON(object: EthernetConfig_IpDnsType): string {
  switch (object) {
    case EthernetConfig_IpDnsType.IP_DNS_TYPE_UNSPECIFIED:
      return "IP_DNS_TYPE_UNSPECIFIED";
    case EthernetConfig_IpDnsType.IP_DNS_TYPE_STATIC:
      return "IP_DNS_TYPE_STATIC";
    case EthernetConfig_IpDnsType.IP_DNS_TYPE_DHCP:
      return "IP_DNS_TYPE_DHCP";
    case EthernetConfig_IpDnsType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** APN配置 */
export interface ApnConfig {
  /** APN */
  apn: string;
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
  /** 其他参数 */
  params: ApnConfig_KeyValue[];
}

export interface ApnConfig_KeyValue {
  key: string;
  value: string;
}

/** WIFI记住 */
export interface WifiRemember {
  ssid: string;
  password: string;
  /** 是否自动连接 */
  autoConnect: boolean;
}

function createBaseHardwareSetting(): HardwareSetting {
  return {
    ethernet1Config: undefined,
    ethernet2Config: undefined,
    wifiConfig: undefined,
    mobileConfig: undefined,
    screenBrightness: undefined,
    screensaverTime: undefined,
    screenOffTime: undefined,
    bootPassword: undefined,
    adminPassword: undefined,
    downloadPassword: undefined,
    buzzerEnabled: undefined,
    ntpEnabled: undefined,
    ntpSyncServer: undefined,
    timezone: undefined,
  };
}

export const HardwareSetting: MessageFns<HardwareSetting> = {
  encode(message: HardwareSetting, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ethernet1Config !== undefined) {
      EthernetConfig.encode(message.ethernet1Config, writer.uint32(10).fork()).join();
    }
    if (message.ethernet2Config !== undefined) {
      EthernetConfig.encode(message.ethernet2Config, writer.uint32(18).fork()).join();
    }
    if (message.wifiConfig !== undefined) {
      EthernetConfig.encode(message.wifiConfig, writer.uint32(26).fork()).join();
    }
    if (message.mobileConfig !== undefined) {
      EthernetConfig.encode(message.mobileConfig, writer.uint32(34).fork()).join();
    }
    if (message.screenBrightness !== undefined) {
      writer.uint32(40).int32(message.screenBrightness);
    }
    if (message.screensaverTime !== undefined) {
      writer.uint32(48).int32(message.screensaverTime);
    }
    if (message.screenOffTime !== undefined) {
      writer.uint32(56).int32(message.screenOffTime);
    }
    if (message.bootPassword !== undefined) {
      writer.uint32(66).string(message.bootPassword);
    }
    if (message.adminPassword !== undefined) {
      writer.uint32(74).string(message.adminPassword);
    }
    if (message.downloadPassword !== undefined) {
      writer.uint32(82).string(message.downloadPassword);
    }
    if (message.buzzerEnabled !== undefined) {
      writer.uint32(88).bool(message.buzzerEnabled);
    }
    if (message.ntpEnabled !== undefined) {
      writer.uint32(96).bool(message.ntpEnabled);
    }
    if (message.ntpSyncServer !== undefined) {
      writer.uint32(106).string(message.ntpSyncServer);
    }
    if (message.timezone !== undefined) {
      writer.uint32(114).string(message.timezone);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HardwareSetting {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHardwareSetting();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.ethernet1Config = EthernetConfig.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.ethernet2Config = EthernetConfig.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.wifiConfig = EthernetConfig.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.mobileConfig = EthernetConfig.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.screenBrightness = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.screensaverTime = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.screenOffTime = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.bootPassword = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.adminPassword = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.downloadPassword = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.buzzerEnabled = reader.bool();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.ntpEnabled = reader.bool();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.ntpSyncServer = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.timezone = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): HardwareSetting {
    return {
      ethernet1Config: isSet(object.ethernet1Config) ? EthernetConfig.fromJSON(object.ethernet1Config) : undefined,
      ethernet2Config: isSet(object.ethernet2Config) ? EthernetConfig.fromJSON(object.ethernet2Config) : undefined,
      wifiConfig: isSet(object.wifiConfig) ? EthernetConfig.fromJSON(object.wifiConfig) : undefined,
      mobileConfig: isSet(object.mobileConfig) ? EthernetConfig.fromJSON(object.mobileConfig) : undefined,
      screenBrightness: isSet(object.screenBrightness) ? globalThis.Number(object.screenBrightness) : undefined,
      screensaverTime: isSet(object.screensaverTime) ? globalThis.Number(object.screensaverTime) : undefined,
      screenOffTime: isSet(object.screenOffTime) ? globalThis.Number(object.screenOffTime) : undefined,
      bootPassword: isSet(object.bootPassword) ? globalThis.String(object.bootPassword) : undefined,
      adminPassword: isSet(object.adminPassword) ? globalThis.String(object.adminPassword) : undefined,
      downloadPassword: isSet(object.downloadPassword) ? globalThis.String(object.downloadPassword) : undefined,
      buzzerEnabled: isSet(object.buzzerEnabled) ? globalThis.Boolean(object.buzzerEnabled) : undefined,
      ntpEnabled: isSet(object.ntpEnabled) ? globalThis.Boolean(object.ntpEnabled) : undefined,
      ntpSyncServer: isSet(object.ntpSyncServer) ? globalThis.String(object.ntpSyncServer) : undefined,
      timezone: isSet(object.timezone) ? globalThis.String(object.timezone) : undefined,
    };
  },

  toJSON(message: HardwareSetting): unknown {
    const obj: any = {};
    if (message.ethernet1Config !== undefined) {
      obj.ethernet1Config = EthernetConfig.toJSON(message.ethernet1Config);
    }
    if (message.ethernet2Config !== undefined) {
      obj.ethernet2Config = EthernetConfig.toJSON(message.ethernet2Config);
    }
    if (message.wifiConfig !== undefined) {
      obj.wifiConfig = EthernetConfig.toJSON(message.wifiConfig);
    }
    if (message.mobileConfig !== undefined) {
      obj.mobileConfig = EthernetConfig.toJSON(message.mobileConfig);
    }
    if (message.screenBrightness !== undefined) {
      obj.screenBrightness = Math.round(message.screenBrightness);
    }
    if (message.screensaverTime !== undefined) {
      obj.screensaverTime = Math.round(message.screensaverTime);
    }
    if (message.screenOffTime !== undefined) {
      obj.screenOffTime = Math.round(message.screenOffTime);
    }
    if (message.bootPassword !== undefined) {
      obj.bootPassword = message.bootPassword;
    }
    if (message.adminPassword !== undefined) {
      obj.adminPassword = message.adminPassword;
    }
    if (message.downloadPassword !== undefined) {
      obj.downloadPassword = message.downloadPassword;
    }
    if (message.buzzerEnabled !== undefined) {
      obj.buzzerEnabled = message.buzzerEnabled;
    }
    if (message.ntpEnabled !== undefined) {
      obj.ntpEnabled = message.ntpEnabled;
    }
    if (message.ntpSyncServer !== undefined) {
      obj.ntpSyncServer = message.ntpSyncServer;
    }
    if (message.timezone !== undefined) {
      obj.timezone = message.timezone;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<HardwareSetting>, I>>(base?: I): HardwareSetting {
    return HardwareSetting.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HardwareSetting>, I>>(object: I): HardwareSetting {
    const message = createBaseHardwareSetting();
    message.ethernet1Config = (object.ethernet1Config !== undefined && object.ethernet1Config !== null)
      ? EthernetConfig.fromPartial(object.ethernet1Config)
      : undefined;
    message.ethernet2Config = (object.ethernet2Config !== undefined && object.ethernet2Config !== null)
      ? EthernetConfig.fromPartial(object.ethernet2Config)
      : undefined;
    message.wifiConfig = (object.wifiConfig !== undefined && object.wifiConfig !== null)
      ? EthernetConfig.fromPartial(object.wifiConfig)
      : undefined;
    message.mobileConfig = (object.mobileConfig !== undefined && object.mobileConfig !== null)
      ? EthernetConfig.fromPartial(object.mobileConfig)
      : undefined;
    message.screenBrightness = object.screenBrightness ?? undefined;
    message.screensaverTime = object.screensaverTime ?? undefined;
    message.screenOffTime = object.screenOffTime ?? undefined;
    message.bootPassword = object.bootPassword ?? undefined;
    message.adminPassword = object.adminPassword ?? undefined;
    message.downloadPassword = object.downloadPassword ?? undefined;
    message.buzzerEnabled = object.buzzerEnabled ?? undefined;
    message.ntpEnabled = object.ntpEnabled ?? undefined;
    message.ntpSyncServer = object.ntpSyncServer ?? undefined;
    message.timezone = object.timezone ?? undefined;
    return message;
  },
};

function createBaseEthernetConfig(): EthernetConfig {
  return {
    ipType: 0,
    ip: undefined,
    subnetMask: undefined,
    gateway: undefined,
    dnsType: 0,
    dns1: undefined,
    dns2: undefined,
    disable: undefined,
    wifiRemembers: [],
    apn: undefined,
  };
}

export const EthernetConfig: MessageFns<EthernetConfig> = {
  encode(message: EthernetConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ipType !== 0) {
      writer.uint32(8).int32(message.ipType);
    }
    if (message.ip !== undefined) {
      writer.uint32(18).string(message.ip);
    }
    if (message.subnetMask !== undefined) {
      writer.uint32(26).string(message.subnetMask);
    }
    if (message.gateway !== undefined) {
      writer.uint32(34).string(message.gateway);
    }
    if (message.dnsType !== 0) {
      writer.uint32(40).int32(message.dnsType);
    }
    if (message.dns1 !== undefined) {
      writer.uint32(50).string(message.dns1);
    }
    if (message.dns2 !== undefined) {
      writer.uint32(58).string(message.dns2);
    }
    if (message.disable !== undefined) {
      writer.uint32(64).bool(message.disable);
    }
    for (const v of message.wifiRemembers) {
      WifiRemember.encode(v!, writer.uint32(74).fork()).join();
    }
    if (message.apn !== undefined) {
      ApnConfig.encode(message.apn, writer.uint32(82).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EthernetConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEthernetConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.ipType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.ip = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subnetMask = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.gateway = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.dnsType = reader.int32() as any;
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.dns1 = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.dns2 = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.disable = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.wifiRemembers.push(WifiRemember.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.apn = ApnConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EthernetConfig {
    return {
      ipType: isSet(object.ipType) ? ethernetConfig_IpDnsTypeFromJSON(object.ipType) : 0,
      ip: isSet(object.ip) ? globalThis.String(object.ip) : undefined,
      subnetMask: isSet(object.subnetMask) ? globalThis.String(object.subnetMask) : undefined,
      gateway: isSet(object.gateway) ? globalThis.String(object.gateway) : undefined,
      dnsType: isSet(object.dnsType) ? ethernetConfig_IpDnsTypeFromJSON(object.dnsType) : 0,
      dns1: isSet(object.dns1) ? globalThis.String(object.dns1) : undefined,
      dns2: isSet(object.dns2) ? globalThis.String(object.dns2) : undefined,
      disable: isSet(object.disable) ? globalThis.Boolean(object.disable) : undefined,
      wifiRemembers: globalThis.Array.isArray(object?.wifiRemembers)
        ? object.wifiRemembers.map((e: any) => WifiRemember.fromJSON(e))
        : [],
      apn: isSet(object.apn) ? ApnConfig.fromJSON(object.apn) : undefined,
    };
  },

  toJSON(message: EthernetConfig): unknown {
    const obj: any = {};
    if (message.ipType !== 0) {
      obj.ipType = ethernetConfig_IpDnsTypeToJSON(message.ipType);
    }
    if (message.ip !== undefined) {
      obj.ip = message.ip;
    }
    if (message.subnetMask !== undefined) {
      obj.subnetMask = message.subnetMask;
    }
    if (message.gateway !== undefined) {
      obj.gateway = message.gateway;
    }
    if (message.dnsType !== 0) {
      obj.dnsType = ethernetConfig_IpDnsTypeToJSON(message.dnsType);
    }
    if (message.dns1 !== undefined) {
      obj.dns1 = message.dns1;
    }
    if (message.dns2 !== undefined) {
      obj.dns2 = message.dns2;
    }
    if (message.disable !== undefined) {
      obj.disable = message.disable;
    }
    if (message.wifiRemembers?.length) {
      obj.wifiRemembers = message.wifiRemembers.map((e) => WifiRemember.toJSON(e));
    }
    if (message.apn !== undefined) {
      obj.apn = ApnConfig.toJSON(message.apn);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EthernetConfig>, I>>(base?: I): EthernetConfig {
    return EthernetConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EthernetConfig>, I>>(object: I): EthernetConfig {
    const message = createBaseEthernetConfig();
    message.ipType = object.ipType ?? 0;
    message.ip = object.ip ?? undefined;
    message.subnetMask = object.subnetMask ?? undefined;
    message.gateway = object.gateway ?? undefined;
    message.dnsType = object.dnsType ?? 0;
    message.dns1 = object.dns1 ?? undefined;
    message.dns2 = object.dns2 ?? undefined;
    message.disable = object.disable ?? undefined;
    message.wifiRemembers = object.wifiRemembers?.map((e) => WifiRemember.fromPartial(e)) || [];
    message.apn = (object.apn !== undefined && object.apn !== null) ? ApnConfig.fromPartial(object.apn) : undefined;
    return message;
  },
};

function createBaseApnConfig(): ApnConfig {
  return { apn: "", username: "", password: "", params: [] };
}

export const ApnConfig: MessageFns<ApnConfig> = {
  encode(message: ApnConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.apn !== "") {
      writer.uint32(10).string(message.apn);
    }
    if (message.username !== "") {
      writer.uint32(18).string(message.username);
    }
    if (message.password !== "") {
      writer.uint32(26).string(message.password);
    }
    for (const v of message.params) {
      ApnConfig_KeyValue.encode(v!, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ApnConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseApnConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.apn = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.username = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.password = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.params.push(ApnConfig_KeyValue.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ApnConfig {
    return {
      apn: isSet(object.apn) ? globalThis.String(object.apn) : "",
      username: isSet(object.username) ? globalThis.String(object.username) : "",
      password: isSet(object.password) ? globalThis.String(object.password) : "",
      params: globalThis.Array.isArray(object?.params)
        ? object.params.map((e: any) => ApnConfig_KeyValue.fromJSON(e))
        : [],
    };
  },

  toJSON(message: ApnConfig): unknown {
    const obj: any = {};
    if (message.apn !== "") {
      obj.apn = message.apn;
    }
    if (message.username !== "") {
      obj.username = message.username;
    }
    if (message.password !== "") {
      obj.password = message.password;
    }
    if (message.params?.length) {
      obj.params = message.params.map((e) => ApnConfig_KeyValue.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ApnConfig>, I>>(base?: I): ApnConfig {
    return ApnConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApnConfig>, I>>(object: I): ApnConfig {
    const message = createBaseApnConfig();
    message.apn = object.apn ?? "";
    message.username = object.username ?? "";
    message.password = object.password ?? "";
    message.params = object.params?.map((e) => ApnConfig_KeyValue.fromPartial(e)) || [];
    return message;
  },
};

function createBaseApnConfig_KeyValue(): ApnConfig_KeyValue {
  return { key: "", value: "" };
}

export const ApnConfig_KeyValue: MessageFns<ApnConfig_KeyValue> = {
  encode(message: ApnConfig_KeyValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ApnConfig_KeyValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseApnConfig_KeyValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ApnConfig_KeyValue {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: ApnConfig_KeyValue): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ApnConfig_KeyValue>, I>>(base?: I): ApnConfig_KeyValue {
    return ApnConfig_KeyValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApnConfig_KeyValue>, I>>(object: I): ApnConfig_KeyValue {
    const message = createBaseApnConfig_KeyValue();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseWifiRemember(): WifiRemember {
  return { ssid: "", password: "", autoConnect: false };
}

export const WifiRemember: MessageFns<WifiRemember> = {
  encode(message: WifiRemember, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ssid !== "") {
      writer.uint32(10).string(message.ssid);
    }
    if (message.password !== "") {
      writer.uint32(18).string(message.password);
    }
    if (message.autoConnect !== false) {
      writer.uint32(24).bool(message.autoConnect);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WifiRemember {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWifiRemember();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.ssid = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.password = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.autoConnect = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WifiRemember {
    return {
      ssid: isSet(object.ssid) ? globalThis.String(object.ssid) : "",
      password: isSet(object.password) ? globalThis.String(object.password) : "",
      autoConnect: isSet(object.autoConnect) ? globalThis.Boolean(object.autoConnect) : false,
    };
  },

  toJSON(message: WifiRemember): unknown {
    const obj: any = {};
    if (message.ssid !== "") {
      obj.ssid = message.ssid;
    }
    if (message.password !== "") {
      obj.password = message.password;
    }
    if (message.autoConnect !== false) {
      obj.autoConnect = message.autoConnect;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WifiRemember>, I>>(base?: I): WifiRemember {
    return WifiRemember.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WifiRemember>, I>>(object: I): WifiRemember {
    const message = createBaseWifiRemember();
    message.ssid = object.ssid ?? "";
    message.password = object.password ?? "";
    message.autoConnect = object.autoConnect ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
