// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/control.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "znd.project.v1";

/** 控制端位置 */
export enum TerminalLocation {
  /** TERMINAL_LOCATION_UNSPECIFIED - 未设置 */
  TERMINAL_LOCATION_UNSPECIFIED = 0,
  /** TERMINAL_LOCATION_LOCAL - 本机 */
  TERMINAL_LOCATION_LOCAL = 1,
  /** TERMINAL_LOCATION_LAN - 局域网 */
  TERMINAL_LOCATION_LAN = 2,
  /** TERMINAL_LOCATION_WAN - 互联网 */
  TERMINAL_LOCATION_WAN = 3,
  /** TERMINAL_LOCATION_LAN_GUEST - 局域网(匿名访客) */
  TERMINAL_LOCATION_LAN_GUEST = 4,
  /** TERMINAL_LOCATION_LAN_USER - 局域网(已登录用户) */
  TERMINAL_LOCATION_LAN_USER = 5,
  /** TERMINAL_LOCATION_WAN_GUEST - 互联网(匿名访客) */
  TERMINAL_LOCATION_WAN_GUEST = 6,
  /** TERMINAL_LOCATION_WAN_USER - 互联网(已登录用户) */
  TERMINAL_LOCATION_WAN_USER = 7,
  UNRECOGNIZED = -1,
}

export function terminalLocationFromJSON(object: any): TerminalLocation {
  switch (object) {
    case 0:
    case "TERMINAL_LOCATION_UNSPECIFIED":
      return TerminalLocation.TERMINAL_LOCATION_UNSPECIFIED;
    case 1:
    case "TERMINAL_LOCATION_LOCAL":
      return TerminalLocation.TERMINAL_LOCATION_LOCAL;
    case 2:
    case "TERMINAL_LOCATION_LAN":
      return TerminalLocation.TERMINAL_LOCATION_LAN;
    case 3:
    case "TERMINAL_LOCATION_WAN":
      return TerminalLocation.TERMINAL_LOCATION_WAN;
    case 4:
    case "TERMINAL_LOCATION_LAN_GUEST":
      return TerminalLocation.TERMINAL_LOCATION_LAN_GUEST;
    case 5:
    case "TERMINAL_LOCATION_LAN_USER":
      return TerminalLocation.TERMINAL_LOCATION_LAN_USER;
    case 6:
    case "TERMINAL_LOCATION_WAN_GUEST":
      return TerminalLocation.TERMINAL_LOCATION_WAN_GUEST;
    case 7:
    case "TERMINAL_LOCATION_WAN_USER":
      return TerminalLocation.TERMINAL_LOCATION_WAN_USER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TerminalLocation.UNRECOGNIZED;
  }
}

export function terminalLocationToJSON(object: TerminalLocation): string {
  switch (object) {
    case TerminalLocation.TERMINAL_LOCATION_UNSPECIFIED:
      return "TERMINAL_LOCATION_UNSPECIFIED";
    case TerminalLocation.TERMINAL_LOCATION_LOCAL:
      return "TERMINAL_LOCATION_LOCAL";
    case TerminalLocation.TERMINAL_LOCATION_LAN:
      return "TERMINAL_LOCATION_LAN";
    case TerminalLocation.TERMINAL_LOCATION_WAN:
      return "TERMINAL_LOCATION_WAN";
    case TerminalLocation.TERMINAL_LOCATION_LAN_GUEST:
      return "TERMINAL_LOCATION_LAN_GUEST";
    case TerminalLocation.TERMINAL_LOCATION_LAN_USER:
      return "TERMINAL_LOCATION_LAN_USER";
    case TerminalLocation.TERMINAL_LOCATION_WAN_GUEST:
      return "TERMINAL_LOCATION_WAN_GUEST";
    case TerminalLocation.TERMINAL_LOCATION_WAN_USER:
      return "TERMINAL_LOCATION_WAN_USER";
    case TerminalLocation.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 控制端类型 */
export enum TerminalType {
  /** TERMINAL_TYPE_UNSPECIFIED - 未设置 */
  TERMINAL_TYPE_UNSPECIFIED = 0,
  /** TERMINAL_TYPE_LOCAL - 本机 */
  TERMINAL_TYPE_LOCAL = 1,
  /** TERMINAL_TYPE_APP - APP */
  TERMINAL_TYPE_APP = 2,
  /** TERMINAL_TYPE_PC - PC */
  TERMINAL_TYPE_PC = 3,
  /** TERMINAL_TYPE_HMI - 其他HMI */
  TERMINAL_TYPE_HMI = 4,
  /** TERMINAL_TYPE_TV - 电视大屏 */
  TERMINAL_TYPE_TV = 5,
  UNRECOGNIZED = -1,
}

export function terminalTypeFromJSON(object: any): TerminalType {
  switch (object) {
    case 0:
    case "TERMINAL_TYPE_UNSPECIFIED":
      return TerminalType.TERMINAL_TYPE_UNSPECIFIED;
    case 1:
    case "TERMINAL_TYPE_LOCAL":
      return TerminalType.TERMINAL_TYPE_LOCAL;
    case 2:
    case "TERMINAL_TYPE_APP":
      return TerminalType.TERMINAL_TYPE_APP;
    case 3:
    case "TERMINAL_TYPE_PC":
      return TerminalType.TERMINAL_TYPE_PC;
    case 4:
    case "TERMINAL_TYPE_HMI":
      return TerminalType.TERMINAL_TYPE_HMI;
    case 5:
    case "TERMINAL_TYPE_TV":
      return TerminalType.TERMINAL_TYPE_TV;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TerminalType.UNRECOGNIZED;
  }
}

export function terminalTypeToJSON(object: TerminalType): string {
  switch (object) {
    case TerminalType.TERMINAL_TYPE_UNSPECIFIED:
      return "TERMINAL_TYPE_UNSPECIFIED";
    case TerminalType.TERMINAL_TYPE_LOCAL:
      return "TERMINAL_TYPE_LOCAL";
    case TerminalType.TERMINAL_TYPE_APP:
      return "TERMINAL_TYPE_APP";
    case TerminalType.TERMINAL_TYPE_PC:
      return "TERMINAL_TYPE_PC";
    case TerminalType.TERMINAL_TYPE_HMI:
      return "TERMINAL_TYPE_HMI";
    case TerminalType.TERMINAL_TYPE_TV:
      return "TERMINAL_TYPE_TV";
    case TerminalType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 禁用外观 */
export enum DisableWidgetMode {
  /** DISABLE_WIDGET_MODE_UNSPECIFIED - 不改变 */
  DISABLE_WIDGET_MODE_UNSPECIFIED = 0,
  /** DISABLE_WIDGET_MODE_HIDE - 禁用时隐藏 */
  DISABLE_WIDGET_MODE_HIDE = 1,
  /** DISABLE_WIDGET_MODE_NORMAL - 禁用但不变灰 */
  DISABLE_WIDGET_MODE_NORMAL = 2,
  /** DISABLE_WIDGET_MODE_GRAY - 禁用变灰 */
  DISABLE_WIDGET_MODE_GRAY = 3,
  /** DISABLE_WIDGET_MODE_ICON - 禁用显示禁用图标 */
  DISABLE_WIDGET_MODE_ICON = 4,
  /** DISABLE_WIDGET_MODE_GRAY_ICON - 禁用变灰且显示禁用图标 */
  DISABLE_WIDGET_MODE_GRAY_ICON = 5,
  UNRECOGNIZED = -1,
}

export function disableWidgetModeFromJSON(object: any): DisableWidgetMode {
  switch (object) {
    case 0:
    case "DISABLE_WIDGET_MODE_UNSPECIFIED":
      return DisableWidgetMode.DISABLE_WIDGET_MODE_UNSPECIFIED;
    case 1:
    case "DISABLE_WIDGET_MODE_HIDE":
      return DisableWidgetMode.DISABLE_WIDGET_MODE_HIDE;
    case 2:
    case "DISABLE_WIDGET_MODE_NORMAL":
      return DisableWidgetMode.DISABLE_WIDGET_MODE_NORMAL;
    case 3:
    case "DISABLE_WIDGET_MODE_GRAY":
      return DisableWidgetMode.DISABLE_WIDGET_MODE_GRAY;
    case 4:
    case "DISABLE_WIDGET_MODE_ICON":
      return DisableWidgetMode.DISABLE_WIDGET_MODE_ICON;
    case 5:
    case "DISABLE_WIDGET_MODE_GRAY_ICON":
      return DisableWidgetMode.DISABLE_WIDGET_MODE_GRAY_ICON;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DisableWidgetMode.UNRECOGNIZED;
  }
}

export function disableWidgetModeToJSON(object: DisableWidgetMode): string {
  switch (object) {
    case DisableWidgetMode.DISABLE_WIDGET_MODE_UNSPECIFIED:
      return "DISABLE_WIDGET_MODE_UNSPECIFIED";
    case DisableWidgetMode.DISABLE_WIDGET_MODE_HIDE:
      return "DISABLE_WIDGET_MODE_HIDE";
    case DisableWidgetMode.DISABLE_WIDGET_MODE_NORMAL:
      return "DISABLE_WIDGET_MODE_NORMAL";
    case DisableWidgetMode.DISABLE_WIDGET_MODE_GRAY:
      return "DISABLE_WIDGET_MODE_GRAY";
    case DisableWidgetMode.DISABLE_WIDGET_MODE_ICON:
      return "DISABLE_WIDGET_MODE_ICON";
    case DisableWidgetMode.DISABLE_WIDGET_MODE_GRAY_ICON:
      return "DISABLE_WIDGET_MODE_GRAY_ICON";
    case DisableWidgetMode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 启用控制 */
export interface EnableControl {
  /** 用权限来控制是否启用(最大只到255，用byte或uint8) */
  permissionBitsU8: number[];
  /** 根据控制端所在位置来判断是否启用（为空表示不限制，有值表示符合了才能使用） */
  terminalLocation: TerminalLocation[];
  /** 根据控制端类型来判断是否启用 */
  terminalTypes: TerminalType[];
  /** 禁用外观 */
  disableMode: DisableWidgetMode;
  /** 禁用时点击弹出提示窗口ID(0表示不弹出) */
  disablePromptPageIdU16: number;
}

function createBaseEnableControl(): EnableControl {
  return { permissionBitsU8: [], terminalLocation: [], terminalTypes: [], disableMode: 0, disablePromptPageIdU16: 0 };
}

export const EnableControl: MessageFns<EnableControl> = {
  encode(message: EnableControl, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(18).fork();
    for (const v of message.permissionBitsU8) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(26).fork();
    for (const v of message.terminalLocation) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(34).fork();
    for (const v of message.terminalTypes) {
      writer.int32(v);
    }
    writer.join();
    if (message.disableMode !== 0) {
      writer.uint32(40).int32(message.disableMode);
    }
    if (message.disablePromptPageIdU16 !== 0) {
      writer.uint32(48).uint32(message.disablePromptPageIdU16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EnableControl {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEnableControl();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag === 16) {
            message.permissionBitsU8.push(reader.uint32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.permissionBitsU8.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag === 24) {
            message.terminalLocation.push(reader.int32() as any);

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.terminalLocation.push(reader.int32() as any);
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag === 32) {
            message.terminalTypes.push(reader.int32() as any);

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.terminalTypes.push(reader.int32() as any);
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.disableMode = reader.int32() as any;
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.disablePromptPageIdU16 = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EnableControl {
    return {
      permissionBitsU8: globalThis.Array.isArray(object?.permissionBitsU8)
        ? object.permissionBitsU8.map((e: any) => globalThis.Number(e))
        : [],
      terminalLocation: globalThis.Array.isArray(object?.terminalLocation)
        ? object.terminalLocation.map((e: any) => terminalLocationFromJSON(e))
        : [],
      terminalTypes: globalThis.Array.isArray(object?.terminalTypes)
        ? object.terminalTypes.map((e: any) => terminalTypeFromJSON(e))
        : [],
      disableMode: isSet(object.disableMode) ? disableWidgetModeFromJSON(object.disableMode) : 0,
      disablePromptPageIdU16: isSet(object.disablePromptPageIdU16)
        ? globalThis.Number(object.disablePromptPageIdU16)
        : 0,
    };
  },

  toJSON(message: EnableControl): unknown {
    const obj: any = {};
    if (message.permissionBitsU8?.length) {
      obj.permissionBitsU8 = message.permissionBitsU8.map((e) => Math.round(e));
    }
    if (message.terminalLocation?.length) {
      obj.terminalLocation = message.terminalLocation.map((e) => terminalLocationToJSON(e));
    }
    if (message.terminalTypes?.length) {
      obj.terminalTypes = message.terminalTypes.map((e) => terminalTypeToJSON(e));
    }
    if (message.disableMode !== 0) {
      obj.disableMode = disableWidgetModeToJSON(message.disableMode);
    }
    if (message.disablePromptPageIdU16 !== 0) {
      obj.disablePromptPageIdU16 = Math.round(message.disablePromptPageIdU16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EnableControl>, I>>(base?: I): EnableControl {
    return EnableControl.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnableControl>, I>>(object: I): EnableControl {
    const message = createBaseEnableControl();
    message.permissionBitsU8 = object.permissionBitsU8?.map((e) => e) || [];
    message.terminalLocation = object.terminalLocation?.map((e) => e) || [];
    message.terminalTypes = object.terminalTypes?.map((e) => e) || [];
    message.disableMode = object.disableMode ?? 0;
    message.disablePromptPageIdU16 = object.disablePromptPageIdU16 ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
