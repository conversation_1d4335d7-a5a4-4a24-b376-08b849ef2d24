// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/dynamic.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "znd.project.v1";

export enum BlinkType {
  BLINK_TYPE_UNSPECIFIED = 0,
  /** BLINK_TYPE_CHANGE_VISIBLE - 显示状态切换 */
  BLINK_TYPE_CHANGE_VISIBLE = 1,
  /** BLINK_TYPE_CHANGE_STYLE - 颜色变化 */
  BLINK_TYPE_CHANGE_STYLE = 2,
  UNRECOGNIZED = -1,
}

export function blinkTypeFromJSON(object: any): BlinkType {
  switch (object) {
    case 0:
    case "BLINK_TYPE_UNSPECIFIED":
      return BlinkType.BLINK_TYPE_UNSPECIFIED;
    case 1:
    case "BLINK_TYPE_CHANGE_VISIBLE":
      return BlinkType.BLINK_TYPE_CHANGE_VISIBLE;
    case 2:
    case "BLINK_TYPE_CHANGE_STYLE":
      return BlinkType.BLINK_TYPE_CHANGE_STYLE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BlinkType.UNRECOGNIZED;
  }
}

export function blinkTypeToJSON(object: BlinkType): string {
  switch (object) {
    case BlinkType.BLINK_TYPE_UNSPECIFIED:
      return "BLINK_TYPE_UNSPECIFIED";
    case BlinkType.BLINK_TYPE_CHANGE_VISIBLE:
      return "BLINK_TYPE_CHANGE_VISIBLE";
    case BlinkType.BLINK_TYPE_CHANGE_STYLE:
      return "BLINK_TYPE_CHANGE_STYLE";
    case BlinkType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 缩放方向 */
export enum ScaleDirectionType {
  SCALE_DIRECTION_TYPE_UNSPECIFIED = 0,
  /** SCALE_DIRECTION_TYPE_FULL - 全方向 */
  SCALE_DIRECTION_TYPE_FULL = 1,
  /** SCALE_DIRECTION_TYPE_HORIZONTAL - 水平 */
  SCALE_DIRECTION_TYPE_HORIZONTAL = 2,
  /** SCALE_DIRECTION_TYPE_VERTICAL - 垂直 */
  SCALE_DIRECTION_TYPE_VERTICAL = 3,
  /** SCALE_DIRECTION_TYPE_LEFT - 左 */
  SCALE_DIRECTION_TYPE_LEFT = 4,
  /** SCALE_DIRECTION_TYPE_RIGHT - 右 */
  SCALE_DIRECTION_TYPE_RIGHT = 5,
  /** SCALE_DIRECTION_TYPE_TOP - 上 */
  SCALE_DIRECTION_TYPE_TOP = 6,
  /** SCALE_DIRECTION_TYPE_BOTTOM - 下 */
  SCALE_DIRECTION_TYPE_BOTTOM = 7,
  UNRECOGNIZED = -1,
}

export function scaleDirectionTypeFromJSON(object: any): ScaleDirectionType {
  switch (object) {
    case 0:
    case "SCALE_DIRECTION_TYPE_UNSPECIFIED":
      return ScaleDirectionType.SCALE_DIRECTION_TYPE_UNSPECIFIED;
    case 1:
    case "SCALE_DIRECTION_TYPE_FULL":
      return ScaleDirectionType.SCALE_DIRECTION_TYPE_FULL;
    case 2:
    case "SCALE_DIRECTION_TYPE_HORIZONTAL":
      return ScaleDirectionType.SCALE_DIRECTION_TYPE_HORIZONTAL;
    case 3:
    case "SCALE_DIRECTION_TYPE_VERTICAL":
      return ScaleDirectionType.SCALE_DIRECTION_TYPE_VERTICAL;
    case 4:
    case "SCALE_DIRECTION_TYPE_LEFT":
      return ScaleDirectionType.SCALE_DIRECTION_TYPE_LEFT;
    case 5:
    case "SCALE_DIRECTION_TYPE_RIGHT":
      return ScaleDirectionType.SCALE_DIRECTION_TYPE_RIGHT;
    case 6:
    case "SCALE_DIRECTION_TYPE_TOP":
      return ScaleDirectionType.SCALE_DIRECTION_TYPE_TOP;
    case 7:
    case "SCALE_DIRECTION_TYPE_BOTTOM":
      return ScaleDirectionType.SCALE_DIRECTION_TYPE_BOTTOM;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ScaleDirectionType.UNRECOGNIZED;
  }
}

export function scaleDirectionTypeToJSON(object: ScaleDirectionType): string {
  switch (object) {
    case ScaleDirectionType.SCALE_DIRECTION_TYPE_UNSPECIFIED:
      return "SCALE_DIRECTION_TYPE_UNSPECIFIED";
    case ScaleDirectionType.SCALE_DIRECTION_TYPE_FULL:
      return "SCALE_DIRECTION_TYPE_FULL";
    case ScaleDirectionType.SCALE_DIRECTION_TYPE_HORIZONTAL:
      return "SCALE_DIRECTION_TYPE_HORIZONTAL";
    case ScaleDirectionType.SCALE_DIRECTION_TYPE_VERTICAL:
      return "SCALE_DIRECTION_TYPE_VERTICAL";
    case ScaleDirectionType.SCALE_DIRECTION_TYPE_LEFT:
      return "SCALE_DIRECTION_TYPE_LEFT";
    case ScaleDirectionType.SCALE_DIRECTION_TYPE_RIGHT:
      return "SCALE_DIRECTION_TYPE_RIGHT";
    case ScaleDirectionType.SCALE_DIRECTION_TYPE_TOP:
      return "SCALE_DIRECTION_TYPE_TOP";
    case ScaleDirectionType.SCALE_DIRECTION_TYPE_BOTTOM:
      return "SCALE_DIRECTION_TYPE_BOTTOM";
    case ScaleDirectionType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 缩放类型 */
export enum ScaleType {
  SCALE_TYPE_UNSPECIFIED = 0,
  /** SCALE_TYPE_ZOOM - 缩放 */
  SCALE_TYPE_ZOOM = 1,
  /** SCALE_TYPE_CUT - 裁剪 */
  SCALE_TYPE_CUT = 2,
  UNRECOGNIZED = -1,
}

export function scaleTypeFromJSON(object: any): ScaleType {
  switch (object) {
    case 0:
    case "SCALE_TYPE_UNSPECIFIED":
      return ScaleType.SCALE_TYPE_UNSPECIFIED;
    case 1:
    case "SCALE_TYPE_ZOOM":
      return ScaleType.SCALE_TYPE_ZOOM;
    case 2:
    case "SCALE_TYPE_CUT":
      return ScaleType.SCALE_TYPE_CUT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ScaleType.UNRECOGNIZED;
  }
}

export function scaleTypeToJSON(object: ScaleType): string {
  switch (object) {
    case ScaleType.SCALE_TYPE_UNSPECIFIED:
      return "SCALE_TYPE_UNSPECIFIED";
    case ScaleType.SCALE_TYPE_ZOOM:
      return "SCALE_TYPE_ZOOM";
    case ScaleType.SCALE_TYPE_CUT:
      return "SCALE_TYPE_CUT";
    case ScaleType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 闪烁 */
export interface Blink {
  /** 间隔时间，单位毫秒, 未设置则不闪烁 */
  intervalTime?:
    | number
    | undefined;
  /** 闪烁类型 */
  blinkType?:
    | BlinkType
    | undefined;
  /** 闪烁样式 */
  blinkStyle?: BlinkStyle | undefined;
}

export interface BlinkStyle {
  backgroundColor?: number | undefined;
  borderColor?: number | undefined;
  textColor?: number | undefined;
}

function createBaseBlink(): Blink {
  return { intervalTime: undefined, blinkType: undefined, blinkStyle: undefined };
}

export const Blink: MessageFns<Blink> = {
  encode(message: Blink, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.intervalTime !== undefined) {
      writer.uint32(8).int32(message.intervalTime);
    }
    if (message.blinkType !== undefined) {
      writer.uint32(16).int32(message.blinkType);
    }
    if (message.blinkStyle !== undefined) {
      BlinkStyle.encode(message.blinkStyle, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Blink {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBlink();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.intervalTime = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.blinkType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.blinkStyle = BlinkStyle.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Blink {
    return {
      intervalTime: isSet(object.intervalTime) ? globalThis.Number(object.intervalTime) : undefined,
      blinkType: isSet(object.blinkType) ? blinkTypeFromJSON(object.blinkType) : undefined,
      blinkStyle: isSet(object.blinkStyle) ? BlinkStyle.fromJSON(object.blinkStyle) : undefined,
    };
  },

  toJSON(message: Blink): unknown {
    const obj: any = {};
    if (message.intervalTime !== undefined) {
      obj.intervalTime = Math.round(message.intervalTime);
    }
    if (message.blinkType !== undefined) {
      obj.blinkType = blinkTypeToJSON(message.blinkType);
    }
    if (message.blinkStyle !== undefined) {
      obj.blinkStyle = BlinkStyle.toJSON(message.blinkStyle);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Blink>, I>>(base?: I): Blink {
    return Blink.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Blink>, I>>(object: I): Blink {
    const message = createBaseBlink();
    message.intervalTime = object.intervalTime ?? undefined;
    message.blinkType = object.blinkType ?? undefined;
    message.blinkStyle = (object.blinkStyle !== undefined && object.blinkStyle !== null)
      ? BlinkStyle.fromPartial(object.blinkStyle)
      : undefined;
    return message;
  },
};

function createBaseBlinkStyle(): BlinkStyle {
  return { backgroundColor: undefined, borderColor: undefined, textColor: undefined };
}

export const BlinkStyle: MessageFns<BlinkStyle> = {
  encode(message: BlinkStyle, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.backgroundColor !== undefined) {
      writer.uint32(8).uint32(message.backgroundColor);
    }
    if (message.borderColor !== undefined) {
      writer.uint32(16).uint32(message.borderColor);
    }
    if (message.textColor !== undefined) {
      writer.uint32(24).uint32(message.textColor);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BlinkStyle {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBlinkStyle();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.backgroundColor = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.borderColor = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.textColor = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BlinkStyle {
    return {
      backgroundColor: isSet(object.backgroundColor) ? globalThis.Number(object.backgroundColor) : undefined,
      borderColor: isSet(object.borderColor) ? globalThis.Number(object.borderColor) : undefined,
      textColor: isSet(object.textColor) ? globalThis.Number(object.textColor) : undefined,
    };
  },

  toJSON(message: BlinkStyle): unknown {
    const obj: any = {};
    if (message.backgroundColor !== undefined) {
      obj.backgroundColor = Math.round(message.backgroundColor);
    }
    if (message.borderColor !== undefined) {
      obj.borderColor = Math.round(message.borderColor);
    }
    if (message.textColor !== undefined) {
      obj.textColor = Math.round(message.textColor);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BlinkStyle>, I>>(base?: I): BlinkStyle {
    return BlinkStyle.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BlinkStyle>, I>>(object: I): BlinkStyle {
    const message = createBaseBlinkStyle();
    message.backgroundColor = object.backgroundColor ?? undefined;
    message.borderColor = object.borderColor ?? undefined;
    message.textColor = object.textColor ?? undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
