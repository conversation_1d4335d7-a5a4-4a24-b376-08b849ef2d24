syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
// import "znd/project/v1/position.proto";
import "znd/project/v1/common.proto";

//工程的语言和字体配置
message ProjectStyleConfig {
    //语言列表
    repeated Language languages = 1;
    //默认语言(语言列表数组下标,从0开始)
    uint32 default_language_no_u8 = 2;
    // 主题数组,数组下标表示主题号(不超过255)
    repeated Theme themes = 3;
    //默认主题,指的是themes数组的下标,默认为0
    uint32 default_theme_no_u8 = 4;
    // 颜色变量的存储(id不超过255个,用u8)
    map<uint32, ColorVariable> color_variables = 5;
    // 主题和颜色变量值的对应(key用u16,key是用主题号+颜色变量id,高位主题号,低位颜色变量id)
    map<uint32, uint32> theme_color_values = 6;
    //key用u8,样式ID
    map<uint32, StyleTemplate> style_templates = 7;
    //元件默认样式(设计端使用)
    uint32 default_style_id_u8 = 8;
    //字体定义,key是字体id, uint8
    map<uint32, FontDefinition> font_definitions = 9;
    //默认字体名称(设计端使用)
    string default_font_name = 10;
    //默认样式属性
    StyleProperties default_style_properties = 11;
    //字体文件,key是字体文件ID(Uint8)，设计端用
    map<uint32, Font> font_files = 12;
    //给运行端用,字体对应的字体文件ID,key是uint16,用(FontDefinition的id+LanguageType)拼接,value是字体文件ID(Uint8)
    map<uint32, uint32> font_file_map = 13;
}


// 语言类型
enum LanguageType {
    LANGUAGE_TYPE_UNSPECIFIED = 0;
    //英语
    LANGUAGE_TYPE_ENGLISH = 1;
    //简体中文
    LANGUAGE_TYPE_ZH_CN = 2;
    //繁体中文
    LANGUAGE_TYPE_ZH_TW = 3;
    //日语
    LANGUAGE_TYPE_JAPANESE = 4;
    //韩语
    LANGUAGE_TYPE_KOREAN = 5;
    //法语
    LANGUAGE_TYPE_FRENCH = 6;
    //德语
    LANGUAGE_TYPE_GERMAN = 7;
    //意大利语
    LANGUAGE_TYPE_ITALIAN = 8;
    //西班牙语
    LANGUAGE_TYPE_SPANISH = 9;
    //葡萄牙语
    LANGUAGE_TYPE_PORTUGUESE = 10;
    //阿拉伯语
    LANGUAGE_TYPE_ARABIC = 11;
    //俄语
    LANGUAGE_TYPE_RUSSIAN = 12;
    //越南语
    LANGUAGE_TYPE_VIETNAMESE = 13;
    //泰语
    LANGUAGE_TYPE_THAI = 14;
    //波兰语
    LANGUAGE_TYPE_POLISH = 15;
    //荷兰语
    LANGUAGE_TYPE_DUTCH = 16;
    //瑞典语
    LANGUAGE_TYPE_SWEDISH = 17;
    //土耳其语
    LANGUAGE_TYPE_TURKISH = 18;
    //斯洛文尼亚语
    LANGUAGE_TYPE_SLOVENIAN = 19;
    //捷克语
    LANGUAGE_TYPE_CZECH = 20;
    //匈牙利语
    LANGUAGE_TYPE_HUNGARIAN = 21;
    //罗马尼亚语
    LANGUAGE_TYPE_ROMANIAN = 22;
    //保加利亚语
    LANGUAGE_TYPE_BULGARIAN = 23;
    //丹麦语
    LANGUAGE_TYPE_DANISH = 24;
    //爱沙尼亚语
    LANGUAGE_TYPE_ESTONIAN = 25;
    //芬兰语
    LANGUAGE_TYPE_FINNISH = 26;
    //希腊语
    LANGUAGE_TYPE_GREEK = 27;
}


//语言
message Language {
    LanguageType language_type = 1;
    //默认字体名称
    string default_font_name = 2;
}

//主题(主题号为ProjectStyleConfig中的themes数组下标)
message Theme {
    string name = 1;
    string memo = 2;
}

//颜色变量
message ColorVariable {
    string name = 1;
    uint32 default_color_u32 = 2;
    string memo = 3;
}

//改为保存工程使用的字体名称,并给个id,工程中其他地方使用id
//考虑过用enum,但一是后续增加字体工作量较大,二是使用字体名称id也可以节省空间
//至于存在云端,改为用户使用时,自动从本地上传到云端(压缩,且先跟云端对比有没有),换电脑时可从云端下载
message FontDefinition {
    //名称
    string name = 1;
    // oneof font {
    //     //所有语言一种字体
    //     Font single_lang = 2;
    //     //不同语言不同字体
    //     MultiLangFont multi_lang = 3;
    // }
    //一定分语言，如果想不分语言，用同步字体
    map<uint32, Font> fonts = 2;
    //备注
    string memo = 3;
    //相同字体名称
    bool same_font_name = 4;
    //相同字号
    bool same_font_size = 5;
    //相同字型
    bool same_font_style = 6;
    //禁用(设计端使用)
    bool disabled = 7;
}

// //多语言字体
// message MultiLangFont {
//     //key是LanguageType, u8
//     map<uint32, Font> fonts = 1;
// }

//字体
message Font {
    //字体名称,从字体文件中读取并显示给用户的
    string font_name = 1;
    //字号
    int32 font_size = 2;
    //字型
    FontStyle font_style = 3;
    // //源字体文件类型
    // FontFileType file_type = 4;
    // //源字体文件名,用于区分字体
    // string file_name = 5;
    // //源字体文件大小
    // uint32 file_size_u32 = 6;
    // //源字体文件md5,用于上传和下载识别
    // string file_md5 = 7;
}


//字体文件类型
enum FontFileType {
    FONT_FILE_TYPE_UNSPECIFIED = 0;
    FONT_FILE_TYPE_TTF = 1;
    FONT_FILE_TYPE_OTF = 2;
    FONT_FILE_TYPE_WOFF = 3;
    FONT_FILE_TYPE_WOFF2 = 4;
}

//字体样式(简化掉删除线)
//以下对应的数字和顺序不能变,因为代码中会用这些数字进行位运算以快速知道是否包含哪种样式
enum FontStyle {
    //正常(0000)
    FONT_STYLE_UNSPECIFIED = 0;
    //粗体(0001)
    FONT_STYLE_BOLD = 1;
    //斜体(0010)
    FONT_STYLE_ITALIC = 2;
    //粗+斜体(0011)
    FONT_STYLE_BOLD_ITALIC = 3;
    //下划线(0100)
    FONT_STYLE_UNDERLINE = 4;
    //粗+下划线(0101)
    FONT_STYLE_BOLD_UNDERLINE = 5;
    //斜+下划线(0110)
    FONT_STYLE_ITALIC_UNDERLINE = 6;
    //粗+斜+下划线(0111)
    FONT_STYLE_BOLD_ITALIC_UNDERLINE = 7;
}

//主题和颜色变量的对应存储
// message ThemeColorValue {
//     int32 theme_no = 1;
//     int32 color_var_id = 2;
//     uint32 color_value = 3;
// }

// //主题变量
// message ThemeVariable {
//     int32 var_id = 1;
//     string var_name = 2;
//     uint32 default_color = 3;
//     optional string memo = 4;
//     // ThemeColorReference color_ref = 3;
//     // ThemeVariableType var_type = 3;
//     // ThemeValue default_value = 4;
//     // //给数字用的单位
//     // optional string suffix = 6;
//     // //给数字用的最小值
//     // optional int32 min = 7;
//     // //给数字用的最大值
//     // optional int32 max = 8;
//     // //给字符串用的可选择项
//     // repeated string items = 9;
// }

// message ThemeValue {
//     oneof value {
//         ThemeColorReference color_ref = 1;
//         ThemeIntReference int_ref = 2;
//         ThemeBoolReference bool_ref = 3;
//         ThemeStringReference string_ref = 4;
//         ThemeGraphicReference graphic_ref = 5;
//         ThemeFontDefinitionReference font_ref = 6;
//     }
// }

// //主题变量值
// message ThemeVariableValue {
//     int32 theme_no = 1;
//     int32 var_id = 2;
//     ThemeValue value = 3;
// }

// //主题变量类型
// enum ThemeVariableType {
//     THEME_VARIABLE_TYPE_UNSPECIFIED = 0;
//     //颜色
//     THEME_VARIABLE_TYPE_COLOR = 1;
//     //整数
//     THEME_VARIABLE_TYPE_INTEGER = 2;
//     //布尔值
//     THEME_VARIABLE_TYPE_BOOLEAN = 3;
//     //字符串
//     THEME_VARIABLE_TYPE_STRING = 4;
//     //图片或图标
//     THEME_VARIABLE_TYPE_GRAPHIC = 5;
//     //字体名称
//     THEME_VARIABLE_TYPE_FONT_DEFINITION = 10;
// }



// //主题项颜色
// message ThemeItemColor {
//     //颜色
//     uint32 color = 1;
//     //渐变颜色
//     optional uint32 gradient_color = 2;
//     //渐变方向
//     optional GradientDirection gradient_direction = 3;
//     //渐变起点
//     optional common.v1.PositionOffset gradient_main_stop = 4;
//     //渐变终点
//     optional common.v1.PositionOffset gradient_grad_stop = 5;
// }

//渐变方向
enum GradientDirection {
    GRADIENT_DIRECTION_UNSPECIFIED = 0;
    //水平
    GRADIENT_DIRECTION_HORIZONTAL = 1;
    //垂直
    GRADIENT_DIRECTION_VERTICAL = 2;
}

// //主题项字体名称
// message ThemeFontDefinitionReference {
//     optional int32 theme_var_id = 1;
//     //如果以下属性有,则覆盖主题项的
//     optional int32 font_id = 2;
//     //这个字段名称正常情况下不要使用,只有在组态编辑过程中,临时使用,保存时会去除,仅留font_id
//     // optional string font_name = 3;
// }

// //主题项图片
// message ThemeGraphicReference {
//     optional int32 theme_var_id = 1;
//     //如果以下属性有,则覆盖主题项的
//     optional GraphicType graphic_type = 2;
//     optional string src = 3; //图片路径或者图标名称
//     optional int32 width = 4;  //图片宽度/图标尺寸
//     optional int32 height = 5; //图片高度/图标尺寸
//     optional int32 opacity = 6; //透明度
//     optional ThemeColorReference recolor = 7; //重新着色
//     // optional bool is_tiled = 8;
// }

// //主题项图标
// message IconReference {
//     optional int32 theme_var_id = 1;
//     //如果以下属性有,则覆盖主题项的
//     optional IconType icon_type = 2;
//     optional string icon_name = 3;
//     optional int32 icon_size = 4;
//     optional ColorReference icon_color = 5;
// }

// message GraphicReference {
//     oneof graphic {
//         ImageReference image = 1;
//         IconReference icon = 2;
//     }
// }

//主题项图片
message GraphicReference {
    //图形类型,默认为IMAGE
    GraphicType graphic_type = 1;
    //图片id/图标编号
    uint32 src = 2;
    //图片宽度/图标尺寸
    uint32 width_u16 = 3;
    //图片高度/图标尺寸
    uint32 height_u16 = 4;
    //透明度,无则表示未设置透明度,否则按0-255设置
    optional uint32 opacity_u8 = 5;
    //重新着色(无值表示不会重新着色)
    optional ColorReference recolor = 6;
}

enum GraphicType {
    GRAPHIC_TYPE_UNSPECIFIED = 0;
    //图片
    GRAPHIC_TYPE_IMAGE = 1;
    //font-awesome图标
    GRAPHIC_TYPE_ICON_FONT_AWESOME = 2;
}

// //主题项线
// message LineReference {
//     optional int32 width = 1;
// }

// //主题项阴影
// message ThemeItemShadow {
//     bool enable = 1;
//     optional int32 offset_x = 2;
//     optional int32 offset_y = 3;
//     optional int32 spread = 4;
// }


// //颜色
// message ColorReference {
//     //颜色变量
//     optional int32 color_var_id = 1;
//     //或颜色值
//     optional uint32 color_value = 2;
//     // //渐变颜色(这个如果未定义,则没有渐变)
//     // optional uint32 gradient_color = 3;
//     // //渐变方向
//     // optional GradientDirection gradient_direction = 4;
//     // //渐变起点
//     // optional PositionOffset gradient_main_stop = 5;
//     // //渐变终点
//     // optional PositionOffset gradient_grad_stop = 6;
// }

// //值引用
// message ThemeIntReference {
//     optional int32 theme_var_id = 1;
//     //如果以下属性有,则覆盖主题变量的
//     optional int32 value = 2;
// }

// message ThemeBoolReference {
//     optional int32 theme_var_id = 1;
//     //如果以下属性有,则覆盖主题变量的
//     optional bool value = 2;
// }

// message ThemeStringReference {
//     optional int32 theme_var_id = 1;
//     //如果以下属性有,则覆盖主题变量的
//     optional string value = 2;
// }

//颜色引用
message ColorReference {
    oneof color {
        //颜色变量id(不超过255,只用一个byte,0表示未设置)
        uint32 color_var_id_u8 = 1;
        //颜色值RGBA
        uint32 color_value_u32 = 2;
    }
}

//边框
message StyleBorder {
    //边框颜色
    ColorReference color = 1;
    //边框宽度
    uint32 width_u8 = 2;
    //是否启用左侧
    bool left = 3;
    //是否启用顶部
    bool top = 4;
    //是否启用右侧
    bool right = 5;
    //是否启用底部
    bool bottom = 6;
    //是否启用内部线
    bool internal = 7;
}

//阴影
message StyleShadow {
    //是否启用
    bool enable = 1;
    //阴影颜色
    ColorReference color = 2;
    //阴影x偏移量
    int32 offset_x_i16 = 3;
    //阴影y偏移量
    int32 offset_y_i16 = 4;
    //阴影扩散程度
    int32 spread_i16 = 5;
    //阴影模糊程度
    int32 width_i16 = 6;
}

enum GraphicTextLayout {
    GRAPHIC_TEXT_LAYOUT_UNSPECIFIED = 0;
    //文字在上层,图片在下层
    GRAPHIC_TEXT_LAYOUT_DEFAULT = 1;
    //文字在右边,图片在左边
    GRAPHIC_TEXT_LAYOUT_HORIZONTAL = 2;
    //文字在下边,图片在上边
    GRAPHIC_TEXT_LAYOUT_VERTICAL = 3;
    //仅文字
    GRAPHIC_TEXT_LAYOUT_ONLY_TEXT = 4;
    //仅图片
    GRAPHIC_TEXT_LAYOUT_ONLY_GRAPHIC = 5;
}

// //样式引用
// message StyleReference {
//     optional int32 style_id = 1;
//     //以下属性有,则覆盖样式定义的
//     optional StyleProperties style_properties = 2;
// }

//样式模板定义
message StyleTemplate {
    // int32 id = 1;
    string name = 1;
    StyleProperties properties = 2;
}

//样式(图片移到图元上)
message StyleProperties {
    //样式id(空表示无样式继承)
    optional uint32 style_id_u8 = 1;
    //背景色
    optional ColorReference background_color = 2;
    //边框
    optional StyleBorder border_props = 3;
    //阴影
    optional StyleShadow shadow_props = 4;
    //字体(把字体加回来，是从文本那边移除了，这样逻辑就可以统一了)
    optional uint32 font_id_u8 = 5;
    //文本位置(数组下标表示语言LanguageType,uint8)
    //所有语言一样时,也会在不同语言中拷贝一样的设置,如果不存在,可能是新增的语言,则取默认语言的值,取不到默认语言则取遍历到的第1个
    optional MultiLangTextLocation text_location = 6;
    //文本颜色
    optional ColorReference text_color = 7;
    //闪烁间隔(100ms),0表示不闪烁
    optional uint32 blink_interval_u8 = 8;
    //跑马灯效果
    optional MarqueeDefine marquee = 9;

    // 把字体注释掉的原因,是放这边会比较复杂,因为文本内部已经有字体了,样式再加字体逻辑有点乱
    // 而且字体本身已经是id定义了,已经可以实现替换字体了,不需要用到样式了
    // //多语言字体
    // optional MultiLanguageFont multi_lang = 5;
    // //单语言字体
    // optional FontProperties single_lang = 6;
}



//字体属性
/*message FontProperties {
    //字体id(至少指向字体0,而字体0在工程中一定存在,不能被删除)
    uint32 font_id_u8 = 1;
    //文本位置(数组下标表示语言LanguageType,uint8)
    //所有语言一样时,也会在不同语言中拷贝一样的设置,如果不存在,可能是新增的语言,则取默认语言的值,取不到默认语言则取遍历到的第1个
    map<uint32, TextLocation> location = 2;
    //文本颜色(不同语言没必要不同颜色,想不出应用场景,MCGS就不支持)
    ColorReference color = 6;
    //闪烁间隔(100ms),0表示不闪烁
    uint32 blink_interval_u8 = 7; 
    //跑马灯效果
    optional MarqueeDefine marquee = 8;

    // //文字对齐(分语言,比如有些语言习惯从右到左)
    // oneof align {
    //     //多语言位置(找不到该语言的,取默认语言的值,默认也没有的,就正居中)
    //     MultiLangTextAlign multi_lang_align = 2;
    //     //单语言位置
    //     AlignType single_lang_align = 3;
    // }
    // //文本边距(分语言,不同语言因为长度不同,需要调整边距来显示)
    // oneof padding {
    //     //多语言位置(找不到该语言的,取默认语言的值,默认也没有的,就全按0)
    //     MultiLangTextPadding multi_lang_padding = 4;
    //     //单语言位置
    //     PositionOffset single_lang_padding = 5;
    // }
    //文本位置
    // oneof location {
    //     //多语言位置(找不到该语言的,取默认语言的值,默认也没有的,就全按0)
    //     MultiLangTextLocation multi_lang_location = 2;
    //     //单语言位置
    //     TextLocation single_lang_location = 3;
    // }
}
*/
// //多语言文本对齐方式
// message MultiLangTextAlign {
//     //key是LanguageType枚举值, uint8
//     map<uint32, AlignType> align = 1;
// }

// //多语言文本边距
// message MultiLangTextPadding {
//     //key是LanguageType枚举值, uint8
//     map<uint32, PositionOffset> padding = 1;
// }

//文本位置属性
message TextLocation {
    //文本对齐方式(0也表示上下左右都居中)
    AlignType text_align = 1;
    //文本边距(没值表示全是0)
    optional PositionOffset text_padding = 2;
}

//多语言文本位置属性
message MultiLangTextLocation {
    //key是LanguageType枚举值, uint8
    map<uint32, TextLocation> multi_lang = 1;
}


//跑马灯定义
message MarqueeDefine {
    //是否启用
    bool enable = 1;
    //每次滚动时间间隔(几个100ms)
    uint32 interval_time_u8 = 2;
    //每次滚动距离(px)
    uint32 scroll_distance_u16 = 3;
    //滚动方向
    ScrollDirection scroll_direction = 4;
    //是否持续循环滚动
    bool loop = 5;
}
