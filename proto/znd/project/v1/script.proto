syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
import "znd/project/v1/common.proto";
import "znd/project/v1/variable.proto";

//启动事件
message StartEvent {
    optional int32 delay = 1;//延迟时间，单位毫秒
}

//定时事件
message TimerEvent {
    optional int32 interval = 1;//间隔时间，单位毫秒
    optional int32 count = 2;//执行次数，未设置时无限次
    VariableReference enable = 3;//启用/禁用
}

//退出事件
message ExitEvent {
    optional int32 delay = 1;//延迟时间，单位毫秒
}

//页面打开事件
message PageOpenEvent {
    optional int32 page_id = 1;//页面ID
    optional int32 delay = 2;//延迟时间，单位毫秒
    optional int32 loop_interval = 3;//循环间隔时间，单位毫秒
    optional int32 loop_count = 4;//循环次数，未设置时为无限次,直到窗口关闭
}

//页面关闭事件
message PageCloseEvent {
    optional int32 page_id = 1;//页面ID
    optional int32 delay = 2;//延迟时间，单位毫秒
}

//触发事件
message DataChangeEvent {
    VariableReference data = 1;//触发数据
    optional DataChangeType change_type = 2;//数据变化类型
    optional float tolerance = 3;//误差范围，仅在变化时有效
    optional int32 delay = 4;//延迟时间，单位毫秒
    // 数据变更执行如果有循环,用户很可能会忘记停止循环,下次改变又启动,所以不能支持循环
    // optional int32 loop_interval = 5; //循环间隔时间，单位毫秒
    // optional int32 loop_count = 6; //循环次数，0-无限次
    // repeated common.v1.DataCompareCondition exit_loop_condition = 7; //满足条件时终止循环,不满足时继续循环
}

//数据变化类型
enum DataChangeType {
    DATA_CHANGE_TYPE_UNSPECIFIED = 0;
    DATA_CHANGE_TYPE_ON_CHANGE = 1;//值改变执行一次(位切换)
    DATA_CHANGE_TYPE_ON_ZERO = 2;//值从非0变为0执行一次(位从ON变为OFF)
    DATA_CHANGE_TYPE_ON_NOT_ZERO = 3;//值从0变为非零执行一次(位从OFF变为ON)
    DATA_CHANGE_TYPE_ON_MINUS = 4;//值从正数变为负数执行一次(位地址无此选项)
    DATA_CHANGE_TYPE_ON_PLUS = 5;//值从负数变为正数执行一次(位地址无此选项)
    DATA_CHANGE_TYPE_ON_ADD = 8;//只在值增加时执行(位地址无此选项)
    DATA_CHANGE_TYPE_ON_SUB = 9;//只在值减少时执行(位地址无此选项)
}

//程序
message Program {
    int32 id = 1;
    string name = 2;
    // 开始事件
    optional StartEvent start_event = 3;
    // 结束事件
    optional ExitEvent exit_event = 4;
    // 页面打开事件
    optional PageOpenEvent page_open_event = 5;
    // 页面关闭事件
    optional PageCloseEvent page_close_event = 6;
    // 定时事件
    optional TimerEvent timer_event = 7;
    // 数据变化事件
    optional DataChangeEvent data_change_event = 8;
    // 执行脚本
    repeated Script scripts = 9;
    // 执行结束后再调用程序ID列表
    repeated int32 after_program_ids = 10;
    // 备注
    optional string memo = 11;
    // 是否禁用
    optional bool is_disable = 12;
    // 是否保护
    optional ProtectType protect_type = 13;
    // 保护密码
    optional string protect_passwd = 14;
    // 是否是子程序
    optional bool is_sub_program = 15;
    // 脚本分类
    optional int32 category_id = 16;
}

//脚本
message Script {
    VariableReference enable = 1;//执行前根据值决定是否执行
    optional ScriptType type = 2;
    optional string code = 3;
}

//代码类型
enum ScriptType {
    SCRIPT_TYPE_UNSPECIFIED = 0;
    SCRIPT_TYPE_LUA = 1;
    SCRIPT_TYPE_PYTHON = 2;
    SCRIPT_TYPE_JS = 3;
    SCRIPT_TYPE_C = 4;
    SCRIPT_TYPE_LD = 5;
    SCRIPT_TYPE_ST = 6;
}

// //策略定时参数
// message StrategyTimerParam {
//     int32 interval = 1; //间隔时间，单位毫秒
//     int32 count = 2; //执行次数，0-无限次
// }

// message StrategyDataTriggerParam {
//     DataReference trigger_data = 1; //触发数据
//     StrategyTriggerType trigger_type = 2;
// }

// message StrategyPageParam {
//     int32 page_id = 1; //关联页面ID
//     optional int32 loop_interval = 2; //循环间隔时间，单位毫秒
//     optional int32 loop_count = 3; //循环次数，0-无限次
// }

// enum StrategyTriggerType {
//     STRATEGY_TRIGGER_TYPE_UNSPECIFIED = 0;
//     STRATEGY_TRIGGER_TYPE_VALUE_CHANGE = 1; //值改变执行一次
//     STRATEGY_TRIGGER_TYPE_VALUE_ZERO = 2; //值为0执行一次
//     STRATEGY_TRIGGER_TYPE_VALUE_NOT_ZERO = 3; //值为1执行一次
//     STRATEGY_TRIGGER_TYPE_VALUE_ZERO_TO_NOT_ZERO = 4; //值从0变为非0执行一次
//     STRATEGY_TRIGGER_TYPE_VALUE_NOT_ZERO_TO_ZERO = 5; //值从非0变为0执行一次
//     STRATEGY_TRIGGER_TYPE_VALUE_ADD = 8; //值增加执行一次
//     STRATEGY_TRIGGER_TYPE_VALUE_SUB = 9; //值减少执行一次
// }