syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;

//数据格式
enum DataFormatType {
    DATA_FORMAT_TYPE_UNSPECIFIED = 0;
    //布尔型
    DATA_FORMAT_TYPE_BOOL = 1;
    //8位无符号整型
    DATA_FORMAT_TYPE_U8 = 2;
    //8位有符号整型
    DATA_FORMAT_TYPE_I8 = 3;
    //16位无符号整型
    DATA_FORMAT_TYPE_U16 = 4;
    //16位有符号整型
    DATA_FORMAT_TYPE_I16 = 5;
    //32位无符号整型
    DATA_FORMAT_TYPE_U32 = 6;
    //32位有符号整型
    DATA_FORMAT_TYPE_I32 = 7;
    //32位浮点型
    DATA_FORMAT_TYPE_FLOAT = 8;
    //BCD16
    DATA_FORMAT_TYPE_BCD16 = 11;
    //BCD32
    DATA_FORMAT_TYPE_BCD32 = 13;
    //16位十六进制
    DATA_FORMAT_TYPE_HEX16 = 15;
    //32位十六进制
    DATA_FORMAT_TYPE_HEX32 = 17;
    //16位二进制
    DATA_FORMAT_TYPE_BIN16 = 18;
    //32位二进制
    DATA_FORMAT_TYPE_BIN32 = 21;
    //64位无符号整型
    DATA_FORMAT_TYPE_U64 = 22;
    //64位有符号整型
    DATA_FORMAT_TYPE_I64 = 23;
    //64位浮点型
    DATA_FORMAT_TYPE_DOUBLE = 25;
    //日期时间，占8字节，64位(年2个字节，月1个字节，日1个字节，时1个字节，分1个字节，秒1个字节，10毫秒1个字节)
    DATA_FORMAT_TYPE_DT64 = 26;
    //短日期时间，不含年份且分钟级别，占4字节，32位(月1个字节，日1个字节，时1个字节，分1个字节)
    DATA_FORMAT_TYPE_DT32 = 27;
    //日期，占4字节，UINT32位(年2个字节，月1个字节，日1个字节各1个字节的U8)
    DATA_FORMAT_TYPE_D32 = 28;
    //时间，占4字节，UINT32位（时、分、秒各1个字节的U8）
    DATA_FORMAT_TYPE_T32 = 29;
    //秒时间戳，32位无符号整型(1970-01-01 00:00:00 到现在的秒数)
    DATA_FORMAT_TYPE_TS32 = 30;
    //毫秒时间戳，64位无符号整型(1970-01-01 00:00:00 到现在的毫秒数)
    DATA_FORMAT_TYPE_TS64 = 31;
    //字符串，UTF8
    DATA_FORMAT_TYPE_UTF8 = 32;
    //字符串，Unicode
    DATA_FORMAT_TYPE_UNICODE = 33;
    //字符串，GBK
    DATA_FORMAT_TYPE_GBK = 34;
    //字符串，UTF8高低互换
    DATA_FORMAT_TYPE_UTF8_LH = 35;
    //字符串，Unicode高低互换
    DATA_FORMAT_TYPE_UNICODE_LH = 36;
    //字符串，GBK高低互换
    DATA_FORMAT_TYPE_GBK_LH = 37;
    //IP地址(4字节)
    DATA_FORMAT_TYPE_IP = 38;
    //结构体
    DATA_FORMAT_TYPE_STRUCT = 39;
}

//结构体数据项
message StructTypeField {
    //字段ID，不用数组下标的原因，是考虑会被调整字段顺序，以及方便检查是否被引用
    uint32 field_id_u8 = 1;
    //字段名称
    string name = 2;
    //数据格式
    DataFormatType data_format = 3;
    //从结构体开始算的起始地址，方便快速计算偏移
    uint32 start_u16 = 4;
    //地址长度
    uint32 length_u8 = 5;
    //数组个数（0表示不是数组）
    uint32 array_count_u8 = 6;
    //位索引
    optional int32 bit_index = 7;
    //字节索引
    optional int32 byte_index = 8;
    //备注
    string memo = 9;
}

//结构体数据
message StructType {
    //结构体名称
    string name = 1;
    //字段列表
    repeated StructTypeField fields = 2;
    //结构体总长度
    uint32 length_u16 = 3;
    //结构体模式，默认连续字地址(其他暂不支持)
    optional StructTypeMode mode = 4;
    //备注
    optional string memo = 5;
}

//结构体类型枚举
enum StructTypeMode {
    //未设置(默认跟1相同，也是连续地址字，因为这个用的最多，节省空间)
    STRUCT_TYPE_MODE_UNSPECIFIED = 0;
    //连续地址字（这个跟未设置一样，目前只支持这个，保留这个的原因是方便理解）
    STRUCT_TYPE_MODE_WORD = 1;
    //连续地址布尔（暂时不支持）
    STRUCT_TYPE_MODE_BOOL = 2;
    //不连续地址的复合类型（暂时不支持）
    STRUCT_TYPE_MODE_COMPOSITE = 3;
}

//变量标签库及初始值(根据设备id分开存放)
message VariableLibrary {
    //按tag_id存储,key是u16
    map<uint32, Variable> variables = 1;
}

//变量引用
message VariableReference {
    //引用用户定义变量的结构
    message UserDefined {
        //设备ID(由设计端保证不超过255)
        uint32 device_id_u8 = 1;
        //地址标签ID（根据设备ID，从地址标签库中取）
        uint32 var_id_u16 = 2;
        //地址固定偏移(优先偏移位地址、再主地址)，有单独固定地址偏移的原因，是为了让引用变量后再偏移，省去创建变量的工作
        int32 address_fixed_offset_i16 = 4;
        //变量下标(依次展开可能是结构体字段id也可能是数组下标)
        repeated DataReferenceUInt8 var_subscripts = 5;
        //变量下标偏移（得到上面的值之后 再偏移得到真正的下标）
        int32 var_subscript_offset_i16 = 6;

        /*//结构体字段（0表示没使用结构体）
        uint32 struct_field_id_u8 = 5;
        //数组1维下标(仅针对数组有效，如果是结构体字段数组，则表示结构体本身的数组1维下标)
        optional DataReferenceUInt8 array_1d_index = 6;
        //数组2维下标(仅针对2维数组有效，如果是结构体字段数组，则表示结构体本身的数组2维下标)
        optional DataReferenceUInt8 array_2d_index = 7;
        //结构体字段数组下标（仅针对结构体字段数组有效，表示结构体内部字段数组1维下标）
        optional DataReferenceUInt8 struct_field_array_1d_index = 8;
        //结构体字段数组2维下标(仅针对结构体字段数组2维有效，表示结构体内部字段数组2维下标)
        optional DataReferenceUInt8 struct_field_array_2d_index = 9;*/
    }

    oneof from {
        // //索引寄存器地址,范围1-255，0保留不用（没引用表示空值）
        // uint32 index_register_u8 = 1;
        // //状态寄存器地址
        // uint32 status_register_u8 = 2;
        //变量引用
        UserDefined var_defined = 3;
        //手动输入
        Variable var_input = 4;
    }
    //地址动态偏移(优先偏移位地址、再主地址)
    optional VariableReference address_dynamic_offset = 5;
    //子设备下标(仅针对动态设备有效，动态设备根据这个下标来引用不同的设备)
    optional DataReferenceUInt8 sub_device_index = 6;
    //系统变量的索引号(仅针对部分系统变量，如设备通讯参数用来指定设备号等)
    optional DataReferenceUInt8 local_hmi_index = 7;
    //地址扩展参数(为不同PLC预留，方便扩展，用map，key类型用u8，不同协议不同枚举)
    map<uint32, DataReferenceInt16> extend_params = 8;

    //把变量得到的值，再进行加上这个固定值
    int32 value_add_i16 = 9;
}

//数据引用
message DataReference {
    oneof from {
        //常量
        AllTypeValue constant = 1;
        //变量
        VariableReference variable = 2;
    }
}


//数值型数据引用
message DataReferenceNumber {
    oneof from {
        //固定值
        NumberValue constant_number = 1;
        //变量引用
        VariableReference variable = 2;
    }
    //偏移量（根据from的值，再加上这个值，才是最终值）
    //int32 offset_i16 = 3;
}

//8位整型引用
message DataReferenceUInt8 {
    oneof from {
        //固定值
        uint32 constant_u8 = 1;
        //变量引用
        VariableReference variable = 2;
    }
}

//8位整型引用
message DataReferenceInt8 {
    oneof from {
        //固定值
        int32 constant_i8 = 1;
        //变量引用
        VariableReference variable = 2;
    }
}

//16位整型引用
message DataReferenceInt16 {
    oneof from {
        //固定值
        int32 constant_i16 = 1;
        //变量引用
        VariableReference variable = 2;
    }
}

//uint16位整型引用
message DataReferenceUInt16 {
    oneof from {
        //固定值
        uint32 constant_u16 = 1;
        //变量引用
        VariableReference variable = 2;
    }
}

//32位整型引用
message DataReferenceInt32 {
    oneof from {
        //固定值
        int32 constant_i32 = 1;
        //变量引用
        VariableReference variable = 2;
    }
}

//uint32位整型引用
message DataReferenceUInt32 {
    oneof from {
        //固定值
        uint32 constant_u32 = 1;
        //变量引用
        VariableReference variable = 2;
    }
}

//64位有符号整型引用
message DataReferenceInt64 {
    oneof from {
        //固定值
        int64 constant_i64 = 1;
        //变量引用
        VariableReference variable = 2;
    }
}

//64位无符号整型引用
message DataReferenceUInt64 {
    oneof from {
        //固定值
        uint64 constant_u64 = 1;
        //变量引用
        VariableReference variable = 2;
    }
}


//32位浮点型引用
message DataReferenceFloat {
    oneof from {
        //固定值
        float constant_float = 1;
        //变量引用
        VariableReference variable = 2;
    }
}

//64位浮点型引用
message DataReferenceDouble {
    oneof from {
        //固定值
        double constant_double = 1;
        //变量引用
        VariableReference variable = 2;
    }
}


//bool型引用
message DataReferenceBool {
    oneof from {
        //固定值
        bool constant_bool = 1;
        //变量引用
        VariableReference variable = 2;
    }
}

// //数据来源
// message DataRef {
//     oneof from {
//         //引用变量
//         VariableReference variable = 1;
//         //直接手动输入
//         Variable input = 2;
//     }
// }

// //变量引用结构
// message VariableUse {
//     //设备ID(由设计端保证不超过255)
//     uint32 device_id_u8 = 1;
//     //地址标签ID（根据设备ID，从地址标签库中取）
//     uint32 var_id_u16 = 2;
//     //结构体字段（0表示没使用结构体）
//     uint32 struct_field_id_u8 = 3;
//     //地址参数
//     optional AddressParameter parameters = 4;
// }

//地址参数（移到VariableReference中）
// message AddressParameter {}

//变量定义
message Variable {
    //名称
    string name = 1;
    //标签备注
    string memo = 2;
    //所属分类,支持多个(设计端用)
    repeated uint32 var_classify_ids_u16 = 3;
    //所属设备（设计端用，是用户归类用的，设计端会约束所有定义中的变量都来自该设备，主要用于在同一个设备中用动态变量偏移地址时使用）
    uint32 var_device_id_u8 = 4;
    //定义
    oneof definition {
        //寄存器地址
        RegisterAddress register_address = 5;
        //动态变量
        DynamicVariable dynamic_variable = 6;
        //表达式
        Expression expression = 7;
    }
    //最终输出的目标数据类型(不管做不做转换，都一定有值，不做转换，就是寄存器数据格式，做转换就是转换后的数据格式)
    //基本类型规定枚举值是在100以下，100及以上表示结构体，结构体ID一定是从100开始，允许的结构体类型不超过150个，足够用了
    DataFormatType data_format = 8;
}

//寄存器地址
message RegisterAddress {
    //(不用了，直接用地址拼出对应KEY)对应的系统预置变量（用户选择了系统变量，保存时会转化地址，0表示没有，设计端使用）
    // uint32 preset_variable_id_u16 = 1;
    //是否来自预置地址标签（如果是，则名称从预置地址标签库中取）
    bool is_preset_address_tag = 1;
    //显示寄存器类型
    string register_display_type = 2;
    //显示PLC地址
    uint32 register_display_address_u32 = 3;
    //设备ID
    uint32 device_id_u8 = 4;
    //真实寄存器类型(不同协议不一样的定义, 最长255，用u8)
    uint32 register_type_u8 = 5;
    //真实PLC地址（对于有些地方浮点型地址的，拆分小数部分到bit_address，目的是为了节省最常用于整型地址占用空间）
    uint32 register_address_u32 = 6;
    //寄存器单地址位长度，0或者16都表示为16，也有8和32（16最常见，用0节省存储空间）
    uint32 register_bit_length_u8 = 7;
    //取位u8(西门子Ix.y中的y，也用这个)
    optional uint32 bit_index_u8 = 8;
    //字节索引（8位整型用）
    optional uint32 byte_index_u8 = 9;
    //（暂时不用，由引用变量时处理）地址动态偏移(优先偏移位地址、再主地址)
    // optional VariableReference address_dynamic_offset = 10;
    //位是否反转，默认不反转(仅位类型使用，含字取位的反转)
    bool bit_reverse = 11;
    //地址长度(如果没值则为1)
    uint32 address_length_u16 = 12;

    //把这个字段注释掉的原因，是不管在创建变量时，还是在引用输入地址，都不会去选择结构体字段，只有在引用结构体变量才会选择
    //结构体字段ID（仅在图元引用地址时才用到，不用数组下标的原因是因为结构体字段会更改）
    //uint32 struct_field_id_u8 = 16;
    //数组第1维长度（零表示不是数组，大于0表示定长数组，变长数组暂不实现，如果后续要实现，另外加个字段标记，这个字段就可以表示最长数组）
    uint32 array_1d_length_u8 = 17;
    //数组第1维起始下标
    uint32 array_1d_start_index_u8 = 18;
    //数组第2维长度（如果为0，则表示不是数组）
    uint32 array_2d_length_u8 = 19;
    //数组第2维起始下标
    uint32 array_2d_start_index_u8 = 20;
    //数据换算
    repeated DataTransform data_transforms = 21;
    //转换前的寄存器的原始寄存器数据格式(只在有数据转换时才有值，如果没有数据转换，这个值是0)
    DataFormatType register_data_format = 22;
    //地址参数（这边取消了，应该建变量时不应该有这些，只有引用时才需要，这样也免得处理哪些覆盖哪些了）
    //AddressParameter parameters = 21;
}

//动态变量
message DynamicVariable {
    //变量列表的下标(可以只支持索引寄存器，以后再考虑支持其他变量)
    VariableReference sub_variable_index = 1;
    //变量列表
    repeated VariableReference sub_variables = 2;
    //起始下标
    int32 sub_variable_index_offset_i16 = 3;
}

//表达式
message Expression {
    //引用地址的map，key由编辑器生成，运行端直接使用，如果有写入功能，则要写入的值固定用v，这里不会嵌套引用表达式地址
    map<string, VariableReference> ref_variables = 1;
    //读取表达式(如果有地址引用，直接使用引用变量的key)
    string read_expression = 2;
    //key是ref_variables中的key，是针对指定地址的写入表达式
    //写入时机，是指针对该tag写入时，将写入值赋给map变量v，然后遍历write_expressions计算，依次写入key对应的地址
    map<string, string> write_expressions = 3;
}

//计算表达式，用于只读计算使用
message CalculateExpression {
    //读取引用地址的map，key由编辑器生成，运行端直接使用
    map<string, VariableReference> ref_variables = 1;
    //读取表达式(如果有地址引用，直接使用引用变量的key)
    string expression = 2;
}


//数据换算(如果每种都有，按以下顺序转换)
message DataTransform {
    //如果为空，则检查下转换后数据格式是否为0，如果为0，则表示不转换，不为0则转换类型
    oneof transform {
        DataReferenceNumber offset = 1;//偏移值，整型或浮点型，用原值加偏移值后输出
        DataReferenceNumber factor = 2;//系数，整型或浮点型，用原值*系数后输出
        LinearTransform linear = 3;//线性转换
        Expression expression = 4;//表达式转换
        DiffTransform diff = 5;//差值转换
    }
    //转换后数据格式（如果为0，则表示不转换）
    DataFormatType data_format = 6;
}

//差值换算(主要用于产能提取，当前输入值减去该变量上一次存起来的值，做为新值)
message DiffTransform {
    //两次值的计算时间间隔（单位100毫秒），如果是0，表示跟上次的值对比，如果是数据采样需要的，则跟上次采样对比
    uint32 time_interval_u16 = 1;
    //是否判断范围
    //要判断时，如果输入值更小，则新值=（输入值-最小值）+（最大值-上一次值）
    bool is_range = 2;
    //最小值
    optional DataReferenceNumber min_value = 3;
    //最大值
    optional DataReferenceNumber max_value = 4;
}

//数据线性转换
message LinearTransform {
    DataReferenceNumber org_value_min = 1;//原值最小值
    DataReferenceNumber org_value_max = 2;//原值最大值
    DataReferenceNumber target_value_min = 3;//目标值最小值
    DataReferenceNumber target_value_max = 4;//目标值最大值
}

// //表达式转换（读取值和写入值固定用v，不支持其他地址参与计算）
// message ExpressionTransform {
//     string read_expression = 1;//读取表达式
//     string write_expression = 2;//写入表达式
// }

//系统内部寄存器类型
enum RegisterTypeLocalHmi {
    REGISTER_TYPE_LOCAL_HMI_UNSPECIFIED = 0;
    // 状态寄存器
    REGISTER_TYPE_LOCAL_HMI_STA = 1;
    // 索引寄存器
    REGISTER_TYPE_LOCAL_HMI_IDX = 2;
    // 用户使用位寄存器
    REGISTER_TYPE_LOCAL_HMI_LB_USR = 3;
    // 用户使用字寄存器
    REGISTER_TYPE_LOCAL_HMI_LW_USR = 4;
    // 用户使用保持位寄存器
    REGISTER_TYPE_LOCAL_HMI_LB_HLD = 5;
    // 用户使用保持字寄存器
    REGISTER_TYPE_LOCAL_HMI_LW_HLD = 6;
    // 系统使用位寄存器
    REGISTER_TYPE_LOCAL_HMI_LB_SYS = 7;
    // 系统使用字寄存器
    REGISTER_TYPE_LOCAL_HMI_LW_SYS = 8;
    /*REGISTER_TYPE_LOCAL_HMI_RP_DATA = 9;
    REGISTER_TYPE_LOCAL_HMI_RP_CUR = 10;
    REGISTER_TYPE_LOCAL_HMI_RP_OPA = 11;
    REGISTER_TYPE_LOCAL_HMI_FS_DATA = 12;
    REGISTER_TYPE_LOCAL_HMI_FS_OPA = 13;
    REGISTER_TYPE_LOCAL_HMI_DB_SERVER = 14;
    REGISTER_TYPE_LOCAL_HMI_DB_QUERY = 15;
    REGISTER_TYPE_LOCAL_HMI_DB_ROW = 16;
    REGISTER_TYPE_LOCAL_HMI_DB_OPA = 17;*/
}

//MODBUS寄存器类型
enum RegisterTypeModbus {
    REGISTER_TYPE_MODBUS_UNSPECIFIED = 0;
    REGISTER_TYPE_MODBUS_0X = 1;
    REGISTER_TYPE_MODBUS_1X = 2;
    REGISTER_TYPE_MODBUS_3X = 3;
    REGISTER_TYPE_MODBUS_4X = 4;
}

message NumberValue {
    //值
    oneof from {
        int32 value_i8 = 1;
        uint32 value_u8 = 2;
        int32 value_i16 = 3;
        uint32 value_u16 = 4;
        int32 value_i32 = 5;
        uint32 value_u32 = 6;
        int64 value_i64 = 7;
        uint64 value_u64 = 8;
        float value_float = 9;
        double value_double = 10;
    }
    //数据类型
    //DataFormatType data_format = 9;
}

message AllTypeValue {
    oneof from {
        int32 value_i8 = 1;
        uint32 value_u8 = 2;
        int32 value_i16 = 3;
        uint32 value_u16 = 4;
        int32 value_i32 = 5;
        uint32 value_u32 = 6;
        int64 value_i64 = 7;
        uint64 value_u64 = 8;
        float value_float = 9;
        double value_double = 10;
        bool value_bool = 11;
        string value_string = 12;
    }
    //数据类型(只针对字符串，标注字符编码)
    optional DataFormatType data_format = 13;
}


//变量触发
message VariableTrigger {
    //变量(数据类型为bool型)
    VariableReference variable_bool = 1;
    //触发方式
    enum Type {
        TYPE_UNSPECIFIED = 0;
        TYPE_OFF_TO_ON = 1;
        TYPE_ON_TO_OFF = 2;
        TYPE_CHANGE = 3;
    }
    Type trigger_type = 2;
    //自动复位（如果为true，则触发后自动复位到变动之前的状态）  
    bool auto_reset = 3;
}

