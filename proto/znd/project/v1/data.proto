syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
import "znd/project/v1/variable.proto";

//存储包括数据采样、报警、配方、操作记录、数据库等配置
message DataConfig {
    //key为采样ID，用u16
    map<uint32, DataSampling> data_samplings = 1;
}

//数据采样
message DataSampling {
    //标识
    string key = 1;
    //名称
    string name = 2;
    //采集方式
    oneof sampling_type {
        //触发采样
        VariableTrigger variable_trigger = 3;
        //定时采样
        Interval interval = 4;
    }
    //定时采样
    message Interval {
        //时间间隔（单位100毫秒）
        uint32 time_interval_u16 = 1;
        //是否准点(意思是，除了启动时的第一次采样外，其他尽量在整点和整分采样)
        //打勾这个的时候，设计端会确保间隔时间符合准点要求
        bool is_on_time = 2;
    }
    //采样字段，也就是数据库字段
    message Field {
        //字段标识（用于匹配数据库字段或者MQTT字段或API字段）
        string key = 1;
        //字段名称
        string name = 2;
        //存储数据格式
        FieldFormat field_format = 3;
        //字段变量
        VariableReference variable = 4;
        //描述
        string memo = 5;
        //数据转换(为什么加在这，两个原因，一是方便不建变量使用，二是为了差值换算，如果变量自身有转换，为了性能和逻辑清晰，设计端会禁用这边转换)
        repeated DataTransform data_transform = 6;
    }
    enum FieldFormat {
        FIELD_FORMAT_UNSPECIFIED = 0;
        FIELD_FORMAT_AUTO = 1; //自动（SQLITE的NUMERIC格式）
        FIELD_FORMAT_INT = 2; //整数
        FIELD_FORMAT_REAL = 3; //实数
        FIELD_FORMAT_TEXT = 4; //文本
    }
    // key为字段ID，U8类型（数据库字段名用这个ID）
    map<uint32, Field> sampling_fields = 6;
    //暂停控制
    optional VariableReference pause_control = 7;
    //清除控制（为1时清除内存中的记录）
    optional VariableReference clear_control = 8;
    //是否优先采样(性能优先，占用显示性能)
    bool priority_sampling = 9;
    //保存参数
    message SaveParam {
        //保存位置：{yyyy}/{MM}/{dd}/{hh}/{mm}/{ss}
        //1. 内部：hmi:path
        //2. U盘：usb:path
        string save_path = 1;
        //保存时长（天），超出表示保存不下
        uint32 save_days_u8 = 2;
        //最大保存条数，超出表示保存不下
        uint32 max_save_count_u32 = 3;
        //最大文件大小（MB）,超出则分隔为新文件
        uint32 max_file_size_u32 = 4;
        //最大存储大小，超出表示保存不下
        uint32 max_storage_size_u32 = 5;
        //最小剩余空间（MB），小于这个表示保存不下
        uint32 min_free_space_u32 = 6;
        //保存不下是否丢弃新数据，默认为否，也就是保存不下时，适当删除旧数据
        bool is_limit_drop_new = 7;
        //是否用csv格式保存，否则就是sqlite
        bool is_csv = 8;
        //清除控制变量
        optional VariableReference clear_control = 9;
    }
    //保存参数，支持同时保存到多个地方
    repeated SaveParam save_params = 10;
    //需要通知的元件(包括趋势图、数据表)
    repeated DataSamplingWidgetReference notify_widgets = 11;
}

//数据采样需要通知的元件
message DataSamplingWidgetReference {
    //页面ID
    uint32 page_id_u16 = 1;
    //元件ID
    uint32 widget_id_u16 = 2;
    //通知哪些数据(如果是空数组，则按采样到的数据数组直接推，如果非空值，则本数组值是采样数据的下标，只把这些下标的数组按顺序组成数组推)
    repeated uint32 notify_variable_index_u8 = 3;
}

