syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
//import "znd/project/v1/action.proto";
// import "znd/project/v1/dynamic.proto";
import "znd/project/v1/control.proto";
import "znd/project/v1/variable.proto";
import "znd/project/v1/style.proto";
import "znd/project/v1/text.proto";
import "znd/project/v1/common.proto";
import "znd/project/v1/active.proto";
// import "znd/project/v1/position.proto";

//页面元件列表(按画面ID存的,画面ID从1开始)
message PageWidgets {
    //元件列表
    repeated Widget widgets = 1;
    //属性绑定地址(key是元件ID，uint16，0表示页面本身)
    map<uint32, WidgetVariableReference> widget_variable_reference = 2;
    //动作列表，key是元件ID，uint16，0表示页面本身事件
    map<uint32, ActionList> widget_action_list = 3;
}

//元件变量引用
message WidgetVariableReference {
    //KEY是u16，前8位表示元件属性ID，后8位表示数组属性的下标
    map<int32, VariableReference> variable = 1;
}

//元件的设计器属性
/*message WidgetDesignerProperty {
    //元件类型
    //WidgetType widget_type = 1;
    //名称
    string name = 2;
    //备注
    string memo = 3;
    //是否可见
    bool visible = 4;
    //是否锁定(包括了锁定位置和大小)
    bool pin = 5;
    //是否只锁定比例
    bool lock_scale = 6;
    //读写类型
    ReadWriteMode rw_mode = 7;
}*/

//状态属性
message StateProperty {
    //状态数量,为0表示没有状态之分
    uint32 state_count_u8 = 1;
    //状态值偏移(读取的地址值-偏移值=状态值),默认为0
    int32 state_offset_value_i16 = 2;
    //状态值等值匹配(数组下标表示状态)
    repeated int32 state_match_value_i16 = 3;
    //状态值范围匹配，大于等于当前值，小于等于下个状态(数组下标表示状态)
    repeated int32 state_range_value_i16 = 4;
    //状态值组合（用多个位组成一个状态）
    repeated bool state_combine_bit = 5;
    //自动切换状态
    optional AutoSwitchState auto_switch_state = 6;
    //如果配置了错误状态，则状态不存在时，跳转到错误状态，否则保留在当前状态，所以需要optional
    optional uint32 error_state_u8 = 7;
    //找不到状态时的处理方式
    NoStateWay no_state_way = 8;
    //LSB,暂时不做(一个16位或者32位整型，根据不是0的最低位判断状态)
    //optional int32 state_lsb = 6;
}

//自动切换状态参数
message AutoSwitchState {
    //是否自动切换状态(地址可绑定，由变量通知)
    bool auto_switch = 1;
    //自动切换时间(单位100毫秒，地址可绑定，由变量通知)
    uint32 auto_switch_time_u16 = 2;
}

//确认参数
message ConfirmParam {
    //确认弹窗
    uint32 confirm_page_id_u16 = 1;
    //确认弹窗等待时间(100毫秒的个数，为0则一直等待)
    uint32 confirm_wait_time_u16 = 2;
    //确认超时是否执行，默念为否
    bool confirm_timeout_run = 3;
}

//元件类型
/*enum WidgetType {
    //未设置
    WIDGET_TYPE_UNSPECIFIED = 0;
    //克隆
    WIDGET_TYPE_CLONE = 1;
    //组合
    WIDGET_TYPE_GROUP = 2;
    //文本
    WIDGET_TYPE_TEXT = 3;
    //图像
    WIDGET_TYPE_GRAPHIC = 4;
    //位
    WIDGET_TYPE_BIT = 5;
    //字
    WIDGET_TYPE_WORD = 6;
    //功能按钮
    WIDGET_TYPE_BUTTON = 7;
    //数值输入
    WIDGET_TYPE_NUMBER = 8;
    //字符输入
    WIDGET_TYPE_STRING = 9;
}*/

//元件的通用属性
message Widget {
    //ID，同一页面内必须唯一
    uint32 id_u16 = 1;
    //名称（设计器使用）
    string name = 2;
    //备注（设计器使用）
    string memo = 3;
    //是否可见（设计器使用）
    bool visible = 4;
    //是否锁定(包括了锁定位置和大小)（设计器使用）
    bool pin = 5;
    //是否只锁定比例（设计器使用）
    bool lock_scale = 6;
    //读写类型（设计器使用）
    ReadWriteMode rw_mode = 7;
    //设计位置以及运行时初始位置(外观类不继承)
    Location location = 8;
    //大小(外观类不继承)
    Size size = 9;
    //控制属性
    optional EnableControl enable_control = 10;
    //状态相关属性
    optional StateProperty state_property = 11;
    //动作列表
    repeated uint32 actions_u16 = 12;
    //最小按压时间(100毫秒的个数，0表示不限制)
    uint32 min_press_time_u16 = 13;
    //最小按压间隔(100毫秒的个数，0表示不限制)
    uint32 min_press_interval_u16 = 14;
    //执行前确认弹窗
    optional ConfirmParam confirm_param = 15;
    //声音反馈(蜂鸣器)
    bool sound_feedback = 16;
    //图片（数组下标表示状态）(外观类有值使用，无值继承)
    repeated GraphicReference graphic = 17;
    //样式（数组下标表示状态）(外观类有值使用，无值继承)
    repeated StyleProperties style = 18;
    //样式同步标记(用位从高到低依次表示字体、文本颜色、背景颜色、位置、边框、阴影是否同步)
    uint32 style_sync_u8 = 19;
    //文本（数组下标表示状态）(外观类有值使用，无值继承)
    repeated TextReference text = 20;
    //其他属性以可选方式直接定义（只能继承，不会有值）
    oneof widget {
        WidgetClone widget_clone = 21;
        WidgetGroup widget_group = 22;
        WidgetText widget_text = 23;
        WidgetGraphic widget_graphic = 24;
        WidgetBit widget_bit = 25;
        WidgetWord widget_word = 26;
        WidgetNumber widget_number = 27;
        WidgetString widget_string = 28;
        WidgetButton widget_button = 29;
        WidgetMatrixButton widget_matrix_button = 30;
        WidgetTrendCurve widget_trend_curve = 31;
        WidgetScatterCurve widget_scatter_curve = 32;
        WidgetBarCurve widget_bar_curve = 33;
        WidgetXYCurve widget_xy_curve = 34;
        WidgetCalendar widget_calendar = 35;
        WidgetRollerList widget_roller_list = 36;
        WidgetDropList widget_drop_list = 37;
        WidgetMeter widget_meter = 38;
        WidgetRuler widget_ruler = 39;
    }
}

//克隆元件(除了标注为外观类的属性，其他都必须直接从克隆元件取值，不支持嵌套克隆)
message WidgetClone {
    //克隆元件所在的页面ID，无值表示当前页面
    optional uint32 page_id_u16 = 1;
    //克隆元件的ID(要去识别页面id)
    uint32 widget_id_u16 = 2;
}


//组合元件
message WidgetGroup {
    //下级元件
    repeated Widget sub_widgets = 1;
    //是否禁止解散(用于用户自定义组合元件)
    bool disable_ungroup = 2;
}

//静态文本
message WidgetText {
    //文本属性
    
}

//静态图片
message WidgetGraphic {

}

//读写类型
enum ReadWriteMode {
    //未设置
    READ_WRITE_MODE_UNSPECIFIED = 0;
    //只读
    READ_WRITE_MODE_READ_ONLY = 1;
    //只写
    READ_WRITE_MODE_WRITE_ONLY = 2;
    //读写同一个地址
    READ_WRITE_MODE_READ_WRITE_SAME = 3;
    //读写不同地址
    READ_WRITE_MODE_READ_WRITE_DIFFERENT = 4;
}

//位元件(固定两个状态)
message WidgetBit {
}

//字元件(状态数可设置)
message WidgetWord {
}

//按钮（固定两个状态，分别是正常和按下,0-正常，1-按下）
message WidgetButton {
    //按钮配置(如果配置了，表示有键盘功能)
    optional ButtonKeyCode button_key_code = 1;
}

//按钮键盘码
enum ButtonKeyCode {
    //未设置
    BUTTON_KEY_CODE_UNSPECIFIED = 0;
    //按钮行结束，换新行
    BUTTON_KEY_CODE_END_LINE = 1;
    //清空
    BUTTON_KEY_CODE_CLEAR = 2;
    //确定
    BUTTON_KEY_CODE_OK = 3;
    //取消
    BUTTON_KEY_CODE_CANCEL = 4;
    //ENTER
    BUTTON_KEY_CODE_ENTER = 5;
    //BACKSPACE
    BUTTON_KEY_CODE_BACKSPACE = 6;
    //DELETE
    BUTTON_KEY_CODE_DELETE = 7;
    //ESC
    BUTTON_KEY_CODE_ESCAPE = 8;
    //数字加
    BUTTON_KEY_CODE_NUMBER_ADD = 9;
    //数字减
    BUTTON_KEY_CODE_NUMBER_SUB = 10;
    //UP
    BUTTON_KEY_CODE_UP = 11;
    //DOWN
    BUTTON_KEY_CODE_DOWN = 12;
    //LEFT
    BUTTON_KEY_CODE_LEFT = 13;
    //RIGHT
    BUTTON_KEY_CODE_RIGHT = 14;
    //HOME
    BUTTON_KEY_CODE_HOME = 15;
    //END
    BUTTON_KEY_CODE_END = 16;
    //PAGE_UP
    BUTTON_KEY_CODE_PAGE_UP = 17;
    //PAGE_DOWN
    BUTTON_KEY_CODE_PAGE_DOWN = 18;
}

enum MatrixButtonLayout {
    //未设置、自定义
    MATRIX_BUTTON_LAYOUT_UNSPECIFIED = 0;
    //小写字母
    MATRIX_BUTTON_LAYOUT_LOWER_LETTER = 1;
    //大写字母
    MATRIX_BUTTON_LAYOUT_UPPER_LETTER = 2;
    //数字
    MATRIX_BUTTON_LAYOUT_NUMBER = 3;
    //特殊字符
    MATRIX_BUTTON_LAYOUT_SPECIAL_CHARACTER = 4;
}

//矩阵按钮/键盘
message WidgetMatrixButton {
    //按钮
    repeated MatrixButtonDefine buttons = 1;
    //样式
    optional StyleProperties style = 2;
    //布局
    MatrixButtonLayout layout = 3;
}

//矩阵按钮配置
message MatrixButtonDefine {
    //按钮功能，下标是矩阵按钮的状态
    repeated MatrixButtonFunction button_functions = 1;
    //执行动作
    repeated uint32 actions_u16 = 2;
    //样式(数组下标表示1按下和0抬起)
    repeated StyleProperties style = 3;
    //图片(数组下标表示1按下和0抬起)
    repeated GraphicReference graphic = 4;
    //矩阵按钮中的按钮宽度
    uint32 button_width_u16 = 5;
}

//矩阵按钮功能
message MatrixButtonFunction {
    oneof func {
        ButtonKeyCode button_key_code = 1;
        //输入按钮，Unicode码(UTF8)
        string unicode = 2;
    }
    //文本
    TextReference text = 3;
}

//数字显示和输入
message WidgetNumber {
    //当前值
    NumberValue current_value = 1;
    //最大值
    NumberValue max_value = 2;
    //最小值
    NumberValue min_value = 3;
    //是否支持输入
    bool support_input = 4;
    //键盘页面id
    uint32 keyboard_page_id_u16 = 5;
    //数据格式
    DataFormatType data_format = 6;
    //整数位数
    uint32 integer_digits_u8 = 7;
    //小数位数
    uint32 decimal_digits_u8 = 8;
    //是否隐藏前导零
    bool hide_leading_zero = 9;
    //是否显示千位分隔符
    bool show_thousands_separator = 10;
    //是否隐藏小数未尾零
    bool hide_trailing_zero = 11;
    //是否显示正号
    bool show_plus_sign = 12;
    //小于最小值的颜色 
    optional ColorReference less_than_min_color = 13;
    //小于最小值的闪烁频率（几个100ms，最大255，用u8）
    uint32 less_than_min_flash_time_u8 = 14;
    //大于最大值的颜色
    optional ColorReference greater_than_max_color = 15;
    //大于最大值的闪烁频率（几个100ms，最大255，用u8）
    uint32 greater_than_max_flash_time_u8 = 16;
    //密码模式
    bool password_mode = 17;
}

//字符串显示和输入
message WidgetString {
    //当前值
    string current_value = 1;
    //是否支持输入
    bool support_input = 2;
    //键盘页面id
    uint32 keyboard_page_id_u16 = 3;
    //编码类型(服务端推送的数据如果带类型，以服务端为准，这里定义的是输入时的类型)
    DataFormatType data_format = 4;
    //最大字符个数
    uint32 max_length_u16 = 5;
    //最小字符个数
    uint32 min_length_u16 = 6;
    //是否显示滚动条
    bool show_scrollbar = 7;
    //密码模式
    bool password_mode = 8;
}


//未找到状态时的处理方式
enum NoStateWay {
    NO_STATE_WAY_UNSPECIFIED = 0;//未设置，则默认使用最后一个状态
    NO_STATE_WAY_USE_LAST = 1;//用最后一个状态
    NO_STATE_WAY_USE_BLANK = 2;//用空白设置或默认设置
    //NO_STATE_WAY_NO_CHANGE = 3;//不改变当前值
}


//下拉框
message ComboBox {
    
}

// //常量列表数据来源(最后一项错误时显示的内容)
// message ConstDataSource {
//     repeated int32 value_i16 = 1;
//     repeated TextReference text = 2;
// }


//曲线（游标数据区用Input事件）
message WidgetCurve {
    //曲线类型
    CurveType curve_type = 1;
    //数据序列数量(几条曲线的意思，从0开始的ID)
    uint32 series_count_u8 = 2;
    //X轴显示方式配置
    oneof x_mode {
        //采样点像素距离
        uint32 point_distance_u16 = 3;
        //曲线上的一屏显示的点数
        uint32 point_count_u16 = 4;
        //一屏的时间范围（秒数）
        uint32 range_second_u16 = 5;
        //一屏的时间范围（分钟）
        uint32 range_minute_u16 = 6;
    }

    // 绘制方向
    ScrollDirection draw_direction = 7;

    //Y轴范围跟随哪个数据序列(设计端使用)
    optional uint32 y_range_use_series_u8 = 8;

    //数据序列配置
    message SeriesConfig {
        // 是否支持比例缩放
        bool support_scale = 1;
        // 是否显示渐变遮罩
        bool show_fade_mask = 2;
        //数据序列颜色
        ColorReference color = 3;
        // 点配置
        optional PointConfig point_config = 4;
        // 线配置
        optional LineConfig line_config = 5;
        // 柱状图配置
        optional BarConfig bar_config = 6;
        //Y轴
        optional NumberValue y_max_value = 7;
        optional NumberValue y_min_value = 8;
        // X轴
        optional NumberValue x_min_value = 9;
        optional NumberValue x_max_value = 10;
    }
    repeated SeriesConfig series_config = 9;

    //是否启用游标
    bool enable_cursor = 10;
    //游标颜色
    optional ColorReference cursor_color = 11;
    //游标宽度
    uint32 cursor_width_u8 = 12;

    // 是否显示实时时间
    bool use_current_time = 13;
    // 显示实时时间格式 
    TimeFormatType time_format= 14;
    // 显示实时日期格式 
    DateFormatType date_format= 15;

    // x 刻度值配置
    optional ScaleValueConfig scale_value_config_x = 16;
    // y 刻度值配置
    optional ScaleValueConfig scale_value_config_y = 17;
    // y2 刻度值配置
    optional ScaleValueConfig scale_value_config_y2 = 18;
}

// 趋势图
message WidgetTrendCurve {
    // 曲线配置
    WidgetCurve curve = 1;
    // 暂停恢复时间 max=60s
    uint32 pause_resume_time_u8 = 2;
    // 曲线显示类型 （实时/历史）
    bool is_history = 3;
    // 是否使用相对时间
    bool use_relative_time = 4;

    // 是否使用标签点
    bool use_time_label = 5;
    // 使用时间标签才可用 
    TimeFormatType time_format= 6;
    // 使用时间标签才可用 日期格式
    DateFormatType date_format= 7;
}

message WidgetXYCurve {
    // 曲线配置
    WidgetCurve curve = 1;
}

message WidgetBarCurve {
    // 曲线配置
    WidgetCurve curve = 1;
}

message WidgetScatterCurve {
    // 曲线配置
    WidgetCurve curve = 1;
}

message WidgetCalendar {
    enum CalendarType {
        // 未设置
        CALENDAR_TYPE_UNSPECIFIED = 0;
        // 下拉
        CALENDAR_TYPE_DROPDOWN = 1;
        // 箭头
        CALENDAR_TYPE_ARROW = 2;
    }
    CalendarType calendar_type = 1;

    // 星期名称背景颜色
    optional ColorReference week_bg_color = 3;
    // 星期名称字体颜色
    optional ColorReference week_font_color = 4;

    // 今天背景颜色
    optional ColorReference today_bg_color = 5;
    // 今天字体颜色
    optional ColorReference today_font_color = 6;

    // 可无
    // 特殊日期高亮颜色
    optional ColorReference highlight_color = 7;
    // 特殊日期高亮字体颜色
    optional ColorReference highlight_font_color = 8;

    message DateConfig {
        // 年
        uint32 year_u16 = 1;
        // 月
        uint32 month_u8 = 2;
        // 日
        uint32 day_u8 = 3;
    }
    repeated DateConfig highlight_date_config = 9;
};

message WidgetOptionList {
    // 数据来源
    ListDataResourceType data_resource_type = 1;
    // 选项 [预留最后一个位错误值]
    repeated string options = 2;
    // 选项值
    repeated string options_value = 3;
    // 选中颜色
    optional ColorReference selected_color = 5;
    // 行间距
    uint32 row_spacing_u8 = 6;
}

message WidgetRollerList {
    enum RollerListMode {
        ROLLER_LIST_MODE_UNSPECIFIED = 0;
        ROLLER_LIST_MODE_INFINITE = 1;
        ROLLER_LIST_MODE_LIMITED = 2;
    }
    // 滚动
    RollerListMode roller_list_mode = 1;
    // 列表
    WidgetOptionList list = 2;
    // 显示数量
    uint32 view_count_u8 = 3;
    // 默认索引
    uint32 def_index_u8 = 4;
};

message WidgetDropList {
    // 下拉
    Direction direction = 1;
    // 列表
    WidgetOptionList list = 2;
    // 下拉内容框颜色
    optional ColorReference list_bg_color = 3;
    // 下拉图标
    SymbolType drop_list_symbol = 4;
    // 是否常开
    bool always_open = 5;
};

message WidgetMeter {
    // 方向 设计端使用
    enum MeterDirection {
        // 未设置
        METER_DIRECTION_UNSPECIFIED = 0;
        // 顺时针
        METER_DIRECTION_CLOCKWISE = 1;
        // 逆时针
        METER_DIRECTION_COUNTERCLOCKWISE = 2;
    }
    MeterDirection meter_direction = 1;
    // 起始
    uint32 start_u16 = 2;
    // 结尾
    uint32 end_u16 = 3;
    // 范围
    // 下限值
    uint32 lower_limit_u16 = 4;
    // 上限值
    uint32 upper_limit_u16 = 5;
    // 宽度
    uint32 limit_width_u8 = 6;
    // 半径
    uint32 limit_radius_u8 = 7;
    // 下限颜色
    ColorReference lower_limit_color = 8;
    // 上限颜色
    ColorReference upper_limit_color = 9;
    // 中间颜色
    ColorReference middle_color = 10;

    // 刻度配置 (注意主刻度数量 为从左边开始的第多少个为一个主刻度)
    ScaleValueConfig scale_value_config = 11;

    // 标签颜色
    ColorReference scale_label_color = 12;
    // 标签小数位数
    uint32 decimal_places_u8 = 13;
    // 标签半径
    uint32 label_radius_u8 = 14;

    // 指针配置
    LineConfig pointer_config = 15;
    // 指针圆心配置
    optional PointConfig pointer_point_config = 16;
};

//曲线类型
enum CurveType {
    //未设置(默认为折线图)
    CURVE_TYPE_UNSPECIFIED = 0;
    //折线图
    CURVE_TYPE_LINE = 1;
    //柱状图
    CURVE_TYPE_BAR = 2;
    //散点图
    CURVE_TYPE_SCATTER = 3;
}

// 线类型
enum LineType {
    // 未设置
    LINE_TYPE_UNSPECIFIED = 0;
    // 实线
    LINE_TYPE_LINE = 1;
    // 虚线
    LINE_TYPE_DASH = 2;
    // 点线
    LINE_TYPE_DOT = 3;
}

//数据导出类型
enum DataFileType {
    // 未设置
    DATA_FILE_TYPE_UNSPECIFIED = 0;
    // 表格
    DATA_FILE_TYPE_CSV = 1;
    // PDF
    DATA_FILE_TYPE_PDF = 2;
}

// 数据导出类型
enum DataExportType {
    // 未设置
    DATA_EXPORT_TYPE_UNSPECIFIED = 0;
    // 数据导出
    DATA_EXPORT_TYPE_PER_DAY = 1;
    // 数据导出所有
    DATA_EXPORT_TYPE_ALL = 2;
}

//数据导出配置
message DataExportConfig {
    // 数据文件类型
    DataFileType file_type = 1;
    // 数据存储类型
    SaveLocation storage_type = 2;
    // 数据导出类型
    DataExportType export_type = 3;
};

// 刻度值配置
message ScaleValueConfig {
    // 刻度值是否显示
    bool show = 1;

    enum ScaleShowType {
        // 未设置
        SCALE_SHOW_TYPE_UNSPECIFIED = 0;
        // 显示点
        SCALE_SHOW_TYPE_POINT = 1;
        // 显示时间
        SCALE_SHOW_TYPE_TIME = 2;
    }
    ScaleShowType scale_show_type = 2;

    // 刻度值是否显示文字
    enum GridShowType {
        // 未设置
        GRID_SHOW_TYPE_UNSPECIFIED = 0;
        // 主刻度
        GRID_SHOW_TYPE_MAIN = 1;
        // 次刻度
        GRID_SHOW_TYPE_SECOND = 2;
    }
    // 刻度显示类型
    GridShowType grid_show_type = 3;
    
    message Scale{
        // 刻度数量
        uint32 scale_count_u8 = 4;
        // 刻度值宽度
        uint32 scale_width_u8 = 6;
        // 刻度绘制长度
        uint32 scale_draw_len = 8;
        // 刻度颜色
        optional ColorReference color = 13;
    };
    // 主刻度配置
    Scale scale_main = 4;
    // 次刻度配置
    Scale scale_sec = 5;
    // 刻度半径
    uint32 scale_radius_u8 = 10;
    // 最小值
    optional NumberValue min_value = 11;
    // 最大值
    optional NumberValue max_value = 12;
    // 网格线配置
    optional LineConfig grid_line_config = 15;
}

// 点配置
enum PointType {
    // 未设置
    POINT_TYPE_UNSPECIFIED = 0;
    // 圆点
    POINT_TYPE_CIRCLE = 1;
    // 方点
    POINT_TYPE_SQUARE = 2;
    // 三角
    POINT_TYPE_TRIANGLE = 3;
    // 菱形
    POINT_TYPE_DIAMOND = 4;
}

message PointConfig {
    // 点类型
    PointType point_type = 1;
    // 点半径
    uint32 radius_u8 = 2;
    // 点宽度
    uint32 width_u8 = 3;
    // 点高度
    uint32 height_u8 = 4;
    // 样式
    optional StyleProperties style = 5;
};

message LineConfig {
    // 线类型
    LineType line_type = 1;
    // 线开头是否圆角
    bool round_start = 2;
    // 线结尾是否圆角
    bool round_end = 3;
    // 虚线间隔
    uint32 dash_gap_u8 = 4;
    // 虚线宽度
    uint32 dash_width_u8 = 5;
    // 线宽度
    uint32 width_u8 = 6;
    // 长度
    uint32 length_u8 = 7;
    // 线颜色
    optional ColorReference color = 8;
};

message BarConfig {
    // 开头是否圆角
    uint32 round_u8 = 1;
    // 直径宽度
    uint32 width_u8 = 2;
    // 样式
    optional StyleProperties style = 3;
};

// 刻度尺类型
enum RulerType {
    // 未设置
    RULER_TYPE_UNSPECIFIED = 0;
    // 水平直线刻度尺
    RULER_TYPE_HORIZONTAL = 1;
    // 垂直直线刻度尺
    RULER_TYPE_VERTICAL = 2;
    // 弧形刻度尺
    RULER_TYPE_ARC = 3;
    // 圆形刻度尺
    RULER_TYPE_CIRCLE = 4;
}

// 刻度尺方向
enum RulerDirection {
    // 未设置
    RULER_DIRECTION_UNSPECIFIED = 0;
    // 刻度在上方/左侧
    RULER_DIRECTION_TOP_LEFT = 1;
    // 刻度在下方/右侧
    RULER_DIRECTION_BOTTOM_RIGHT = 2;
    // 刻度在两侧
    RULER_DIRECTION_BOTH = 3;
}

// 刻度标签位置
enum RulerLabelPosition {
    // 未设置
    RULER_LABEL_POSITION_UNSPECIFIED = 0;
    // 标签在刻度外侧
    RULER_LABEL_POSITION_OUTSIDE = 1;
    // 标签在刻度内侧
    RULER_LABEL_POSITION_INSIDE = 2;
    // 标签在刻度中心
    RULER_LABEL_POSITION_CENTER = 3;
}

// 刻度尺组件
message WidgetRuler {
    // 刻度尺类型
    RulerType ruler_type = 1;
    // 刻度方向
    RulerDirection direction = 2;
    // 标签位置
    RulerLabelPosition label_position = 3;

    // 基础刻度配置
    ScaleValueConfig scale_config = 4;

    // 刻度尺主线配置
    optional LineConfig main_line = 5;
    // 刻度尺背景颜色
    optional ColorReference background_color = 6;

    // 标签颜色
    optional ColorReference label_color = 7;
    // 标签偏移距离
    uint32 label_offset_u8 = 8;

    // 弧形刻度尺专用：起始角度（度）
    uint32 start_angle_u16 = 9;
    // 弧形刻度尺专用：结束角度（度）
    uint32 end_angle_u16 = 10;
    // 弧形刻度尺专用：半径
    uint32 radius_u16 = 11;
    // 弧形刻度尺专用：中心点X偏移
    int32 center_offset_x_i16 = 12;
    // 弧形刻度尺专用：中心点Y偏移
    int32 center_offset_y_i16 = 13;

    // 是否显示标签
    bool show_labels = 14;
    // 是否显示主线
    bool show_main_line = 15;
    // 是否显示网格线
    bool show_grid_lines = 16;
    // 是否自动计算刻度间距
    bool auto_spacing = 17;

    // 当前值（用于高亮显示）
    optional NumberValue current_value = 18;
    // 自定义刻度标记列表
    repeated RulerCustomMark custom_marks = 19;
}

// 自定义刻度标记
message RulerCustomMark {
    // 标记值
    NumberValue value = 1;
    // 标签文本（可选）
    optional string label = 2;
    // 标记颜色
    optional ColorReference color = 3;
    // 标记线宽度
    uint32 width_u8 = 4;
    // 标记长度
    uint32 length_u8 = 5;
};