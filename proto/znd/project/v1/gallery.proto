syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;

//工程图库列表
message ProjectGalleryList {
    //工程图库源文件列表
    map<uint32, ProjectGallery> gallery_list = 1;
    //工程图库生成后图片文件列表（供界面端使用）
    map<uint32, GeneratedImage> generated_image_list = 2;
}

//工程图库源文件
message ProjectGallery {
    //用md5作为识别图片文件的key
    string md5 = 2;
    //图片宽度
    int32 width_i16 = 3;
    //图片高度
    int32 height_i16 = 4;
    //图片文件大小
    uint32 file_size_u32 = 5;
    //图片类型
    GalleryFileType file_type = 6;
    //引用计数
    int32 ref_count_i16 = 7;
}

//图片类型
enum GalleryFileType {
    GALLERY_FILE_TYPE_UNSPECIFIED = 0;
    GALLERY_FILE_TYPE_PNG = 1;

    GALLERY_FILE_TYPE_JPEG = 2;
    GALLERY_FILE_TYPE_BMP = 3;
    GALLERY_FILE_TYPE_GIF = 4;
    GALLERY_FILE_TYPE_TIFF = 5;
    GALLERY_FILE_TYPE_ICO = 6;
    GALLERY_FILE_TYPE_WEBP = 7;
}

//生成后图片文件
message GeneratedImage {
    //图片id
    // uint32 id = 1;
    //工程图库源文件id
    uint32 project_gallery_id = 2;
    //图片宽度
    int32 width_i16 = 3;
    //图片高度
    int32 height_i16 = 4;
    //引用计数
    int32 ref_count_i16 = 5;
}


