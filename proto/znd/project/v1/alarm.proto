syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
import "znd/project/v1/variable.proto";
import "znd/project/v1/common.proto";
// import "znd/project/v1/script.proto";
import "znd/project/v1/action.proto";
// import "znd/project/v1/logic.proto";
import "znd/project/v1/text.proto";

//报警配置
message AlarmConfig {
    //报警组名称(下标表示组号，0-255，如果下标没到255的，显示默认名称GroupN)
    repeated string alarm_group_name = 1;
    //报警项
    repeated AlarmItem alarm_items = 2;
    //报警保存选项（数组是表示多个位置存放）
    repeated SaveOption save_option = 3;
}

//报警等级
enum AlarmLevel {
    //未定义
    ALARM_LEVEL_UNSPECIFIED = 0;
    //低
    ALARM_LEVEL_LOW = 1;
    //中
    ALARM_LEVEL_MIDDLE = 2;
    //高
    ALARM_LEVEL_HIGH = 3;
    //紧急
    ALARM_LEVEL_EMERGENCY = 4;
}

//报警
message AlarmItem {
    //报警id
    int32 id = 1;
    //报警名称
    string name = 2;
    //报警组
    int32 group = 3;
    //报警等级
    AlarmLevel level = 4;
    //触发条件
    VariableReference trigger_conditions = 5;
    //根据条件是否启用(暂不实现)
    VariableReference enable_conditions = 6;
    //报警文本
    oneof alarm_text {
        //文本标签id
        uint32 tag_id_u16 = 7;
        //输入的文本
        TextTag input = 8;
    }
    //触发时动作
    repeated Action trigger_actions = 9;
    //恢复时动作
    repeated Action recover_actions = 10;
    //确认时动作
    repeated Action confirm_actions = 11;
    //内容前景色
    optional uint32 text_color = 12;
    //内容背景色
    optional uint32 bg_color = 13;
    //内容字体
    optional int32 font_id = 14;
}