syntax = "proto3";
// import "znd/project/v1/address.proto";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;

//保护类型
enum ProtectType {
    //不保护
    PROTECT_TYPE_UNSPECIFIED = 0;
    //修改保护，表示只读
    PROTECT_TYPE_MODIFY = 1;
    //打开保护，表示不让打开
    PROTECT_TYPE_OPEN = 2;
}

//位置类型
enum PositionType {
    POSITION_TYPE_UNSPECIFIED = 0;
    POSITION_TYPE_LEFT_TOP = 1;
    POSITION_TYPE_CENTER_TOP = 2;
    POSITION_TYPE_RIGHT_TOP = 3;
    POSITION_TYPE_LEFT_CENTER = 4;
    POSITION_TYPE_CENTER_CENTER = 5;
    POSITION_TYPE_RIGHT_CENTER = 6;
    POSITION_TYPE_LEFT_BOTTOM = 7;
    POSITION_TYPE_CENTER_BOTTOM = 8;
    POSITION_TYPE_RIGHT_BOTTOM = 9;
}

message Location {
    int32 left_i16 = 1;
    int32 top_i16 = 2;
}

message Size {
    int32 width_i16 = 1;
    int32 height_i16 = 2;
}

//适用于边距之类的，比如margin或者padding
message PositionOffset {
    int32 left_i16 = 1;
    int32 top_i16 = 2;
    int32 right_i16 = 3;
    int32 bottom_i16 = 4;
}

enum AlignType {
    ALIGN_TYPE_UNSPECIFIED = 0;//未定义
    ALIGN_TYPE_TOP_LEFT = 1;//左上对齐
    ALIGN_TYPE_TOP_MID = 2;//顶部居中
    ALIGN_TYPE_TOP_RIGHT = 3;//右上对齐

    ALIGN_TYPE_BOTTOM_LEFT = 4;//左下
    ALIGN_TYPE_BOTTOM_MID = 5;//下边居中
    ALIGN_TYPE_BOTTOM_RIGHT = 6;//右下角

    ALIGN_TYPE_LEFT_MID = 7;//左中对齐
    ALIGN_TYPE_RIGHT_MID = 8;//右边居中
    ALIGN_TYPE_CENTER = 9;//居正中

    // ALIGN_TYPE_JUSTIFY_TOP =10;     //顶部两端对齐
    // ALIGN_TYPE_JUSTIFY_CENTER = 11; //居中两端对齐
    // ALIGN_TYPE_JUSTIFY_BOTTOM = 12; //底部两端对齐
}


//保存位置
enum SaveLocation {
    SAVE_LOCATION_UNSPECIFIED = 0;
    SAVE_LOCATION_LOCAL = 1;
    SAVE_LOCATION_USB = 2;
    SAVE_LOCATION_CLOUD = 3;
}

//保存选项
message SaveOption {
    //保存位置
    SaveLocation save_location = 1;
    //目录名(含有规则)
    string dir_name = 2;
    //文件名(含有规则)
    string file_name = 3;
    //保留时长（天）
    int32 keep_days = 4;
    //保留条数
    int32 keep_count = 5;
    //超出后不保存，默认清掉旧的
    bool discard_when_full = 6;
}

//滚动方向
enum ScrollDirection {
    SCROLL_DIRECTION_UNSPECIFIED = 0;
    SCROLL_DIRECTION_LEFT_TO_RIGHT = 1;
    SCROLL_DIRECTION_RIGHT_TO_LEFT = 2;
    SCROLL_DIRECTION_TOP_TO_BOTTOM = 3;
    SCROLL_DIRECTION_BOTTOM_TO_TOP = 4;
}

// 方向
enum Direction {
    DIRECTION_UNSPECIFIED = 0;
    DIRECTION_LEFT = 1;
    DIRECTION_RIGHT = 2;
    DIRECTION_UP = 3;
    DIRECTION_DOWN = 4;
}

// 时间格式 HMS
enum TimeFormatType {
    // 未设置
    TIME_FORMAT_TYPE_UNSPECIFIED = 0;
    // 时间格式 HH:MM:SS
    TIME_FORMAT_TYPE_HMS = 1;
    // 时间格式 HH:MM
    TIME_FORMAT_TYPE_HM = 2;
    // 时间格式 MM:SS
    TIME_FORMAT_TYPE_MS = 3;
};

// 日期格式 YDM
enum DateFormatType {
    // 未设置 
    DATE_FORMAT_TYPE_UNSPECIFIED = 0;
    // 分隔符 /
    // 日期格式 MM/DD/YYYY
    DATE_FORMAT_TYPE_MDY = 1;
    // 日期格式 DD/MM/YYYY
    DATE_FORMAT_TYPE_DMY= 2;
    // 日期格式 YYYY/MM/DD
    DATE_FORMAT_TYPE_YMD = 3;
    // 日期格式 MM/DD
    DATE_FORMAT_TYPE_MD = 4;
    // 日期格式 DD/MM
    DATE_FORMAT_TYPE_DM = 5;
    // 日期格式 DD
    DATE_FORMAT_TYPE_D = 6;

    /// 分隔符 .
    // 日期格式 MM.DD.YYYY
    DATE_FORMAT_TYPE_MDY_POINT = 7;
    // 日期格式 DD.MM.YYYY
    DATE_FORMAT_TYPE_DMY_POINT = 8;
    // 日期格式 YYYY.MM.DD
    DATE_FORMAT_TYPE_YMD_POINT = 9;
    // 日期格式 MM.DD
    DATE_FORMAT_TYPE_MD_POINT = 10;
    // 日期格式 DD.MM
    DATE_FORMAT_TYPE_DM_POINT = 11;
    // 日期格式 DD
    DATE_FORMAT_TYPE_D_POINT = 12;

    // 分隔符 -
    // 日期格式 MM-DD-YYYY
    DATE_FORMAT_TYPE_MDY_DASH = 13;
    // 日期格式 DD-MM-YYYY
    DATE_FORMAT_TYPE_DMY_DASH = 14;
    // 日期格式 YYYY-MM-DD
    DATE_FORMAT_TYPE_YMD_DASH = 15;
    // 日期格式 MM-DD
    DATE_FORMAT_TYPE_MD_DASH = 16;
    // 日期格式 DD-MM
    DATE_FORMAT_TYPE_DM_DASH = 17;
    // 日期格式 DD
    DATE_FORMAT_TYPE_D_DASH = 18;
};

// 数据来源
enum ListDataResourceType {
    // 自定义
    LIST_DATA_RESOURCE_TYPE_UNSPECIFIED = 0;
    // 历史数据日期
    LIST_DATA_RESOURCE_TYPE_HISTORY_DATE = 1;
    // 用户账号
    LIST_DATA_RESOURCE_TYPE_USER_ACCOUNT = 2;
}

// 图标样式
// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
enum SymbolType {
    SYMBOL_TYPE_BULLET = 0;
    SYMBOL_TYPE_AUDIO = 1;
    SYMBOL_TYPE_VIDEO = 2;
    SYMBOL_TYPE_LIST = 3;
    SYMBOL_TYPE_OK = 4;
    SYMBOL_TYPE_CLOSE = 5;
    SYMBOL_TYPE_POWER = 6;
    SYMBOL_TYPE_SETTINGS = 7;
    SYMBOL_TYPE_HOME = 8;
    SYMBOL_TYPE_DOWNLOAD = 9;
    SYMBOL_TYPE_DRIVE = 10;
    SYMBOL_TYPE_REFRESH = 11;
    SYMBOL_TYPE_MUTE = 12;
    SYMBOL_TYPE_VOLUME_MID = 13;
    SYMBOL_TYPE_VOLUME_MAX = 14;
    SYMBOL_TYPE_IMAGE = 15;
    SYMBOL_TYPE_TINT = 16;
    SYMBOL_TYPE_PREV = 17;
    SYMBOL_TYPE_PLAY = 18;
    SYMBOL_TYPE_PAUSE = 19;
    SYMBOL_TYPE_STOP = 20;
    SYMBOL_TYPE_EJECT = 21;
    SYMBOL_TYPE_LEFT = 22;
    SYMBOL_TYPE_RIGHT = 23;
    SYMBOL_TYPE_PLUS = 24;
    SYMBOL_TYPE_MINUS = 25;
    SYMBOL_TYPE_EYE_OPEN = 26;
    SYMBOL_TYPE_EYE_CLOSE = 27;
    SYMBOL_TYPE_WARNING = 28;
    SYMBOL_TYPE_SHUFFLE = 29;
    SYMBOL_TYPE_UP = 30;
    SYMBOL_TYPE_DOWN = 31;
    SYMBOL_TYPE_LOOP = 32;
    SYMBOL_TYPE_DIRECTORY = 33;
    SYMBOL_TYPE_UPLOAD = 34;
    SYMBOL_TYPE_CALL = 35;
    SYMBOL_TYPE_CUT = 36;
    SYMBOL_TYPE_COPY = 37;
    SYMBOL_TYPE_SAVE = 38;
    SYMBOL_TYPE_BARS = 39;
    SYMBOL_TYPE_ENVELOPE = 40;
    SYMBOL_TYPE_CHARGE = 41;
    SYMBOL_TYPE_PASTE = 42;
    SYMBOL_TYPE_BELL = 43;
    SYMBOL_TYPE_KEYBOARD = 44;
    SYMBOL_TYPE_GPS = 45;
    SYMBOL_TYPE_FILE = 46;
    SYMBOL_TYPE_WIFI = 47;
    SYMBOL_TYPE_BATTERY_FULL = 48;
    SYMBOL_TYPE_BATTERY_3 = 49;
    SYMBOL_TYPE_BATTERY_2 = 50;
    SYMBOL_TYPE_BATTERY_1 = 51;
    SYMBOL_TYPE_BATTERY_EMPTY = 52;
    SYMBOL_TYPE_USB = 53;
    SYMBOL_TYPE_BLUETOOTH = 54;
    SYMBOL_TYPE_TRASH = 55;
    SYMBOL_TYPE_EDIT = 56;
    SYMBOL_TYPE_BACKSPACE = 57;
    SYMBOL_TYPE_SD_CARD = 58;
    SYMBOL_TYPE_NEW_LINE = 59;
    SYMBOL_TYPE_DUMMY = 60;
};