syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
//import "znd/project/v1/variable.proto";

message HardwareSetting {
    // repeated SerialConfig serial_configs = 1;   //串口参数移到设备中定义，没有设备不可能定义串口
    optional EthernetConfig ethernet1_config = 1;   //以太网1参数
    optional EthernetConfig ethernet2_config = 2;   //以太网2参数
    optional EthernetConfig wifi_config = 3;   //WIFI参数
    optional EthernetConfig mobile_config = 4;   //4G参数
    optional int32 screen_brightness = 5; // 屏幕亮度
    optional int32 screensaver_time = 6; // 屏保时间
    optional int32 screen_off_time = 7; // 熄屏时间
    optional string boot_password = 8; // 开机密码
    optional string admin_password = 9; // 后台密码
    optional string download_password = 10; // 下载密码
    optional bool buzzer_enabled = 11; // 蜂鸣器是否启用
    optional bool ntp_enabled = 12; // 是否启用网络对时
    optional string ntp_sync_server = 13; // 对时服务器地址
    optional string timezone = 14; // 时区
}

//以太网口配置
message EthernetConfig {
    IpDnsType ip_type = 1; //IP类型
    optional string ip = 2;
    optional string subnet_mask = 3;
    optional string gateway = 4;
    IpDnsType dns_type = 5; //DNS类型
    optional string dns1 = 6;
    optional string dns2 = 7;
    optional bool disable = 8;
    repeated WifiRemember wifi_remembers = 9; //WIFI记住
    optional ApnConfig apn = 10; //APN(4G才有用)

    enum IpDnsType {
        IP_DNS_TYPE_UNSPECIFIED = 0;
        IP_DNS_TYPE_STATIC = 1;
        IP_DNS_TYPE_DHCP = 2;
    }
}

//APN配置
message ApnConfig {
    string apn = 1; //APN
    string username = 2; //用户名
    string password = 3; //密码
    repeated KeyValue params = 4; //其他参数
    message KeyValue {
        string key = 1;
        string value = 2;
    }
}

//WIFI记住
message WifiRemember {
    string ssid = 1;
    string password = 2;
    bool auto_connect = 3; //是否自动连接
}

