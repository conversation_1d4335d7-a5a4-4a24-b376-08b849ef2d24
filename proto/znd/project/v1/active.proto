syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
// import "znd/project/v1/variable.proto";
import "znd/project/v1/action.proto";

/*
//单页面的元件活动属性
message PageWidgetActiveProperty {
    //属性绑定地址(key是元件ID，0表示页面本身)
    map<int32, WidgetPropertyAddress> widget_property_addresses = 1;
    //动作列表，key是元件ID，0表示页面本身事件
    map<int32, ActionList> widget_action_list = 2;
}*/

//动作列表
message ActionList {
    repeated Action actions = 1;
}

//事件id
enum EventEnum {
    EVENT_ENUM_UNSPECIFIED = 0;
    //元件按下
    EVENT_ENUM_WIDGET_PRESS = 1;
    //元件松开
    EVENT_ENUM_WIDGET_RELEASE = 2;
    //元件点击（点击事件表示同时触发按下和松开，实际动作列表没有对应的执行时机）
    EVENT_ENUM_WIDGET_CLICK = 3;
    //元件输入
    //协议：TLV格式
    //执行InputAction，根据其中的multi_address执行两种写入逻辑
    EVENT_ENUM_WIDGET_INPUT = 4;
}



//考虑不止是属性，还有事件或者动作
//执行动作时，前端只发送动作ID，后端根据动作ID，在actions.proto中找到对应的动作
//然后根据动作的参数，如果需要变量的值，由后端自行提前采集，执行动作
//元件属性ID
enum WidgetPropertyEnum {
    WIDGET_PROPERTY_ENUM_UNSPECIFIED = 0;
    //位置LEFT
    WIDGET_PROPERTY_ENUM_LOCATION_LEFT = 1;
    //位置TOP
    WIDGET_PROPERTY_ENUM_LOCATION_TOP = 2;
    //宽
    WIDGET_PROPERTY_ENUM_SIZE_WIDTH = 3;
    //高
    WIDGET_PROPERTY_ENUM_SIZE_HEIGHT = 4;
    //控制能否使用的条件运算结果(TRUE/FALSE)
    WIDGET_PROPERTY_ENUM_ENABLE_CONTROL_CONDITION = 5;
    //当前状态值
    WIDGET_PROPERTY_ENUM_STATE_VALUE = 8;
    //当前显示值
    WIDGET_PROPERTY_ENUM_DISPLAY_VALUE = 9;
    //动作中的页面跳转ID
    WIDGET_PROPERTY_ENUM_ACTION_PAGE_ID = 10;
    //动作中位操作写入地址（如果位动作是切换，则这个地址需要纳入采集，否则只是存入位写入动作）
    WIDGET_PROPERTY_ENUM_ACTION_BIT_WRITE_ADDRESS = 11;
    //动作中位操作脉冲宽度
    WIDGET_PROPERTY_ENUM_ACTION_BIT_PULSE_WIDTH = 12;
    //动作中字操作写入地址（如果字动作需要原值，则这个地址需要纳入采集，否则只是存入字写入动作）
    WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_ADDRESS = 13;
    //动作中字操作写入值或加减值(根据dataFormat写到NumberValue中不同的值)
    WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_VALUE = 14;
    //动作中字操作最大值
    WIDGET_PROPERTY_ENUM_ACTION_WORD_MAX_VALUE = 15;
    //动作中字操作最小值
    WIDGET_PROPERTY_ENUM_ACTION_WORD_MIN_VALUE = 16;
    //截屏动作中的页面ID
    WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_PAGE_ID = 19;
    //截屏动作中的保存路径
    WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_SAVE_PATH = 20;
    //截屏动作中的文件名
    WIDGET_PROPERTY_ENUM_ACTION_SCREENSHOT_FILE_NAME = 21;
    //复制数据动作中的源地址
    WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_SOURCE_ADDRESS = 22;
    //复制数据动作中的目标地址
    WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_TARGET_ADDRESS = 23;
    //复制数据动作中的数据长度
    WIDGET_PROPERTY_ENUM_ACTION_COPY_DATA_LENGTH = 24;
    //播放声音中的声音ID
    WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_SOUND_ID = 25;
    //播放声音中的持续时长
    WIDGET_PROPERTY_ENUM_ACTION_PLAY_SOUND_DURATION = 26;
    //延时动作中的延时时长
    WIDGET_PROPERTY_ENUM_ACTION_DELAY_MILLISECONDS = 27;
    //操作记录中的文本(多语言要用JSON)
    WIDGET_PROPERTY_ENUM_ACTION_LOG_TEXT = 28;
    //输入动作中的写入地址
    WIDGET_PROPERTY_ENUM_ACTION_INPUT_WRITE_ADDRESS = 29;
    //输入动作中的最大值
    WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_VALUE = 30;
    //输入动作中的最小值
    WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_VALUE = 31;
    //输入动作中的最短长度
    WIDGET_PROPERTY_ENUM_ACTION_INPUT_MIN_LENGTH = 32;
    //输入动作中的最长长度
    WIDGET_PROPERTY_ENUM_ACTION_INPUT_MAX_LENGTH = 33;
    //MQTT推送中的主题
    WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_TOPIC = 34;
    //MQTT推送中的消息
    WIDGET_PROPERTY_ENUM_ACTION_PUSH_MQTT_MESSAGE = 35;
    //HTTP调用中的API
    WIDGET_PROPERTY_ENUM_ACTION_HTTP_API = 36;
    //确认弹窗中的页面ID
    WIDGET_PROPERTY_ENUM_CONFIRM_PAGE_ID = 37;
    //按下时的声音反馈
    WIDGET_PROPERTY_ENUM_PRESS_SOUND_ID = 38;
    //按下时的声音反馈持续时长
    WIDGET_PROPERTY_ENUM_PRESS_SOUND_DURATION = 39;
    //样式背景色（通过这个一定是颜色值，不会是主题颜色）
    WIDGET_PROPERTY_ENUM_STYLE_BACKGROUND_COLOR = 40;
    //非法输入
    WIDGET_PROPERTY_ENUM_INVALID_INPUT = 41;
    //文本
    WIDGET_PROPERTY_ENUM_TEXT = 42;
}

//图元属性从150开始，前面留给公用属性
enum WidgetTextPropertyEnum {
    WIDGET_TEXT_PROPERTY_ENUM_UNSPECIFIED = 0;
    //文本内容(通过这个发送的，如果是字符串，直接显示，如果要多语言，需要JSON格式的字符串)
    WIDGET_TEXT_PROPERTY_ENUM_TEXT = 150;
    //文本颜色
    WIDGET_TEXT_PROPERTY_ENUM_TEXT_COLOR = 151;
}

//图元属性从150开始，前面留给公用属性
enum WidgetMatrixButtonPropertyEnum {
    WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_UNSPECIFIED = 0;
    //状态0（松开时）的按钮文本
    WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S0 = 150;
    //状态1（按下时）的按钮文本
    WIDGET_MATRIX_BUTTON_PROPERTY_ENUM_BUTTON_TEXT_S1 = 151;
}

//图元属性从150开始，前面留给公用属性
enum WidgetBitPropertyEnum {
    WIDGET_BIT_PROPERTY_ENUM_UNSPECIFIED = 0;
    //文本内容，下标是状态(通过这个发送的，如果是字符串，直接显示，如果要多语言，需要JSON格式的字符串)
    WIDGET_BIT_PROPERTY_ENUM_TEXT = 150;
    //文本颜色（不同下标表示不同状态）
    WIDGET_BIT_PROPERTY_ENUM_TEXT_COLOR = 151;
}

//图元属性从150开始，前面留给公用属性
enum WidgetWordPropertyEnum {
    WIDGET_WORD_PROPERTY_ENUM_UNSPECIFIED = 0;
    //文本内容，下标是状态(通过这个发送的，如果是字符串，直接显示，如果要多语言，需要JSON格式的字符串)
    WIDGET_WORD_PROPERTY_ENUM_TEXT = 150;
    //文本颜色（不同下标表示不同状态）
    WIDGET_WORD_PROPERTY_ENUM_TEXT_COLOR = 151;
}

//图元属性从150开始，前面留给公用属性
enum WidgetNumberPropertyEnum {
    WIDGET_NUMBER_PROPERTY_ENUM_UNSPECIFIED = 0;
    //显示数值
    WIDGET_NUMBER_PROPERTY_ENUM_VALUE = 150;
    //文本颜色
    WIDGET_NUMBER_PROPERTY_ENUM_TEXT_COLOR = 151;
}

//图元属性从150开始，前面留给公用属性
enum WidgetStringPropertyEnum {
    WIDGET_STRING_PROPERTY_ENUM_UNSPECIFIED = 0;
    //文本内容
    WIDGET_STRING_PROPERTY_ENUM_VALUE = 150;
    //文本颜色
    WIDGET_STRING_PROPERTY_ENUM_TEXT_COLOR = 151;
}

//曲线
enum WidgetCurvePropertyEnum {
    WIDGET_CURVE_PROPERTY_ENUM_UNSPECIFIED = 0;
    //曲线Y轴最小值
    WIDGET_CURVE_PROPERTY_ENUM_Y_MIN_VALUE = 150;
    //曲线Y轴最大值
    WIDGET_CURVE_PROPERTY_ENUM_Y_MAX_VALUE = 151;

    //采样点像素距离
    WIDGET_CURVE_PROPERTY_ENUM_X_POINT_DISTANCE = 152;
    //曲线上的一屏显示的点数
    WIDGET_CURVE_PROPERTY_ENUM_X_POINT_COUNT = 153;
    //一屏的时间范围（秒数）
    WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_SECOND = 154;
    //一屏的时间范围（分钟）
    WIDGET_CURVE_PROPERTY_ENUM_X_RANGE_MINUTE = 155;

    //游标是否显示
    WIDGET_CURVE_PROPERTY_ENUM_CURSOR_VISIBLE = 156;

    //数据序列的动态配置
    //曲线宽度（下标是数据序列的序号）
    WIDGET_CURVE_PROPERTY_ENUM_SERIES_WIDTH = 157;
    //曲线是否显示（下标是数据序列的序号）
    WIDGET_CURVE_PROPERTY_ENUM_SERIES_VISIBLE = 158;
    //曲线最小值（下标是数据序列的序号）
    WIDGET_CURVE_PROPERTY_ENUM_SERIES_MIN_VALUE = 159;
    //曲线最大值（下标是数据序列的序号）
    WIDGET_CURVE_PROPERTY_ENUM_SERIES_MAX_VALUE = 160;
}

enum WidgetRollerPropertyEnum {
    WIDGET_ROLLER_PROPERTY_ENUM_UNSPECIFIED = 0;
    //滚动模式
    WIDGET_ROLLER_PROPERTY_ENUM_ROLLER_MODE = 150;
}

enum WidgetDropListPropertyEnum {
    WIDGET_DROP_LIST_PROPERTY_ENUM_UNSPECIFIED = 0;
    //下拉方向
    WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_DIRECTION = 150;
    //下拉图标
    WIDGET_DROP_LIST_PROPERTY_ENUM_DROP_LIST_SYMBOL = 151;
    //是否常开
    WIDGET_DROP_LIST_PROPERTY_ENUM_ALWAYS_OPEN = 152;
}

enum WidgetMeterPropertyEnum {
    WIDGET_METER_PROPERTY_ENUM_UNSPECIFIED = 0;
    // 上限值
    WIDGET_METER_PROPERTY_ENUM_UPPER_LIMIT = 150;
    // 下限值
    WIDGET_METER_PROPERTY_ENUM_LOWER_LIMIT = 151;
}