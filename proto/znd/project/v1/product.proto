syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;

//产品信息
message Product {
    //产品id
    int32 product_id_u32 = 1;
    //产品名称
    string product_name = 2;
    //产品包含的工程id列表
    repeated ProductProject project_list = 3;
}

//产品包含的工程信息
message ProductProject {
    //工程id
    int32 project_id_u32 = 1;
    //工程版本id列表
    repeated int32 project_version_id_u32 = 2;
    //是否最新版本
    int32 latest_version_id_u32 = 3;
}
