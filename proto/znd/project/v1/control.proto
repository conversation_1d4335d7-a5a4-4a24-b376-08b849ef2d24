syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
//import "znd/project/v1/address.proto";

//启用控制
message EnableControl {
    //用条件来控制是否启用
    //repeated DataCompareCondition data_conditions = 1;

    //用权限来控制是否启用(最大只到255，用byte或uint8)
    repeated uint32 permission_bits_u8 = 2;

    //根据控制端所在位置来判断是否启用（为空表示不限制，有值表示符合了才能使用）
    repeated TerminalLocation terminal_location = 3;

    //根据控制端类型来判断是否启用
    repeated TerminalType terminal_types = 4;

    //禁用外观
    DisableWidgetMode disable_mode = 5;

    //禁用时点击弹出提示窗口ID(0表示不弹出)
    uint32 disable_prompt_page_id_u16 = 6;
}

//控制端位置
enum TerminalLocation {
    //未设置
    TERMINAL_LOCATION_UNSPECIFIED = 0;
    //本机
    TERMINAL_LOCATION_LOCAL = 1;
    //局域网
    TERMINAL_LOCATION_LAN = 2;
    //互联网
    TERMINAL_LOCATION_WAN = 3;
    //局域网(匿名访客)
    TERMINAL_LOCATION_LAN_GUEST = 4;
    //局域网(已登录用户)
    TERMINAL_LOCATION_LAN_USER = 5;
    //互联网(匿名访客)
    TERMINAL_LOCATION_WAN_GUEST = 6;
    //互联网(已登录用户)
    TERMINAL_LOCATION_WAN_USER = 7;
}

//控制端类型
enum TerminalType {
    //未设置
    TERMINAL_TYPE_UNSPECIFIED = 0;
    //本机
    TERMINAL_TYPE_LOCAL = 1;
    //APP
    TERMINAL_TYPE_APP = 2;
    //PC
    TERMINAL_TYPE_PC = 3;
    //其他HMI
    TERMINAL_TYPE_HMI = 4;
    //电视大屏
    TERMINAL_TYPE_TV = 5;
}

// 禁用外观
enum DisableWidgetMode {
    //不改变
    DISABLE_WIDGET_MODE_UNSPECIFIED = 0;
    //禁用时隐藏
    DISABLE_WIDGET_MODE_HIDE = 1;
    //禁用但不变灰
    DISABLE_WIDGET_MODE_NORMAL = 2;
    //禁用变灰
    DISABLE_WIDGET_MODE_GRAY = 3;
    //禁用显示禁用图标
    DISABLE_WIDGET_MODE_ICON = 4;
    //禁用变灰且显示禁用图标
    DISABLE_WIDGET_MODE_GRAY_ICON = 5;
}