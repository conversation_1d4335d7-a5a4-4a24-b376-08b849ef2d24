syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
//版本号
message Version {
    uint32 major_u8 = 1;
    uint32 minor_u8 = 2;
    uint32 patch_u16 = 3;
    uint32 build_u16 = 4;
}

//工程对象类型
enum ProjectObjectType {
    //未设置
    PROJECT_OBJECT_TYPE_UNSPECIFIED = 0;
    //工程信息（唯一）
    PROJECT_OBJECT_TYPE_PROJECT_INFO = 1;
    //显示列表
    PROJECT_OBJECT_TYPE_DISPLAY_LIST = 2;
    //页面元件（一个页面一个文件）
    PROJECT_OBJECT_TYPE_PAGE_WIDGETS = 3;
    //设备列表
    PROJECT_OBJECT_TYPE_TUNNEL_DEVICE_LIST = 4;
    //变量定义（一个设备一个文件,0表示公用，比如动态变量和表达式）
    PROJECT_OBJECT_TYPE_VARIABLE = 5;
    //样式配置文件（唯一）
    PROJECT_OBJECT_TYPE_STYLE_CONFIG = 6;
    //文本标签库（0是用户建的，其他用pageId保存）
    PROJECT_OBJECT_TYPE_TEXT_TAG_LIBRARY = 7;
    //字体文件（一个字体一个文件）
    PROJECT_OBJECT_TYPE_FONT_FILE = 8;
    //图片列表
    PROJECT_OBJECT_TYPE_IMAGE_LIST = 9;
    //图片转换后文件（一个图片一个文件）
    PROJECT_OBJECT_TYPE_IMAGE_FILE = 10;
    //工程图库列表(这个还是会下载到设备端,后续上载时可以从云端根据md5读源文件)
    PROJECT_OBJECT_TYPE_PROJECT_GALLERY_LIST = 11;
    //图片源文件（工程图库，一个图片一个文件，这个不会下载到设备端）
    PROJECT_OBJECT_TYPE_PROJECT_GALLERY_FILE = 12;
    //数据配置（数据采样、报警、操作记录、数据库）
    PROJECT_OBJECT_TYPE_DATA_CONFIG = 13;
    //配方配置
    PROJECT_OBJECT_TYPE_RECIPE_CONFIG = 14;
    //工程上次编辑的状态
    PROJECT_OBJECT_TYPE_PROJECT_EDIT_STATUS = 15;
    //页面的缩略图
    PROJECT_OBJECT_TYPE_PAGE_THUMBNAIL = 16;
    //字体使用文本(ID是字体定义ID,如果不用上载，则不用下载到设备端)
    PROJECT_OBJECT_TYPE_FONT_DEFINITION_TEXT = 17;
}

enum HardwareType {
    HARDWARE_TYPE_UNSPECIFIED = 0;
    HARDWARE_TYPE_HMI = 1;
    HARDWARE_TYPE_BOX = 2;
    HARDWARE_TYPE_GATEWAY = 3;
    HARDWARE_TYPE_PC = 10;
}

message ProjectInfo {
    HardwareType hardware_type = 1;//HMI类型
    Version create_builder_version = 2;//创建的组态版本
    Version update_builder_version = 3;//更新时的组态版本
    Version min_firmware_version = 4;//最低固件版本号
    optional string design_password = 5;//设计打开密码
    optional string upload_password = 6;//上载密码，为空不允许上载
    optional string download_password = 7;//下载密码(这个应该改到HMI硬件中去设置？)
    optional string uncompile_password = 8;//反编译密码，为空不允许反编译
    optional string protect_password = 9;//内容保护密码
    optional string hardware_bind_password = 10;//HMI硬件绑定密码（逻辑待梳理）
    //uint32 project_id_u32 = 11;//工程唯一id(从云端申请的)
    //bool is_local_project = 12;//是否是本地工程
    // string project_name = 13;//工程名称(这里面不该有工程名称，在线工程是在线保存，本地工程是靠工程目录名或文件名，不然复制工程就成问题了)
    //uint32 project_version_id_u32 = 14;//工程版本id(升版本时从云端申请)
}

//工程上次编辑的状态
message ProjectEditStatus {
    //bool changed = 1;//是否发生变更
    int32 editor_view = 2;//当前编辑器视图
    int32 project_config_index = 3;//当前工程配置选中项
    string iot_config_view = 4;//当前IOT配置选中项
    int32 active_page_id = 5;//当前编辑的页面
    int32 zoom = 6;//当前缩放比例
    int32 page_state_value = 7;//当前页面状态值
    int32 page_language_no = 8;//当前页面语言值
    int32 page_theme_no = 9;//当前页面主题值
}

/*message ProjectIndexFile {
    bytes random_data = 1;
    uint32 project_id_u32 = 2;
    string project_name = 3;
    bool is_local_project = 4;//是否是本地工程
}*/


// //保存在工程中的图片文件，其他地方通过md5值引用
// message ProjectImage {
//     string md5 = 1;                 //md5值（裁剪后的md5值）
//     PictureFormatType type = 2;     //图片格式
//     optional bytes content = 3;     //图片内容(如果原始文件过大，则导入时提示用户会自动裁剪)
// }

// //独立的图库文件，类似威纶通的，可以被共享、下载、浏览
// message GalleryFile {
//     message GalleryKeyWord {
//         KeyWordMap key_word = 1;
//     }
//     //图库中的多状态图片格式
//     message GalleryMultiPicture {
//         int32 state_count = 1;
//         repeated int32 single_picture_index = 2;
//         repeated int32 key_word_indexs = 3;
//     }

//     //图库中的单文件图片格式
//     message GallerySinglePicture {
//         PictureFormatType type = 1;
//         uint32 size = 2;
//         string md5 = 3;
//         optional bytes org_content = 4; //原始文件，下载时可以去除
//         optional bytes use_content = 5; //根据工程分辨率和使用尺寸，裁剪到最大大小的尺寸，下载时需要
//         repeated int32 key_word_indexs = 6; //整理后的keyword的index
//     }

//     repeated GallerySinglePicture single_pictures = 1;
//     repeated GalleryMultiPicture multi_pictures = 2; //多状态的，其中图片一定在单图片中
//     repeated GalleryKeyWord key_words = 3;
// }

// //存在磁盘上的单文件图片，用于导入到工程或者图库，导入后会转换为GalleryPicture
// message PictureFile {
//     PictureFormatType type = 1;
//     uint32 size = 2;
//     bytes content = 3;
//     repeated KeyWordMap key_words = 4;
// }

// message KeyWordMap {
//     string key_word_en = 1;
//     string key_word_cn = 2;
// }


// enum PictureFormatType {
//     PICTURE_FORMAT_TYPE_UNSPECIFIED = 0;
//     PICTURE_FORMAT_TYPE_BMP = 1;
//     PICTURE_FORMAT_TYPE_JPG = 2;
//     PICTURE_FORMAT_TYPE_PNG = 3;
//     PICTURE_FORMAT_TYPE_GIF = 4;
//     PICTURE_FORMAT_TYPE_SVG = 5;
//     PICTURE_FORMAT_TYPE_ICO = 6;
// }



// message StateLangTextProps {
//     int32 state_value = 1;
//     int32 lang_id = 2;
//     optional string content = 3; //如果不是用文本库，则这边手工输入
//     Font font = 4;
//     AlignType text_align = 5;
//     AlignType textarea_align = 6;
//     Location textarea_location = 7;
//     uint32 font_color = 8;
// }









