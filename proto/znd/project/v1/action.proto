syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
import "znd/project/v1/variable.proto";
import "znd/project/v1/text.proto";

//动作
message Action {
    //执行时机
    ActionTiming action_timing = 1;
    //匹配id，客户端会传来matchId，如果进行对比（比如子元件的ID或者高级图元的子事件ID）
    optional int32 match_id_u8 = 2;
    //是否等待执行结果
    bool wait_result = 3;
    //具体动作
    oneof action {
        ActionPage page = 4;
        ActionBitOpera bit_opera = 5;
        ActionWordWriteValue word_write_value = 6;
        ActionWordAddValue word_add_value = 7;
        ActionWordSubValue word_sub_value = 8;
        ActionWordKeepAddValue word_keep_add_value = 9;
        ActionWordKeepSubValue word_keep_sub_value = 10;
        ActionWordAutoAddSubValue word_auto_add_sub_value = 11;
        ActionWordAddSubValue word_add_sub_value = 12;
        ActionWordKeepAddSubValue word_keep_add_sub_value = 13;
        ActionInput input = 14;
        ActionScript script = 16;
        ActionSystem system = 17;
        ActionCopyDataParam copy_data = 18;
        ActionPlaySound play_sound = 19;
        ActionOperationLog operation_log = 20;
        ActionPushMqtt push_mqtt = 21;
        ActionHttp http = 22;
        ActionDelay delay = 23;
    }
}

//执行时机
enum ActionTiming {
    //未设置
    ACTION_TIMING_UNSPECIFIED = 0;
    //执行前
    ACTION_TIMING_BEFORE = 1;
    //按下时
    ACTION_TIMING_PRESS = 2;
    //抬起时
    ACTION_TIMING_RELEASE = 3;
    //输入时
    ACTION_TIMING_INPUT = 4;
    //批量输入时
    ACTION_TIMING_BATCH_INPUT = 5;
    //画面打开时(以本动作所属的pageId为对比)
    ACTION_TIMING_OPEN_PAGE = 6;
    //画面关闭时(以本动作所属的pageId为对比)
    ACTION_TIMING_CLOSE_PAGE = 7;
    //屏幕熄屏时(此类任务的pageId=0)
    ACTION_TIMING_SCREEN_BLACK = 8;
    //屏幕亮屏时(此类任务的pageId=0)
    ACTION_TIMING_SCREEN_LIGHT = 9;
    //应用启动时(此类任务的pageId=0)
    ACTION_TIMING_APP_START = 10;
    //应用退出时(仅PC端，或者HMI端点击重启时，同样此类任务的pageId=0)
    ACTION_TIMING_APP_EXIT = 11;
    //执行成功后
    ACTION_TIMING_AFTER_SUCCESS = 12;
    //执行失败后
    ACTION_TIMING_AFTER_FAILURE = 13;
    //执行后
    ACTION_TIMING_AFTER = 14;
}

//输入内容
message ActionInput {
    //写入数据地址
    repeated VariableReference write_variable = 1;
    //是否多地址写入，是则一个地址写入一个值，否则只把第1个地址当起始地址写入
    bool multi_address = 2;
}

//延时
message ActionDelay {
    //延时毫秒(范围：50-10000)
    DataReferenceUInt16 delay_milliseconds_i16 = 1;
}

//操作记录
message ActionOperationLog {
    //记录的文本内容
    oneof content {
        //文本标签id
        uint32 tag_id_u16 = 1;
        //输入的文本
        TextTag input = 2;
    }
}

//页面跳转类型
enum PageActionType {
    PAGE_ACTION_TYPE_UNSPECIFIED = 0;
    //切换基本窗口
    PAGE_ACTION_TYPE_BASE_CHANGE = 1;
    //返回主页
    PAGE_ACTION_TYPE_BASE_HOME = 2;
    //返回上一页
    PAGE_ACTION_TYPE_BASE_BACK = 3;
    //前进到刚才的页面
    PAGE_ACTION_TYPE_BASE_FORWARD = 4;
    //跳到下个窗口号
    PAGE_ACTION_TYPE_BASE_NEXT = 5;
    //跳到上个窗口号
    PAGE_ACTION_TYPE_BASE_PREV = 6;
    //切换公共窗口
    PAGE_ACTION_TYPE_COMMON_CHANGE = 7;
    //弹出窗口
    PAGE_ACTION_TYPE_POPUP_SHOW = 8;
    //关闭最后一个弹出窗口
    PAGE_ACTION_TYPE_POPUP_CLOSE_LAST = 9;
    //关闭所有弹出窗口
    PAGE_ACTION_TYPE_POPUP_CLOSE_ALL = 10;
}

// 窗口跳转动作
message ActionPage {
    // 动作类型
    PageActionType action_type = 1;
    // 指定要操作的窗口ID（有些类型不需要）
    optional DataReferenceUInt16 page_id = 2;
}

// 执行脚本
message ActionScript {
    // 执行程序ID
    int32 program_id_i16 = 1;
}

//位操作,写入地址从active.proto中获取
message ActionBitOpera {
    //位写入模式
    BitSetMode bit_write_mode = 1;
    //写入地址
    VariableReference write_variable = 2;
    //脉冲宽度（几个100ms，理论最大255，实际限制最大15），仅在脉冲情况才有此参数
    optional DataReferenceUInt8 pulse_width = 3;
}

//位操作模式
enum BitSetMode {
    //未设置
    BIT_SET_MODE_UNSPECIFIED = 0;
    //设置ON按钮
    BIT_SET_MODE_SET_ON = 1;
    //设置OFF按钮
    BIT_SET_MODE_SET_OFF = 2;
    //切换按钮
    BIT_SET_MODE_TOGGLE = 3;
    //按下时设ON，释放时设OFF
    BIT_SET_MODE_PRESS_ON_RELEASE_OFF = 4;
    //按下时设OFF，释放时设ON
    BIT_SET_MODE_PRESS_OFF_RELEASE_ON = 5;
    //设置ON脉冲
    BIT_SET_MODE_SET_ON_PULSE = 6;
    //设置OFF脉冲
    BIT_SET_MODE_SET_OFF_PULSE = 7;
}

//字写值
message ActionWordWriteValue {
    //写入地址
    VariableReference write_variable = 1;
    //写入值(如果这里为空，表示前端会传过来值和类型用于写入)
    optional DataReference write_value = 2;
}

//字加值
message ActionWordAddValue {
    //写入地址
    VariableReference write_variable = 1;
    //读取地址
    optional VariableReference read_variable = 2;
    //加值
    DataReferenceNumber add_value = 3;
    //最大值
    DataReferenceNumber max_value = 4;
}

//字持续递加
//当按压时间超过触发时间时，以递加间隔递加值，松开停止
message ActionWordKeepAddValue {
    //写入地址
    VariableReference write_variable = 1;
    //读取地址
    optional VariableReference read_variable = 2;
    //加值
    DataReferenceNumber add_value = 3;
    //最大值
    DataReferenceNumber max_value = 4;
    //触发时间(单位100ms)
    int32 trigger_time_u8 = 5;
    //递加间隔(单位100ms)
    int32 add_interval_u8 = 6;
}

//字减值，写入地址从active.proto中获取
message ActionWordSubValue {
    //写入地址
    VariableReference write_variable = 1;
    //读取地址
    optional VariableReference read_variable = 2;
    //减值
    DataReferenceNumber sub_value = 3;
    //最小值
    DataReferenceNumber min_value = 4;
}

//字持续递减
//当按压时间超过触发时间时，以递减间隔递减值，松开停止
message ActionWordKeepSubValue {
    //写入地址
    VariableReference write_variable = 1;
    //读取地址
    optional VariableReference read_variable = 2;
    //减值
    DataReferenceNumber sub_value = 3;
    //最小值
    DataReferenceNumber min_value = 4;
    //触发时间(单位100ms)
    int32 trigger_time_u8 = 5;
    //递减间隔(单位100ms)
    int32 sub_interval_u8 = 6;
}

//字加减值,写入地址从active.proto中获取
message ActionWordAddSubValue {
    //加减值方式
    enum Mode {
        //未设置
        MODE_UNSPECIFIED = 0;
        //先加到最大值后回到最小值
        MODE_ADD_LOOP = 1;
        //先减到最小值后回到最大值
        MODE_SUB_LOOP = 2;
        //先加，到限制后减，再到最小值后再加，循环往复
        MODE_ADD_OSCILLATE = 3;
        //先减，到限制后加，再到最大值后再减，循环往复
        MODE_SUB_OSCILLATE = 4;
    }
    //加减值方式
    Mode mode = 1;
    //写入地址
    VariableReference write_variable = 2;
    //读取地址
    optional VariableReference read_variable = 3;
    //加减值
    DataReferenceNumber add_sub_value = 4;
    //最大值
    DataReferenceNumber max_value = 5;
    //最小值
    DataReferenceNumber min_value = 6;
}

//字持续加减值
//当按压时间超过触发时间时，以递减值递减值，松开停止
message ActionWordKeepAddSubValue {
    //加减值方式
    enum Mode {
        //未设置
        MODE_UNSPECIFIED = 0;
        //先加到最大值后回到最小值
        MODE_ADD_LOOP = 1;
        //先减到最小值后回到最大值
        MODE_SUB_LOOP = 2;
        //先加，到限制后减，再到最小值后再加，循环往复
        MODE_ADD_OSCILLATE = 3;
        //先减，到限制后加，再到最大值后再减，循环往复
        MODE_SUB_OSCILLATE = 4;
    }
    //加减值方式
    Mode mode = 1;
    //写入地址
    VariableReference write_variable = 2;
    //读取地址
    optional VariableReference read_variable = 3;
    //加减值
    DataReferenceNumber add_sub_value = 4;
    //最大值
    DataReferenceNumber max_value = 5;
    //最小值
    DataReferenceNumber min_value = 6;
    //触发时间(单位100ms)
    int32 trigger_time_u8 = 7;
    //间隔时间(单位100ms)
    int32 interval_u8 = 8;
}

//字自动加减值
//当按压时间超过触发时间时，以递减值递减值，松开停止
message ActionWordAutoAddSubValue {
    //加减值方式
    enum Mode {
        //未设置
        MODE_UNSPECIFIED = 0;
        //加到最大值
        MODE_ADD_TO_MAX = 1;
        //减到最小值
        MODE_SUB_TO_MIN = 2;
        //先加到最大值后回到最小值
        MODE_ADD_LOOP = 3;
        //先减到最小值后回到最大值
        MODE_SUB_LOOP = 4;
        //先加，到限制后减，再到最小值后再加，循环往复
        MODE_ADD_OSCILLATE = 5;
        //先减，到限制后加，再到最大值后再减，循环往复
        MODE_SUB_OSCILLATE = 6;
    }
    //加减值方式
    Mode mode = 1;
    //写入地址
    VariableReference write_variable = 2;
    //读取地址
    optional VariableReference read_variable = 3;
    //加减值
    DataReferenceNumber add_sub_value = 4;
    //最大值
    DataReferenceNumber max_value = 5;
    //最小值
    DataReferenceNumber min_value = 6;
    //间隔时间(单位100ms)
    int32 interval_u8 = 7;
}

//系统动作
message ActionSystem {
    SystemActionType system_action_type = 1;
    //部分系统动作的值，比如屏幕亮度
    optional DataReferenceInt16 param_value = 2;
}

//系统动作类型
enum SystemActionType {
    SYSTEM_ACTION_TYPE_UNSPECIFIED = 0;
    SYSTEM_ACTION_TYPE_OPEN_SETTING = 1;//打开设置后台
    SYSTEM_ACTION_TYPE_REBOOT = 2;//重启
    SYSTEM_ACTION_TYPE_CLEAR_BOM = 3;//清空配方
    SYSTEM_ACTION_TYPE_CLEAR_HISTORY = 4;//清空历史记录
    SYSTEM_ACTION_TYPE_CLEAR_ALARM = 5;//清空报警
    SYSTEM_ACTION_TYPE_CONFIRM_ALARM = 6;//确认所有报警
    SYSTEM_ACTION_TYPE_SCREEN_LIGHT = 7;//亮屏
    SYSTEM_ACTION_TYPE_SCREEN_SET_BRIGHTNESS = 8;//设置亮度
    SYSTEM_ACTION_TYPE_SCREEN_BLACK = 9;//立即熄屏
    SYSTEM_ACTION_TYPE_RESTORE_FACTORY = 10;//恢复出厂设置
    SYSTEM_ACTION_TYPE_TOUCH_CALIBRATION = 11;//触摸校准
    // SYSTEM_ACTION_TYPE_SHOW_NETWORK_INFO = 12;    //显示网络信息
    // SYSTEM_ACTION_TYPE_SHOW_SYSTEM_INFO = 13;    //显示系统信息
    // SYSTEM_ACTION_TYPE_SET_NETWORK = 14;    //设置网络
    // SYSTEM_ACTION_TYPE_SCREENSHOT = 15; //截屏操作（只能存到USB，用时间命名，不能重复）
    // SYSTEM_ACTION_TYPE_COPY_DATA = 16; //复制数据
}

// 亮度直接操作系统变量就好了
// message ActionScreenBrightness {
//     ScreenBrightnessType brightness_type = 1; //亮度类型
//     int32 brightness_value = 2; //亮度值或者增加减少的亮度值

//     enum ScreenBrightnessType {
//         SCREEN_BRIGHTNESS_TYPE_UNSPECIFIED = 0;
//         SCREEN_BRIGHTNESS_TYPE_MAX = 1; //调到最亮
//         SCREEN_BRIGHTNESS_TYPE_DEFAULT = 2; //调到默认亮度
//         SCREEN_BRIGHTNESS_TYPE_MIN = 3; //调到最暗
//         SCREEN_BRIGHTNESS_TYPE_SET = 4; //设置为指定亮度
//         SCREEN_BRIGHTNESS_TYPE_ADD = 5; //增加亮度
//         SCREEN_BRIGHTNESS_TYPE_SUB = 6; //减少亮度
//     }
// }

/*/截屏参数
message ActionScreenshotParam {
    optional int32 page_id = 1;//页面ID，如果无配置，则截当前屏幕
    optional bool is_print = 2;//是否立即打印
    optional bool is_save_local = 3;//是否保存到本地
    optional bool is_save_usb = 4;//是否保存到USB
    optional bool is_save_cloud = 5;//是否保存到云端
    optional string save_path = 6;//保存路径
    optional string file_name = 7;//截屏文件格式
}*/

//复制数据参数（源地址和目标地址从active.proto中获取）
message ActionCopyDataParam {
    //源起始地址
    VariableReference source_variable = 1;
    //目标起始地址
    VariableReference target_variable = 2;
    //数据长度
    int32 length = 3;
    //间隔时间(单位100ms)，如果有值，则是自动间隔执行，如果无值，则只执行一次
    optional DataReferenceUInt8 interval = 4;
    //是否执行
    optional DataReferenceBool is_execute = 5;
}

/*/按键参数
message ActionKey {
    //按键类型
    KeyCodeType key_code_type = 1;
    //按键码或UNICODE码
    uint32 key_code = 2;
}


enum KeyCodeType {
    KEY_CODE_TYPE_UNSPECIFIED = 0;
    KEY_CODE_TYPE_KEY_CODE = 1;//按钮码
    KEY_CODE_TYPE_UNICODE = 2;//ASCII/unicode码
}
*/

//播放声音
message ActionPlaySound {
    //声音ID(0-蜂鸣器)
    DataReferenceUInt16 sound_id = 1;
    //持续时长(未设置表示播放到完毕)
    optional DataReferenceUInt16 duration = 2;
}

//MQTT推送
message ActionPushMqtt {
    //MQTT主题(支持插入地址或标签)
    string topic = 1;
    //MQTT消息(支持插入地址或标签)
    string message = 2;
}

//HTTP调用
message ActionHttp {
    //HTTP方法
    HttpMethod http_method = 1;
    //API地址(要加上HTTP服务器前缀，支持插入地址或标签)
    string api = 2;
    //HTTP头
    repeated HttpKeyValue header = 3;
    //HTTP体
    repeated HttpKeyValue body = 4;
    //BODY类型
    optional HttpContentType body_type = 5;
}

enum HttpMethod {
    HTTP_METHOD_UNSPECIFIED = 0;
    HTTP_METHOD_GET = 1;
    HTTP_METHOD_POST = 2;
    HTTP_METHOD_PUT = 3;
    HTTP_METHOD_DELETE = 4;
}

enum HttpContentType {
    HTTP_CONTENT_TYPE_UNSPECIFIED = 0;
    HTTP_CONTENT_TYPE_FORM_DATA = 1;
    HTTP_CONTENT_TYPE_URLENCODED = 2;
    HTTP_CONTENT_TYPE_BINARY = 3;
    HTTP_CONTENT_TYPE_MSG_PACK = 4;
    HTTP_CONTENT_TYPE_RAW = 5;
}

message HttpKeyValue {
    string key = 1;
    string type = 2;
    string value = 3;
}