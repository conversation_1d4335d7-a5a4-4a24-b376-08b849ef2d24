syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
//import "znd/project/v1/address.proto";
// // 元件动态
// message WidgetDynamic {
//     // gif动画配置
//     optional DynamicImage image = 1;
//     // 缩放
//     optional LinearTransformValue scale = 2;
//     // 位置x
//     optional LinearTransformValue location_x = 3;
//     // 位置y
//     optional LinearTransformValue location_y = 4;
//     // 闪烁(通过状态来解决是否闪烁)
//     optional Blink blink = 5;
// }

//动态位置
/*message DynamicLocation {
    optional DataReference left = 1; 
    optional DataReference top = 2;
    optional DataReference width = 3;
    optional DataReference height = 4;
}*/

//闪烁
message Blink {
    optional int32 interval_time = 1;//间隔时间，单位毫秒, 未设置则不闪烁
    optional BlinkType blink_type = 2;//闪烁类型
    optional BlinkStyle blink_style = 3;//闪烁样式
}

enum BlinkType {
    BLINK_TYPE_UNSPECIFIED = 0;
    BLINK_TYPE_CHANGE_VISIBLE = 1;//显示状态切换
    BLINK_TYPE_CHANGE_STYLE = 2;//颜色变化
}

message BlinkStyle {
    optional uint32 background_color = 1;
    optional uint32 border_color = 2;
    optional uint32 text_color = 3;
}

// message AnimationLocationX {
//     DataReference x_value = 1;
//     int32 min_offset = 2;
//     int32 max_offset = 3;
//     int32 min_data_value = 4;
//     int32 max_data_value = 5;
// }

// message AnimationLocationY {
//     DataReference y_value = 1;
//     int32 min_offset = 2;
//     int32 max_offset = 3;
//     int32 min_data_value = 4;
//     int32 max_data_value = 5;
// }

//线性转换值（比如数据值从0-100，转换到100-1000）
/*message LinearTransformValue {
    optional DataReference data_value = 1;
    optional LinearTransform linear_transform = 2;
}*/

//缩放方向
enum ScaleDirectionType {
    SCALE_DIRECTION_TYPE_UNSPECIFIED = 0;
    SCALE_DIRECTION_TYPE_FULL = 1;//全方向
    SCALE_DIRECTION_TYPE_HORIZONTAL = 2;//水平
    SCALE_DIRECTION_TYPE_VERTICAL = 3;//垂直
    SCALE_DIRECTION_TYPE_LEFT = 4;//左
    SCALE_DIRECTION_TYPE_RIGHT = 5;//右
    SCALE_DIRECTION_TYPE_TOP = 6;//上
    SCALE_DIRECTION_TYPE_BOTTOM = 7;//下
}

//缩放类型
enum ScaleType {
    SCALE_TYPE_UNSPECIFIED = 0;
    SCALE_TYPE_ZOOM = 1;//缩放
    SCALE_TYPE_CUT = 2;//裁剪
}
//这个没用,直接用action里的字循环不就是了
// message AnimationSwitchState {
//     //用地址来控制是否切换，满足条件就按时间间隔和次数进行切换
//     common.v1.DataCompareCondition play_control_data = 1;
//     device.v1.DataReference interval_time = 2; //间隔时间，单位毫秒
//     //开始时的状态
//     optional device.v1.DataReference start_state = 3;
//     //停止时的状态
//     optional device.v1.DataReference stop_state = 4;
//     optional device.v1.DataReference loop_count = 5; //循环次数，0为无限循环
// }

//动态图片，暂时用不同状态不同主图，然后自动改变状态来实现
//动态图片，包括GIF和多张静态图片两种模式
// message DynamicImage {
//     // 静态图片地址
//     repeated string img_src = 1; //也可能组态端就是选择了多张静态图片
//     // 间隔时间，单位毫秒, 未设置则不切换
//     optional int32 interval_time = 2; 
//     // gif和图片二选一，如果性能不行，就在组态端自动换为多张静态图片
//     optional string gif_src = 3; 
// }
