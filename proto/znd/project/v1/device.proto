syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
import "znd/project/v1/variable.proto";
// import "device/v1/hardware.proto";

//串口参数全部设备存储，为了序列化
message TunnelDeviceList {
    //按通讯隧道编号存储(key是通讯号，u8)
    map<uint32, Tunnel> tunnels = 3;
    //按设备ID存储(key是设备ID，u8，从1开始，0表示工程用)
    map<uint32, Device> devices = 4;
    //设备编号映射，key是设备号，value是设备ID，都是u8
    map<uint32, uint32> device_no_map = 5;
    //自定义结构体数据类型，key是u8
    map<uint32, StructType> struct_data_types = 6;
    //索引寄存器信息(key是u8)
    map<uint32, IndexRegisterInfo> index_register_info = 7;
    //变量分类（key是u16）
    map<uint32, VariableClassify> variable_classifies = 8;
}

//变量分类
message VariableClassify {
    //名称
    string name = 1;
    //允许多个上级（用途不明确，只是估计有些场景有用）
    repeated uint32 parent_id_u16 = 2;
    //是否禁用
    bool disable = 3;
    //备注
    string memo = 4;
}

//索引寄存器信息
message IndexRegisterInfo {
    string name = 1;
    //类型分为：bool、int8、uint8、int16、uint16、int32、uint32、float32,无值则默认为int16
    DataFormatType data_format = 2;
    //初始值
    NumberValue init_value = 3;
    //同步PLC地址（双向同步）
    optional VariableReference sync_variable = 4;
    //是否禁用
    bool disable = 5;
    //备注
    string memo = 6;
}

/*enum TunnelType {
    TUNNEL_TYPE_UNSPECIFIED = 0;
    TUNNEL_TYPE_SERIAL = 1;//串口
    TUNNEL_TYPE_ETHERNET = 2;//以太网
    TUNNEL_TYPE_CLOUD = 3;//云设备（局域网内的可以直接加在以太网）
}*/

//通讯隧道
message Tunnel {
    string tunnel_type = 1; // 通道类型：COM、ETH
    optional int32 no = 2;//通道口
    string name = 3;//正常可由应用自动生成，这边默认为无设置
    string memo = 4;//备注
    bool disable = 5;//是否禁用
    CommunicationParams communication_params = 6;//通讯参数
    //串口参数
    optional SerialTunnelParam serial_param = 7;
    //仿真时的串口参数
    optional SerialTunnelParam simulated_serial_param = 8;
}

//设备
message Device {
    //设备名称
    string name = 2;
    //通讯隧道编号
    uint32 tunnel_id_u8 = 3;
    //设备品牌(设计端用)
    string device_brand = 4;
    //设备协议配置(设计端用)
    string device_config = 5;
    //协议类型
    ProtocolType protocol = 6;
    //协议参数
    repeated ProtocolParam protocol_params = 7;
    //是否禁用
    bool disable = 8;
    //备注
    string memo = 9;
    //设备参数
    oneof device_params {
        //以太网设备参数(以太网设备一定有这个，不然没有地址)
        EthernetDeviceParam ethernet_param = 11;
        //串口设备参数(串口设备不一定有，因为通道有了)
        SerialDeviceParam serial_param = 12;
        //云设备参数(云设备一定有这个，不然没有地址)
        CloudDeviceParam cloud_param = 13;
        //子设备列表及参数(如果有子设备，表示是动态设备)
        MultiDeviceParam multi_device_param = 14;
    }
    //站号
    DataReferenceUInt8 station = 15;
    //广播端口(可能不启用)
    optional DataReferenceUInt8 broadcast_port = 16;
    //其他参数（比如西门子的机架号、插槽号）
    map<uint32, DataReferenceInt16> extend_params = 17;
    //通讯参数(未设置则继承自通道)
    optional CommunicationParams communication_params = 18;
    //初始值列表
    repeated VariableInitValue init_values = 19;
    //父设备(如果有父设备，表示这是动态设备下的子设备)
    optional int32 parent_device_id = 21;
    //仿真时的设备参数(可能都没值，都没值时以真实参数为准，有可能真实设备是串口，仿真时却是以太网或者云设备)
    oneof simulated_device_params {
        //以太网设备参数(以太网设备一定有这个，不然没有地址)
        EthernetDeviceParam simulated_ethernet_param = 22;
        //串口设备参数(串口设备不一定有，因为通道有了)
        SerialDeviceParam simulated_serial_param = 23;
        //云设备参数(云设备一定有这个，不然没有地址)
        CloudDeviceParam simulated_cloud_param = 24;
    }
}

//设置初始值
message VariableInitValue {
    VariableReference variable = 1;
    AllTypeValue value = 2;
}

//协议本身特定参数
message ProtocolParam {
    ProtocolParamType param_type = 1;
    DataReferenceInt16 param_value = 2;
}

//协议参数类型
enum ProtocolParamType {
    PROTOCOL_PARAM_TYPE_UNSPECIFIED = 0;
    //MODBUS的基址（0-基址0，1-基址1）
    PROTOCOL_PARAM_TYPE_MODBUS_BASE = 1;
    //MODBUS的0区写功能码（0-自动判断、1-只用05、2-只用15）
    PROTOCOL_PARAM_TYPE_MODBUS_WRITE_0X = 2;
    //MODBUS的4区写功能码（0-自动判断、1-只用06、2-只用16）
    PROTOCOL_PARAM_TYPE_MODBUS_WRITE_4X = 3;
    //MODBUS的地址进制（0-十进制、8-八进制、16-十六进制）
    PROTOCOL_PARAM_TYPE_MODBUS_ADDRESS_SYSTEM = 4;
}

//协议类型
enum ProtocolType {
    PROTOCOL_TYPE_UNSPECIFIED = 0;
    PROTOCOL_TYPE_MODBUS_MASTER = 1;//主站，如果是COM，则使用RTU，否则使用以太网TCP
    PROTOCOL_TYPE_MODBUS_SLAVE = 2;//从站，如果是COM，则使用RTU，否则使用以太网TCP
    PROTOCOL_TYPE_MODBUS_MASTER_ASCII = 3;
    PROTOCOL_TYPE_MODBUS_SLAVE_ASCII = 4;//MODBUS从站

    PROTOCOL_TYPE_SIEMENS_PPI = 10;//西门子
    PROTOCOL_TYPE_SIEMENS_MPI2 = 11;//西门子
    PROTOCOL_TYPE_SIEMENS_MPI3 = 12;//西门子
    PROTOCOL_TYPE_SIEMENS_MPI4 = 13;//西门子
    PROTOCOL_TYPE_SIEMENS_S7COMM = 14;//西门子

    PROTOCOL_TYPE_MITSUBISHI_MC = 20;//三菱
    PROTOCOL_TYPE_MITSUBISHI_MC1E = 21;//三菱
    PROTOCOL_TYPE_MITSUBISHI_MC2E = 22;//三菱
    PROTOCOL_TYPE_MITSUBISHI_MC3E = 23;//三菱
    PROTOCOL_TYPE_MITSUBISHI_MC4C = 24;//三菱
    PROTOCOL_TYPE_MITSUBISHI_SLMP = 25;//三菱

    PROTOCOL_TYPE_OMRON_FINS = 30;//欧姆龙
    PROTOCOL_TYPE_OMRON_HOSTLINK = 31;//欧姆龙
    PROTOCOL_TYPE_OMRON_ENTERNET_CIP = 32;//欧姆龙

    PROTOCOL_TYPE_FATEK = 40;//永宏
    PROTOCOL_TYPE_VIGOR_VSPROTOCOL = 41;//丰炜
    PROTOCOL_TYPE_PANASONIC_NEWTOCOL = 42;//松下
    PROTOCOL_TYPE_ROCKWELL_IDEC_SERIAL = 43;//罗克韦尔
    PROTOCOL_TYPE_AIBUS = 44;//宇电

    PROTOCOL_TYPE_MAXCOMM = 90;//MAXCOMM，连接另一台HMI设备
    PROTOCOL_TYPE_FREE = 98;//自由协议
    PROTOCOL_TYPE_PASS_THROUGH = 99;//透传协议（仅透传用）

    PROTOCOL_TYPE_LOCAL_HMI = 100;//本地设备
}

message EthernetDeviceParam {
    //用4个字节表示ip地址
    DataReferenceUInt32 ip_address = 1;
    //端口号
    DataReferenceUInt16 ip_port = 2;
    //是否UDP,否则为TCP
    bool is_udp = 4;
}

//多设备集合
message MultiDeviceParam {
    //子设备ID的列表，Tag上用站号指向此数组下标
    repeated int32 sub_device_ids = 1;
    //子设备索引，用于全局统一切换该设备，如果有了这个，图元上可以不选，如果图元有选，以图元为准
    optional int32 sub_device_index_register_u16 = 2;
    //索引偏移值，也就是把子设备索引的值，再加上这个值，才是子设备ID数组的下标
    int32 sub_device_index_offset_i16 = 3;
}

enum SerialType {
    SERIAL_TYPE_UNSPECIFIED = 0;//未定义
    SERIAL_TYPE_RS232 = 1;//RS232
    SERIAL_TYPE_RS422 = 2;//RS422
    SERIAL_TYPE_RS485 = 3;//RS485
}

enum ParityType {
    //未定义
    PARITY_TYPE_UNSPECIFIED = 0;
    //偶校验
    PARITY_TYPE_EVEN = 1;
    //奇校验
    PARITY_TYPE_ODD = 2;
}

enum StopBitsType {
    STOP_BITS_TYPE_UNSPECIFIED = 0;//未定义
    STOP_BITS_TYPE_1 = 1;//1个停止位
    STOP_BITS_TYPE_2 = 2;//2个停止位(不需要1.5个停止位，因为同行都没有)
}

enum DataBitsType {
    DATA_BITS_TYPE_UNSPECIFIED = 0;//未定义
    DATA_BITS_TYPE_7 = 7;//7个数据位
    DATA_BITS_TYPE_8 = 8;//8个数据位
}

//串口设备参数
message SerialDeviceParam {
    //波特率
    DataReferenceUInt16 baudrate = 3;
    //数据位(DataBitsType)
    DataReferenceUInt8 databits = 4;
    //校验位(ParityType)
    DataReferenceUInt8 parity = 5;
    //停止位(StopBitsType)
    DataReferenceUInt8 stopbits = 6;
}

//串口通道参数
message SerialTunnelParam {
    //串口编号，从1开始
    DataReferenceUInt8 serial_no = 1;
    //串口类型（SerialType）
    DataReferenceUInt8 serial_type = 2;
    //波特率
    DataReferenceUInt16 baudrate = 3;
    //数据位(DataBitsType)
    DataReferenceUInt8 databits = 4;
    //校验位(ParityType)
    DataReferenceUInt8 parity = 5;
    //停止位(StopBitsType)
    DataReferenceUInt8 stopbits = 6;
}

//云设备参数
message CloudDeviceParam {
    //云设备KEY
    string cloud_device_key = 5;
}

// enum ParityType {
//     PARITY_TYPE_UNSPECIFIED = 0;    //无校验位
//     PARITY_TYPE_ODD = 1;    //奇校验位
//     PARITY_TYPE_EVEN = 2;   //偶校验位
//     PARITY_TYPE_MARK = 3;   //标记校验位
//     PARITY_TYPE_SPACE = 4;  //空格校验位
// }

// enum StopBitsType {
//     STOP_BITS_TYPE_UNSPECIFIED = 0; //未定义
//     STOP_BITS_TYPE_1 = 10;   //1个停止位
//     STOP_BITS_TYPE_1_5 = 15; //1.5个停止位
//     STOP_BITS_TYPE_2 = 20;   //2个停止位
// }

// enum DataBitsType {
//     DATA_BITS_TYPE_UNSPECIFIED = 0; //未定义
//     DATA_BITS_TYPE_7 = 7;   //7个数据位
//     DATA_BITS_TYPE_8 = 8;   //8个数据位
// }

// enum BaudRateType {
//     BAUD_RATE_TYPE_UNSPECIFIED = 0; //未定义
//     BAUD_RATE_TYPE_110 = 110; //110
//     BAUD_RATE_TYPE_300 = 300; //300
//     BAUD_RATE_TYPE_600 = 600; //600
//     BAUD_RATE_TYPE_1200 = 1200; //1200
//     BAUD_RATE_TYPE_2400 = 2400; //2400
//     BAUD_RATE_TYPE_4800 = 4800; //4800
//     BAUD_RATE_TYPE_9600 = 9600; //9600
//     BAUD_RATE_TYPE_14400 = 14400; //14400
//     BAUD_RATE_TYPE_19200 = 19200; //19200
//     BAUD_RATE_TYPE_38400 = 38400; //38400
//     BAUD_RATE_TYPE_56000 = 56000; //56000
//     BAUD_RATE_TYPE_57600 = 57600; //57600
//     BAUD_RATE_TYPE_115200 = 115200; //115200
//     BAUD_RATE_TYPE_128000 = 128000; //128000
//     BAUD_RATE_TYPE_1875000 = 1875000; //1875000
// }

enum Word16Order {
    WORD16_ORDER_UNSPECIFIED = 0;//未定义
    WORD16_ORDER_1_2 = 1;//12
    WORD16_ORDER_2_1 = 2;//21
}

enum Word32Order {
    WORD32_ORDER_UNSPECIFIED = 0;//未定义
    WORD32_ORDER_1_2_3_4 = 1;//1234
    WORD32_ORDER_2_1_4_3 = 2;//2143
    WORD32_ORDER_3_4_1_2 = 3;//3412
    WORD32_ORDER_4_3_2_1 = 4;//4321
}

//通讯参数(所有参数可选，未设置作用对应定义的枚举常量DEFAULT值)
message CommunicationParams {
    //超时时间，单位毫秒(默认1000)
    optional uint32 timeout_u16 = 1;
    //重试次数，尝试次数之后再提示失败，失败之后还是会连(默认3次)
    optional uint32 retry_count_u8 = 2;
    //重试间隔时间，单位毫秒(默认1000)
    optional uint32 retry_interval_u16 = 3;
    //最小读取间隔时间，单位毫秒(默认1000)
    optional uint32 min_read_interval_u16 = 4;
    //位组间隔,指间隔多少后，放弃组在一包(默认5)
    optional uint32 bit_group_distance_u16 = 5;
    //位组最大数(默认128)
    optional uint32 bit_group_max_count_u16 = 6;
    //位写入最大长度(默认128)
    optional uint32 bit_write_max_length_u16 = 7;
    //字组间隔，指间隔多少后，放弃组在一包(默认5)
    optional uint32 word_group_distance_u16 = 8;
    //字组最大长度(默认64)
    optional uint32 word_group_max_count_u16 = 9;
    //字写入最大长度(默认64)
    optional uint32 word_write_max_length_u16 = 10;
    //错误提示时间，单位秒(-1表示不提示，0表示一直提示)
    optional int32 error_prompt_time_i16 = 12;
    //16位字组顺序(默认21)
    optional Word16Order int16_order = 13;
    //32位整型字组顺序(默认4321)
    optional Word32Order int32_order = 14;
    //32位浮点字组顺序(默认4321)
    optional Word32Order float32_order = 15;

    //以下是未设置的默认值

    enum TimeoutDefault {
        TIMEOUT_DEFAULT_UNSPECIFIED = 0;
        TIMEOUT_DEFAULT_VALUE = 1000;
    }
    enum RetryCountDefault {
        RETRY_COUNT_DEFAULT_UNSPECIFIED = 0;
        RETRY_COUNT_DEFAULT_VALUE = 3;
    }
    enum RetryIntervalDefault {
        RETRY_INTERVAL_DEFAULT_UNSPECIFIED = 0;
        RETRY_INTERVAL_DEFAULT_VALUE = 1000;
    }
    enum MinReadIntervalDefault {
        MIN_READ_INTERVAL_DEFAULT_UNSPECIFIED = 0;
        MIN_READ_INTERVAL_DEFAULT_VALUE = 1000;
    }
    enum BitGroupDistanceDefault {
        BIT_GROUP_DISTANCE_DEFAULT_UNSPECIFIED = 0;
        BIT_GROUP_DISTANCE_DEFAULT_VALUE = 5;
    }
    enum BitGroupMaxCountDefault {
        BIT_GROUP_MAX_COUNT_DEFAULT_UNSPECIFIED = 0;
        BIT_GROUP_MAX_COUNT_DEFAULT_VALUE = 128;
    }
    enum BitWriteMaxLengthDefault {
        BIT_WRITE_MAX_LENGTH_DEFAULT_UNSPECIFIED = 0;
        BIT_WRITE_MAX_LENGTH_DEFAULT_VALUE = 128;
    }
    enum WordGroupDistanceDefault {
        WORD_GROUP_DISTANCE_DEFAULT_UNSPECIFIED = 0;
        WORD_GROUP_DISTANCE_DEFAULT_VALUE = 5;
    }
    enum WordGroupMaxCountDefault {
        WORD_GROUP_MAX_COUNT_DEFAULT_UNSPECIFIED = 0;
        WORD_GROUP_MAX_COUNT_DEFAULT_VALUE = 64;
    }
    enum WordWriteMaxLengthDefault {
        WORD_WRITE_MAX_LENGTH_DEFAULT_UNSPECIFIED = 0;
        WORD_WRITE_MAX_LENGTH_DEFAULT_VALUE = 64;
    }
    enum ErrorPromptTimeDefault {
        ERROR_PROMPT_TIME_DEFAULT_UNSPECIFIED = 0;
    }
    enum Int16OrderDefault {
        INT16_ORDER_DEFAULT_UNSPECIFIED = 0;
        INT16_ORDER_DEFAULT_VALUE = 2;
    }
    enum Int32OrderDefault {
        INT32_ORDER_DEFAULT_UNSPECIFIED = 0;
        INT32_ORDER_DEFAULT_VALUE = 4;
    }
    enum Float32OrderDefault {
        FLOAT32_ORDER_DEFAULT_UNSPECIFIED = 0;
        FLOAT32_ORDER_DEFAULT_VALUE = 4;
    }
}

