syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
//import "znd/project/v1/address.proto";
// import "znd/project/v1/style.proto";
// import "znd/project/v1/position.proto";
// import "znd/project/v1/common.proto";

//文本标签库，只有用户建的，objectId为0
message TextTagLibrary {
    //key用u16
    map<int32, TextTag> tags = 1;
    //key用u16
    map<int32, TextTagClassify> classify = 2;
    // (移到style.proto中了，因为那边是工程统一存的样式，这里是跟画面相关的)字体定义，只有id为0的才有值，id是pageId的不会有此项
    // map<int32, FontDefinition> font_definitions = 3;
}

//文本标签引用
message TextReference {
    //文本内容
    oneof from {
        //文本标签id
        uint32 tag_id_u16 = 1;
        //输入的文本
        TextTag input = 2;
    }
}

//字体及使用到的文字(单独一个对象类型,ID是字体定义ID)
//后续优化时，可能可以只从这边读，而不从元件属性中读取，以节省空间占用，但要考虑怎么提升性能
message FontDefinitionText {
    //使用到的文字
    //key是uint32,是页面ID+元件ID(如果是报警和日志的元件，则根据元件上的字体，找到报警记录中的配置文本)
    map<uint32, WidgetUse> widget_use = 1;
    
    //字体定义使用到的内容
    message WidgetUse {
        //key是uint8,是属性ID
        map<uint32, TextReferenceArray> text = 1;
        //引用的文本，如果属性值不是数组，则只取第一个
        message TextReferenceArray {
            repeated TextReference text = 1;
        }
        //使用的预置类型
        repeated PresetType preset = 2;
        //预置类型
        enum PresetType {
            //未定义
            PRESET_TYPE_UNSPECIFIED = 0;
            //数字
            PRESET_TYPE_NUMBER = 1;
            //字母
            PRESET_TYPE_LETTER = 2;
            //特殊字符
            PRESET_TYPE_SPECIAL = 3;
            //中文精简
            PRESET_TYPE_CHINESE_MINI = 4;
            //中文全面
            PRESET_TYPE_CHINESE_FULL = 5;
        }
        //使用的来源
        repeated Source source = 3;
        //来源类型
        enum SourceType {
            //未定义
            SOURCE_TYPE_UNSPECIFIED = 0;
            //变量（根据变量类型：数字、字母、特殊字符、中文精简、中文全面）
            SOURCE_TYPE_VARIABLE = 1;
            //文本标签
            SOURCE_TYPE_TEXT_TAG = 2;
            //报警
            SOURCE_TYPE_ALARM = 3;
            //操作记录
            SOURCE_TYPE_OPERATION = 4;
            //数据采样
            SOURCE_TYPE_SAMPLING = 5;
        }
        //数据来源
        message Source {
            SourceType type = 1;
            //key是u16，是各种对象的ID
            uint32 id = 2;
        }
    }
}



//文本标签分类
message TextTagClassify {
    // int32 id = 1;
    string name = 2;
    optional int32 parent_id = 3;//父分类id
}

//多语言文本
message MultiLanguageText {
    //key是LanguageType枚举值, uint8
    map<uint32, string> content = 1;
}

//文本标签
message TextTag {
    oneof from {
        //多语言
        MultiLanguageText multi_lang = 1;
        //单语言
        string single_lang = 2;
    }
    //多行文本
    bool multi_line = 3;
    //所属分类id(设计端使用)
    uint32 classify_id_u16 = 4;
}







// //字体模板引用
// message FontTemplateReference {
//     int32 template_id = 1;
//     //以下可以不设置，使用字体属性中的默认值
//     optional string font_name = 3;
//     optional int32 font_size = 4;
//     optional FontStyle font_style = 5;
//     optional common.v1.AlignType text_align = 6;
//     // optional uint32 font_color = 7;
//     // optional uint32 font_bg_color = 8;
//     // optional common.v1.PositionOffset text_padding = 9;
// }

// //字体引用
// message FontReference {
//     //引用字体模板
//     optional int32 template_id = 1;
//     //以下属性有，则覆盖字体模板的
//     FontProperties properties = 2;
// }

//字体模板
// message FontTemplate {
//     int32 id = 1;
//     string name = 2;
//     FontProperties properties = 3;
// }
