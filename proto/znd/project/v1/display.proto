syntax = "proto3";
package znd.project.v1;
option optimize_for = LITE_RUNTIME;
// import "znd/project/v1/position.proto";
import "znd/project/v1/common.proto";
import "znd/project/v1/style.proto";

message DisplayList {
    //key为屏幕ID，用u8
    map<uint32, Display> displays = 1;
    //key为页面ID，用u16(pageId为全局唯一)
    map<uint32, PageInfo> page_infos = 7;//窗口列表
    //各端的display入口，key是DisplayOutputType，value为display的id，用u8，如果该端没有，则使用默认display
    map<uint32, uint32> display_entries = 8;
    //默认display，用u8
    uint32 default_display_id_u8 = 9;
}

message Display {
    //屏幕名称
    string name = 2;
    //屏幕尺寸，单位像素
    Size size = 3;
    //旋转角度
    RotateType rotate = 4;
    //页面分类列表
    repeated PageClassify page_classifies = 5;
    //主窗口ID
    uint32 main_page_id_u16 = 6;
    //窗口列表
    repeated uint32 page_ids_u16 = 7;
    //默认公共窗口(确实可能没有公共窗口)
    optional uint32 common_page_id_u16 = 8;
    //公共窗口是否在基本窗口的上方，否则就是在下方
    bool common_page_on_top = 9;
    //样式
    optional StyleProperties style = 10;
    //始终缓存的页面
    repeated uint32 always_cache_page_ids_u16 = 11;
    //缓存页面最大数量
    uint32 cache_page_max_count_u16 = 12;
    //输出类型,支持多种输出，数组为空表示都支持
    repeated DisplayOutputType output_types = 13;
}

//旋转角度类型
enum RotateType {
    //不旋转
    ROTATE_TYPE_UNSPECIFIED = 0;
    //90度
    ROTATE_TYPE_90 = 1;
    //180度
    ROTATE_TYPE_180 = 2;
    //270度
    ROTATE_TYPE_270 = 3;
}

//屏幕输出类型
enum DisplayOutputType {
    //未定义
    DISPLAY_OUTPUT_TYPE_UNSPECIFIED = 0;
    //本地
    DISPLAY_OUTPUT_TYPE_LOCAL = 1;
    //移动端
    DISPLAY_OUTPUT_TYPE_MOBILE = 2;
    //平板
    DISPLAY_OUTPUT_TYPE_PAD = 3;
    //电视
    DISPLAY_OUTPUT_TYPE_TV = 4;
    //PC
    DISPLAY_OUTPUT_TYPE_PC = 5;
    //TYPEC
    DISPLAY_OUTPUT_TYPE_TYPEC = 6;
    //HDMI1
    DISPLAY_OUTPUT_TYPE_HDMI1 = 7;
    //HDMI2
    DISPLAY_OUTPUT_TYPE_HDMI2 = 8;
}


// message PageInfo {
//     int32 id = 1;
//     string name = 2;
//     PageType page_type = 3;
//     optional int32 page_no = 4;
//     optional int32 page_classify_id = 5; //页面分类ID
//     optional ProtectType protect_type = 6;  //是否受保护，为了在列表中显示出保护的标识
// }

//页面信息(元件列表移到了widget.proto中的PageWidgets)
message PageInfo {
    string name = 2;
    PageType page_type = 3;
    //画面号，只在display内部唯一，优先在display内部找，如果找不到，则去找default display中的画面号
    optional uint32 page_no_u16 = 4;
    //页面分类ID
    uint32 page_classify_id_u16 = 5;
    //布局类型
    LayoutType layout = 6;
    //是否保护，分为只读和不让打开
    ProtectType protect_type = 7;
    //保护密码，如果为空，则取工程参数中的公共保护密码
    optional string protect_passwd = 8;
    //层叠窗口，如果为空，则不层叠
    //如果有则按顺序先渲染层叠的，最后自己
    repeated uint32 stack_page_ids = 10;
    //样式
    optional StyleProperties style = 11;
    //背景图
    optional GraphicReference background_graphic = 12;
    //位置，一般是0不用设置，除非弹出时指定位置
    optional Location location = 13;
    //大小，未设置则是跟随display的尺寸或者自动大小
    optional Size size = 14;
    //自动大小类型(如果为空，则不自动大小)，尺寸的优先遵守原则是：size>auto_size_type>display.size
    PageSizeType auto_size_type = 15;
    //弹出窗口方式
    optional PagePopupType popup_type = 16;
    //弹出窗口位置(如果未定义，则取location的值)
    optional PositionType popup_position = 17;
    //键盘信息
    optional KeyboardInfo keyboard_info = 19;
    //可跳转窗口列表,用于运行时预加载
    repeated int32 jump_page_ids = 18;
}

//键盘窗口信息
message KeyboardInfo {
    //最大值或最长长度元件id
    optional int32 max_value_widget_id = 1;
    //最小值或最短长度元件id
    optional int32 min_value_widget_id = 2;
    //当前值元件id
    optional int32 current_value_widget_id = 3;
    //候选字区域坐标(相对键盘窗口坐标)
    optional Location candidate_location = 4;
    //候选字区域大小(相对键盘窗口坐标)
    optional Size candidate_size = 5;
}

//页面分类
message PageClassify {
    //id
    uint32 id_u16 = 1;
    //名称
    string name = 2;
    //父分类id，没父类的为空
    optional uint32 parent_id_u16 = 3;
}

//页面自动大小类型
enum PageSizeType {
    //未定义（自由尺寸）
    PAGE_SIZE_TYPE_UNSPECIFIED = 0;
    //全屏
    PAGE_SIZE_TYPE_FULL = 1;
    //大尺寸
    PAGE_SIZE_TYPE_LARGE = 2;
    //中尺寸
    PAGE_SIZE_TYPE_MEDIUM = 3;
    //小尺寸
    PAGE_SIZE_TYPE_SMALL = 4;
}

//页面布局类型
enum LayoutType {
    LAYOUT_TYPE_UNSPECIFIED = 0;//自由布局
    LAYOUT_TYPE_FLEX = 1;
    LAYOUT_TYPE_GRID = 2;
}

//页面类型（界面上主要分为基本窗口和功能窗口，基本窗口一般是主操作窗口，功能窗口不叫系统窗口的原因是，客户会增加和自定义）
//更细区分类型的原因，主要是为了用户在界面上选择时可以根据场景找到相应的窗口
//窗口逻辑：HMI的窗口主要分为基本窗口和公共窗口，基本窗口在HMI上只会有一个，进行切换，公共窗口同理
//画面跳转就分为基本窗口跳转和公共窗口跳转，基本窗口有且只有一个，公共窗口可以跳到0，也就是可以没有公共窗口
enum PageType {
    //未定义
    PAGE_TYPE_UNSPECIFIED = 0;
    //基本窗口，或者叫普通窗口
    PAGE_TYPE_NORMAL = 1;
    //公共窗口
    PAGE_TYPE_COMMON = 2;
    //功能窗口中的键盘窗口
    //是为了图元中选择输入键盘可以少一些
    PAGE_TYPE_KEYBOARD = 3;
    //功能窗口中的信息窗口，信息提示、确认、询问类，如权限不足提醒，执行确认、通讯报错等
    //是为了图元中执行确认的选择可以少一些
    PAGE_TYPE_MESSAGE = 4;
    //功能窗口中的其他窗口，如用户登录、权限配置、配方操作、网络设置、后台设置等
    PAGE_TYPE_FUNCTION = 5;

    // 登录窗口
    // PAGE_TYPE_SYSTEM_LOGIN = 3;
    // PAGE_TYPE_SYSTEM_KEY_CHINESE = 6; //汉字窗口
    // PAGE_TYPE_SYSTEM_SET_ETHERNET = 7; //设置以太网窗口
    // PAGE_TYPE_SYSTEM_SET_WIFI = 8; //设置WIFI窗口
    // PAGE_TYPE_SYSTEM_SET_MOBILE = 9; //设置移动网络窗口
    // PAGE_TYPE_SYSTEM_SET_BLUETOOTH = 10; //设置蓝牙窗口
    // PAGE_TYPE_SYSTEM_SET_DEVICE = 11; //设置设备窗口
    // PAGE_TYPE_SYSTEM_SET_TIME = 12; //设置时间窗口
    // PAGE_TYPE_SYSTEM_SET_LANGUAGE = 13; //设置语言窗口
}

// //子窗口参数
// message SubPageParam {
//     bool is_model = 1;
//     bool is_close_click_outer = 2;
//     optional Location location = 3;
//     optional PositionType position = 4;
//     optional bool screen_mask=5;  //屏幕是否用蒙版
//     optional int32 shadow_distance=6;
//     optional uint32 shadow_color=7;
//     optional int32 shadow_opacity=8;
// }

//弹出窗口方式
enum PagePopupType {
    //非弹出窗口
    PAGE_POPUP_TYPE_UNSPECIFIED = 0;
    //半垄断式弹出(半模态窗口，指点击外部可关闭，默认)
    PAGE_POPUP_TYPE_HALF_MODEL = 1;
    //垄断式弹出(模态窗口)
    PAGE_POPUP_TYPE_MODEL = 2;
    //浮窗式弹出(非模态窗口)
    PAGE_POPUP_TYPE_FLOATING = 3;
}

