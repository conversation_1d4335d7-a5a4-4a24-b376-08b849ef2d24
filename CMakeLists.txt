cmake_minimum_required(VERSION 3.22)

if(WIN32)
    add_compile_options("/utf-8")
endif()

project("znd_proto" LANGUAGES CXX VERSION 0.0.6)

# add_definitions(-DDLLBUILD)
add_library(${PROJECT_NAME} STATIC)

file(GLOB_RECURSE SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/znd/project/v1/*.cc"
)

include("${CMAKE_CURRENT_SOURCE_DIR}/cmake/${PLATFORM}-debug.cmake")

target_sources(${PROJECT_NAME} PRIVATE ${SOURCES})
target_include_directories(${PROJECT_NAME} PUBLIC "${CMAKE_CURRENT_LIST_DIR}")
message(STATUS "CMAKE_CURRENT_LIST_DIR: ${CMAKE_CURRENT_LIST_DIR}")

set(protobuf_DIR "${CMAKE_CURRENT_LIST_DIR}/../lib/${PLATFORM}/${CMAKE_BUILD_TYPE}/protobuf")

message(STATUS "protobuf_DIR: ${protobuf_DIR}")
find_package(protobuf REQUIRED)
target_include_directories(${PROJECT_NAME} PUBLIC "${protobuf_INCLUDE_DIRS}")
target_link_libraries(${PROJECT_NAME} PUBLIC ${protobuf_LIBRARIES})
