cmake_minimum_required(VERSION 3.22)

set(URQ_ROOT_PROJECT ON)

project("urq" VERSION 0.0.1)

include("${CMAKE_CURRENT_LIST_DIR}/cmake/common.cmake")
# include("${CMAKE_CURRENT_LIST_DIR}/cmake/c.cmake")

set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_C_STANDARD 11)

set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_STANDARD 20)

add_subdirectory(core     "core")
add_subdirectory(parse    "parse")
add_subdirectory(cxx      "cxx")
add_subdirectory(lib      "lib")
add_subdirectory(ui       "ui")
add_subdirectory(example  "example")

#if("${URQ_BUILD_TYPE}" STREQUAL "debug" AND "${URQ_BUILD_PLATFORM}" STREQUAL "linux_64")
#    add_subdirectory(core-test "core-test")
#    add_subdirectory(lib-test "lib-test")
#endif()
