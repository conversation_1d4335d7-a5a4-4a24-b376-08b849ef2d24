set(PACKAGE_VERSION_MAJOR 1)
set(PACKAGE_VERSION_MINOR 0)
set(PACKAGE_VERSION_PATCH 0)
set(PACKAGE_VERSION 1.0.0)

if(PACKAGE_VERSION_MAJOR EQUAL PACKAGE_FIND_VERSION_MAJOR AND
   PACKAGE_VERSION_MINOR EQUAL PACKAGE_FIND_VERSION_MINOR)

    if(PACKAGE_VERSION_PATCH EQUAL PACKAGE_FIND_VERSION_PATCH)
        set(PACKAGE_VERSION_EXACT TRUE)
    elseif(PACKAGE_VERSION_PATCH GREATER PACKAGE_FIND_VERSION_PATCH)
        set(PACKAGE_VERSION_COMPATIBLE TRUE)
    else()
        set(PACKAGE_VERSION_COMPATIBLE FALSE)
    endif()
else()
    set(PACKAGE_VERSION_COMPATIBLE FALSE)
endif()