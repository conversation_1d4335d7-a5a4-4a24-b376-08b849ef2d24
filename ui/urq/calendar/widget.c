#include "urq/calendar/widget.h"
#include "lvgl.h"
#include "urq/preload.h"
#include "urq/user_data.h"
#include "urq/util/color.h"
#include "urq/util/control.h"
#include "urq/util/event.h"
#include "urq/util/font.h"
#include "urq/util/state.h"
#include "urq/util/style.h"
#include "urq/util/time.h"
#include "urq/widget.h"
#include "urq/widget/conf.h"
#include "urq/widget/type.h"

#define Self urq_calendar_widget_t

static const char *s_day_names[] = {
    "Sun", "<PERSON>", "<PERSON>e", "Wed", "Thu", "Fri", "Sat"};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_CALENDAR_WIDGET_CLASS = {
    .base_class = &lv_img_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    lv_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_calendar_widget_config, NULL,
        urq_calendar_widget_set_state, NULL);

    widget->calendar = lv_calendar_create(obj);
    widget->conf = NULL;
    widget->common_conf = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;
    if (widget->user_data.type != URQ_WIDGET_TYPE_CLONE) {
        urq_user_data_free(&widget->user_data);
        if (widget->conf != NULL) {
            urq_calendar_widget_conf_free_inplace(widget->conf);
            urq_free(widget->conf);
            widget->conf = NULL;
        }
        if (widget->common_conf != NULL) {
            urq_widget_common_conf_free_inplace(widget->common_conf);
            urq_free(widget->common_conf);
            widget->common_conf = NULL;
        }
        widget->calendar = NULL;
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (urq_event_filter_exit(class_p, e)) {
        return;
    }
}

static void urq_draw_event_cb(lv_event_t *e)
{
    lv_obj_t *obj = (lv_obj_t *)lv_event_get_user_data(e);
    Self *widget = (Self *)obj;
    urq_project_t *project = widget->user_data.project;
    lv_obj_draw_part_dsc_t *dsc = lv_event_get_draw_part_dsc(e);
    urq_theme_color_t *theme_color = project->env.theme_color;

    if (dsc->part == LV_PART_ITEMS) {
        uint32_t row = dsc->id / 7; // 假设每行7列

        // 获取按钮矩阵
        lv_obj_t *btnm = lv_calendar_get_btnmatrix(widget->calendar);
        const char *txt = lv_btnmatrix_get_btn_text(btnm, (uint16_t)dsc->id);

        // 日期名称（第一行）
        if (row == 0) {
            dsc->label_dsc->color =
                urq_get_color(theme_color, &widget->conf->week_font_color);
            dsc->rect_dsc->bg_opa = LV_OPA_100;
            dsc->rect_dsc->bg_color =
                urq_get_color(theme_color, &widget->conf->week_bg_color);
        } else if (
            lv_calendar_get_today_date(widget->calendar)->day == atoi(txt)) {
            dsc->rect_dsc->border_color =
                urq_get_color(theme_color, &widget->conf->today_bg_color);
            dsc->label_dsc->color =
                urq_get_color(theme_color, &widget->conf->today_font_color);
            dsc->rect_dsc->bg_color =
                urq_get_color(theme_color, &widget->conf->today_bg_color);
        } else if (
            lv_calendar_get_highlighted_dates(widget->calendar)->day ==
            atoi(txt)) {
            dsc->label_dsc->color =
                urq_get_color(theme_color, &widget->conf->highlight_font_color);
            dsc->rect_dsc->bg_color =
                urq_get_color(theme_color, &widget->conf->highlight_color);
        }
    }
}

lv_obj_t *urq_calendar_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj =
        lv_obj_class_create_obj(&URQ_CALENDAR_WIDGET_CLASS, (lv_obj_t *)parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

static void urq_calendar_widget_conf(
    lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *widget = (Self *)self;
    // urq_project_t *const project = widget->user_data.project;

    lv_obj_set_size(widget->calendar, mut_conf->size.w, mut_conf->size.h);
    lv_obj_set_align(widget->calendar, LV_ALIGN_CENTER);

    lv_obj_t *btnm = lv_calendar_get_btnmatrix(widget->calendar);
    lv_obj_add_event_cb(
        btnm, urq_draw_event_cb, LV_EVENT_DRAW_PART_BEGIN, (void *)widget);

    // if (widget->conf->font != NULL) {
    //     // TODO
    //     urq_set_font(self, widget->conf->font);
    //     lv_obj_set_style_text_color(
    //         widget->calendar,
    //         urq_get_color(project->env.theme_color,
    //         &widget->conf->font->color), LV_PART_MAIN);
    // }

    lv_calendar_set_day_names(widget->calendar, s_day_names);

    struct tm *tm = urq_get_current_time();
    uint16_t year = (uint16_t)(tm->tm_year + 1900);
    uint8_t month = (uint8_t)(tm->tm_mon + 1);
    uint8_t day = (uint8_t)(tm->tm_mday);

    /// 设置当前日期
    lv_calendar_set_today_date(widget->calendar, year, month, day);
    /// 设置显示年月
    lv_calendar_set_showed_date(widget->calendar, year, month);

    // 设置高亮日期
    if (widget->conf->hightlight_date_num > 0) {
        lv_calendar_set_highlighted_dates(
            widget->calendar, widget->conf->hightlight_date,
            widget->conf->hightlight_date_num);
    }

    // 设置日期显示方式
    if (widget->conf->type == URQ_DATE_SHOW_TYPE_ARROW) {
        lv_calendar_header_arrow_create(widget->calendar);
    } else if (widget->conf->type == URQ_DATE_SHOW_TYPE_DROPDOWN) {
        lv_calendar_header_dropdown_create(widget->calendar);
    }
}

void urq_calendar_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *widget = (Self *)self;
    widget->conf = mut_conf->config.calendar;
    widget->common_conf = mut_conf->common_conf;

    urq_widget_config(self, mut_conf, urq_calendar_widget_conf);
}

void urq_calendar_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    Self *const widget = (Self *)self;
    urq_set_state(self, widget->common_conf, state, platfrom);
}
