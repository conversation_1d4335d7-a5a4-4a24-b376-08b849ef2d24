#pragma once

#include "lvgl.h"
#include "urq/ruler/conf.h"
#include "urq/user_data.h"

#ifndef URQ__RULER__WIDGET_H
#define URQ__RULER__WIDGET_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 刻度尺组件结构
typedef struct {
    /// @brief 父组件
    lv_obj_t supper;
    /// @brief user data 字段
    urq_user_data_t user_data;
    /// @brief 配置
    urq_ruler_widget_conf_t *conf;
    /// @brief 通用配置
    urq_widget_common_conf_t *common_conf;
    /// @brief 组件大小
    urq_size_t size;
    /// @brief 当前值（用于高亮显示）
    lv_coord_t current_value;
    /// @brief 是否启用
    bool enabled;
} urq_ruler_widget_t;

/// @brief 创建刻度尺组件
/// @param parent 父组件
/// @return 刻度尺组件对象
lv_obj_t *urq_ruler_widget_create(lv_obj_t *parent)
    __attribute__((__nonnull__(1)));

/// @brief 配置刻度尺组件
/// @param self 组件对象
/// @param mut_conf 配置结构
void urq_ruler_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
    __attribute__((__nonnull__(1, 2)));

/// @brief 设置刻度尺状态
/// @param self 组件对象
/// @param state 状态
/// @param platform 平台
void urq_ruler_widget_set_state(lv_obj_t *self, urq_state_t state, uint8_t platform)
    __attribute__((__nonnull__(1)));

/// @brief 刷新语言
/// @param self 组件对象
void urq_ruler_widget_refresh_language(lv_obj_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 设置当前值（用于高亮显示）
/// @param self 组件对象
/// @param value 当前值
void urq_ruler_widget_set_value(lv_obj_t *self, lv_coord_t value)
    __attribute__((__nonnull__(1)));

/// @brief 获取当前值
/// @param self 组件对象
/// @return 当前值
lv_coord_t urq_ruler_widget_get_value(lv_obj_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 设置刻度范围
/// @param self 组件对象
/// @param min_value 最小值
/// @param max_value 最大值
void urq_ruler_widget_set_range(lv_obj_t *self, lv_coord_t min_value, lv_coord_t max_value)
    __attribute__((__nonnull__(1)));

/// @brief 添加自定义刻度标记
/// @param self 组件对象
/// @param value 刻度值
/// @param label 标签文本（可为NULL）
/// @param color 标记颜色
void urq_ruler_widget_add_mark(lv_obj_t *self, lv_coord_t value, const char *label, lv_color_t color)
    __attribute__((__nonnull__(1)));

/// @brief 清除所有自定义标记
/// @param self 组件对象
void urq_ruler_widget_clear_marks(lv_obj_t *self)
    __attribute__((__nonnull__(1)));

#ifdef __cplusplus
}
#endif

#endif // URQ__RULER__WIDGET_H
