#pragma once

#include "lvgl.h"
#include "urq/group/conf.h"
#include "urq/user_data.h"

#ifndef URQ__GROUP__WIDGET_H
#define URQ__GROUP__WIDGET_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 父组件
    lv_obj_t supper;
    /// @brief user data 字段
    urq_user_data_t user_data;
    /// @brief 配置
    urq_group_widget_conf_t *conf;
    /// @brief 通用配置
    urq_widget_common_conf_t *common_conf;
} urq_group_widget_t;

/// @brief 创建 group_widget
/// @param parent 当前组件所属的页面
/// @returns 组件
lv_obj_t *urq_group_widget_create(lv_obj_t *parent)
    __attribute__((__nonnull__(1)));

/// @brief 配置 group_widget
/// @param self     组件自身
/// @param mut_conf 配置的内容
/// @returns 组件
void urq_group_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
    __attribute__((__nonnull__(1, 2)));

/// @brief 设置组件状态
/// @param self  组件自身
/// @param state 状态
/// @returns void
void urq_group_widget_set_state(lv_obj_t *self, urq_state_t state, uint8_t platfrom)
    __attribute__((__nonnull__(1)));

/// @brief 刷新语言
/// @param self 组件自身
/// @returns void
void urq_group_widget_refresh_language(lv_obj_t *self)
    __attribute__((__nonnull__(1)));
// impl ---------------------------------------------------------------

#ifdef __cplusplus
}
#endif
#endif // URQ__GROUP__WIDGET_H
