    #include "urq/group/widget.h"
#include "lvgl.h"
#include "urq/group/conf.h"
#include "urq/preload.h"
#include "urq/user_data.h"
#include "urq/util/control.h"
#include "urq/util/event.h"
#include "urq/util/state.h"
#include "urq/widget.h"
#include "urq/widget/type.h"
#include "urq/page/load.h"
#include <string.h>

#define Self urq_group_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_GROUP_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(user_data, urq_group_widget_config, NULL, urq_group_widget_set_state, NULL);

    widget->conf = NULL;
    widget->common_conf = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    urq_group_widget_t *widget = (urq_group_widget_t *)obj;
    urq_group_widget_conf_t *conf = widget->conf;
    if(widget->user_data.type != URQ_WIDGET_TYPE_CLONE) {
        urq_user_data_free(&widget->user_data);

        if (conf != NULL) {
            urq_group_widget_conf_free_inplace(conf);
            urq_free(conf);
            widget->conf = NULL;
        }
        if (widget->common_conf != NULL) {
            urq_widget_common_conf_free_inplace(widget->common_conf);
            urq_free(widget->common_conf);
            widget->common_conf = NULL;
        }
    }
    urq_group_widget_conf_free_inplace(widget->conf);
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (urq_event_filter_exit(class_p, e)) {
        return;
    }

    Self *widget = (Self *)lv_event_get_target(e);
    urq_widget_common_conf_t *common_conf = widget->common_conf;
    urq_page_t *const page = widget->user_data.page;

    static uint32_t count = 0;
    // 检查是否执行点击事件
    if (urq_event_check_exec_click(e, &count, common_conf)) {
        // exec
        // printf("可执行\n");
    }

    if (common_conf != NULL && common_conf->has_action) {
        urq_event_send_action(e, page, common_conf->id);
    }
}

lv_obj_t *urq_group_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj;

    obj = lv_obj_class_create_obj(&URQ_GROUP_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

void urq_group_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;
    urq_page_t *const page = widget->user_data.page;

    widget->conf = mut_conf->config.group;
    widget->common_conf = mut_conf->common_conf;

    urq_page_load_foreach_widget((lv_obj_t*)page, self, &widget->conf->widgets);

    urq_widget_config(self, mut_conf, NULL);
}

void urq_group_widget_set_state(lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    uint32_t num = lv_obj_get_child_cnt(self);
    for(uint32_t idx =0; idx < num; ++idx) {
        lv_obj_t *_obj = lv_obj_get_child(self, (int32_t)idx);
        urq_user_data_t *udata = urq_obj_get_user_data(_obj);
        if(udata != NULL && udata->set_state != NULL) {
            udata->set_state(_obj, state, platfrom);
        }
    }
}

void urq_group_widget_refresh_language(lv_obj_t *self)
{
    uint32_t num = lv_obj_get_child_cnt(self);
    for(uint32_t idx =0; idx < num; ++idx) {
        lv_obj_t *_obj = lv_obj_get_child(self, (int32_t)idx);
        urq_user_data_t *udata = urq_obj_get_user_data(_obj);
        if(udata != NULL && udata->refresh_language != NULL) {
            udata->refresh_language(_obj);
        }
    }
}
