#include "lvgl.h"
#include "urq/page/page.h"
#include "urq/project/project.h"
#include "urq/widget/conf.h"
#include "urq/widget/state.h"
#include "urq/widget/type.h"

#ifndef URQ__USER_DATA_H
#define URQ__USER_DATA_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 配置组件
/// @param self    组件自身
/// @param mut_conf 组件配置
/// @return 无
typedef void (*urq_user_data_conf_fn_t)(
    lv_obj_t *self, urq_widget_conf_t *mut_conf);

/// @brief 设置组件状态状态
/// @param self 组件自身
/// @param state 状态
/// @return 无
typedef void (*urq_user_data_set_state_fn_t)(lv_obj_t *self, urq_state_t state, uint8_t platfrom);

/// @brief 刷新语言
/// @param self 组件自身
/// @return 无
typedef void (*urq_user_data_refresh_language_fn_t)(lv_obj_t *self);

/// @brief 设置组件属性
/// @param self 组件自身
/// @param data 数据
/// @return 无
typedef void (*urq_user_data_set_property_fn_t)(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value);

/// @brief 所有 lv_obj 的 user_data 字段
typedef struct {
    urq_widget_type_t type;                               // 组件的类型
    int32_t id;                                           // 组件的 id
    urq_project_t *project;                               // 所属的 project
    urq_page_t *page;                                     // 所属的 page
    lv_obj_t *label;                                      // 文本显示组件
    lv_obj_t *img;
    urq_user_data_conf_fn_t conf;                         // 配置组件自身
    urq_user_data_refresh_language_fn_t refresh_language; // 刷新语言
    urq_user_data_set_state_fn_t set_state; // 设置状态，这个方法是组态端用的
    urq_user_data_set_property_fn_t set_property; // 设置属性
} urq_user_data_t;

static inline void urq_user_data_init(urq_user_data_t* user_data,  urq_user_data_conf_fn_t conf, urq_user_data_refresh_language_fn_t refresh_language, urq_user_data_set_state_fn_t set_state, urq_user_data_set_property_fn_t set_property) {
    user_data->conf = conf;
    user_data->refresh_language = refresh_language;
    user_data->set_state = set_state;
    user_data->set_property = set_property;
    user_data->label = NULL;
    user_data->img = NULL;
}

static inline void urq_user_data_free(urq_user_data_t *user_data)
{
    user_data->conf = NULL;
    user_data->set_state = NULL;
    user_data->set_property = NULL;
    user_data->refresh_language = NULL;
    user_data->label = NULL;
    user_data->img = NULL;
}

/// @brief 获取 lv_obj 的 user_data
/// @param self lv_obj
/// @return user_data
static inline urq_user_data_t *urq_obj_get_user_data(lv_obj_t *self);

/// @brief 设置 lv_obj 的 user_data
/// @param self lv_obj
/// @param user_data user_data
static inline void urq_obj_set_user_data(
    lv_obj_t *self, urq_user_data_t *user_data)
    __attribute__((__nonnull__(1, 2)));

// ---------------------------------------------------------------

static inline urq_user_data_t *urq_obj_get_user_data(lv_obj_t *self)
{
    return (urq_user_data_t *)lv_obj_get_user_data(self);
}

static inline void urq_obj_set_user_data(
    lv_obj_t *self, urq_user_data_t *user_data)
{
    lv_obj_set_user_data(self, (void *)user_data);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__USER_DATA_H
