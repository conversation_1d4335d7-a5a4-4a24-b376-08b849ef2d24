#pragma once

#include "urq/errno.h"
#include "urq/page/group.h"
#include "urq/preload.h"
#include "urq/project/project.h"
#include "urq/var/detail.h"
#include "urq/var/table.h"
#include <errno.h>
#include <stdint.h>

#ifndef URQ__PROJECT__VAR_H
#define URQ__PROJECT__VAR_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 处理远端发送过来的变量改变数据
/// @param self  项目
/// @param count 变量个数
/// @param reader 变量数据读取器
/// @returns 是否成功
static inline int urq_project_var_set_data(
    urq_project_t *self, uint16_t count, urq_buf_read_t *reader)
    __attribute__((__nonnull__(1, 3), __warn_unused_result__()));

// impl

static inline int urq_project_var_set_data(
    urq_project_t *self, uint16_t count, urq_buf_read_t *reader)
{
    uint16_t size = 0;
    urq_arr_t(urq_var_detail_t *) details =
        (urq_var_detail_t **)urq_malloc(sizeof(urq_var_detail_t) * count);
    if (details == NULL) {
        errno = URQ_ENOMEM;
        return -1;
    }

    if (urq_var_table_set_data(self->var_table, reader, &size, &details)) {
        return -1;
    }
    for (uint16_t i = 0; i < size; ++i) {
        urq_var_detail_exec_cb(details[i]);
    }
    urq_page_group_var_change_cb(
        (urq_page_group_t *)self->page_showing, size, details);

    urq_free(details);
    return 0;
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__PROJECT__VAR_H
