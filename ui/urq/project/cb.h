#pragma once

#include "urq/conn/res.h"
#include <stdint.h>

#ifndef URQ__PROJECT__CB_H
#define URQ__PROJECT__CB_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 接收到 low frequency 消息时的回调
/// @param self 项目
/// @param code 功能
/// @param ext  ext
/// @param size 数据长度
/// @param ref_data 系统变量
/// @returns void
void urq_project_cb_operator(
    void *self, urq_conn_res_t code, uint32_t ext, uint32_t size,
    uint8_t *ref_data);

/// @brief 变量回调
/// @param self project 自身
/// @param size 数据长度
/// @param mv_data 数据
/// @returns void
void urq_project_cb_var(
    void *const self, uint32_t size, uint8_t *const mv_data);

/// @brief 项目回调
/// @param self project 自身
/// @param code 功能
/// @param ext  ext
/// @param size 数据长度
/// @param ref_data 系统变量
/// @returns void
void urq_project_cb_asset(void *self, uint32_t size, uint8_t *ref_data);

/// @brief file 回调
/// @param self project 自身
/// @param size 数据长度
/// @param mv_data 数据
/// @returns void
void urq_project_cb_file_content(
    void *self, uint32_t ext, uint32_t size, uint8_t *ref_data);

/// @brief 动作回调
/// @param self project 自身
/// @param size 数据长度
/// @param mv_data 数据
/// @returns void
void urq_project_cb_action(
    void *const self, uint32_t size, uint8_t *const mv_data);

/// @brief 状态寄存器回调
/// @param self project 自身
/// @param size 数据长度
/// @param mv_data 数据
/// @returns void
void urq_project_cb_status_register(
    void *self, uint32_t size, uint8_t *mv_data);
#ifdef __cplusplus
}
#endif
#endif // URQ__PROJECT__CB_H
