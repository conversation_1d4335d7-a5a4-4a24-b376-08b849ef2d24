#pragma once

#include <stddef.h>
#include <stdint.h>

#ifndef URQ__PROJECT__LOAD_H
#define URQ__PROJECT__LOAD_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 加载变量配置
///
/// @param project 项目
/// @param key     设备表的 key
/// @return void
void urq_project_load_atags(void *self);

void urq_project_parse_atags(
    void *self, const uint8_t *const data, size_t size);

/// @brief 加载设备列表
///
/// 设备加载与工程加载是并行的，没有依赖关系
/// @param project 项目
/// @returns void
void urq_project_load_device(void *self);
void urq_project_parse_device(
    void *self, const uint8_t *const data, size_t size);

/// @brief 加载 display 配置
///
/// 工程加载顺序：加载工程配置 -> 加载工程语言 -> 加载 display 配置 -> 创建页面
/// @param self 项目
/// @param project 项目
/// @returns void
void urq_project_load_display(void *self);
void urq_project_parse_display(
    void *self, const uint8_t *const data, size_t size);

/// @brief 加载工程语言
///
/// 工程加载顺序：加载工程配置 -> 加载工程语言 -> 加载 display 配置 -> 创建页面
/// @param project 项目
/// @returns void
void urq_project_load_language(void *self);
void urq_project_reload_language(void *self);
void urq_project_parse_language(
    void *self, const uint8_t *const data, size_t size);

/// @brief 加载工程配置
///
/// 工程加载顺序：加载工程配置 -> 加载工程语言 -> 加载 display 配置 -> 创建页面
/// @param project 项目
/// @returns void
void urq_project_load_project(void *self);
void urq_project_parse_project(
    void *self, const uint8_t *const data, size_t size);
/// @brief 加载主题样式
///
/// @param project 项目
/// @returns void
void urq_project_load_theme_style(void *self);
void urq_project_parse_theme_style(
    void *self, const uint8_t *const data, size_t size);

/// @brief 加载文件
///
/// @param self 项目
/// @returns void
void urq_project_load_files(void *self);
void urq_project_parse_files(
    void *self, const uint8_t *const data, size_t size);

#ifdef __cplusplus
}
#endif
#endif // URQ__PROJECT__LOAD_H
