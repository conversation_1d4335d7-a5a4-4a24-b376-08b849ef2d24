#include "util.h"
#include "urq/datastruct/circular_link.h"
#include "urq/page/group.h"
#include "urq/project/project.h"
#include <lvgl/src/core/lv_disp.h>
#include <lvgl/src/core/lv_obj_class.h>

int urq_project_switch_base_page(urq_project_t *project)
{
    return urq_project_show_page(
        (lv_obj_t *)project, project->display->main_page_id);
}

int urq_project_switch_main_page(urq_project_t *project)
{
    return urq_project_show_page(
        (lv_obj_t *)project, project->display->main_page_id);
}

int urq_project_switch_prev_page(urq_project_t *project)
{
    // 删除当前页面缓存ID，切换回去
    urq_clink_pop_back(project->save_page_id);
    //
    //if (urq_circular_size(project->save_pages) <= 1)
    //    return -1;
    //urq_circular_pop_back(project->save_pages);
    //lv_obj_t *page = urq_circular_back_value(project->save_pages);
    return true;
}

int urq_project_switch_adv_prev_page(urq_project_t *project)
{
    urq_used(project);
    //if (urq_circular_size(project->save_pages) <= 1)
    //    return false;
    //lv_obj_t *page = urq_circular_front_value(project->save_pages);
    //lv_scr_load(page);

    return true;
}

void urq_project_switch_next_page_no(urq_project_t *project)
{
    lv_obj_t *self = (lv_obj_t *)project;
    urq_page_group_t *pages = (urq_page_group_t *)project->page_showing;
    // TODO
    urq_project_show_page(self, pages->page_no + 1);
}

void urq_project_switch_prev_page_no(urq_project_t *project)
{
    lv_obj_t *self = (lv_obj_t *)project;
    urq_page_group_t *pages = (urq_page_group_t *)project->page_showing;
    // TODO
    urq_project_show_page(self, pages->page_no - 1);
}

int urq_project_switch_common_page(urq_project_t *project)
{
    return urq_project_show_page(
        (lv_obj_t *)project, project->display->common_page_id);
}

int urq_project_switch_keyboard_page(urq_project_t *project)
{
    // TODO
    return urq_project_show_page(
        (lv_obj_t *)project, project->display->main_page_id);
}

int urq_project_switch_open_page(urq_project_t *project)
{
    return urq_project_show_page(
        (lv_obj_t *)project, project->display->main_page_id);
}

int urq_project_switch_close_page(urq_project_t *project)
{
    // lv_obj_del 删除窗口
    return urq_project_show_page(
        (lv_obj_t *)project, project->display->main_page_id);
}
