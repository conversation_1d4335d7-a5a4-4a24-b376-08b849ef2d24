#include "urq/project/cb.h"
#include "lvgl.h"
#include "urq/buf/read.h"
#include "urq/device/local.h"
#include "urq/log/debug.h"
#include "urq/page.h"
#include "urq/page/group.h"
#include "urq/page/load.h"
#include "urq/path.h"
#include "urq/preload.h"
#include "urq/project/load.h"
#include "urq/project/project.h"
#include "urq/user_data.h"
#include <lvgl/src/core/lv_obj.h>
#include <stdint.h>
#include <string.h>
#include <unistd.h>

void urq_project_cb_operator(
    void *self, urq_conn_res_t code, uint32_t ext, uint32_t size,
    uint8_t *ref_data)
{
    urq_used(ext);

    switch (code) {
    // case URQ_CONN_RES_PAGE_VAR:
    //     urq_project_cb_var(self, ext, size, ref_data);
    //     break;
    case URQ_CONN_RES_ASSET:
        urq_project_cb_asset(self, size, ref_data);
        break;
    case URQ_CONN_RES_EXEC_ACTION:
        urq_project_cb_action(self, size, ref_data);
        break;
    case URQ_CONN_RES_STATUS_REGISTER:
        urq_project_cb_status_register(self, size, ref_data);
        break;
    default:
        break;
    }
}

void urq_project_cb_var(void *const self, uint32_t size, uint8_t *const mv_data)
{
    urq_project_t *const project = (urq_project_t *)self;

    urq_buf_read_t reader = urq_buf_read_make(size, mv_data);

    const urq_page_id_t page_id = urq_buf_read_u16(&reader); // 当前画面
    const uint16_t widget_id = urq_buf_read_u16(&reader);    // 控件ID
    const uint8_t attr_id = urq_buf_read_u8(&reader);        // 属性ID
    const uint8_t index = urq_buf_read_u8(&reader);          // 列表索引
    const uint16_t value = urq_buf_read_u16(&reader);        // 控件ID

    if (page_id == 0) {
        // TODD 是否需要处理
    } else {
        urq_page_group_t *page = (urq_page_group_t *)project->page_showing;
        // 当前画面ID
        if (urq_page_map_has_key(page->pages, page_id)) {
            // TODO: 判断画面列表
            urq_page_t *target_page = urq_page_map_get(page->pages, page_id);
            lv_obj_t *obj =
                urq_lvgl_map_get(target_page->widget_map, widget_id);
            if (obj != NULL) {
                urq_user_data_t *user_data = urq_obj_get_user_data(obj);
                if (user_data != NULL) {
                    user_data->set_property(obj, attr_id, index, value);
                }
            }
        }
    }

    urq_free_if_not_null(mv_data);
}

void urq_project_cb_asset(void *self, uint32_t size, uint8_t *ref_data)
{
    urq_used(size);
    urq_used(ref_data);

    urq_project_t *project = (urq_project_t *)self;

    urq_buf_read_t reader = urq_buf_read_make(size, ref_data);
    urq_buf_read_bytes(&reader, 16, (uint8_t *)project->env.var_sys.name);
    // project->var_sys.theme = 1;
    // project->var_sys.lang = 0;
    // project->var_sys.page = 1;
    // project->var_sys.display_id = 1;
    // TODO 转换语言ID
    // project.env.var_sys.language_list = urq_buf_read_u8(&reader);

    URQ_SET_DEVICE_SYSTEM_VAR(project->env.var_sys, LANGUAGE, 0);
    URQ_SET_DEVICE_SYSTEM_VAR(project->env.var_sys, THEME, 1);
    URQ_SET_DEVICE_SYSTEM_VAR(project->env.var_sys, BASIC_PAGE_NO, 0);

    log_i(
        "theme: %u, lang: %u, name: %s, page: %u\n",
        URQ_GET_DEVICE_SYSTEM_VAR(project->env.var_sys, THEME),
        URQ_GET_DEVICE_SYSTEM_VAR(project->env.var_sys, LANGUAGE),
        project->env.var_sys.name,
        URQ_GET_DEVICE_SYSTEM_VAR(project->env.var_sys, BASIC_PAGE_NO));

    if (urq_fs_set_project_id(&project->fs, project->env.var_sys.name)) {
        log_e("unable to set project id, errno: %d\n", errno);
        return;
    }

    urq_project_load_device(self);
    // urq_project_load_files(self);
}

void urq_project_cb_action(
    void *const self, uint32_t size, uint8_t *const mv_data)
{
    urq_used(self);
    urq_used(size);
    urq_used(mv_data);
    urq_buf_read_t reader = urq_buf_read_make(size, mv_data);
    // urq_buf_read_bytes(&reader, 16, (uint8_t *)project->var_sys.name);
    uint8_t id = urq_buf_read_u8(&reader);
    uint16_t value = urq_buf_read_u16(&reader);
    printf("action id: %u, value: %u\n", id, value);
}

void urq_project_cb_file_content(
    void *self, uint32_t ext, uint32_t size, uint8_t *ref_data)
{
    if (size == 0)
        return;
    urq_used(self);

    urq_buf_read_t reader = urq_buf_read_make(size, ref_data);
    uint8_t file_id_p = (uint8_t)(ext & 0xFF);
    uint8_t file_id = (uint8_t)((ext >> 8) & 0xFF);
    // uint8_t flag = (uint8_t)((ext >> 16) & 0xFF);

    uint8_t content[size];
    urq_buf_read_bytes(&reader, size, content);
    switch (file_id_p) {
    case URQ_PATH_CONF_FOLDER_PROJECT:
        urq_project_parse_project(self, content, size);
        break;
    case URQ_PATH_CONF_FOLDER_DISPLAY:
        urq_project_parse_display(self, content, size);
        break;
    case URQ_PATH_CONF_FOLDER_WIDGETS:
        // urq_page_load_widget(self, content, size, file_id, flag);
        break;
    case URQ_PATH_CONF_FOLDER_DEVICE:
        urq_project_parse_device(self, content, size);
        break;
    case URQ_PATH_CONF_FOLDER_ATAG:
        // urq_project_parse_atags(self, content, size);
        break;
    case URQ_PATH_CONF_FOLDER_LANGUAGE:
        urq_project_parse_language(self, content, size);
        if (file_id != 0) {
            urq_page_load_language(self, content, size);
        }
        break;
    case URQ_PATH_CONF_FOLDER_STYLE:
        urq_project_parse_theme_style(self, content, size);
        break;
    case URQ_PATH_CONF_FOLDER_FONT:
        // urq_project_parse_font(self, content, size);
        break;
    case URQ_PATH_CONF_FOLDER_IMAGE:
        // urq_project_parse_image(self, content, size);
        break;
    case URQ_PATH_CONF_FOLDER_PROJECT_GALLERY:
        break;
    case URQ_PATH_CONF_FOLDER_PROJECT_GALLERY_FILE:
        break;
    default:
        break;
    }
}

void urq_project_cb_status_register(void *self, uint32_t size, uint8_t *mv_data)
{
    urq_used(self);
    urq_project_t *project = (urq_project_t *)self;
    urq_buf_read_t reader = urq_buf_read_make(size, mv_data);
    // urq_buf_read_bytes(&reader, 16, (uint8_t *)project->var_sys.name);
    uint8_t id = urq_buf_read_u8(&reader);
    uint16_t value = urq_buf_read_u16(&reader);
    project->env.var_sys.registers.data[id] = (int16_t)value;

    if (id == URQ_DEVICE_LOCAL_ADDR_BASIC_PAGE_NO) {
        urq_project_show_page(self, value);
    }
}
