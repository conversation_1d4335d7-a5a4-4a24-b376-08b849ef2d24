#pragma once

#include "urq/project/project.h"
#ifndef URQ__PROJECT__UTIL_H
#define URQ__PROJECT__UTIL_H
#ifdef __cplusplus
extern "C" {
#endif

/// 切换到基本窗口
int urq_project_switch_base_page(urq_project_t *project);
/// 返回主页
int urq_project_switch_main_page(urq_project_t *project);
/// 返回上一页
int urq_project_switch_prev_page(urq_project_t *project);
/// 前进到刚才的页面
int urq_project_switch_adv_prev_page(urq_project_t *project);
/// 跳到下个窗口号
void urq_project_switch_next_page_no(urq_project_t *project);
/// 跳到上个窗口号
void urq_project_switch_prev_page_no(urq_project_t *project);
/// 切换公共窗口
int urq_project_switch_common_page(urq_project_t *project);
/// 切换键盘窗口
int urq_project_switch_keyboard_page(urq_project_t *project);
/// 弹出窗口
int urq_project_switch_open_page(urq_project_t *project);
/// 关闭弹出窗口
int urq_project_switch_close_page(urq_project_t *project);

#ifdef __cplusplus
}
#endif

#endif
