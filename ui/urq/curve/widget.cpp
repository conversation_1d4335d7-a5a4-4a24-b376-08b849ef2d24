#include "urq/curve/widget.hpp"
#include "lvgl.h"
#include "urq/curve/conf.h"
#include "urq/curve/series.h"
#include "urq/style/line.h"
#include "urq/user_data.h"
#include "urq/util/color.h"
#include "urq/util/control.h"
#include "urq/util/curve.h"
#include "urq/util/state.h"
#include "urq/util/style.h"
#include "urq/util/time.h"
#include "urq/widget.h"
#include "urq/widget/common.h"
#include <cmath>
#include <cstddef>

#define Self urq_curve_widget_t

static lv_timer_t *add_data_timer = NULL;
static int64_t s_start_x_idx = 0;
static lv_coord_t s_cnt = 0;

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_CURVE_WIDGET_CLASS = {
    &lv_obj_class,                  // base_class
    _constructor_cb,                // constructor_cb
    _destructor_cb,                 // destructor_cb
    NULL,                           // user_data
    _event_cb,                      // event_cb
    LV_DPI_DEF,                     // width_def
    LV_DPI_DEF,                     // height_def
    LV_OBJ_CLASS_EDITABLE_INHERIT,  // editable
    LV_OBJ_CLASS_GROUP_DEF_INHERIT, // group_def
    sizeof(Self)                    // instance_size
};

static void urq_curve_widget_draw_event(lv_event_t *e)
{
    lv_obj_t *obj = (lv_obj_t *)lv_event_get_user_data(e);
    lv_obj_draw_part_dsc_t *dsc = lv_event_get_draw_part_dsc(e);
    Self *const widget = (Self *)obj;

    if (dsc->type == LV_CHART_DRAW_PART_LINE_AND_POINT &&
        dsc->part == LV_PART_ITEMS) {
        lv_chart_series_t *series = (lv_chart_series_t *)dsc->sub_part_ptr;
        urq_curve_series_t *series_conf = NULL;
        for (uint8_t i = 0; i < widget->conf->series_conf.size; i++) {
            if (widget->series[i] == series) {
                series_conf = widget->conf->series_conf.data[i];
                break;
            }
        }

        if (series_conf == NULL) {
            return;
        }

        if (dsc->line_dsc != NULL) {
            urq_style_line_t *line = series_conf->line;
            if (line != NULL) {
                urq_set_style_draw_line(
                    &widget->user_data.project->env, dsc->line_dsc, line);
            }
        }
        if (dsc->p1 != NULL) {
            urq_style_point_t *point = series_conf->point;
            if (point != NULL) {
                urq_set_style_draw_part_point(
                    &widget->user_data.project->env, dsc, point);
            }

            if (series_conf->show_fade_mask && dsc->p2 != NULL) {
                lv_draw_mask_line_param_t line_mask_param;
                lv_draw_mask_line_points_init(
                    &line_mask_param, dsc->p1->x, dsc->p1->y, dsc->p2->x,
                    dsc->p2->y, LV_DRAW_MASK_LINE_SIDE_BOTTOM);
                int16_t line_mask_id = lv_draw_mask_add(&line_mask_param, NULL);

                // 添加渐变遮罩
                lv_coord_t h = lv_obj_get_height(obj);
                lv_draw_mask_fade_param_t fade_mask_param;
                lv_draw_mask_fade_init(
                    &fade_mask_param, &obj->coords, LV_OPA_COVER,
                    obj->coords.y1 + h / 8, LV_OPA_TRANSP, obj->coords.y2);
                int16_t fade_mask_id = lv_draw_mask_add(&fade_mask_param, NULL);

                // 绘制矩形
                lv_draw_rect_dsc_t draw_rect_dsc;
                lv_draw_rect_dsc_init(&draw_rect_dsc);
                draw_rect_dsc.bg_opa = LV_OPA_100;
                draw_rect_dsc.bg_color = dsc->line_dsc->color;

                lv_area_t a;
                a.x1 = dsc->p1->x;
                a.x2 = dsc->p2->x - 1;
                a.y1 = LV_MIN(dsc->p1->y, dsc->p2->y);
                a.y2 = obj->coords.y2;
                lv_draw_rect(dsc->draw_ctx, &draw_rect_dsc, &a);

                // 移除遮罩
                lv_draw_mask_free_param(&line_mask_param);
                lv_draw_mask_free_param(&fade_mask_param);
                lv_draw_mask_remove_id(line_mask_id);
                lv_draw_mask_remove_id(fade_mask_id);
            }
        }
    } else if (
        dsc->type == LV_CHART_DRAW_PART_BAR && dsc->part == LV_PART_ITEMS) {
        lv_chart_series_t *series = (lv_chart_series_t *)dsc->sub_part_ptr;
        urq_curve_series_t *series_conf = NULL;
        for (uint8_t i = 0; i < widget->conf->series_conf.size; i++) {
            if (widget->series[i] == series) {
                series_conf = widget->conf->series_conf.data[i];
                break;
            }
        }

        if (series_conf == NULL) {
            return;
        }

        if (dsc->rect_dsc != NULL) {
            urq_style_bar_t *bar = series_conf->bar;
            if (bar != NULL) {
                if (bar->style != NULL)
                    urq_set_draw_style(
                        &widget->user_data.project->env, dsc, bar->style);
                dsc->rect_dsc->radius = bar->round;
                dsc->draw_area->x1 += (lv_coord_t)(bar->width / 2);
            }
        }
    } else if (
        dsc->type == LV_CHART_DRAW_PART_DIV_LINE_HOR &&
        widget->conf->y_scale != NULL) {
        if (widget->conf->y_scale->line != NULL) {
            urq_set_style_draw_line(
                &widget->user_data.project->env, dsc->line_dsc,
                widget->conf->y_scale->line);
        }

        // 修改水平网格线位置
        if (dsc->p1 != NULL && dsc->p2 != NULL && widget->grid_offset_y != 0) {
            lv_point_t hor_p1_new = *dsc->p1;
            lv_point_t hor_p2_new = *dsc->p2;

            hor_p1_new.y += widget->grid_offset_y;
            hor_p2_new.y += widget->grid_offset_y;

            *((lv_point_t *)dsc->p1) = hor_p1_new;
            *((lv_point_t *)dsc->p2) = hor_p2_new;
        }
    } else if (
        dsc->type == LV_CHART_DRAW_PART_DIV_LINE_VER && dsc->line_dsc != NULL &&
        widget->conf->x_scale != NULL) {
        if (widget->conf->x_scale->line != NULL) {
            urq_set_style_draw_line(
                &widget->user_data.project->env, dsc->line_dsc,
                widget->conf->x_scale->line);
        }

        // 修改垂直网格线位置
        if (dsc->p1 != NULL && dsc->p2 != NULL && widget->grid_offset_x != 0) {
            lv_point_t ver_p1_new = *dsc->p1;
            lv_point_t ver_p2_new = *dsc->p2;

            ver_p1_new.x += widget->grid_offset_x;
            ver_p2_new.x += widget->grid_offset_x;

            *((lv_point_t *)dsc->p1) = ver_p1_new;
            *((lv_point_t *)dsc->p2) = ver_p2_new;
        }
    } else if (
        dsc->type == LV_CHART_DRAW_PART_TICK_LABEL && dsc->text != NULL) {
        // TODO
        if (dsc->id == LV_CHART_AXIS_PRIMARY_X &&
            widget->conf->x_scale != NULL) {
            //     color_p = widget->conf->x_scale->color;
            urq_curve_widget_set_x_label(obj, dsc);
        } else if (
            dsc->id == LV_CHART_AXIS_PRIMARY_Y &&
            widget->conf->y_scale != NULL) {
            // color = widget->conf->y_scale->color;
            // urq_curve_widget_set_y_label(obj, dsc);
        } else if (
            dsc->id == LV_CHART_AXIS_SECONDARY_Y &&
            widget->conf->y_scale2 != NULL) {
            // color = widget->conf->y_scale2->color;
            // urq_curve_widget_set_y_label(obj, dsc);
        }
        // 设置颜色
        // lv_draw_label_dsc_t * label_dsc = dsc->label_dsc;
        // label_dsc->color = lv_color_make(color.ch.r, color.ch.g, color.ch.b);
        // label_dsc->opa = color.ch.a;
    }
}

static void urq_trace_timer_cb(lv_timer_t *timer)
{
    Self *const widget = (Self *)timer->user_data;
    widget->enable_trace = true;
    widget->has_trace_timer = false;
}

static void urq_slider_event_cb(lv_event_t *e)
{
    lv_obj_t *obj = (lv_obj_t *)lv_event_get_user_data(e);
    Self *const widget = (Self *)obj;
    // 获取事件代码
    lv_event_code_t code = lv_event_get_code(e);
    // 如果是拖拽事件
    if (code == LV_EVENT_PRESSING) {
        lv_point_t point;
        lv_indev_get_point(lv_indev_get_act(), &point);

        if (widget->last_drag_point.x == 0 && widget->last_drag_point.y == 0) {
            widget->last_drag_point = point;
            return;
        }

        lv_coord_t dx = point.x - widget->last_drag_point.x;
        lv_coord_t dy = point.y - widget->last_drag_point.y;

        if (dx != 0) {
            urq_curve_scale_t *xscale = widget->conf->x_scale;
            // 计算新的显示起始索引
            int32_t points_to_move = dx / 10; // 每10像素移动一个数据点
            int32_t new_start_idx = widget->startx_idx - points_to_move;

            // 确保索引在有效范围内 TODO
            uint16_t point_count = lv_chart_get_point_count(widget->chart);
            if (new_start_idx >= 0 &&
                new_start_idx <=
                    (int32_t)(widget->history_datas[0]->size - point_count)) {
                widget->startx_idx = (uint16_t)new_start_idx;

                if (widget->cursor != NULL) {
                    // 获取图表区域的x坐标范围
                    lv_coord_t chart_width = lv_obj_get_width(widget->chart);
                    lv_coord_t rel_x = point.x - widget->chart->coords.x1;

                    float point_spacing = (float)chart_width / point_count;
                    uint16_t nearest_point = (uint16_t)(rel_x / point_spacing);

                    widget->last_cursor_point = nearest_point;
                    lv_chart_set_cursor_point(
                        widget->chart, widget->cursor, NULL, nearest_point);
                }

                if (xscale != NULL && xscale->grid_line_cnt > 0) {

                    if (xscale->grid_spacing == 0) {
                        lv_coord_t chart_width =
                            lv_obj_get_width(widget->chart);
                        xscale->grid_spacing =
                            (lv_coord_t)(chart_width /
                                         (xscale->grid_line_cnt + 1));
                    }

                    if (xscale->grid_spacing > 0) {
                        widget->grid_offset_x +=
                            (lv_coord_t)(dx / 2); // 缩小偏移量以使移动更平滑

                        if (widget->grid_offset_x > xscale->grid_spacing) {
                            widget->grid_offset_x -= xscale->grid_spacing;
                            lv_obj_invalidate(widget->chart);
                        } else if (
                            widget->grid_offset_x < -xscale->grid_spacing) {
                            widget->grid_offset_x += xscale->grid_spacing;
                            lv_obj_invalidate(widget->chart);
                        }
                    }
                }
                if (!widget->has_trace_timer) {
                    widget->enable_trace = false;
                    widget->has_trace_timer = true;
                    lv_timer_create(urq_trace_timer_cb, 5000, widget);
                }
            }
        }

        if (dy != 0) {
            // TODO 辅助轴
            urq_curve_scale_t *yscale = widget->conf->y_scale;
            if (yscale != NULL) {
                widget->enable_trace = false;
                lv_coord_t current_range =
                    yscale->max_value - yscale->min_value;
                lv_coord_t chart_height = lv_obj_get_height(widget->chart);
                float y_scale_factor =
                    (float)current_range / (float)chart_height;

                lv_coord_t dy_val = 0;
                if (point.y < widget->last_drag_point.y) {
                    dy_val =
                        (lv_coord_t)((widget->last_drag_point.y - point.y) *
                                     y_scale_factor);
                    yscale->min_value -= dy_val;
                    yscale->max_value -= dy_val;
                } else {
                    dy_val =
                        (lv_coord_t)((point.y - widget->last_drag_point.y) *
                                     y_scale_factor);
                    yscale->min_value += dy_val;
                    yscale->max_value += dy_val;
                }

                if (yscale->grid_line_cnt > 0) {
                    if (yscale->grid_spacing == 0) {
                        yscale->grid_spacing =
                            (lv_coord_t)(chart_height /
                                         (yscale->grid_line_cnt + 1));
                    }
                    widget->grid_offset_y -= (lv_coord_t)(dy_val / 4);

                    if (widget->grid_offset_y > yscale->grid_spacing) {
                        widget->grid_offset_y -= yscale->grid_spacing;
                        lv_obj_invalidate(widget->chart);
                    } else if (widget->grid_offset_y < -yscale->grid_spacing) {
                        widget->grid_offset_y += yscale->grid_spacing;
                        lv_obj_invalidate(widget->chart);
                    }
                }

                lv_chart_set_range(
                    widget->chart, LV_CHART_AXIS_PRIMARY_Y, yscale->min_value,
                    yscale->max_value);
            }
        }

        widget->last_drag_point = point;
    } else if (code == LV_EVENT_RELEASED) {
        widget->last_drag_point = {0, 0};
    }
}

static void urq_dropdown_event_cb(lv_event_t *e)
{
    lv_obj_t *obj = (lv_obj_t *)lv_event_get_user_data(e);
    Self *const widget = (Self *)obj;
    uint16_t selected = lv_dropdown_get_selected(widget->dropdown);
    static lv_coord_t y_min = widget->conf->y_scale->min_value;
    static lv_coord_t y_max = widget->conf->y_scale->max_value;
    switch (selected) {
    case 0: // 放大
    {
        uint16_t old_point_count = lv_chart_get_point_count(widget->chart);
        lv_chart_set_point_count(widget->chart, old_point_count + 2);
        widget->conf->range_value = lv_chart_get_point_count(widget->chart);
        // 重置网格偏移
        widget->grid_offset_x = 0;
        widget->grid_offset_y = 0;
    } break;
    case 1:
        y_min += 10;
        y_max -= 10;
        if (y_min > y_max)
            break;

        lv_chart_set_range(
            widget->chart, LV_CHART_AXIS_PRIMARY_Y, y_min, y_max);
        // 重置垂直网格偏移
        widget->grid_offset_y = 0;
        break;
    case 2:
        lv_chart_set_point_count(
            widget->chart, lv_chart_get_point_count(widget->chart) - 2);
        widget->zoom_point_num -= 2;
        // 重置网格偏移
        widget->grid_offset_x = 0;
        widget->grid_offset_y = 0;
        break;
    case 3:
        y_min -= 10;
        y_max += 10;
        lv_chart_set_range(
            widget->chart, LV_CHART_AXIS_PRIMARY_Y, y_min, y_max);
        // 重置垂直网格偏移
        widget->grid_offset_y = 0;
        break;
    case 4:
        y_min = widget->conf->y_scale->min_value;
        y_max = widget->conf->y_scale->max_value;
        lv_chart_set_range(
            widget->chart, LV_CHART_AXIS_PRIMARY_Y, y_min, y_max);
        lv_chart_set_point_count(widget->chart, widget->conf->range_value);
        // 重置所有网格偏移
        widget->grid_offset_x = 0;
        widget->grid_offset_y = 0;
        break;
    default:
        break;
    }
    // widget->conf->range_value = lv_chart_get_point_count(widget->chart);
    urq_curve_widget_update_chart(obj);
    lv_dropdown_open(widget->dropdown);
}

static void urq_cursor_event_cb(lv_event_t *e)
{
    lv_obj_t *obj = (lv_obj_t *)lv_event_get_user_data(e);
    Self *const widget = (Self *)obj;

    lv_event_code_t code = lv_event_get_code(e);

    if (code == LV_EVENT_VALUE_CHANGED) {
        uint16_t id = (uint16_t)lv_chart_get_pressed_point(widget->chart);
        if (id != LV_CHART_POINT_NONE) {
            widget->last_cursor_point = id;
            lv_chart_set_cursor_point(
                widget->chart, widget->cursor, NULL, (uint16_t)id);
        }
    } else if (code == LV_EVENT_DRAW_POST_END) {
        if (widget->last_cursor_point == LV_CHART_POINT_NONE)
            return;

        for (uint8_t idx = 0; idx < widget->conf->series_conf.size; idx++) {
            lv_chart_series_t *ser = widget->series[idx];
            lv_point_t p;
            lv_chart_get_point_pos_by_id(
                widget->chart, ser, widget->last_cursor_point, &p);

            lv_coord_t value = 0;
            if (widget->last_cursor_point + widget->startx_idx <=
                widget->history_datas[idx]->size) {
                value =
                    widget->history_datas[idx]
                        ->array[widget->last_cursor_point + widget->startx_idx]
                        .y;
            }

            char buf[16] = {0};
            lv_snprintf(buf, sizeof(buf), LV_SYMBOL_DUMMY "$%d", value);

            lv_draw_rect_dsc_t draw_rect_dsc;
            lv_draw_rect_dsc_init(&draw_rect_dsc);
            draw_rect_dsc.bg_color = lv_color_black();
            draw_rect_dsc.bg_opa = LV_OPA_50;
            draw_rect_dsc.radius = 3;
            draw_rect_dsc.bg_img_src = buf;
            draw_rect_dsc.bg_img_recolor = lv_color_white();

            lv_area_t a;
            a.x1 = (lv_coord_t)(widget->chart->coords.x1 + p.x - 20);
            a.x2 = (lv_coord_t)(widget->chart->coords.x1 + p.x + 20);
            a.y1 = (lv_coord_t)(widget->chart->coords.y1 + p.y - 30);
            a.y2 = (lv_coord_t)(widget->chart->coords.y1 + p.y - 10);

            lv_draw_ctx_t *draw_ctx = lv_event_get_draw_ctx(e);
            lv_draw_rect(draw_ctx, &draw_rect_dsc, &a);
        }
        //}else if(code == LV_EVENT_RELEASED) {
        //    lv_chart_set_cursor_point(widget->chart, widget->cursor, NULL,
        //    LV_CHART_POINT_NONE);
    }
}

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    // user data
    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_curve_widget_config, NULL, urq_curve_widget_set_state,
        NULL);

    widget->chart = lv_chart_create(obj);
    widget->cursor = NULL;
    widget->series = NULL;
    widget->history_datas = NULL;
    widget->zoom_point_num = 1;
    widget->grid_offset_x = 0;
    widget->grid_offset_y = 0;
    widget->enable = true;
    widget->enable_trace = true;
    widget->last_cursor_point = LV_CHART_POINT_NONE;
    widget->has_trace_timer = false;
    widget->last_drag_point = {0, 0};
    widget->startx_idx = 0;
    widget->last_startx_idx = 0;
    widget->last_history_count = 0;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    urq_curve_widget_t *widget = (urq_curve_widget_t *)obj;
    urq_curve_widget_conf_t *conf = widget->conf;

    if (widget->user_data.type != URQ_WIDGET_TYPE_CLONE) {
        urq_user_data_free(&widget->user_data);

        if (add_data_timer != NULL) {
            lv_timer_del(add_data_timer);
            add_data_timer = NULL;
            s_cnt = 0;
        }

        lv_obj_remove_event_cb(obj, urq_slider_event_cb);
        lv_obj_remove_event_cb(obj, urq_curve_widget_draw_event);
        lv_obj_remove_event_cb(obj, urq_dropdown_event_cb);
        lv_obj_remove_event_cb(obj, urq_cursor_event_cb);

        if (conf != NULL) {
            urq_curve_widget_conf_free_inplace(conf);
            urq_free(conf);
        }
        if (widget->common_conf != NULL) {
            urq_widget_common_conf_free_inplace(widget->common_conf);
            urq_free(widget->common_conf);
        }
        if (widget->series != NULL) {
            urq_free(widget->series);
        }
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    Self *widget = (Self *)lv_event_get_target(e);
    urq_used(class_p);
    urq_used(widget);
}

lv_obj_t *urq_curve_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj;

    obj = lv_obj_class_create_obj(&URQ_CURVE_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

static void urq_add_data(lv_timer_t *timer)
{
    Self *const widget = (Self *)timer->user_data;
    urq_user_data_t *udata = urq_obj_get_user_data((lv_obj_t *)widget);

    // 添加新数据
    urq_curve_widget_set_data((lv_obj_t *)widget, 0, (lv_coord_t)(s_cnt));

    if (widget->zoom_point_num <= lv_chart_get_point_count(widget->chart)) {
        if (udata->type == URQ_WIDGET_TYPE_TREND_CURVE ||
            udata->type == URQ_WIDGET_TYPE_BAR_CURVE) {
            lv_chart_set_value_by_id(
                widget->chart, widget->series[0],
                (uint16_t)(widget->zoom_point_num - 1), (lv_coord_t)(s_cnt));
        } else {
            lv_chart_set_value_by_id2(
                widget->chart, widget->series[0],
                (uint16_t)(widget->zoom_point_num - 1),
                (lv_coord_t)(rand() % 11), (lv_coord_t)(rand() % 100));
        }
        widget->zoom_point_num++;
    } else {
        if (udata->type == URQ_WIDGET_TYPE_TREND_CURVE ||
            udata->type == URQ_WIDGET_TYPE_BAR_CURVE) {
            lv_chart_set_next_value(
                widget->chart, widget->series[0], (lv_coord_t)(s_cnt));
        } else {
            lv_chart_set_next_value2(
                widget->chart, widget->series[0], (lv_coord_t)(rand() % 11),
                (lv_coord_t)(rand() % 100));
        }
    }

    // if(widget->history_datas[0]->size - widget->last_startx_idx <=
    // lv_chart_get_point_count(widget->chart)) {
    //     lv_chart_set_value_by_id(widget->chart, widget->series[0],
    //     (uint16_t)(widget->history_datas[0]->size - 1), (lv_coord_t)(cnt));
    // }else {
    //     lv_chart_set_next_value(widget->chart, widget->series[0],
    //     (lv_coord_t)(cnt));
    // }
    //  lv_chart_refresh(widget->chart);
    // lv_chart_set_next_value(widget->chart, widget->series[0],
    // (lv_coord_t)(cnt));
    s_cnt++;
}

// 添加一个事件回调函数，处理图表大小变化
static void urq_chart_event_cb(lv_event_t *e)
{
    lv_obj_t *chart = lv_event_get_target(e);
    lv_obj_t *obj = (lv_obj_t *)lv_event_get_user_data(e);
    Self *const widget = (Self *)obj;
    lv_event_code_t code = lv_event_get_code(e);

    if (code == LV_EVENT_SIZE_CHANGED) {
        // 图表大小改变时重置网格偏移
        widget->grid_offset_x = 0;
        widget->grid_offset_y = 0;

        // 强制刷新图表
        lv_obj_invalidate(chart);
    }
}

static void urq_curve_widget_conf(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;
    urq_curve_widget_conf_t *conf = widget->conf;
    urq_project_t *const project = widget->user_data.project;
    urq_theme_color_t *theme_color = project->env.theme_color;

    lv_obj_set_pos(widget->chart, conf->offset_x, 0);
    // 是否有刻度要求显示 若有，则退长度
    lv_coord_t w =
        conf->y_scale != NULL
            ? (lv_coord_t)(widget->size.w - conf->offset_x - conf->offset_x2)
            : widget->size.w;
    lv_coord_t h = conf->x_scale != NULL
                       ? (lv_coord_t)(widget->size.h - conf->offset_y)
                       : widget->size.h;
    lv_obj_set_size(widget->chart, w, h);

    // 配置chart
    lv_chart_set_type(widget->chart, mut_conf->config.curve->type);
    lv_chart_set_update_mode(
        widget->chart, mut_conf->config.curve->data_update_mode);

    if (conf->show_range_type == URQ_CURVE_SHOW_TYPE_POINT) {
        lv_chart_set_point_count(widget->chart, conf->range_value);
    } else if (conf->show_range_type == URQ_CURVE_SHOW_TYPE_PIXEL_DISTANCE) {
        lv_chart_set_point_count(
            widget->chart, (uint16_t)(widget->size.w / conf->range_value));
    }

    // 初始化滑动相关变量
    // 刻度配置
    urq_curve_scale_t *yscale = conf->y_scale;
    urq_curve_scale_t *yscale2 = conf->y_scale2;
    urq_curve_scale_t *xscale = conf->x_scale;

    // 刻度
    if (yscale != NULL) {
        urq_curve_set_ticks(widget->chart, yscale, LV_CHART_AXIS_PRIMARY_Y);
    }

    if (yscale2 != NULL) {
        urq_curve_set_ticks(widget->chart, yscale2, LV_CHART_AXIS_SECONDARY_Y);
    }

    if (xscale != NULL) {
        urq_curve_set_ticks(widget->chart, xscale, LV_CHART_AXIS_PRIMARY_X);
    }

    // 网格
    if (yscale != NULL && xscale != NULL &&
        (yscale->grid_line_cnt != 0 || xscale->grid_line_cnt != 0)) {
        lv_chart_set_div_line_count(
            widget->chart, yscale->grid_line_cnt, xscale->grid_line_cnt);
    }

    // 设置曲线
    if (conf->series_conf.size > 0) {
        size_t series_count = conf->series_conf.size;
        widget->series = (lv_chart_series_t **)urq_malloc(
            sizeof(lv_chart_series_t *) * series_count);
        urq_curve_series_ptr_list_t series_conf = conf->series_conf;
        widget->history_datas = (urq_point_cqueue_t **)urq_malloc(
            sizeof(urq_point_cqueue_t *) * series_count);

        for (size_t i = 0; i < series_count; i++) {
            urq_color_ref_t color = series_conf.data[i]->color;
            widget->series[i] = lv_chart_add_series(
                widget->chart, urq_get_color(theme_color, &color),
                LV_CHART_AXIS_PRIMARY_Y);
            widget->history_datas[i] =
                urq_point_cqueue_create(URQ_CURVE_WIDGET_MAX_HISTORY_SIZE);
        }
    }

    // 浮标
    urq_curve_cursor_t *cursor_conf = conf->cursor_conf;
    if (cursor_conf != NULL) {
        widget->cursor = lv_chart_add_cursor(
            widget->chart, urq_get_color(theme_color, &cursor_conf->color),
            LV_DIR_TOP | LV_DIR_BOTTOM);
        lv_obj_set_style_opa(
            widget->chart, urq_get_opacity(theme_color, &cursor_conf->color),
            LV_PART_CURSOR);
        lv_obj_set_style_line_width(
            widget->chart, cursor_conf->cursor_width, LV_PART_CURSOR);
        lv_obj_add_event_cb(
            widget->chart, urq_cursor_event_cb, LV_EVENT_ALL, self);
    }

    // event
    lv_obj_add_event_cb(widget->chart, urq_slider_event_cb, LV_EVENT_ALL, self);
    lv_obj_add_event_cb(
        widget->chart, urq_curve_widget_draw_event, LV_EVENT_DRAW_PART_BEGIN,
        self);
    lv_obj_add_event_cb(
        widget->chart, urq_chart_event_cb, LV_EVENT_SIZE_CHANGED, self);

    // 配置下拉列表
    widget->dropdown = lv_dropdown_create(self);
    lv_obj_set_size(widget->dropdown, 40, 40);
    lv_dropdown_set_options(widget->dropdown, "bx\nby\nsx\nsy\nreset");
    // lv_dropdown_set_selected(widget->dropdown, );
    lv_dropdown_set_text(widget->dropdown, ""); // 将显示文本设置为空
    lv_obj_align_to(
        widget->dropdown, widget->chart, LV_ALIGN_TOP_RIGHT, 10, -10);
    lv_obj_set_style_bg_color(widget->dropdown, lv_color_black(), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(widget->dropdown, LV_OPA_50, LV_PART_MAIN);
    lv_obj_set_style_bg_opa(widget->dropdown, LV_OPA_50, LV_PART_SELECTED);
    lv_dropdown_set_symbol(widget->dropdown, LV_SYMBOL_SETTINGS);
    lv_obj_add_event_cb(
        widget->dropdown, urq_dropdown_event_cb, LV_EVENT_VALUE_CHANGED, self);
    lv_dropdown_set_selected_highlight(widget->dropdown, false);

    lv_obj_t *dp_list = lv_dropdown_get_list(widget->dropdown);
    lv_obj_set_size(dp_list, 40, 40);
    lv_obj_set_style_bg_color(dp_list, lv_color_black(), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(dp_list, LV_OPA_50, LV_PART_MAIN);
    lv_obj_set_style_bg_opa(dp_list, LV_OPA_50, LV_PART_SELECTED);
}

void urq_curve_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;
    widget->conf = mut_conf->config.curve;
    widget->common_conf = mut_conf->common_conf;
    widget->size = mut_conf->size;

    // TODO
    mut_conf->pos.x = 0;
    mut_conf->pos.y = 0;

    urq_widget_config(self, mut_conf, urq_curve_widget_conf);

    /// 设置曲线值
    add_data_timer = lv_timer_create(urq_add_data, 1000, self);
}

void urq_curve_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    Self *const widget = (Self *)self;
    urq_set_state(self, widget->common_conf, state, platfrom);
}

void urq_curve_widget_data_clear(lv_obj_t *self)
{
    Self *const widget = (Self *)self;
    if (widget->chart == NULL || widget->series == NULL ||
        widget->conf == NULL) {
        return;
    }

    for (uint8_t i = 0; i < widget->conf->series_conf.size; i++) {
        lv_chart_series_t *_series = widget->series[i];
        if (_series == NULL)
            continue;

        for (size_t j = 0; j < lv_chart_get_point_count(widget->chart); j++) {
            _series->y_points[j] = LV_CHART_POINT_NONE;
        }
    }
    lv_chart_refresh(widget->chart);
}

void urq_curve_widget_set_data(
    lv_obj_t *self, uint8_t series_idx, lv_coord_t y_data)
{
    Self *const widget = (Self *)self;
    if (widget->chart == NULL || widget->series == NULL ||
        widget->conf == NULL || !widget->enable) {
        return;
    }

    if (widget->conf->series_conf.size > series_idx) {
        if (widget->history_datas[series_idx]->size >
            URQ_CURVE_WIDGET_MAX_HISTORY_SIZE) {
            urq_point_cqueue_pop_front(widget->history_datas[series_idx]);
        }

        // 保存原始数据值，不应用任何缩放
        urq_point_cqueue_enqueue(
            widget->history_datas[series_idx],
            urq_point_t{s_start_x_idx, y_data});
        urq_curve_widget_get_x_next_label(self);
        if (widget->enable_trace) {
            widget->startx_idx =
                widget->history_datas[series_idx]->size >
                        widget->conf->range_value
                    ? (uint16_t)(widget->history_datas[series_idx]->size -
                                 widget->conf->range_value)
                    : 0;
        }
    }
    widget->last_startx_idx = widget->startx_idx;

    //// 如果历史数据数量小于等于显示点数，直接设置点值，否则更新图表
    // if(widget->history_datas[series_idx]->size <=
    // lv_chart_get_point_count(widget->chart)) {
    //     // 直接设置图表点的值
    //     lv_chart_set_value_by_id(widget->chart, widget->series[series_idx],
    //                         (uint16_t)(widget->history_datas[series_idx]->size
    //                         - 1), y_data);
    // } else {
    //     // 需要更新整个图表
    //     urq_curve_widget_update_chart(self);
    // }
}

void urq_curve_widget_set_all_series_next_value(
    lv_obj_t *self, lv_coord_t y_data)
{
    Self *const widget = (Self *)self;
    if (widget->chart == NULL || widget->series == NULL ||
        widget->conf == NULL || !widget->enable) {
        return;
    }
    for (uint8_t i = 0; i < widget->conf->series_conf.size; i++) {
        lv_chart_set_next_value(widget->chart, widget->series[i], y_data);
    }
}

void urq_curve_widget_set_data_with_x(
    lv_obj_t *self, uint8_t series_idx, lv_coord_t y_data, int64_t x_data)
{
    Self *const widget = (Self *)self;
    if (widget->chart == NULL || widget->series == NULL ||
        widget->conf == NULL) {
        return;
    }

    if (widget->conf->series_conf.size > series_idx) {
        // lv_chart_set_next_value(widget->chart, widget->series[series_idx],
        // y_data);
        if (widget->history_datas[series_idx]->size >
            URQ_CURVE_WIDGET_MAX_HISTORY_SIZE) {
            urq_point_cqueue_pop_front(widget->history_datas[series_idx]);
        }

        if (widget->conf->series_conf.data[series_idx]->support_scale) {
            // 获取当前序列的Y轴范围
            lv_coord_t y_min =
                widget->conf->series_conf.data[series_idx]->y_min_value;
            lv_coord_t y_max =
                widget->conf->series_conf.data[series_idx]->y_max_value;

            if (y_min != -1 && y_max != -1) {
                float scale_factor = (float)(widget->conf->y_scale->max_value -
                                             widget->conf->y_scale->min_value) /
                                     (float)(y_max - y_min);
                y_data = (lv_coord_t)((y_data - y_min) * scale_factor);
            }
        }
        urq_point_cqueue_enqueue(
            widget->history_datas[series_idx], urq_point_t{x_data, y_data});
        if (widget->enable_trace) {
            widget->startx_idx =
                widget->history_datas[series_idx]->size >
                        widget->conf->range_value
                    ? (uint16_t)(widget->history_datas[series_idx]->size -
                                 widget->conf->range_value)
                    : 0;
        }
    }
}

void urq_curve_widget_update_chart(lv_obj_t *self)
{
    Self *const widget = (Self *)self;
    if (widget->chart == NULL || widget->series == NULL ||
        widget->conf == NULL || !widget->enable) {
        return;
    }
    int diff = widget->startx_idx - widget->last_startx_idx;

    if (diff == 0 ||
        widget->last_history_count != widget->history_datas[0]->size) {
        urq_curve_widget_update_all_chart(self);
    } else {
        urq_curve_widget_update_incremental_chart(self);
    }
}

void urq_curve_widget_update_all_chart(lv_obj_t *self)
{
    Self *const widget = (Self *)self;
    if (widget->chart == NULL || widget->series == NULL ||
        widget->conf == NULL || !widget->enable) {
        return;
    }

    uint16_t point_count = lv_chart_get_point_count(widget->chart);

    for (uint8_t i = 0; i < widget->conf->series_conf.size; i++) {
        // 从历史数据中获取数据点
        urq_point_cqueue_t *history_datas = widget->history_datas[i];
        for (uint16_t j = 0; j < point_count; j++) {
            uint16_t idx = widget->startx_idx + j;
            if (idx < history_datas->size) {
                // 获取原始数据值
                lv_coord_t y_value = history_datas->array[idx].y;

                // 更新图表点的值
                lv_chart_set_value_by_id(
                    widget->chart, widget->series[i], j, y_value);
            }
        }
    }

    // 强制刷新图表
    lv_chart_refresh(widget->chart);
}

void urq_curve_widget_update_incremental_chart(lv_obj_t *self)
{
    Self *const widget = (Self *)self;
    // if(widget->chart == NULL || widget->series == NULL || widget->conf ==
    // NULL || !widget->enable) {
    //     return;
    // }

    int diff = widget->startx_idx - widget->last_startx_idx;

    if (abs(diff) < 10) {
        if (diff > 0) {
            // 向左滑动
            for (uint16_t i = 0; i < diff; i++) {
                // 添加右侧新点
                int new_idx = widget->startx_idx + 10 - 1 - i;
                lv_coord_t y_data =
                    new_idx < widget->history_datas[0]->size
                        ? widget->history_datas[0]->array[new_idx].y
                        : 0;
                urq_curve_widget_set_all_series_next_value(self, y_data);
            }
        } else {
            // 向右滑动
            diff = -diff;
            for (uint16_t i = 0; i < diff; i++) {
                // 在开头添加新点
                uint16_t new_idx = widget->startx_idx + i;
                lv_coord_t y_data =
                    new_idx < widget->history_datas[0]->size
                        ? widget->history_datas[0]->array[new_idx].y
                        : 0;
                urq_curve_widget_set_all_series_next_value(self, y_data);
            }
        }
    }
}

void urq_curve_widget_zoom_x(lv_obj_t *self, float zoom)
{
    Self *const widget = (Self *)self;
    lv_chart_set_zoom_x(widget->chart, (uint16_t)(zoom * 256));
}

void urq_curve_widget_zoom_y(lv_obj_t *self, float zoom)
{
    Self *const widget = (Self *)self;
    lv_chart_set_zoom_y(widget->chart, (uint16_t)(zoom * 256));
}

void urq_curve_widget_control_run(lv_obj_t *self, bool run)
{
    Self *const widget = (Self *)self;
    widget->enable = run;
}

void urq_curve_widget_set_cursor(lv_obj_t *self, bool show)
{
    Self *const widget = (Self *)self;
    urq_project_t *const project = widget->user_data.project;
    if (show) {
        if (widget->cursor == NULL) {
            urq_curve_cursor_t *cursor_conf = widget->conf->cursor_conf;
            widget->cursor = lv_chart_add_cursor(
                widget->chart,
                urq_get_color(project->env.theme_color, &cursor_conf->color),
                LV_DIR_TOP | LV_DIR_BOTTOM);
        }
    } else {
        if (widget->cursor != NULL) {
            lv_chart_set_cursor_point(
                widget->chart, widget->cursor, NULL, LV_CHART_POINT_NONE);
        }
    }
}

void urq_curve_widget_set_x_range(
    lv_obj_t *self, lv_coord_t min, lv_coord_t max)
{
    Self *const widget = (Self *)self;
    lv_chart_set_range(widget->chart, LV_CHART_AXIS_PRIMARY_X, min, max);
}

void urq_curve_widget_set_y_range(
    lv_obj_t *self, lv_coord_t min, lv_coord_t max)
{
    Self *const widget = (Self *)self;
    lv_chart_set_range(widget->chart, LV_CHART_AXIS_PRIMARY_Y, min, max);
}

int64_t urq_curve_widget_get_x_next_label(lv_obj_t *self)
{
    Self *const widget = (Self *)self;

    if (widget->conf->x_scale != NULL &&
        widget->conf->x_scale->show_type == URQ_CURVE_SCALE_SHOW_TYPE_POINT) {
        s_start_x_idx += (int64_t)ceil(
            widget->conf->range_value / widget->conf->x_scale->main_tick_count);
        // s_start_x_idx += 1;
    }
    return s_start_x_idx;
}

void urq_curve_widget_set_x_label(lv_obj_t *self, lv_obj_draw_part_dsc_t *dsc)
{
    Self *const widget = (Self *)self;
    urq_curve_scale_t *x_scale = widget->conf->x_scale;
    int32_t idx = (int32_t)widget->startx_idx + dsc->value;
    char buf[16] = {" "};

    if (x_scale->show_type == URQ_CURVE_SCALE_SHOW_TYPE_POINT) {
        uint16_t point_count = lv_chart_get_point_count(widget->chart);
        lv_chart_series_t *ser = lv_chart_get_series_next(widget->chart, NULL);
        int cnt = 0;
        while (ser != NULL) {
            urq_point_cqueue_t *data = widget->history_datas[cnt];
            int data_size = data->size;
            if (point_count > x_scale->main_tick_count) {
                float N = (float)(point_count) /
                          (float)(x_scale->main_tick_count + 1);
                int _cnt = (int32_t)std::ceil((N * (float)dsc->value));
                if ((N * (float)dsc->value) <
                    (float)(x_scale->main_tick_count)) {
                    if (_cnt + widget->startx_idx < data_size) {
                        snprintf(
                            buf, sizeof(buf), "%d",
                            (int32_t)data->array[_cnt + widget->startx_idx].x);
                    }
                }
            } else if (point_count == x_scale->main_tick_count) {
                if (idx < data_size) {
                    snprintf(
                        buf, sizeof(buf), "%d", (int32_t)data->array[idx].x);
                }
            } else {
                int N = (int)(x_scale->main_tick_count) / (int)(point_count);

                if (dsc->value % N == 0 || dsc->value == 0 || dsc->value == 9) {
                    if (idx < data_size) {
                        snprintf(
                            buf, sizeof(buf), "%d",
                            (int32_t)data->array[idx].x);
                    }
                }
            }
            ser = lv_chart_get_series_next(widget->chart, ser);
            cnt++;
        }

    } else if (x_scale->show_type == URQ_CURVE_SCALE_SHOW_TYPE_TIME) {
        if (x_scale->time_format != URQ_TIME_FORMAT_UNSPECIFIED &&
            x_scale->date_format != URQ_DATE_FORMAT_UNSPECIFIED) {
            urq_timestamp_to_datetime_string(
                idx, x_scale->date_format, x_scale->time_format, buf,
                sizeof(buf));
        } else if (x_scale->time_format != URQ_TIME_FORMAT_UNSPECIFIED) {
            urq_timestamp_to_time_string(
                idx, x_scale->time_format, buf, sizeof(buf));
        } else if (x_scale->date_format != URQ_DATE_FORMAT_UNSPECIFIED) {
            urq_timestamp_to_date_string(
                idx, x_scale->date_format, buf, sizeof(buf));
        }
    }
    if (strcmp(buf, "") != 0) {
        strcpy(dsc->text, buf);
    }
}

void urq_curve_widget_set_y_label(lv_obj_t *self, lv_obj_draw_part_dsc_t *dsc)
{
    Self *const widget = (Self *)self;
    int32_t value = dsc->value;

    char buf[16] = {0};
    if (widget->conf->y_scale &&
        widget->conf->y_scale->show_type != URQ_CURVE_SCALE_SHOW_TYPE_NONE) {
        // 计算实际数值
        lv_coord_t min = widget->conf->y_scale->min_value;
        lv_coord_t max = widget->conf->y_scale->max_value;

        if (min != -1 && max != -1) {
            // 使用设置的最大最小值计算比例
            double ratio = (double)value / 100; // LVGL内部使用0-100表示刻度位置
            double actual_value = min + ratio * (max - min);

            // 根据值的大小选择适当的精度
            if (fabs(actual_value) < 0.1) {
                snprintf(
                    buf, sizeof(buf), "%.3f",
                    actual_value); // 小数值显示更多小数位
            } else if (fabs(actual_value) < 10) {
                snprintf(
                    buf, sizeof(buf), "%.2f",
                    actual_value); // 适中值显示两位小数
            } else {
                snprintf(
                    buf, sizeof(buf), "%.1f",
                    actual_value); // 大值只显示一位小数
            }
        } else {
            // 直接使用LVGL提供的值
            snprintf(buf, sizeof(buf), "%d", value);
        }
    } else {
        // 默认情况下直接使用值
        snprintf(buf, sizeof(buf), "%d", value);
    }

    strcpy(dsc->text, buf);
}

void urq_curve_widget_set_series_width(
    lv_obj_t *self, uint8_t series_idx, uint8_t width)
{
    Self *const widget = (Self *)self;
    widget->conf->series_conf.data[series_idx]->line->width = width;
}

void urq_curve_widget_set_series_visible(
    lv_obj_t *self, uint8_t series_idx, bool visible)
{
    Self *const widget = (Self *)self;
    lv_chart_hide_series(widget->chart, widget->series[series_idx], !visible);
}

void urq_curve_widget_set_series_range(
    lv_obj_t *self, uint8_t series_idx, lv_coord_t min, lv_coord_t max)
{
    Self *const widget = (Self *)self;
    urq_used(widget);
    urq_used(series_idx);
    urq_used(min);
    urq_used(max);
    // widget->conf->series_conf.data[series_idx]->y_min = min;
    // widget->conf->series_conf.data[series_idx].y_max = max;
}

void urq_curve_widget_set_data_with_x(
    lv_obj_t *self, uint8_t series_idx, lv_coord_t y_data, lv_coord_t x_data)
{
    Self *const widget = (Self *)self;
    if (widget->chart == NULL || widget->series == NULL ||
        widget->conf == NULL || !widget->enable) {
        return;
    }

    // 仅作为示例，实际实现需要根据业务需求
    if (widget->conf->series_conf.size > series_idx) {
        // lv_chart_set_next_value(widget->chart, widget->series[series_idx],
        // y_data);
        if (widget->history_datas[series_idx]->size >
            URQ_CURVE_WIDGET_MAX_HISTORY_SIZE) {
            urq_point_cqueue_pop_front(widget->history_datas[series_idx]);
        }
        urq_point_cqueue_enqueue(
            widget->history_datas[series_idx], urq_point_t{x_data, y_data});
    }
}

void urq_curve_widget_parse_series_data(lv_obj_t *self, uint8_t *data)
{
    // 解析数据格式：时间戳，通道1，通道2，...，通道n
    urq_used(self);
    urq_used(data);
    // Self *const widget = (Self *)self;
    // urq_buf_read_t reader = urq_buf_read_make(0, data); // 创建读取器

    //// 读取时间戳
    // int64_t timestamp = urq_buf_read_i32(&reader);

    //// 遍历所有通道数据
    // for (uint8_t i = 0; i < widget->conf->series_conf.size; i++) {
    //     // 读取通道数据
    //     lv_coord_t y_value = urq_buf_read_i16(&reader);
    //
    //     // 设置数据点
    //     urq_curve_widget_set_data_with_x(self, i, y_value, timestamp);
    // }

    // urq_curve_widget_update_chart(self);
}

void urq_curve_widget_parse_series_history_data(lv_obj_t *self, uint8_t *data)
{
    //
    // 时间区间
    // 数据条数
    // 时间戳、通道...
    urq_used(self);
    urq_used(data);
}

void urq_curve_widget_set_grid_offset(
    lv_obj_t *self, lv_coord_t x_offset, lv_coord_t y_offset)
{
    Self *const widget = (Self *)self;
    widget->grid_offset_x = x_offset;
    widget->grid_offset_y = y_offset;

    // 强制刷新图表以显示网格变化
    lv_obj_invalidate(widget->chart);
}
