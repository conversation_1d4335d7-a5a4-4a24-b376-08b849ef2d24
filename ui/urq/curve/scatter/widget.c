
#include "urq/curve/scatter/widget.h"
#include "lvgl.h"

#include "urq/user_data.h"

#define Self urq_scatter_curve_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_SCATTER_CURVE_WIDGET_CLASS = {
    &lv_obj_class,
    _constructor_cb,
    _destructor_cb,
    NULL,
    _event_cb,
    LV_DPI_DEF,
    LV_DPI_DEF,
    LV_OBJ_CLASS_EDITABLE_INHERIT,
    LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    // user data
    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_scatter_curve_widget_config, NULL,
        urq_curve_widget_set_state, NULL);
    widget->xy_curve = urq_curve_widget_create(obj);
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    urq_used(obj);
    Self *widget = (Self *)obj;
    urq_user_data_free(&widget->user_data);
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    Self *widget = (Self *)lv_event_get_target(e);
    urq_used(widget);
    urq_used(class_p);
}

lv_obj_t *urq_scatter_curve_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj;

    obj = lv_obj_class_create_obj(&URQ_SCATTER_CURVE_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

void urq_scatter_curve_widget_config(
    lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;

    lv_obj_set_pos(self, mut_conf->pos.x, mut_conf->pos.y);
    lv_obj_set_size(self, mut_conf->size.w, mut_conf->size.h);

    // 创建曲线
    urq_curve_widget_conf_t *conf = mut_conf->config.scatter_curve->conf;
    conf->data_update_mode = LV_CHART_UPDATE_MODE_CIRCULAR;
    conf->type = URQ_CURVE_TYPE_SCATTER;
    for (uint8_t i = 0; i < conf->series_conf.size; i++) {
        conf->series_conf.data[i]->line->width = 0;
    }

    mut_conf->type = URQ_WIDGET_TYPE_NONE;
    mut_conf->config.curve = conf;

    urq_user_data_t *udata = urq_obj_get_user_data(widget->xy_curve);
    udata->type = URQ_WIDGET_TYPE_SCATTER_CURVE;
    udata->project = widget->user_data.project;
    udata->page = widget->user_data.page;
    udata->conf(widget->xy_curve, mut_conf);
}

void urq_scatter_curve_widget_set_data(lv_obj_t *self, lv_coord_t data)
{
    Self *const widget = (Self *)self;
    urq_used(widget);
    urq_used(data);
    // urq_curve_widget_set_data(widget->xy_curve->vy_curve, 0, data);
    // update
}

void urq_scatter_curve_widget_set_data_with_x(
    lv_obj_t *self, lv_coord_t y_data, lv_coord_t x_data)
{
    ///
    Self *const widget = (Self *)self;
    urq_used(widget);
    urq_used(y_data);
    urq_used(x_data);
    // urq_curve_widget_set_data_with_x(widget->xy_curve->vy_curve, 0, y_data,
    // x_data); update
}
