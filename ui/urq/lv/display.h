#pragma once

#include "lvgl.h"
#include <stdint.h>

#ifndef URQ__LV__DISPLAY_H
#define URQ__LV__DISPLAY_H
#ifdef __cplusplus
extern "C" {
#endif

/// 创建一个显示设备
///
/// @param id     js 那边的上下文 id
/// @param width  显示设备的宽度
/// @param height 显示设备的高度
/// @return 0 成功，其他失败
int urq_lv_display_init(
    int32_t id,       //
    lv_coord_t width, //
    lv_coord_t height //
    )                 //
    __attribute__((__warn_unused_result__()));

/// 销毁一个显示设备
///
/// @return 0 成功，其他失败
void urq_lv_display_deinit(void);

#ifdef __cplusplus
}
#endif
#endif // URQ__LV__DISPLAY_H
