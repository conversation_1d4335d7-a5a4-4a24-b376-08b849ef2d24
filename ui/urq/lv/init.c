#include "urq/lv/init.h"
#include "urq/lv/display.h"
#include "urq/lv/fs.h"
#include "urq/lv/keyboard.h"
#include "urq/lv/mouse.h"
#include <lvgl/src/misc/lv_area.h>
#include <stdint.h>

int urq_lv_init(
    int id, lv_coord_t width, lv_coord_t height, lv_indev_t **keyboard)
{
    lv_init();

    int code = urq_lv_display_init(id, width, height);
    if (code != 0) {
        return code;
    }

    lv_indev_t *mouse = urq_lv_mouse_init(id);
    if (mouse == NULL) {
        return -1;
    }

    *keyboard = urq_lv_keyboard_init(id);

    urq_lv_fs_init(id);
    return 0;
}
