#pragma once

#include "lvgl/src/hal/lv_hal_indev.h"
#include <stdint.h>
#ifndef URQ__LV__INIT_H
#define URQ__LV__INIT_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 初始化 lvgl
/// @param id       iid
/// @param width    宽度
/// @param height   高度
/// @param keyboard 键盘输入设备
/// @returns 0 成功
int urq_lv_init(
    int id, lv_coord_t width, lv_coord_t height, lv_indev_t **keyboard)
    __attribute__((__nonnull__(4))) __attribute__((__warn_unused_result__()));

#ifdef __cplusplus
}
#endif
#endif // URQ__LV__INIT_H
