#include "urq/compile.h"
#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM

#include "urq/lv/mouse.h"
#include "urq/web.h"
#include <lvgl/lvgl.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>

typedef struct {
    /// js 那边的上下文 id
    int32_t id;
    urq_web_mouse_event_t event;
} _user_data_t;

static void _read_mouse(
    struct _lv_indev_drv_t *indev_drv, lv_indev_data_t *data)
{
    _user_data_t *user_data = indev_drv->user_data;
    urq_web_read_mouse(user_data->id, &user_data->event);
    urq_web_mouse_event_t event = user_data->event;

    data->point.x = event.x;
    data->point.y = event.y;
    data->state =
        event.pressure == 0 ? LV_INDEV_STATE_RELEASED : LV_INDEV_STATE_PRESSED;
    data->continue_reading = event.next != 0;
}

/// 创建一个输入设备
lv_indev_t *urq_lv_mouse_init(int id)
{
    lv_indev_drv_t *indev_drv = urq_malloc(sizeof(lv_indev_drv_t));
    if (indev_drv == NULL) {
        printf("malloc indev_drv failed\n");
        return NULL;
    }
    _user_data_t *user_data = urq_malloc(sizeof(_user_data_t));
    if (user_data == NULL) {
        free(indev_drv);
        printf("malloc user_data failed\n");
        return NULL;
    }
    user_data->id = id;

    lv_indev_drv_init(indev_drv);
    indev_drv->type = LV_INDEV_TYPE_POINTER;
    indev_drv->read_cb = _read_mouse;
    indev_drv->user_data = user_data;
    return lv_indev_drv_register(indev_drv);
}

#else 
#include <stddef.h>

// void __empty_function(void) {
//     return;
// }

#endif // #if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
