#include "urq/compile.h"
#include "urq/preload.h"

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64 &&                   \
    URQ_COMPILE_SDL == 1

#include "urq/lv/mouse.h"
#include <lv_drivers/sdl/sdl_common.h>

/// 创建一个鼠标输入设备
///
/// @param id js 那边的上下文 id，其它平台随便传
/// @return 输入设备指针，失败返回 NULL
lv_indev_t *urq_lv_mouse_init(int32_t id)
{
    urq_used(id);
    static lv_indev_drv_t indev_drv;

    lv_indev_drv_init(&indev_drv);
    indev_drv.type = LV_INDEV_TYPE_POINTER;
    indev_drv.read_cb = sdl_mouse_read;
    return lv_indev_drv_register(&indev_drv);
}
#else 
#include <stddef.h>

// void __empty_function(void) {
//     return;
// }

#endif // #if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64
