#include "urq/compile.h"
#include "urq/compile.h" //
#include "urq/exit.h"
#include "urq/urq.h"
#include "urq/exit.h"

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64 &&                   \
    URQ_COMPILE_SDL == 0

#include "time.h"
#include "urq/log/info.h"
#include "urq/lv/mouse.h"
#include <errno.h>
#include <fcntl.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>

#define EVENT_PATH "/tmp/urq.mouse.event" // 事件 unix socket 路径

/// @brief 事件
typedef struct {
    int down; // 是否按下
    int x;    // 鼠标 x 坐标
    int y;    // 鼠标 y 坐标
} urq_lv_mouse_event_t;

typedef struct {
    urq_lv_mouse_event_t event;
} urq_lv_mouse_event_data_t;

/// @brief 连接到 unix socket
/// @return 连接成功返回 fd，失败返回 -1
static int _connect_server(void)
{
    struct sockaddr_un server_addr;

    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sun_family = AF_UNIX;
    strncpy(server_addr.sun_path, EVENT_PATH, sizeof(server_addr.sun_path) - 1);

    int fd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (fd < 0) {
        perror("socket");
        return -1;
    }
    if (connect(fd, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        perror("connect");
        close(fd);
        return -1;
    }

    // 设置非阻塞
    // int flags = fcntl(fd, F_GETFL, 0);
    // fcntl(fd, F_SETFL, flags | O_NONBLOCK);
    return fd;
}

/// @brief 接收事件
static void *_recv_event(void *arg)
{
    urq_lv_mouse_event_data_t *dest = (urq_lv_mouse_event_data_t *)arg;
    urq_lv_mouse_event_t event;

    int fd = -1;
    while (urq_is_running) {
        fd = _connect_server();
        if (fd < 0) {
            log_i(
                "connect mouse event server failed, server: " EVENT_PATH
                ", error: %s\n",
                strerror(errno));
            sleep(1);
            continue;
        }
        log_v("connect mouse event server success, server: %s\n", EVENT_PATH);
        while (true) {
            ssize_t read_size = read(fd, &event, sizeof(event));
            if (read_size < 0) {
                printf("read event failed, error: %s\n", strerror(errno));
                sleep(1);
                break;
            }
            if (read_size == 0) {
                log_i("server closed, reconnect\n");
                close(fd);
                fd = -1;
                urq_exit(0); // TODO: 退出程序
                break;
            }
            if (read_size != sizeof(event)) {
                log_e("read event size error, size: %ld\n", read_size);
                close(fd);
                fd = -1;
                break;
            }
            log_v(
                "receive mouse event: %s, %d:%d\n", event.down ? "down" : "up",
                event.x, event.y);
            dest->event = event;
        }
    }

    return NULL;
}

/// @brief 读取事件
static void _mouse_read(lv_indev_drv_t *indev_drv, lv_indev_data_t *data)
{
    urq_lv_mouse_event_data_t *event_data =
        (urq_lv_mouse_event_data_t *)indev_drv->user_data;

    int32_t x = 4095;
    int32_t y = 4095;

    data->point.x = (lv_coord_t)event_data->event.x / x;
    data->point.y = (lv_coord_t)event_data->event.y / y;
    data->state = event_data->event.down ? LV_INDEV_STATE_PRESSED
                                         : LV_INDEV_STATE_RELEASED;
    sdl_mouse_read(indev_drv, data);
    
    // /* 映射到屏幕分辨率 */
    // data->point.x = (x * LV_HOR_RES) / TOUCH_X_MAX; // TOUCH_X_MAX 是触摸屏的最大X坐标
    // data->point.y = (y * LV_VER_RES) / TOUCH_Y_MAX; // TOUCH_Y_MAX 是触摸屏的最大Y坐标
    // data->state = LV_INDEV_STATE_PR; // 按下状态
    printf("========> %d, %d\n", x, y);
}

/// @brief 创建一个鼠标输入设备
///
/// @param id js 那边的上下文 id，其它平台随便传
/// @return 输入设备指针，失败返回 NULL
lv_indev_t *urq_lv_mouse_init(int32_t id)
{
    static lv_indev_drv_t indev_drv;
    static urq_lv_mouse_event_data_t event_data;
    pthread_t thread;

    pthread_create(&thread, NULL, _recv_event, &event_data);

    lv_indev_drv_init(&indev_drv);
    indev_drv.type = LV_INDEV_TYPE_POINTER;
    indev_drv.read_cb = _mouse_read;

    indev_drv.user_data = (void *)(uintptr_t)&event_data;
    log_i("urq_lv_mouse_init: %d\n", id);
    return lv_indev_drv_register(&indev_drv);
}

#endif // #if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64
