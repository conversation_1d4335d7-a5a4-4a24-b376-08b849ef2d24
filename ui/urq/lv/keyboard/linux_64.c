#include "urq/compile.h"
#include "urq/log/debug.h"
#include "urq/preload.h"
#include <stddef.h>

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64 &&                   \
    URQ_COMPILE_SDL == 0

#include "urq/lv/keyboard.h"
// #include <lv_drivers/sdl/sdl_common.h>

/// @brief 读取键盘事件
static void _keyboard_read(lv_indev_drv_t *indev_drv, lv_indev_data_t *data)
{
    // sdl_keyboard_read(indev_drv, data);
    urq_used(indev_drv);
    urq_used(data);
}

/// @brief 初始化键盘
///
/// @param id js 那边的上下文 id，其它平台随便传
/// @return lv_indev_t* 键盘输入设备
lv_indev_t *urq_lv_keyboard_init(int32_t id)
{
    static lv_indev_drv_t indev_drv;

    lv_indev_drv_init(&indev_drv);
    indev_drv.type = LV_INDEV_TYPE_KEYPAD;
    indev_drv.read_cb = _keyboard_read;
    log_i("urq_lv_keyboard_init: %d\n", id);
    return lv_indev_drv_register(&indev_drv);
}

#endif // #if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64
