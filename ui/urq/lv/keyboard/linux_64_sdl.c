#include "urq/compile.h"
#include "urq/preload.h"
#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64 &&                   \
    URQ_COMPILE_SDL == 1

#include "urq/lv/keyboard.h"
#include <lv_drivers/sdl/sdl_common.h>

/// 初始化键盘
///
/// @param id js 那边的上下文 id，其它平台随便传
/// @return lv_indev_t* 键盘输入设备
lv_indev_t *urq_lv_keyboard_init(int32_t id)
{
    urq_used(id);
    static lv_indev_drv_t indev_drv;

    lv_indev_drv_init(&indev_drv);
    indev_drv.type = LV_INDEV_TYPE_KEYPAD;
    indev_drv.read_cb = sdl_keyboard_read;
    return lv_indev_drv_register(&indev_drv);
}

#else 

#include <stddef.h>
// void __empty_function(void) {
//     return;
// }

#endif // #if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64
