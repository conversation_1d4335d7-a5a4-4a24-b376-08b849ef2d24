#include "urq/compile.h"

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM

#include "lvgl.h"
#include <errno.h>
// #include "urq/log/verbose.h"
// #include "urq/log/debug.h"
#include "urq/log/info.h"
#include "urq/preload.h"
#include "urq/web.h"
#include <stddef.h>
#include <stdint.h>
#include <stdio.h>
#include <string.h>

static inline int _check_path(const char *const path)
{
    if (path[0] == '/') {
        errno = EINVAL;
        return -1;
    }
    return 0;
}

typedef struct {
    uint32_t size;
    uint32_t cursor;
    uint8_t data[0];
} _file_t;

// open
static void *_open(lv_fs_drv_t *drv, const char *path, lv_fs_mode_t mode)
{
    int32_t id = (int32_t)(intptr_t)drv->user_data;
    _file_t *file;
    log_v("open %s\n", path);
    if (_check_path(path) == -1) {
        return NULL;
    }
    if (urq_web_fs_open(id, path, (void **)&file)) {
        return NULL;
    }
    log_d(
        "open %s, file: %p, size: %d, cursor: %d\n", path, file, file->size,
        file->cursor);
    return file;
}

// read
static lv_fs_res_t _read(
    lv_fs_drv_t *drv, void *file, void *buf, uint32_t len, uint32_t *read_len)
{
    _file_t *f = (_file_t *)file;
    if (f->cursor >= f->size) {
        *read_len = 0;
        return LV_FS_RES_OK;
    }
    *read_len = f->size - f->cursor;
    if (*read_len > len) {
        *read_len = len;
    }
    memcpy(buf, f->data + f->cursor, *read_len);
    f->cursor += *read_len;
    return LV_FS_RES_OK;
}

// seek
static lv_fs_res_t _seek(
    lv_fs_drv_t *drv, void *file_p, uint32_t pos, lv_fs_whence_t whence)
{
    _file_t *f = (_file_t *)file_p;
    if (whence == LV_FS_SEEK_SET) {
        f->cursor = pos;
    } else if (whence == LV_FS_SEEK_CUR) {
        f->cursor += pos;
    } else if (whence == LV_FS_SEEK_END) {
        f->cursor = f->size - pos;
    }
    return LV_FS_RES_OK;
}

// close
static lv_fs_res_t _close(lv_fs_drv_t *drv, void *file)
{
    log_d("close, file: %p\n", file);
    urq_free_if_not_null(file);
    return LV_FS_RES_OK;
}

// tell
static lv_fs_res_t _tell(lv_fs_drv_t *drv, void *file, uint32_t *pos_p)
{
    _file_t *f = (_file_t *)file;
    *pos_p = f->cursor;
    return LV_FS_RES_OK;
}

// write
static lv_fs_res_t _write(
    lv_fs_drv_t *drv, void *file, const void *buf, uint32_t len,
    uint32_t *write_len)
{
    log_e("write %d\n", len);
    return LV_FS_RES_HW_ERR;
}

/// 创建一个显示设备
///
/// @param id     js 那边的上下文 id
/// @return 0 成功，其他失败
void urq_lv_fs_init(int32_t id)
{
    // 1. 定义文件系统驱动
    static lv_fs_drv_t fs_drv;
    lv_fs_drv_init(&fs_drv);

    // 2. 设置驱动函数
    fs_drv.letter = 'S'; // 驱动器标识符
    fs_drv.open_cb = _open;
    fs_drv.close_cb = _close;
    fs_drv.read_cb = _read;
    fs_drv.seek_cb = _seek;
    fs_drv.tell_cb = _tell;
    fs_drv.write_cb = _write;
    fs_drv.user_data = (void *)(intptr_t)id;

    lv_fs_drv_register(&fs_drv);
}
#else

#include <stddef.h>
// void __empty_function(void) {
//     return;
// }

#endif // #if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
