#include "urq/compile.h"
#include "urq/preload.h"

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64 ||                   \
    URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_ARM_V7HF

#include "lvgl.h"
#include "urq/fs/fs.h"
#include "urq/log/debug.h"
#include "urq/lv/fs.h"
#include "urq/path.h"
#include <stdio.h>

// open
static void *_open(lv_fs_drv_t *drv, const char *path, lv_fs_mode_t mode)
{
    urq_used(drv);

    char p[URQ_PATH_MAX];
    snprintf(p, URQ_PATH_MAX, "%s/%s", URQ_WORKING_FOLDER, path);
    FILE *fp = fopen(p, mode == LV_FS_MODE_WR ? "wb" : "rb");
    log_d("open file: %s, absolute path: %s\n", path, p);
    return (void *)fp;
}

// read
static lv_fs_res_t _read(
    lv_fs_drv_t *drv, void *file, void *buf, uint32_t len, uint32_t *read_len)
{
    urq_used(drv);

    *read_len = (uint32_t)fread(buf, 1, len, (FILE *)file);
    return LV_FS_RES_OK;
}

// seek
static lv_fs_res_t _seek(
    lv_fs_drv_t *drv, void *file_p, uint32_t pos, lv_fs_whence_t whence)
{
    urq_used(drv);

    // log_d("fseek, file: %p\n", file_p);
    fseek((FILE *)file_p, (long)pos, (int)whence);
    return LV_FS_RES_OK;
}

// close
static lv_fs_res_t _close(lv_fs_drv_t *drv, void *file)
{
    urq_used(drv);

    fclose((FILE *)file);
    log_d("close file: %p\n", file);
    return LV_FS_RES_OK;
}

// tell
static lv_fs_res_t _tell(lv_fs_drv_t *drv, void *file, uint32_t *pos_p)
{
    urq_used(drv);

    *pos_p = (uint32_t)ftell((FILE *)file);
    return LV_FS_RES_OK;
}

// write
static lv_fs_res_t _write(
    lv_fs_drv_t *drv, void *file, const void *buf, uint32_t len,
    uint32_t *write_len)
{
    urq_used(drv);

    *write_len = (uint32_t)fwrite(buf, 1, len, (FILE *)file);
    return LV_FS_RES_OK;
}

/// 创建一个显示设备
///
/// @param id     js 那边的上下文 id
/// @return 0 成功，其他失败
void urq_lv_fs_init(int32_t id)
{
    // 1. 定义文件系统驱动
    static lv_fs_drv_t fs_drv;
    lv_fs_drv_init(&fs_drv);

    // 2. 设置驱动函数
    fs_drv.letter = 'S'; // 驱动器标识符
    fs_drv.open_cb = _open;
    fs_drv.close_cb = _close;
    fs_drv.read_cb = _read;
    fs_drv.seek_cb = _seek;
    fs_drv.tell_cb = _tell;
    fs_drv.write_cb = _write;

    if (id == 0) {
        log_d("id is 0\n");
    }

    lv_fs_drv_register(&fs_drv);
}

#endif // #if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64
