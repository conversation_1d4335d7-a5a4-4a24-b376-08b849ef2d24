#pragma once

#include "lvgl.h"

#ifndef URQ__LV__LV_EVENT_CODE_H
#define URQ__LV__LV_EVENT_CODE_H

/// @brief 将 lv_event_code_t 转换为字符串
/// @param value lv_event_code_t 值
/// @return 字符串
__attribute__((__warn_unused_result__())) static inline const char *
urq_lv_event_code(lv_event_code_t value)
{
    static const char *const URQ_LV_EVENT_CODE_NAMES[] = {
        "ALL",
        /* Input device events*/
        "PRESSED",
        "PRESSING",
        "PRESS_LOST",
        "SHORT_CLICKED",
        "LONG_PRESSED",
        "LONG_PRESSED_REPEAT",
        "CLICKED",
        "RELEASED",
        "SCROLL_BEGIN",
        "SCROLL_END",
        "SCROLL",
        "GESTURE",
        "KEY",
        "FOCUSED",
        "DEFOCUSED",
        "LEAVE",
        "HIT_TEST",
        /* Drawing events*/
        "COVER_CHECK",
        "REFR_EXT_DRAW_SIZE",
        "DRAW_MAIN_BEGIN",
        "DRAW_MAIN",
        "DRAW_MAIN_END",
        "DRAW_POST_BEGIN",
        "DRAW_POST",
        "DRAW_POST_END",
        "DRAW_PART_BEGIN",
        "DRAW_PART_END",
        /* Special events*/
        "VALUE_CHANGED",
        "INSERT",
        "REFRESH",
        "READY",
        "CANCEL",
        /* Other events*/
        "DELETE",
        "CHILD_CHANGED",
        "CHILD_CREATED",
        "CHILD_DELETED",
        "SCREEN_UNLOAD_START",
        "SCREEN_LOAD_START",
        "SCREEN_LOADED",
        "SCREEN_UNLOADED",
        "SIZE_CHANGED",
        "STYLE_CHANGED",
        "LAYOUT_CHANGED",
        "GET_SELF_SIZE",

        /* Other events*/
        "_LAST",
        "PREPROCESS",
        "Invalid event code",
    };
    if (value == LV_EVENT_PREPROCESS) {
        return URQ_LV_EVENT_CODE_NAMES[46];
    } else if (value <= _LV_EVENT_LAST) {
        return URQ_LV_EVENT_CODE_NAMES[value];
    } else {
        return URQ_LV_EVENT_CODE_NAMES[47];
    }
}

#endif // URQ__LV__LV_EVENT_CODE_H
