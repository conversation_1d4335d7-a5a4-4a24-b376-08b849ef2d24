#include "urq/compile.h"
#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM

#include "urq/lv/display.h"
#include "urq/web.h"
#include <emscripten/em_asm.h>
#include <lvgl/lvgl.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>

/// RGBA 颜色
typedef struct {
    uint8_t r;
    uint8_t g;
    uint8_t b;
    uint8_t a;
} _rgba_t;

/// ARGB 颜色
typedef struct {
    uint8_t b;
    uint8_t g;
    uint8_t r;
    uint8_t a;
} _argb_t;

/// 显示上下文
typedef struct {
    int32_t id;
} _display_data_t;

/// 刷新回调函数
///
/// @param disp 显示设备
/// @param area 刷新区域
/// @param px_map 像素映射
static void _flush_callback(
    lv_disp_drv_t *disp_drv, const lv_area_t *area, lv_color_t *color_p)
{
    _display_data_t *data = disp_drv->user_data;
    urq_web_display_flush(
        data->id, area->x1, area->y1, area->x2, area->y2, (uint8_t *)color_p);
    lv_disp_flush_ready(disp_drv);
}

/// 创建一个显示设备
///
/// @param id     js 那边的上下文 id
/// @param width  显示设备的宽度
/// @param height 显示设备的高度
int urq_lv_display_init(int32_t id, lv_coord_t width, lv_coord_t height)
{
    urq_web_create_display(id, width, height);

    lv_color_t *buf = urq_malloc(width * height * sizeof(lv_color_t));
    if (buf == NULL) {
        printf("malloc buf failed\n");
        return 1;
    }

    lv_disp_draw_buf_t *disp_buf = urq_malloc(sizeof(lv_disp_draw_buf_t));
    if (disp_buf == NULL) {
        free(buf);
        printf("malloc disp_buf failed\n");
        return 1;
    }
    lv_disp_drv_t *disp_drv = urq_malloc(sizeof(lv_disp_drv_t));
    if (disp_drv == NULL) {
        free(buf);
        free(disp_buf);
        printf("malloc disp_drv failed\n");
        return 1;
    }

    _display_data_t *data = urq_malloc(sizeof(_display_data_t));
    if (data == NULL) {
        free(buf);
        free(disp_buf);
        free(disp_drv);
        printf("malloc data failed\n");
        return 1;
    }
    data->id = id;

    lv_disp_draw_buf_init(disp_buf, buf, NULL, width * height);
    lv_disp_drv_init(disp_drv);

    disp_drv->draw_buf = disp_buf;
    disp_drv->flush_cb = _flush_callback;
    disp_drv->hor_res = width;
    disp_drv->ver_res = height;
    disp_drv->user_data = data;
    disp_drv->full_refresh = false;

    lv_disp_drv_register(disp_drv);
    return 0;
}

void urq_lv_display_deinit(void)
{
    lv_disp_t *disp = lv_disp_get_default();
    lv_disp_drv_t *disp_drv = disp->driver;

    if (disp != NULL) {
        // 2. 关闭共享内存
        if (disp->driver->user_data != NULL) {
            free(disp->driver->user_data);
            disp->driver->user_data = NULL;
        }

        // 3. 释放显示缓冲区
        if (disp->driver->draw_buf != NULL) {
            if(disp->driver->draw_buf->buf1 != NULL)
                free(disp->driver->draw_buf->buf1);
            free(disp->driver->draw_buf);
            disp->driver->draw_buf = NULL;
        }

        free(disp_drv);
    }

    lv_disp_remove(disp);
}

#else
#include <stddef.h>
// void __empty_function(void) {
//     return;
// }
#endif // #if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
