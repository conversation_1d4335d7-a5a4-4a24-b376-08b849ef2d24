#include "urq/compile.h"
#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_ARM_V7HF

#include "lvgl.h"
#include "urq/preload.h"
#include <errno.h>
#include <fcntl.h>
#include <linux/fb.h>
#include <pthread.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <time.h>
#include <unistd.h>

#define FBDEV_PATH "/dev/fb0"
#ifndef DIV_ROUND_UP
#define DIV_ROUND_UP(n, d) (((n) + (d)-1) / (d))
#endif

static int fbfd;
static char *fbp = 0;
static long int screensize = 0;
static struct fb_var_screeninfo vinfo;
static struct fb_fix_screeninfo finfo;

static int _fbdev_init(void)
{
    fbfd = open(FBDEV_PATH, O_RDWR);
    if (fbfd == -1) {
        perror("Error: cannot open framebuffer device");
        return -1;
    }
    printf("The framebuffer device was opened successfully\n");

    if (ioctl(fbfd, FBIOBLANK, FB_BLANK_UNBLANK) != 0) {
        perror("ioctl(FBIOBLANK)");
        return -2;
    }

    if (ioctl(fbfd, FBIOGET_FSCREENINFO, &finfo) == -1) {
        perror("Error reading fixed information");
        return -3;
    }

    if (ioctl(fbfd, FBIOGET_VSCREENINFO, &vinfo) == -1) {
        perror("Error reading variable information");
        return -4;
    }

    printf("%dx%d, %dbpp\n", vinfo.xres, vinfo.yres, vinfo.bits_per_pixel);

    screensize = finfo.smem_len;

    fbp = (char *)mmap(
        0, screensize, PROT_READ | PROT_WRITE, MAP_SHARED, fbfd, 0);
    if ((intptr_t)fbp == -1) {
        perror("Error: failed to map framebuffer device to memory");
        return -5;
    }

    printf("The framebuffer device was mapped to memory successfully\n");
    return 0;
}

/**
 * Flush a buffer to the marked area
 * @param drv pointer to driver where this function belongs
 * @param area an area where to copy `color_p`
 * @param color_p an array of pixels to copy to the `area` part of the screen
 */
static void _fbdev_flush(
    lv_disp_drv_t *drv,    //
    const lv_area_t *area, //
    lv_color_t *color_p    //
)
{
    if (fbp == NULL || area->x2 < 0 || area->y2 < 0 ||
        area->x1 > (int32_t)vinfo.xres - 1 ||
        area->y1 > (int32_t)vinfo.yres - 1) {
        lv_disp_flush_ready(drv);
        return;
    }

    /*Truncate the area to the screen*/
    int32_t act_x1 = area->x1 < 0 ? 0 : area->x1;
    int32_t act_y1 = area->y1 < 0 ? 0 : area->y1;
    int32_t act_x2 =
        area->x2 > (int32_t)vinfo.xres - 1 ? (int32_t)vinfo.xres - 1 : area->x2;
    int32_t act_y2 =
        area->y2 > (int32_t)vinfo.yres - 1 ? (int32_t)vinfo.yres - 1 : area->y2;

    lv_coord_t w = (act_x2 - act_x1 + 1);
    long int location = 0;
    long int byte_location = 0;
    unsigned char bit_location = 0;

    // TODO
    // 等待垂直同步信号，减少画面撕裂
    //if (ioctl(fbfd, FBIO_WAITFORVSYNC, 0) != 0) {
    //    perror("Error waiting for vsync");
    //}

    /*32 or 24 bit per pixel*/
    if (vinfo.bits_per_pixel == 32 || vinfo.bits_per_pixel == 24) {
        uint32_t *fbp32 = (uint32_t *)fbp;
        int32_t y;
        for (y = act_y1; y <= act_y2; y++) {
            location = (act_x1 + vinfo.xoffset) +
                       (y + vinfo.yoffset) * finfo.line_length / 4;
            memcpy(
                &fbp32[location], (uint32_t *)color_p,
                (act_x2 - act_x1 + 1) * 4);
            color_p += w;
        }
    }
    /*16 bit per pixel*/
    else if (vinfo.bits_per_pixel == 16) {
        uint16_t *fbp16 = (uint16_t *)fbp;
        int32_t y;
        for (y = act_y1; y <= act_y2; y++) {
            location = (act_x1 + vinfo.xoffset) +
                       (y + vinfo.yoffset) * finfo.line_length / 2;
            memcpy(
                &fbp16[location], (uint32_t *)color_p,
                (act_x2 - act_x1 + 1) * 2);
            color_p += w;
        }
    }
    /*8 bit per pixel*/
    else if (vinfo.bits_per_pixel == 8) {
        uint8_t *fbp8 = (uint8_t *)fbp;
        int32_t y;
        for (y = act_y1; y <= act_y2; y++) {
            location = (act_x1 + vinfo.xoffset) +
                       (y + vinfo.yoffset) * finfo.line_length;
            memcpy(&fbp8[location], (uint32_t *)color_p, (act_x2 - act_x1 + 1));
            color_p += w;
        }
    }
    /*1 bit per pixel*/
    else if (vinfo.bits_per_pixel == 1) {
        uint8_t *fbp8 = (uint8_t *)fbp;
        int32_t x;
        int32_t y;
        for (y = act_y1; y <= act_y2; y++) {
            for (x = act_x1; x <= act_x2; x++) {
                location =
                    (x + vinfo.xoffset) + (y + vinfo.yoffset) * vinfo.xres;
                byte_location =
                    location / 8; /* find the byte we need to change */
                bit_location = location % 8; /* inside the byte found, find the
                                                bit we need to change */
                fbp8[byte_location] &= ~(((uint8_t)(1)) << bit_location);
                fbp8[byte_location] |= ((uint8_t)(color_p->full))
                                       << bit_location;
                color_p++;
            }

            color_p += area->x2 - act_x2;
        }
    } else {
        /*Not supported bit per pixel*/
    }

    // TODO
    // 如果支持页面翻转功能，则使用页面翻转来减少画面撕裂
    // 这需要有两个缓冲区，并通过yoffset切换
    //struct fb_var_screeninfo new_vinfo = vinfo;
    //new_vinfo.yoffset = (vinfo.yoffset == 0) ? vinfo.yres : 0;
    //if (ioctl(fbfd, FBIOPAN_DISPLAY, &new_vinfo) != 0) {
    //    perror("Error panning display");
    //} else {
    //    vinfo = new_vinfo;
    //}

    lv_disp_flush_ready(drv);
}

static void _fbdev_get_sizes(uint32_t *width, uint32_t *height, uint32_t *dpi)
{
    if (width)
        *width = vinfo.xres;

    if (height)
        *height = vinfo.yres;

    if (dpi && vinfo.height)
        *dpi = DIV_ROUND_UP(vinfo.xres * 254, vinfo.width * 10);
}

static void _fbdev_set_offset(uint32_t xoffset, uint32_t yoffset)
{
    vinfo.xoffset = xoffset;
    vinfo.yoffset = yoffset;
}

/// 如果不存在 disp_init 进程，则启动 disp_init
/// @param arg
/// @return
static void _start_disp_init()
{
    setenv("LD_LIBRARY_PATH", "/config/lib:/config/wifi:/customer/lib", 1);
    system("if [ -f /customer/disp_init ]; then exit 0; fi");
    system("pid=$(ps aux | grep disp_init | grep -v grep | awk '{print $1}');"
           "if [ -n \"$pid\" ]; then exit 0; fi;"
           "echo \"start disp_init\";"
           "/customer/disp_init &");
}

/// @brief 停止 disp_init
/// @param arg
/// @return
static void *_stop_disp_init(void *arg)
{
    printf("stop disp_init\n");
    sleep(1);
    system("killall disp_init");
    return NULL;
}

/// 创建一个显示设备
///
/// @param id     js 那边的上下文 id
/// @param width  显示设备的宽度
/// @param height 显示设备的高度
/// @return 0 成功，其他失败
int urq_lv_display_init(
    int32_t id,     //
    uint32_t width, //
    uint32_t height //
    )               //
{
    urq_used(id);
    urq_used(width);
    urq_used(height);

    _start_disp_init();

    if (_fbdev_init()) {
        fprintf(stderr, "urq_lvgl__display_arm_v7hf_init failed\n");
        return -1;
    }
    printf("fbdev init success\n");

    _fbdev_get_sizes(&width, &height, NULL);
    printf("width: %u, height: %u\n", width, height);


    // 分配双缓冲或单缓冲
    lv_color_t *buf1 = (lv_color_t *)malloc(width * height * sizeof(lv_color_t));
    if (buf1 == NULL) {
        printf("malloc buf1 failed\n");
        errno = ENOMEM;
        return -1;
    }

    lv_color_t *buf2 = NULL;
    // 如果支持页面翻转，则分配第二个缓冲区
    buf2 = (lv_color_t *)malloc(width * height * sizeof(lv_color_t));
    if (buf2 == NULL) {
        free(buf1);
        printf("malloc buf2 failed\n");
        errno = ENOMEM;
        return -1;
    }
    printf("Double buffering enabled\n");

    lv_disp_draw_buf_t *disp_buf =
        (lv_disp_draw_buf_t *)malloc(sizeof(lv_disp_draw_buf_t));
    if (disp_buf == NULL) {
        free(buf1);
        free(buf2);
        printf("malloc disp_buf failed\n");
        errno = ENOMEM;
        return -1;
    }
    lv_disp_drv_t *disp_drv = (lv_disp_drv_t *)malloc(sizeof(lv_disp_drv_t));
    if (disp_drv == NULL) {
        free(buf1);
        free(buf2);
        free(disp_buf);
        printf("malloc disp_drv failed\n");
        errno = ENOMEM;
        return -1;
    }

    // 初始化显示缓冲区，使用双缓冲或单缓冲
    lv_disp_draw_buf_init(disp_buf, buf1, buf2, width * height);
    lv_disp_drv_init(disp_drv);

    disp_drv->draw_buf = disp_buf;
    disp_drv->flush_cb = _fbdev_flush;
    disp_drv->hor_res = width;
    disp_drv->ver_res = height;
    // TODO 在用户数据中存储是否启用页面翻转的标志
    // disp_drv->user_data = (void *)(intptr_t)has_page_flip;
    disp_drv->full_refresh = false;
    lv_disp_drv_register(disp_drv);

    pthread_t thread;
    pthread_create(&thread, NULL, _stop_disp_init, NULL);
    pthread_detach(thread);

    return 0;
}
#else
#include <stddef.h>

// void __empty_function(void) {
//     return;
// }
#endif // #if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_ARM_V7HF
