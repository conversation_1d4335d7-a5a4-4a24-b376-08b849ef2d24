#include "urq/compile.h"
#include "urq/preload.h"

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64 &&                   \
    URQ_COMPILE_SDL == 0

#include "lvgl/src/hal/lv_hal_disp.h"
#include "lvgl/src/misc/lv_area.h"
#include "lvgl/src/misc/lv_color.h"
#include "urq/lv/display.h"
#include <errno.h>
#include <fcntl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/mman.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <sys/un.h>
#include <unistd.h>

#define WINDOW_WIDTH  800
#define WINDOW_HEIGHT 600
#define FRAME_SIZE    800 * 600 * 4

#define SHM_PATH "/urq.framebuffer"

// 指向共享内存的指针
static void *shm_ptr = NULL;

/// 打开共享内存
static void *_open_share_memory(void)
{
    int fd;
    // 判断共享内存是否存在

    if (access("/dev/shm" SHM_PATH, F_OK) == 0) {
        fd = shm_open(SHM_PATH, O_RDWR, 0600);
        if (fd < 0) {
            printf("打开共享内存失败: %s\n", strerror(errno));
            return NULL;
        }
    } else {
        // 创建共享内存
        fd = shm_open(SHM_PATH, O_CREAT | O_RDWR | O_EXCL, 0777);
        if (fd < 0) {
            printf("创建共享内存失败: %s\n", strerror(errno));
            return NULL;
        }
        ftruncate(fd, FRAME_SIZE);
    }

    // 映射内存
    void *ptr =
        mmap(NULL, FRAME_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
    if (ptr == MAP_FAILED) {
        return NULL;
    }

    // 关闭文件描述符，内存映射仍然有效
    close(fd);

    return ptr;
}

static void _flush_cb(
    struct _lv_disp_drv_t *disp_drv, const lv_area_t *area, lv_color_t *color_p)
{
    lv_color_t *framebuffer = (lv_color_t *)disp_drv->user_data;
    
    // 如果未设置共享内存帧缓冲，则直接返回
    if (framebuffer == NULL) {
        lv_disp_flush_ready(disp_drv);
        return;
    }

    // 计算需要刷新的区域大小
    int32_t width = area->x2 - area->x1 + 1;
    int32_t height = area->y2 - area->y1 + 1;
    
    // 逐行复制到帧缓冲
    for (int32_t y = 0; y < height; y++) {
        const int32_t dest_y = y + area->y1;
        // 计算目标行的起始位置
        lv_color_t *dest_line = framebuffer + dest_y * WINDOW_WIDTH + area->x1;
        // 计算源行的起始位置
        lv_color_t *src_line = color_p + y * width;
        // 复制一整行
        memcpy(dest_line, src_line, (size_t)((unsigned int)width * sizeof(lv_color_t)));
    }

    lv_disp_flush_ready(disp_drv);
}

/// 创建一个显示设备
///
/// @param id     js 那边的上下文 id
/// @param width  显示设备的宽度
/// @param height 显示设备的高度
/// @return 0 成功，其他失败
int urq_lv_display_init(
    int32_t id,       //
    lv_coord_t width, //
    lv_coord_t height //
)
{
    urq_used(id);
    urq_used(width);
    urq_used(height);

    // 分配两个缓冲区用于双缓冲
    static lv_color_t buf1[WINDOW_WIDTH * WINDOW_HEIGHT];
    static lv_color_t buf2[WINDOW_WIDTH * WINDOW_HEIGHT];
    static lv_disp_draw_buf_t disp_buf;
    static lv_disp_drv_t driver;

    // 初始化显示缓冲区 - 使用双缓冲
    lv_disp_draw_buf_init(&disp_buf, buf1, buf2, WINDOW_WIDTH * WINDOW_HEIGHT);
    
    // 初始化显示驱动
    lv_disp_drv_init(&driver);
    driver.draw_buf = &disp_buf;
    driver.flush_cb = _flush_cb;
    driver.hor_res = WINDOW_WIDTH;
    driver.ver_res = WINDOW_HEIGHT;
    
    // 关闭全屏刷新，使用增量刷新提高性能
    driver.full_refresh = false;
    
    // 打开共享内存
    shm_ptr = _open_share_memory();
    if (shm_ptr != NULL) {
        driver.user_data = shm_ptr;
    }
    
    // 注册显示驱动
    lv_disp_drv_register(&driver);

    return 0;
}

void urq_lv_display_deinit(void)
{
    lv_disp_t *disp = lv_disp_get_default();

    // 2. 关闭共享内存
    if (disp->driver->user_data != NULL) {
        munmap(disp->driver->user_data, FRAME_SIZE);
        disp->driver->user_data = NULL;
        shm_ptr = NULL;
        
        // 删除共享内存
        shm_unlink(SHM_PATH);
    }

    // 3. 删除显示设备
    lv_disp_remove(disp);
}

#endif // #if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64
