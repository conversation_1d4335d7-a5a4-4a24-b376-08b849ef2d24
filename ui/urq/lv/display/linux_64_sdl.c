#include "urq/compile.h"
#include "urq/preload.h"

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64 &&                   \
    URQ_COMPILE_SDL == 1

//#include "urq/lv/display.h"
#include <lv_drivers/sdl/sdl.h>
#include <stdlib.h>

/// 创建一个显示设备
///
/// @param id     js 那边的上下文 id
/// @param width  显示设备的宽度
/// @param height 显示设备的高度
/// @return 0 成功，其他失败
int urq_lv_display_init(
    int32_t id,     //
    uint32_t width, //
    uint32_t height //
)
{
    urq_used(id);
    urq_used(width);
    urq_used(height);
    sdl_init();

    static lv_color_t buf[SDL_HOR_RES * SDL_VER_RES];
    static lv_disp_draw_buf_t disp_buf;
    static lv_disp_drv_t driver;

    lv_disp_draw_buf_init(&disp_buf, buf, NULL, SDL_HOR_RES * SDL_VER_RES);
    lv_disp_drv_init(&driver);
    driver.draw_buf = &disp_buf;
    driver.flush_cb = sdl_display_flush;
    driver.hor_res = SDL_HOR_RES;
    driver.ver_res = SDL_VER_RES;
    lv_disp_drv_register(&driver);

    return 0;
}

#else
#include <stddef.h>

// static void __empty_function(void) {
//     return;
// }

#endif // #if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64
