#include "urq/label/widget.h"
#include "lvgl.h"
#include "urq/label/conf.h"
#include "urq/log/verbose.h"
#include "urq/page/page.h"
#include "urq/preload.h"
#include "urq/project/project.h"
#include "urq/req/action.h"
#include "urq/text/ptr_list.h"
#include "urq/text/text.h"
#include "urq/user_data.h"
#include "urq/util/control.h"
#include "urq/util/event.h"
#include "urq/util/font.h"
#include "urq/util/marquee.h"
#include "urq/util/show_lvgl.h"
#include "urq/util/state.h"
#include "urq/widget.h"
#include "urq/widget/property.h"
#include "urq/widget/type.h"

#define Self urq_label_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_LABEL_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_label_widget_config, urq_label_widget_refresh_language,
        urq_label_widget_set_state, urq_label_widget_set_property);

    widget->common_conf = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;

    if (widget->user_data.type != URQ_WIDGET_TYPE_CLONE) {
        urq_user_data_free(&widget->user_data);
        if (widget->common_conf != NULL) {
            if (widget->common_conf->styles != NULL) {
                for (size_t idx = 0; idx < widget->common_conf->styles->size;
                     ++idx) {
                    urq_style_t *style = widget->common_conf->styles->data[idx];
                    if (style != NULL && style->font.marquee != NULL) {
                        urq_marquee_disable(style->font.marquee);
                    }
                }
            }
            urq_widget_common_conf_free_inplace(widget->common_conf);
            urq_free(widget->common_conf);
        }
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (urq_event_filter_exit(class_p, e)) {
        return;
    }
    Self *widget = (Self *)lv_event_get_target(e);
    urq_widget_common_conf_t *common_conf = widget->common_conf;
    urq_page_t *const page = widget->user_data.page;
    static uint32_t count = 0;
    // 检查是否执行点击事件
    if (urq_event_check_exec_click(e, &count, common_conf)) {
        // exec
        printf("可执行\n");
    }
    if (common_conf != NULL && common_conf->has_action) {
        if (urq_page_add_frame_cb3(
                page, urq_req_wirte_set_action, urq_noop3, (void *)page,
                (void *)(intptr_t)common_conf->id, (void *)(intptr_t)e->code)) {
            printf("添加成功\n");
        }
    }
}

lv_obj_t *urq_label_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj;

    obj = lv_obj_class_create_obj(&URQ_LABEL_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

void urq_label_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;

    widget->common_conf = mut_conf->common_conf;
    // lv_obj_align(udata->label, LV_ALIGN_CENTER, 0, 0);
    // 设置内容
    // urq_text_t *text = widget->common_conf->texts->data[0];
    // if(text->content != NULL)
    // // TODO 是否要在设置完文本后 更新对象大小
    // lv_obj_refr_size(widget->label);

    // // 设置字体
    // if (text->font != NULL) {
    //     if (text->font->marquee != NULL) {
    //         // 设置跑马灯
    //         text->font->marquee->start_pos = mut_conf->size.w;
    //         text->font->marquee->end_pos = mut_conf->size.h;
    //     }
    //     // 设置字体
    //     urq_set_font(self, text->font);
    //     // 设置对齐方式
    //     lv_obj_align(widget->label, text->font->text_align, 0, 0);
    // }
    urq_widget_config(self, mut_conf, NULL);
}

void urq_label_widget_set_property_callback(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    urq_used(self);
    urq_used(index);
    urq_used(value);
    switch (property_id) {
    // case URQ_PROPERTY_ID_TEXT:
    //     lv_label_set_text(widget->label, (const char *)value);
    //     break;
    default:
        break;
    }
}

void urq_label_widget_set_property(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    urq_label_widget_t *const widget = (urq_label_widget_t *)self;
    urq_widget_set_property(
        self, widget->common_conf, property_id, index, value,
        urq_label_widget_set_property_callback);
}

void urq_label_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    Self *const widget = (Self *)self;
    urq_set_state(self, widget->common_conf, state, platfrom);
}

void urq_label_widget_refresh_language(lv_obj_t *self)
{
    Self *const widget = (Self *)self;
    urq_user_data_t *udata = urq_obj_get_user_data((lv_obj_t *)widget);

    urq_text_ptr_list_t *text = widget->common_conf->texts;
    if (text == NULL || text->size < 1) {
        return;
    }
    urq_show_lvgl_text(udata, self, text->data[0]);
}
