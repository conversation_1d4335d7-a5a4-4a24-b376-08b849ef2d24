#pragma once

#include "urq/user_data.h"

#ifndef URQ__SHOW_LVGL_H
#define URQ__SHOW_LVGL_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 初始化显示
/// @param obj 对象
void urq_init_widget_show(lv_obj_t *obj);

/// @brief 显示图片
/// @param u_data 用户数据
/// @param u_obj 对象
/// @param u_graphic 图形
void urq_show_lvgl_image(void *u_data, void *u_obj, void *u_graphic)
    __attribute__((__nonnull__(1, 2, 3)));

/// @brief 显示文本
/// @param u_data 用户数据
/// @param u_obj 对象
/// @param u_text 文本
void urq_show_lvgl_text(void *u_data, void *u_obj, void *u_text)
    __attribute__((__nonnull__(1, 2, 3)));

/// @brief 显示字体样式
/// @param u_data 用户数据
/// @param u_obj 对象
/// @param u_font 字体
void urq_show_lvgl_font_style(void *u_data, void *u_obj, void *u_font)
    __attribute__((__nonnull__(1, 2, 3)));

#ifdef __cplusplus
}
#endif
#endif // URQ__SHOW_LVGL_H
