#include "position.h"
#include <lvgl/src/misc/lv_area.h>

lv_coord_t urq_get_center_x(lv_coord_t x, lv_coord_t w)
{
    return (lv_coord_t)(w - x) / 2;
}

lv_coord_t urq_get_center_y(lv_coord_t y, lv_coord_t h)
{
    return (lv_coord_t)(h - y) / 2;
}

lv_coord_t urq_get_relative_x(lv_coord_t pos, lv_align_t align)
{
    if (align == LV_ALIGN_TOP_LEFT || align == LV_ALIGN_BOTTOM_LEFT ||
        align == LV_ALIGN_LEFT_MID) {
        // 左边
        return 0;
    } else if (
        align == LV_ALIGN_TOP_RIGHT || align == LV_ALIGN_BOTTOM_RIGHT ||
        align == LV_ALIGN_RIGHT_MID) {
        // 右边
        return pos;
    } else if (
        align == LV_ALIGN_TOP_MID || align == LV_ALIGN_BOTTOM_MID ||
        align == LV_ALIGN_CENTER) {
        // 中间
        return pos / 2;
    }
    return pos / 2;
}

lv_coord_t urq_get_relative_y(lv_coord_t pos, lv_align_t align)
{
    if (align == LV_ALIGN_TOP_LEFT || align == LV_ALIGN_TOP_RIGHT ||
        align == LV_ALIGN_TOP_MID) {
        // 上边
        return 0;
    } else if (
        align == LV_ALIGN_BOTTOM_LEFT || align == LV_ALIGN_BOTTOM_RIGHT ||
        align == LV_ALIGN_BOTTOM_MID) {
        // 下边
        return pos;
    } else if (
        align == LV_ALIGN_LEFT_MID || align == LV_ALIGN_RIGHT_MID ||
        align == LV_ALIGN_CENTER) {
        // 中间
        return pos / 2;
    }
    return pos / 2;
}
