#include "anim.h"

static void urq_anim_text_visible_cb(void *arg, int v)
{
    lv_obj_t *obj = (lv_obj_t *)arg;
    if (v == 0) {
        lv_obj_add_flag((lv_obj_t *)obj, LV_OBJ_FLAG_HIDDEN); // 隐藏文本
    } else {
        lv_obj_clear_flag((lv_obj_t *)obj, LV_OBJ_FLAG_HIDDEN); // 显示文本
    }
}

void urq_set_anim_flashing(lv_obj_t *obj, lv_anim_t *anim, uint8_t flash_time)
{
    // 设置闪烁
    lv_anim_init(anim);
    lv_anim_set_var(anim, obj);
    lv_anim_set_exec_cb(anim, urq_anim_text_visible_cb);
    lv_anim_set_values(anim, 0, 1);     // 0 = 隐藏，1 = 显示
    lv_anim_set_time(anim, flash_time); // 300ms 切换一次
    lv_anim_set_repeat_count(anim, LV_ANIM_REPEAT_INFINITE); // 无限循环
    lv_anim_start(anim);
}
