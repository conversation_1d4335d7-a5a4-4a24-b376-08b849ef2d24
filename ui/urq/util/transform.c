#include "urq/util/transform.h"

int urq_transform_input_to_struct(
    urq_project_t *project, urq_keyboard_data_t *data)
{
    urq_used(project);
    urq_used(data);
    /// 输入的内容组合到结构体中
    // send data to server
    /// 发送结构体到服务端上
    return 0;
}

/// @brief 将结构体转换为输入框内容
/// @param data 结构体
/// @return 0表示成功，-1表示失败
int urq_transform_struct_to_input(
    urq_project_t *project, urq_keyboard_data_t *data)
{
    urq_used(project);
    urq_used(data);
    // 通过page获取当前页面
    // 通过display获取当前显示输入对图元
    // 将结构体值输入到图元上
    return 0;
}
