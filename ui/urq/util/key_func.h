#pragma once
#include "lvgl/lvgl.h"

#ifndef URQ__UTIL__KEY_FUNC_H
#define URQ__UTIL__KEY_FUNC_H

#ifdef __cplusplus
extern "C" {
#endif

/// 键盘事件处理
void urq_keyboard_key_clear(lv_obj_t *obj);

/// 键盘回车事件处理
void urq_keyboard_key_enter(lv_obj_t *obj, void *arg1);

/// 键盘空格事件处理
void urq_keyboard_key_space(lv_obj_t *obj);

/// 键盘切换数字键盘
void urq_keyboard_key_switch_number(lv_obj_t *obj);

/// 键盘切换字母键盘
void urq_keyboard_key_switch_abc(lv_obj_t *obj);

#ifdef __cplusplus
}
#endif

#endif // URQ__UTIL__KEY_FUNC_H
