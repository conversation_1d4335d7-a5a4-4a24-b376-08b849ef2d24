#pragma once

#include "lvgl/lvgl.h"
#include "urq/keyboard/trigger_data.h"
#include "urq/page/page.h"
#include "urq/preload.h"
#include "urq/widget/common.h"
#ifndef URQ__UTIL__EVENT_H
#define URQ__UTIL__EVENT_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 事件过滤
/// @param e 事件
/// @return 是否过滤
bool urq_event_filter_exit(const lv_obj_class_t *class_p, lv_event_t *e)
    __attribute__((__nonnull__(1, 2)));

/// @brief 发送动作
/// @param e 事件
/// @param conn 连接
/// @param page_id 页面ID
/// @param widget_id 组件ID
void urq_event_send_action(
    lv_event_t *e, urq_page_t *page, urq_widget_id_t widget_id)
    __attribute__((__nonnull__(1, 2)));

/// @brief 检查是否执行点击时间
/// @param e 事件
/// @param common_conf 通用配置
static inline bool urq_event_check_exec_click(
    lv_event_t *e, uint32_t *time, urq_widget_common_conf_t *common_conf)
{
    if (e->code == LV_EVENT_PRESSED && common_conf->min_press_time > 0) {
        if ((lv_tick_get() - *time) > common_conf->min_press_interval) {
            // 最小按压间隔
            *time = lv_tick_get();
            // printf("[urq_event_check_exec_click]: 最小按压间隔\n");
        } else {
            *time = 0;
            // printf("[urq_event_check_exec_click]: 最小按压间隔 太快 \n");
        }
    } else if (
        e->code == LV_EVENT_RELEASED && common_conf->min_press_time > 0) {
        if ((lv_tick_get() - *time) >= common_conf->min_press_time &&
            *time > 0) {
            // printf("[urq_event_check_exec_click]: release 满足\n");
            return true;
        } else {
            // printf(
            //     "[urq_event_check_exec_click]: release 不满足, %d, %d\n",
            //     (lv_tick_get() - *time), common_conf->min_press_time);
        }
    } else if (
        e->code == LV_EVENT_CLICKED && common_conf->min_press_time == 0) {
        // printf("[urq_event_check_exec_click]: 点击事件执行\n");
        return true;
    }

    return false;
}

/// @param e 事件
/// @param project 项目
/// @param keyboard_page 键盘页面
void urq_event_click_input(
    lv_obj_t *project, urq_page_id_t keyboard_page,
    urq_keyboard_trigger_data_t *trigger_data)
    __attribute__((__nonnull__(1, 3)));

/// @brief 键盘输入事件
/// @param e 事件
void urq_event_keyboard_input(lv_event_t *e) __attribute__((__nonnull__(1)));

#ifdef __cplusplus
}
#endif
#endif // URQ__UTIL__EVENT_H
