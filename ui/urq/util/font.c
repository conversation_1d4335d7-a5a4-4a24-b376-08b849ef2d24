#include "font.h"
#include "marquee.h"
#include "urq/marquee.h"
#include "urq/util/anim.h"
#include "urq/util/color.h"
#include "urq/util/position.h"
#include "urq/util/show_lvgl.h"

void urq_set_font(lv_obj_t *obj, urq_font_t *font)
{
    urq_user_data_t *ud = urq_obj_get_user_data(obj);
    urq_project_t *project = ud->project;
    urq_theme_color_t *theme_color = project->env.theme_color;
    urq_show_lvgl_font_style(ud, obj, font);

    // 设置文本边距
    if (font->text_padding != NULL) {
        if (font->text_padding->left != 0) {
            lv_obj_set_style_pad_left(
                obj, font->text_padding->left, LV_PART_MAIN);
        }
        if (font->text_padding->top != 0) {
            lv_obj_set_style_pad_top(
                obj, font->text_padding->top, LV_PART_MAIN);
        }
        if (font->text_padding->right != 0) {
            lv_obj_set_style_pad_right(
                obj, font->text_padding->right, LV_PART_MAIN);
        }
        if (font->text_padding->bottom != 0) {
            lv_obj_set_style_pad_bottom(
                obj, font->text_padding->bottom, LV_PART_MAIN);
        }
    }

    // 设置字体颜色
    lv_obj_set_style_text_color(
        obj, urq_get_color(theme_color, &font->color), LV_PART_MAIN);

    if (font->blink_interval > 0) {
        // 设置闪烁
        static lv_anim_t anim;
        urq_set_anim_flashing(obj, &anim, font->blink_interval);
        // lv_anim_init(&anim);
        // lv_anim_set_var(&anim, obj);
        // lv_anim_set_exec_cb(&anim, urq_anim_text_visible_cb);
        // lv_anim_set_values(&anim, 0, 1);               // 0 = 隐藏，1 = 显示
        // lv_anim_set_time(&anim, font->blink_interval); // 300ms 切换一次
        // lv_anim_set_repeat_count(&anim, LV_ANIM_REPEAT_INFINITE); // 无限循环
        // lv_anim_start(&anim);
    }

    // 跑马灯动画
    if (font->marquee != NULL) {
        if (font->marquee->enable) {
            // 获取相对位置
            lv_obj_t *label = lv_obj_get_child(obj, 0);
            lv_coord_t label_w = lv_obj_get_width(label);
            lv_coord_t label_h = lv_obj_get_height(label);

            lv_coord_t lw = font->marquee->start_pos;
            lv_coord_t lh = font->marquee->end_pos;
            lv_coord_t x = urq_get_relative_x(lw, font->text_align);
            lv_coord_t y = urq_get_relative_y(lh, font->text_align);
            // 设置跑马灯位置
            if (font->marquee->scroll_direction ==
                URQ_SCROLL_DIRECTION_RIGHT_TO_LEFT) {
                // 水平滚动
                font->marquee->start_pos = (lv_coord_t)(lw - x - label_w);
                font->marquee->end_pos = (lv_coord_t)(-x + label_w);
            } else if (
                font->marquee->scroll_direction ==
                URQ_SCROLL_DIRECTION_LEFT_TO_RIGHT) {
                // 水平滚动
                font->marquee->start_pos = (lv_coord_t)(-x - label_w);
                font->marquee->end_pos = (lv_coord_t)(lw - x + label_w);
            } else if (
                font->marquee->scroll_direction ==
                URQ_SCROLL_DIRECTION_TOP_TO_BOTTOM) {
                // 垂直滚动
                font->marquee->start_pos = (lv_coord_t)(-y - label_h);
                font->marquee->end_pos = (lv_coord_t)(lh - y + label_h);
            } else if (
                font->marquee->scroll_direction ==
                URQ_SCROLL_DIRECTION_BOTTOM_TO_TOP) {
                // 垂直滚动
                font->marquee->start_pos = (lv_coord_t)(lh - y - label_h);
                font->marquee->end_pos = (lv_coord_t)(-y + label_h);
            }
            printf(
                "x:%d, y: %d|start_pos: %d, end_pos: %d\n", x, y,
                font->marquee->start_pos, font->marquee->end_pos);
            // 启用跑马灯
            urq_marquee_enable(font->marquee, obj);
        }
    }
}
