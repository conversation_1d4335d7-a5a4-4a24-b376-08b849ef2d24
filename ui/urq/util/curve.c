#include "urq/util/curve.h"

void urq_curve_set_ticks(lv_obj_t *obj, urq_curve_scale_t *scale, uint8_t axis)
{
    lv_chart_set_axis_tick(
        obj, axis, scale->main_tick_len, scale->sub_tick_len,
        scale->main_tick_count, scale->sub_tick_count,
        scale->show_type != URQ_CURVE_SCALE_SHOW_TYPE_NONE,
        scale->draw_tick_len);
    if (scale->min_value != -1 && scale->max_value != -1) {
        lv_chart_set_range(obj, axis, scale->min_value, scale->max_value);
    }
}
