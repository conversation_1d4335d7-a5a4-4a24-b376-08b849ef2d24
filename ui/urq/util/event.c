#include "urq/util/event.h"
#include "urq/keyboard/conf.h"
#include "urq/keyboard/widget.h"
#include "urq/page/group.h"
#include "urq/util/color.h"
#include "urq/project/project.h"
#include "urq/util/key_func.h"
#include "urq/conf/map/lvgl.h"
#include <string.h>

bool urq_event_filter_exit(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (lv_obj_event_base(class_p, e) != LV_RES_OK) {
        return true;
    }
    if (e->code >= LV_EVENT_HIT_TEST) {
        return true;
    }
    return false;
}

void urq_event_send_action(
    lv_event_t *e, urq_page_t *page, urq_widget_id_t widget_id)
{
    urq_used(e);
    urq_used(page);
    urq_used(widget_id);
    //urq_page_add_frame_cb3(page, urq_req_wirte_set_action, urq_noop3, (void*)page, (void *)(intptr_t)widget_id, (void *)(intptr_t)e->code);
   // if (e->code == LV_EVENT_PRESSED) {
   //     code = URQ_EVENT_CODE_WIDGET_PRESS;
   // } else if (e->code == LV_EVENT_RELEASED) {
   //     code = URQ_EVENT_CODE_WIDGET_RELEASE;
   // } else if (e->code == LV_EVENT_CLICKED) {
   //     code = URQ_EVENT_CODE_WIDGET_CLICK;
   // }

   // if (code != URQ_EVENT_CODE_UNSPECIFIED) {
   //     // send
   //     log_i("[urq_event_send_action] send action: %d\n", code);
   //     urq_used(page);
   //     urq_used(widget_id);
   //     urq_used(code);
   //     // urq_page_add_frame_cb3(page, urq_req_wirte_set_action, urq_noop3, (void*)page, (void *)(intptr_t)widget_id, (void *)(intptr_t)code);
   //     // urq_req_wirte_set_action(&page->project->conn, page->id, widget_id, code);
   // }
}

void urq_event_click_input(
    lv_obj_t *project, urq_page_id_t keyboard_page,
    urq_keyboard_trigger_data_t *trigger_data)
{
    urq_project_show_page_keyboard(project, keyboard_page, trigger_data);
}

void urq_event_keyboard_input(lv_event_t *e)
{
    lv_obj_t *const obj = lv_event_get_user_data(e);
    urq_keyboard_widget_t *const widget = (urq_keyboard_widget_t *)obj;
    urq_project_t *const project = widget->user_data.project;
    urq_page_t *const page = widget->user_data.page;
    urq_page_group_t *const group =
        (urq_page_group_t *)project->keyboard_showing;
    urq_theme_color_t *theme_color = project->env.theme_color;
    if (group == NULL) {
        return;
    }
    urq_keyboard_trigger_data_t *const trigger_data = group->trigger_data;

    if (trigger_data == NULL) {
        return;
    }
    if (!trigger_data->is_init) {
        trigger_data->is_init = true;
        urq_keyboard_info_t *info = urq_keyboard_map_get(
            project->display->keyboard_map, (uint32_t)page->id);
        if (info == NULL) {
            return;
        }

        // 根据图元ID找到临时输入框
        lv_obj_t *const input = urq_lvgl_map_get(
            page->widget_map, (uint32_t)info->current_value_widget_id);
        if (input == NULL) {
            return;
        }
        lv_obj_t *const input_child = lv_obj_get_child(input, 0);
        if (input_child == NULL) {
            return;
        }
        lv_keyboard_set_textarea(widget->keyboard, input_child);
        const char *tmp = lv_textarea_get_text(trigger_data->input);
        if (tmp != NULL) {
            lv_textarea_set_text(input_child, tmp);
        }

        // 数值大小显示框
        lv_obj_t *const min = urq_lvgl_map_get(
            page->widget_map, (uint32_t)info->min_value_widget_id);

        if (min != NULL) {
            lv_obj_t *const min_child = lv_obj_get_child(min, 0);
            if (min_child != NULL) {
                char _tmp[10];
                urq_data_all_type_to_string(
                    trigger_data->min_value,
                    trigger_data->min_value->data_format, _tmp);
                lv_textarea_set_text(min_child, _tmp);
            }
        }
        lv_obj_t *const max = urq_lvgl_map_get(
            page->widget_map, (uint32_t)info->max_value_widget_id);
        if (max != NULL) {
            lv_obj_t *const max_child = lv_obj_get_child(max, 0);
            if (max_child != NULL) {
                char _tmp[10];
                urq_data_all_type_to_string(
                    trigger_data->max_value,
                    trigger_data->max_value->data_format, _tmp);
                lv_textarea_set_text(max_child, _tmp);
            }
        }
    }

    if (lv_event_get_code(e) == LV_EVENT_VALUE_CHANGED) {
        lv_obj_t *const textarea = lv_keyboard_get_textarea(widget->keyboard);
        uint16_t btn = lv_keyboard_get_selected_btn(widget->keyboard);
        const char *txt = lv_keyboard_get_btn_text(widget->keyboard, btn);

        if (widget->conf->type == URQ_KEYBOARD_LAYOUT_UNSPECIFIED) {
            // 自定义键盘，根据用户配置输入值
            urq_string_map_t *map = widget->conf->show_map;
            const char *value = urq_string_map_get(map, txt);
            if (value != NULL) {
                lv_textarea_add_text(textarea, value);
            } else {
                lv_textarea_add_text(textarea, txt);
            }
        } else {
            if (strcmp(txt, "Enter") == 0) {
                const char *text = lv_textarea_get_text(textarea);

                // 确认弹窗
                if (trigger_data->confirm_win.id != 0) {
                    // urq_confirm_win_show(&trigger_data->confirm_win);
                    lv_obj_t *win = urq_project_show_page_message(
                        (lv_obj_t *)project, trigger_data->confirm_win.id);
                    urq_page_t *win_page = urq_page_map_get(
                        ((urq_page_group_t *)win)->pages,
                        trigger_data->confirm_win.id);
                    if (win_page != NULL) {
                        win_page->message_win_cb.cb = urq_keyboard_key_enter;
                        win_page->message_win_cb.arg1 = (void *)text;
                    }
                    //   显示弹窗
                    //   lv_timer_t *timer = lv_timer_create(
                    //      urq_project_close_page_message,
                    //      trigger_data->confirm_win.wait_time, obj);
                    //   urq_used(timer);
                    //   关闭后是否要执行
                } else {
                    urq_keyboard_key_enter((lv_obj_t *)project, (void *)text);
                }
            } else if (strcmp(txt, "Clear") == 0) {
                urq_keyboard_key_clear(textarea);
            } else if (strcmp(txt, "123") == 0) {
                urq_keyboard_key_switch_number(widget->keyboard);
            } else if (strcmp(txt, "Caps") == 0) {
                urq_keyboard_key_switch_abc(widget->keyboard);
            } else if (strcmp(txt, "Space") == 0) {
                urq_keyboard_key_space(textarea);
            } else {
                lv_keyboard_def_event_cb(e);
            }
        }
    } else if (
        lv_event_get_code(e) == LV_EVENT_DRAW_PART_BEGIN &&
        widget->conf->type == URQ_KEYBOARD_LAYOUT_UNSPECIFIED) {
        lv_obj_draw_part_dsc_t *dsc = lv_event_get_draw_part_dsc(e);
        if (dsc == NULL) {
            return;
        }
        if (dsc->class_p == &lv_btnmatrix_class &&
            dsc->type == LV_BTNMATRIX_DRAW_PART_BTN) {
            if (dsc->id < widget->conf->key_styles->size) {
                urq_keyboard_base_conf_t *conf =
                    widget->conf->key_styles->keys[dsc->id];
                if (conf->styles != NULL) {
                    // 阴影
                    urq_style_t *style = conf->styles->data[0];
                    if (style->shadow != NULL) {
                        if (style->shadow->enable) {
                            dsc->rect_dsc->shadow_width = style->shadow->width;
                            dsc->rect_dsc->shadow_ofs_x =
                                style->shadow->offset_x;
                            dsc->rect_dsc->shadow_ofs_y =
                                style->shadow->offset_y;
                            dsc->rect_dsc->shadow_spread =
                                style->shadow->spread;
                            dsc->rect_dsc->shadow_color = urq_get_color(theme_color, &style->shadow->bg);
                        }
                    }
                    if (style->border != NULL) {
                        dsc->rect_dsc->radius = 0;
                        dsc->rect_dsc->border_width = style->border->width;
                        dsc->rect_dsc->shadow_color = urq_get_color(theme_color, &style->shadow->bg);
                        if (style->border->bottom) {
                            dsc->rect_dsc->border_side |= LV_BORDER_SIDE_BOTTOM;
                        } else if (style->border->top) {
                            dsc->rect_dsc->border_side |= LV_BORDER_SIDE_TOP;
                        } else if (style->border->left) {
                            dsc->rect_dsc->border_side |= LV_BORDER_SIDE_LEFT;
                        } else if (style->border->right) {
                            dsc->rect_dsc->border_side |= LV_BORDER_SIDE_RIGHT;
                        }
                        dsc->rect_dsc->border_color = urq_get_color(theme_color, &style->border->color);
                    }

                    // 设置按钮背景颜色
                    if (style->bg.rgba.rgba != 0x000000FF) {
                        dsc->rect_dsc->bg_color = urq_get_color(theme_color, &style->bg);
                    }
                }
            }
        }
    }
}

void urq_event_message_close(lv_timer_t *obj)
{
    urq_used(obj);
    // urq_project_t *project = (urq_project_t *)obj;
    // urq_lvgl_map_free(project->message_showing);
}
