#pragma once

#include "lvgl.h"
#include "urq/page.h"

#ifndef URQ__UTIL__POSITION_H
#define URQ__UTIL__POSITION_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 获取中心位置
/// @param x 位置
/// @param w 宽度
/// @return 中心位置
lv_coord_t urq_get_center_x(lv_coord_t x, lv_coord_t w);

/// @brief 获取中心位置
lv_coord_t urq_get_center_y(lv_coord_t y, lv_coord_t h);

/// @brief 获取相对位置
/// @param pos 位置
/// @param align 对齐方式
/// @return 相对位置
lv_coord_t urq_get_relative_x(lv_coord_t pos, lv_align_t align);

/// @brief 获取相对位置
/// @param pos 位置
/// @param align 对齐方式
/// @return 相对位置
lv_coord_t urq_get_relative_y(lv_coord_t pos, lv_align_t align);

#ifdef __cplusplus
}
#endif

#endif
