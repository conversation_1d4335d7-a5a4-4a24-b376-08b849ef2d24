#pragma once

#include "urq/marquee.h"

#ifndef URQ__UTIL__MARQUEE_H
#define URQ__UTIL__MARQUEE_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 跑马灯回调
/// @param arg1 参数1
/// @param arg2 参数2
/// @param arg3 参数3
// void urq_marquee_callback(void *arg1, void *arg2, void *arg3)
//     __attribute__((__nonnull__(1, 2, 3)));

/// @brief 跑马灯动画回调
/// @param obj 对象
/// @param v 值
void urq_marquee_anim_callback(void *obj, int v)
    __attribute__((__nonnull__(1)));

/// @brief 启用跑马灯
/// @param marquee 跑马灯
/// @param obj 对象
void urq_marquee_enable(urq_marquee_t *marquee, lv_obj_t *obj)
    __attribute__((__nonnull__(1, 2)));

/// @brief 禁用跑马灯
/// @param marquee 跑马灯
void urq_marquee_disable(urq_marquee_t *marquee);

#ifdef __cplusplus
}
#endif

#endif // URQ_UTIL__SET_MARQUEE_H
