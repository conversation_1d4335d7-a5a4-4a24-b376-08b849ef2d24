#pragma once

#include "lvgl.h"
#include "urq/page.h"
#include "urq/style/line.h"
#include "urq/style/point.h"
#include "urq/style/style.h"
#include "urq/widget/parse_context.h"

#ifndef URQ_UTIL__SET_STYLE_H
#define URQ_UTIL__SET_STYLE_H

#ifdef __cplusplus
extern "C" {
#endif

/*======================================设置边框===================================*/
/// @brief 设置边框
/// @param obj 自定义widget
/// @param border style
/// @param selector style selector
void urq_set_style_border(
    lv_obj_t *obj, urq_style_border_t *border, lv_style_selector_t selector)
    __attribute__((__nonnull__(1)));

/// @brief 设置边框
/// @param env parse context
/// @param dsc draw part dsc
/// @param border
void urq_set_style_draw_border(
    urq_widget_parse_context_t *env, lv_draw_rect_dsc_t *rect_dsc,
    urq_style_border_t *border) __attribute__((__nonnull__(1, 2)));

/*======================================设置阴影===================================*/
/// @brief 设置阴影
/// @param obj 自定义widget
/// @param shadow 阴影
/// @param selector style selector
void urq_set_style_shadow(
    lv_obj_t *obj, urq_style_shadow_t *shadow, lv_style_selector_t selector)
    __attribute__((__nonnull__(1, 2)));

/// @brief 设置阴影
/// @param env parse context
/// @param dsc draw part dsc
/// @param shadow style
void urq_set_style_draw_shadow(
    urq_widget_parse_context_t *env, lv_draw_rect_dsc_t *rect_dsc,
    urq_style_shadow_t *shadow) __attribute__((__nonnull__(1, 2)));

/*=====================================设置背景===================================*/
/// @brief 设置背景
/// @param obj lvgl widget
/// @param project project
/// @param bg color
/// @param selector style selector
void urq_set_style_child_bg(
    lv_obj_t *obj, urq_project_t *project, urq_color_ref_t bg,
    lv_style_selector_t selector);

/// @brief 设置背景
/// @param obj 自定义widget
/// @param bg color
/// @param selector style selector
void urq_set_style_bg(
    lv_obj_t *obj, urq_color_ref_t bg, lv_style_selector_t selector)
    __attribute__((__nonnull__(1)));

/// @brief 设置背景
/// @param env parse context
/// @param obj widget
/// @param bg color
void urq_set_style_page_bg(
    urq_widget_parse_context_t *env, lv_obj_t *obj, urq_color_ref_t bg)
    __attribute__((__nonnull__(1)));

/// @brief 设置背景
/// @param env parse context
/// @param dsc draw part dsc
/// @param bg color
void urq_set_style_draw_bg(
    urq_widget_parse_context_t *env, lv_draw_rect_dsc_t *rect_dsc,
    urq_color_ref_t bg) __attribute__((__nonnull__(1)));

/*=====================================设置样式===================================*/
/// @brief 设置样式
/// @param env parse context
/// @param dsc draw part dsc
/// @param style style
void urq_set_draw_style(
    urq_widget_parse_context_t *env, lv_obj_draw_part_dsc_t *dsc,
    urq_style_t *style) __attribute__((__nonnull__(1)));

/// @brief 设置样式
/// @param obj 自定义widget
/// @param style 样式
/// @param selector style selector
void urq_set_style(
    lv_obj_t *obj, urq_style_t *style, lv_style_selector_t selector)
    __attribute__((__nonnull__(1)));

/*=====================================设置线样式===================================*/
/// @brief 设置线样式
/// @param obj 自定义widget
/// @param line 线样式
/// @param selector style selector
void urq_set_style_line(
    lv_obj_t *obj, urq_style_line_t *line, lv_style_selector_t selector)
    __attribute__((__nonnull__(1)));

/// @brief 设置线样式
/// @param env parse context
/// @param dsc draw part dsc
/// @param line 线样式
void urq_set_style_draw_line(
    urq_widget_parse_context_t *env, lv_draw_line_dsc_t *dsc,
    urq_style_line_t *line) __attribute__((__nonnull__(1)));

/*=====================================设置点样式===================================*/
/// @brief 设置点样式
/// @param env parse context
/// @param dsc draw part dsc
/// @param point 点样式
void urq_set_style_draw_part_point(
    urq_widget_parse_context_t *env, lv_obj_draw_part_dsc_t *dsc,
    urq_style_point_t *point) __attribute__((__nonnull__(1)));

/// @brief 设置点样式
/// @param env parse context
/// @param draw_ctx draw context
/// @param center_x center x
/// @param center_y center y
/// @param point 点样式
void urq_set_style_draw_main_point(
    urq_widget_parse_context_t *env, lv_draw_ctx_t *draw_ctx,
    lv_coord_t center_x, lv_coord_t center_y, urq_style_point_t *point)
    __attribute__((__nonnull__(1)));

/// @brief 设置点样式
/// @param obj 自定义widget
/// @param point 点样式
void urq_set_style_point(lv_obj_t *obj, urq_style_point_t *point)
    __attribute__((__nonnull__(1)));

/*=====================================设置无状态样式===================================*/
/// @brief 设置无状态样式
/// @param obj 自定义widget
/// @param way style way
void urq_set_no_state_style_with_fail(lv_obj_t *obj, urq_no_state_way_t way)
    __attribute__((__nonnull__(1)));

/// @brief 设置无状态样式
/// @param obj 自定义widget
/// @param way style way
void urq_set_no_state_style_with_success(lv_obj_t *obj, urq_no_state_way_t way)
    __attribute__((__nonnull__(1)));
#ifdef __cplusplus
}
#endif
#endif // URQ_UTIL__SET_STYLE_H
