
#include "urq/util/control.h"

bool urq_control_condition_check(urq_widget_control_conf_t *control)
{
    if (control == NULL) {
        return true;
    }

    // TODO 权限控制
    // TODO 控制端位置控制
    // TODO 控制端类型控制
    if (control->condition) {
        return true;
    }

    // TODO 禁用外观控制

    if (control->page_id != 0) {
        // TODO 弹出页面ID控制
        return false;
    }

    return false;
}

void urq_control_disable_widget(
    urq_widget_control_conf_t *control, lv_obj_t *obj)
{
    if (control == NULL) {
        return;
    }

    if (!urq_control_condition_check(control)) {
        return;
    }

    switch (control->disable_widget_mode) {
    case DISABLE_WIDGET_MODE_HIDE:
        lv_obj_add_flag(obj, LV_OBJ_FLAG_HIDDEN);
        break;
    case DISABLE_WIDGET_MODE_NORMAL:
        // auto 禁用不变灰
        lv_obj_clear_flag(obj, LV_OBJ_FLAG_HIDDEN);
        lv_obj_clear_flag(obj, LV_STATE_DISABLED);
        /// 清楚不可被点击
        lv_obj_clear_flag(obj, LV_OBJ_FLAG_CLICKABLE);
        break;
    case DISABLE_WIDGET_MODE_GRAY:
        lv_obj_add_flag(obj, LV_STATE_DISABLED);
        break;
    case DISABLE_WIDGET_MODE_ICON:
        // AUTO 显示图标
        // lv_obj_set_src(obj, control->disable_widget_icon);
        break;
    case DISABLE_WIDGET_MODE_UNSPECIFIED:
    default:
        break;
    }
}
