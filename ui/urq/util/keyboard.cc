#include "urq/util/keyboard.h"
#include "urq/preload.h"

void urq_keyboard_input_value(lv_obj_t *self, const char *value)
{
    urq_used(self);
    urq_used(value);
    // 是一个输入控件，往内部输入值
}

void urq_keyboard_set_min_value(lv_obj_t *self, int32_t value)
{
    urq_used(self);
    urq_used(value);
    /// 设置最小值
}

void urq_keyboard_set_max_value(lv_obj_t *self, int32_t value)
{
    urq_used(self);
    urq_used(value);
}
