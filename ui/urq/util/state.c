#include "urq/util/state.h"
#include "urq/util/show_lvgl.h"
#include "urq/util/style.h"
#include "urq/widget/state.h"

bool urq_set_state_graphic(
    lv_obj_t *udata, lv_obj_t *obj, urq_graphic_ptr_list_t *graphics,
    urq_state_t state)
{
    if (graphics != NULL && graphics->size > state) {
        urq_graphic_t *graphic = graphics->data[state];
        urq_show_lvgl_image(udata, obj, graphic);
        return true;
    }
    return false;
}

bool urq_set_state_style(
    urq_project_t *project, lv_obj_t *obj, urq_style_ptr_list_t *styles,
    urq_state_t state)
{
    if (styles != NULL && styles->size > state) {
        printf(
            "[urq_set_state_style]: state: %d, size: %d\n", state,
            styles->size);
        urq_used(project);
        urq_style_t *style = styles->data[state];
        urq_set_style(obj, style, LV_PART_MAIN);
        return true;
    }
    return false;
}

bool urq_set_state_text(
    lv_obj_t *udata, lv_obj_t *obj, urq_text_ptr_list_t *texts,
    urq_state_t state)
{
    if (texts != NULL && texts->size > state) {
        urq_text_t *text = texts->data[state];
        urq_show_lvgl_text(udata, obj, text);
        return true;
    }
    return false;
}

void urq_set_state(
    lv_obj_t *obj, urq_widget_common_conf_t *common_conf, urq_state_t state,
    uint8_t platfrom)
{
    urq_user_data_t *udata = urq_obj_get_user_data(obj);
    urq_used(platfrom);

    urq_no_state_way_t state_way = URQ_NO_STATE_WAY_UNSPECIFIED;
    if (common_conf->stat_property != NULL) {
        state_way = common_conf->stat_property->no_state_way;
    }

    bool rt = false;
    if (udata->img != NULL) {
        rt = urq_set_state_graphic(
            (lv_obj_t *)udata, udata->img, common_conf->graphics, state);
        // if(!rt && platfrom == 0) {
        //     lv_img_set_src(udata->img, NULL);
        // }
        if (!rt) {
            urq_set_no_state_style_with_fail(udata->img, state_way);
        } else {
            urq_set_no_state_style_with_success(udata->img, state_way);
        }
    }

    if (udata->label != NULL) {
        rt = urq_set_state_text(
            (lv_obj_t *)udata, obj, common_conf->texts, state);
        // if(!rt && platfrom == 0) {
        //     lv_label_set_text(udata->label, " ");
        // }
        if (!rt) {
            urq_set_no_state_style_with_fail(obj, state_way);
        } else {
            urq_set_no_state_style_with_success(udata->label, state_way);
        }
    }

    rt = urq_set_state_style(udata->project, obj, common_conf->styles, state);
    if (!rt && state != 0) {
        // urq_set_no_state_style_with_fail(obj, state_way);
        if (state_way == URQ_NO_STATE_WAY_USE_BLANK) {
            lv_obj_set_style_border_width(obj, 0, LV_PART_MAIN);
            lv_obj_set_style_border_opa(obj, LV_OPA_TRANSP, LV_PART_MAIN);

            lv_obj_set_style_shadow_width(obj, 0, LV_PART_MAIN);
            lv_obj_set_style_shadow_opa(obj, LV_OPA_TRANSP, LV_PART_MAIN);
            lv_obj_set_style_bg_color(obj, lv_color_white(), LV_PART_MAIN);
            lv_obj_set_style_bg_opa(obj, 0, LV_PART_MAIN);
        } else {
            // 使用最后一个状态
        }
        //}else {
        //    if(state_way == URQ_NO_STATE_WAY_USE_BLANK) {
        //        // 空白
        //    }else {
        //        // 使用最后一个状态
        //    }
    }
}
