#pragma once

#include "lvgl/lvgl.h"
#include "urq/keyboard/data.h"
#include "urq/project/project.h"
#include <stddef.h>

#ifndef URQ__UTIL__TRANSFORM_H
#define URQ__UTIL__TRANSFORM_H

#ifdef __cplusplus
extern "C" {
#endif

// 通过page获取当前页面
// 通过display获取当前显示输入对图元
// 将结构体值输入到图元上
/// @brief 将输入框内容转换为结构体
/// @param page 当前页面
/// @param display 当前显示输入对图元
/// @return 0表示成功，-1表示失败
int urq_transform_input_to_struct(
    urq_project_t *project, urq_keyboard_data_t *data);

/// 输入的内容组合到结构体中
/// 发送结构体到服务端上
/// @brief 将结构体转换为输入框内容
/// @param data 结构体
/// @return 0表示成功，-1表示失败
int urq_transform_struct_to_input(
    urq_project_t *projetc, urq_keyboard_data_t *data);

#ifdef __cplusplus
}
#endif
#endif // URQ__UTIL__TRANSFORM_H
