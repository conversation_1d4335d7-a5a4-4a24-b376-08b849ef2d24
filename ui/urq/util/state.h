#pragma once

#include "urq/graphic/ptr_list.h"
#include "urq/project.h"
#include "urq/style/list_ptr.h"
#include "urq/text/ptr_list.h"
#include "urq/widget/common.h"
#include "urq/widget/state.h"

#ifndef URQ__UTIL__STATE_H
#define URQ__UTIL__STATE_H

#ifdef __cplusplus
extern "C" {
#endif

bool urq_set_state_graphic(
    lv_obj_t *udata, lv_obj_t *obj, urq_graphic_ptr_list_t *graphic,
    urq_state_t state) __attribute__((__nonnull__(1)));

void urq_set_state_graphic_way(lv_obj_t* obj, bool rt, urq_no_state_way_t no_state_way);

bool urq_set_state_style(
    urq_project_t *project, lv_obj_t *obj, urq_style_ptr_list_t *styles,
    urq_state_t state) __attribute__((__nonnull__(1)));

bool urq_set_state_text(
    lv_obj_t *udata, lv_obj_t *obj, urq_text_ptr_list_t *text,
    urq_state_t state) __attribute__((__nonnull__(1)));

void urq_set_state(
    lv_obj_t *obj, urq_widget_common_conf_t *common_conf,
    urq_state_t state, uint8_t platfrom) __attribute__((__nonnull__(1)));

#ifdef __cplusplus
}
#endif

#endif // URQ__UTIL__STATE_H
