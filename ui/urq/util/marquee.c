#include "marquee.h"

// void urq_marquee_callback(void *arg1, void *arg2, void *arg3)
// {
//     lv_obj_t *obj = (lv_obj_t *)arg1;
//     urq_page_t *page = (urq_page_t *)arg2;
//     urq_marquee_t *marquee = (urq_marquee_t *)arg3;
//
//     // 获取文本对象
//     lv_obj_t *child = lv_obj_get_child(obj, 0);
//
//     static int cnt_exec = 0;
//     static lv_coord_t step = 0;
//     cnt_exec += 1;
//     step = lv_obj_get_x(child);
//
//     if (cnt_exec >= marquee->interval->value.int_value) {
//         cnt_exec = 0;
//         step += (lv_coord_t)marquee->scroll_distance->value.int_value;
//         if (step - lv_obj_get_width(child) >= lv_obj_get_width(obj)) {
//             step = -lv_obj_get_width(child);
//         }
//         lv_obj_set_x(child, step);
//         lv_obj_refr_pos(obj);
//     }
//
//     if (marquee->enable->value.bool_value) {
//         urq_page_add_frame_cb3(
//             page, urq_marquee_callback, urq_noop3, obj, page, marquee);
//     }
// }

void urq_marquee_anim_callback(void *obj, int v)
{
    urq_marquee_t *marquee = (urq_marquee_t *)obj;
    lv_obj_t *child = lv_obj_get_child(marquee->obj, 0);

    lv_coord_t v_ = (lv_coord_t)v + (lv_coord_t)marquee->scroll_distance;
    if (marquee->scroll_direction ==
        URQ_SCROLL_DIRECTION_LEFT_TO_RIGHT) {
        lv_obj_set_x(child, v_);
    } else if (
        marquee->scroll_direction ==
        URQ_SCROLL_DIRECTION_RIGHT_TO_LEFT) {
        lv_obj_set_x(child, v_);
    } else if (
        marquee->scroll_direction ==
        URQ_SCROLL_DIRECTION_TOP_TO_BOTTOM) {
        lv_obj_set_y(child, v_);
    } else if (marquee->scroll_direction ==URQ_SCROLL_DIRECTION_BOTTOM_TO_TOP) {
        lv_obj_set_y(child, v_);
    }
}

void urq_marquee_enable(urq_marquee_t *marquee, lv_obj_t *obj)
{

    marquee->obj = obj;
    marquee->marquee_anim = urq_malloc(sizeof(lv_anim_t));
    lv_anim_init(marquee->marquee_anim);
    // 参数设置
    lv_anim_set_var(marquee->marquee_anim, marquee);
    // 设置动画值
    lv_anim_set_values(
        marquee->marquee_anim, marquee->start_pos, marquee->end_pos);
    // 设置动画时间
    lv_anim_set_time(marquee->marquee_anim, 5000);
    // 设置动画重复次数
    lv_anim_set_repeat_delay(marquee->marquee_anim, marquee->interval);
    lv_anim_set_repeat_count(marquee->marquee_anim, LV_ANIM_REPEAT_INFINITE);
    // 设置动画回调
    lv_anim_set_exec_cb(marquee->marquee_anim, urq_marquee_anim_callback);
    // 设置动画路径
    lv_anim_set_path_cb(
        marquee->marquee_anim, lv_anim_path_linear); // 设置线性路径
    // 启动动画
    lv_anim_start(marquee->marquee_anim);
}

void urq_marquee_disable(urq_marquee_t *marquee)
{
    if (marquee != NULL && marquee->marquee_anim != NULL) {
        lv_anim_del(marquee, urq_marquee_anim_callback);
        urq_free(marquee->marquee_anim);
        marquee->marquee_anim = NULL;
        marquee->obj = NULL;
    }
}
