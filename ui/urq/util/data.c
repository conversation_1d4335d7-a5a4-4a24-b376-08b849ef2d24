#include "urq/util/data.h"
#include <string.h>

void urq_add_thousands_separator(const char *value, char *out, size_t out_size)
{
    if ((strlen(value) / 3) + strlen(value) > out_size)
        return;
    char *dot = strchr(value, '.'); // 找小数点
    size_t int_len = strlen(value);
    if (dot) {
        int_len = 0;
        const char *_p = value;
        while (*_p && *_p != '.') {
            _p++;
            int_len++;
        }
    }

    int out_i = 0, tmp_i = 0;
    size_t digit_count = int_len % 3;
    if (digit_count == 0)
        digit_count = 3;
    for (size_t i = 0; i < int_len; ++i) {
        out[out_i++] = value[tmp_i++];
        if (--digit_count == 0 && i != int_len - 1) {
            out[out_i++] = ','; // 插入逗号
            digit_count = 3;
        }
    }

    // 复制小数部分
    if (dot) {
        while (value[tmp_i]) {
            out[out_i++] = value[tmp_i++];
        }
    }

    out[out_i] = '\0';
}
