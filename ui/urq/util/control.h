#pragma once

#include "urq/widget/common.h"
#include <lvgl/src/core/lv_obj.h>

#ifndef URQ__UTIL__CONTROL__H
#define URQ__UTIL__CONTROL__H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 检查控制条件
/// @param control 控制配置
/// @return 是否满足控制条件
bool urq_control_condition_check(urq_widget_control_conf_t *control);

/// @brief 禁用控件
/// @param control 控制配置
/// @param obj 控件
void urq_control_disable_widget(
    urq_widget_control_conf_t *control, lv_obj_t *obj);

#ifdef __cplusplus
}
#endif

#endif // URQ__UTIL__CONTROL__H
