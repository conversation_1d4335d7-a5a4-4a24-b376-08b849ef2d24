#pragma once

#include "lvgl/lvgl.h"

#ifndef URQ__UTIL__KEYBOARD_H
#define URQ__UTIL__KEYBOARD_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 键盘输入
/// @param self 键盘
/// @param key 按键
/// @param value 值
void urq_keyboard_input(lv_obj_t *self, lv_event_t *e)
    __attribute__((__nonnull__(1, 2)));

/// @brief 设置最小值
/// @param self 键盘
/// @param value 值
void urq_keyboard_set_min_value(lv_obj_t *self, int32_t value)
    __attribute__((__nonnull__(1)));

/// @brief 设置最大值
/// @param self 键盘
/// @param value 值
void urq_keyboard_set_max_value(lv_obj_t *self, int32_t value)
    __attribute__((__nonnull__(1)));

#ifdef __cplusplus
}
#endif

#endif // URQ_UTIL__SET_MARQUEE_H
