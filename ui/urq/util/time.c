#include "urq/util/time.h"



int urq_timestamp_to_date_string(int64_t timestamp, urq_date_format_t format, char* buffer, size_t buffer_size) {
    if (!buffer || buffer_size < 15) {
        return -1;
    }
    
    // 将时间戳转换为本地时间
    time_t time_value = (time_t)timestamp;
    struct tm* tm_info = localtime(&time_value);
    
    if (!tm_info) {
        return -2;
    }
    
    // 根据格式类型选择日期格式
    switch (format) {
        case URQ_DATE_FORMAT_MDY:
            snprintf(buffer, buffer_size, "%02d/%02d/%04d",
                     tm_info->tm_mon + 1, tm_info->tm_mday, tm_info->tm_year + 1900);
            break;
        case URQ_DATE_FORMAT_DMY:
            snprintf(buffer, buffer_size, "%02d/%02d/%04d", 
                     tm_info->tm_mday, tm_info->tm_mon + 1, tm_info->tm_year + 1900);
            break;
        case URQ_DATE_FORMAT_YMD:
            snprintf(buffer, buffer_size, "%04d/%02d/%02d",
                     tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday);
            break;
        case URQ_DATE_FORMAT_MD:
            snprintf(buffer, buffer_size, "%02d/%02d",
                     tm_info->tm_mon + 1, tm_info->tm_mday);
            break;
        case URQ_DATE_FORMAT_DM:
            snprintf(buffer, buffer_size, "%02d/%02d",
                     tm_info->tm_mday, tm_info->tm_mon + 1);
            break;
        case URQ_DATE_FORMAT_D:
            snprintf(buffer, buffer_size, "%02d", tm_info->tm_mday);
            break;
        case URQ_DATE_FORMAT_MDY_POINT:
            snprintf(buffer, buffer_size, "%02d.%02d.%04d",
                     tm_info->tm_mon + 1, tm_info->tm_mday, tm_info->tm_year + 1900);
            break;
        case URQ_DATE_FORMAT_DMY_POINT:
            snprintf(buffer, buffer_size, "%02d.%02d.%04d",
                     tm_info->tm_mday, tm_info->tm_mon + 1, tm_info->tm_year + 1900);
            break;
        case URQ_DATE_FORMAT_YMD_POINT:
            snprintf(buffer, buffer_size, "%04d.%02d.%02d",
                     tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday);
            break;
        case URQ_DATE_FORMAT_MD_POINT:
            snprintf(buffer, buffer_size, "%02d.%02d", 
                     tm_info->tm_mon + 1, tm_info->tm_mday);
            break;
        case URQ_DATE_FORMAT_DM_POINT:
            snprintf(buffer, buffer_size, "%02d.%02d", 
                     tm_info->tm_mday, tm_info->tm_mon + 1);
            break;
        case URQ_DATE_FORMAT_D_POINT:
            snprintf(buffer, buffer_size, "%02d", tm_info->tm_mday);
            break;
        case URQ_DATE_FORMAT_MDY_DASH:
            snprintf(buffer, buffer_size, "%02d-%02d-%04d", 
                     tm_info->tm_mon + 1, tm_info->tm_mday, tm_info->tm_year + 1900);
            break;
        case URQ_DATE_FORMAT_DMY_DASH:
            snprintf(buffer, buffer_size, "%02d-%02d-%04d", 
                     tm_info->tm_mday, tm_info->tm_mon + 1, tm_info->tm_year + 1900);
            break;
        case URQ_DATE_FORMAT_YMD_DASH:
            snprintf(buffer, buffer_size, "%04d-%02d-%02d", 
                     tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday);
            break;
        case URQ_DATE_FORMAT_MD_DASH:
            snprintf(buffer, buffer_size, "%02d-%02d", 
                     tm_info->tm_mon + 1, tm_info->tm_mday);
            break;
        case URQ_DATE_FORMAT_DM_DASH:
            snprintf(buffer, buffer_size, "%02d-%02d", 
                     tm_info->tm_mday, tm_info->tm_mon + 1);
            break;
        case URQ_DATE_FORMAT_D_DASH:
            snprintf(buffer, buffer_size, "%02d", tm_info->tm_mday);
            break;
        default:
            snprintf(buffer, buffer_size, "%04d-%02d-%02d", 
                     tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday);
            break;
    }
    
    return 0;
}

int urq_timestamp_to_time_string(int64_t timestamp, urq_time_format_t format, char* buffer, size_t buffer_size) {
    if (buffer == NULL || buffer_size == 0) {
        return -1;
    }
    
    time_t time_value = (time_t)timestamp;
    struct tm *tm_info = localtime(&time_value);
    
    if (tm_info == NULL) {
        return -1;
    }
    
    switch (format) {
        case URQ_TIME_FORMAT_HMS:
            snprintf(buffer, buffer_size, "%02d:%02d:%02d", 
                     tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);
            break;
        case URQ_TIME_FORMAT_HM:
            snprintf(buffer, buffer_size, "%02d:%02d", 
                     tm_info->tm_hour, tm_info->tm_min);
            break;
        case URQ_TIME_FORMAT_MS:
            snprintf(buffer, buffer_size, "%02d:%02d", 
                     tm_info->tm_min, tm_info->tm_sec);
            break;
        default:
            snprintf(buffer, buffer_size, "%02d:%02d:%02d", 
                     tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);
            break;
    }
    
    return 0;

}

int urq_timestamp_to_datetime_string(int64_t timestamp, urq_date_format_t date_format, urq_time_format_t time_format, char* buffer, size_t buffer_size) {
    if (buffer == NULL || buffer_size == 0) {
        return -1;
    }
    
    char date_buf[32] = {0};
    char time_buf[16] = {0};
    
    int date_result = urq_timestamp_to_date_string(timestamp, date_format, date_buf, sizeof(date_buf));
    int time_result = urq_timestamp_to_time_string(timestamp, time_format, time_buf, sizeof(time_buf));
    
    if (date_result != 0 || time_result != 0) {
        return -1;
    }
    
    snprintf(buffer, buffer_size, "%s %s", date_buf, time_buf);
    return 0;
}

struct tm *urq_get_current_time(void) {
    time_t t = time(NULL);
    return localtime(&t);   
}