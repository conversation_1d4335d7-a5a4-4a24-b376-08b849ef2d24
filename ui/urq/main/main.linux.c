
#include "lvgl.h"
#include "urq/exit.h"
#include "urq/ip.h"
#include "urq/lv/init.h"
#include "urq/main/common.h"
#include "urq/project/project.h"
#include "urq/urq.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

/// @brief 初始化
/// @param id 设备 id
/// @param width 宽度
/// @param height 高度
/// @returns project
static int _ui_main(int id, lv_coord_t width, lv_coord_t height);

static int _ui_main(int iid, lv_coord_t width, lv_coord_t height)
{
    lv_indev_t *keyboard;

    if (urq_lv_init(iid, width, height, &keyboard)) {
        printf("241231235010098, urq_lv_init failed");
    }

    URQ_ROOT_PROJECT = urq_project_create(lv_scr_act());
    lv_obj_set_size(URQ_ROOT_PROJECT, (lv_coord_t)width, (lv_coord_t)height);
    lv_obj_set_pos(URQ_ROOT_PROJECT, 0, 0);

    if (urq_project_set_host(
            URQ_ROOT_PROJECT, iid, urq_ip_make4(127, 0, 0, 1), 8850)) {
        return -1;
    }
    return 0;
}

int main(int argc, char *argv[])
{
    system("clear");                                            // 清除终端
    static const struct timespec URQ_SLEEP_TIME = {0, 5000000}; // 5ms

    if (urq_fs_mod_init(argc, argv)) {
        return -1;
    }

    _ui_main(1, 800, 600);
    while (urq_is_running) {
        urq_main_time_inc();
        nanosleep(&URQ_SLEEP_TIME, NULL);
    }

    lv_obj_del(URQ_ROOT_PROJECT);
    URQ_ROOT_PROJECT = NULL;

    return urq_exit_code;
}
