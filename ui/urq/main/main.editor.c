#include "lvgl.h"
#include "urq/conf/map/lvgl.h"
#include "urq/device/local.h"
#include "urq/exit.h"
#include "urq/fs/fs.h"
#include "urq/log/verbose.h"
#include "urq/lv/display.h"
#include "urq/lv/init.h"
#include "urq/main/common.h"
#include "urq/page/group.h"
#include "urq/page/page.h"
#include "urq/preload.h"
#include "urq/project/load.h"
#include "urq/project/project.h"
#include "urq/sleep.h"
#include "urq/theme/color.h"
#include "urq/user_data.h"
#include "urq/util/style.h"
#include "urq/widget/state.h"
#include <emscripten.h>
#include <limits.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>

#define PROJECT_PREFIX "project-"

#define VERSION "1.0.0"

static int8_t s_overlay_opa = -1;
static bool s_is_running = false;

/// @brief 初始化
/// @param id    project_id
/// @param width  宽度
/// @param height 高度
/// @param project_width 工程宽度
/// @param project_height 工程高度
/// @returns project
// static int _ui_main(int iid, lv_coord_t width, lv_coord_t height);

static int _ui_main(
    int project_id, int16_t display_id, uint16_t main_page_id, lv_coord_t width,
    lv_coord_t height);

static int _load_project(
    int project_id, int16_t display_id, uint16_t page_id, lv_coord_t width,
    lv_coord_t height)
{
    URQ_ROOT_PROJECT = urq_project_create(lv_scr_act());
    urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;

    project->env.var_sys.display_id = (int8_t)display_id;

    if (page_id > 0)
        project->env.var_sys.registers
            .data[URQ_DEVICE_LOCAL_ADDR_BASIC_PAGE_NO] = (int16_t)page_id;

    lv_obj_set_size(
        URQ_ROOT_PROJECT, (lv_coord_t)URQ_PROJECT_WIDTH,
        (lv_coord_t)URQ_PROJECT_HEIGHT);
    lv_obj_set_pos(URQ_ROOT_PROJECT, 0, 0);

    lv_obj_set_size(
        project->display->top_layer, (lv_coord_t)URQ_PROJECT_WIDTH,
        (lv_coord_t)URQ_PROJECT_HEIGHT);
    lv_obj_set_pos(project->display->top_layer, 0, 0);

    lv_obj_set_size(
        project->display->page_layer, (lv_coord_t)URQ_PROJECT_WIDTH,
        (lv_coord_t)URQ_PROJECT_HEIGHT);
    lv_obj_set_pos(project->display->page_layer, 0, 0);

    URQ_SET_DEVICE_SYSTEM_VAR(
        project->env.var_sys, BASIC_PAGE_NO, (int16_t)page_id);
    URQ_SET_DEVICE_SYSTEM_VAR(project->env.var_sys, LANGUAGE, 0);
    URQ_SET_DEVICE_SYSTEM_VAR(project->env.var_sys, THEME, 0);

    if (s_overlay_opa != -1) {
        project->env.var_sys.overlay_opa = s_overlay_opa;
    }

    urq_fs_set_host(&project->fs, project_id, 0, 0);
    urq_conn_set_host(&project->conn, project_id, 0, 0);

    char prefix[32] = {0};
    sprintf(prefix, "%s%d", PROJECT_PREFIX, project_id);
    if (urq_fs_set_project_id(&project->fs, prefix)) {
        return -1;
    }
    urq_project_load_device(project);
    return 0;
}

// static int _ui_main(int project_id, lv_coord_t width, lv_coord_t height)
// {
//     _ui_main2(project_id, -1, 0, width, height);
// }

static int _ui_main(
    int project_id, int16_t display_id, uint16_t main_page_id, lv_coord_t width,
    lv_coord_t height)
{
    lv_indev_t *keyboard;
    URQ_PROJECT_WIDTH = width;
    URQ_PROJECT_HEIGHT = height;

    if (urq_lv_init(project_id, width, height, &keyboard)) {
        printf("241231235010098, urq_lv_init failed");
    }

    _load_project(project_id, display_id, main_page_id, width, height);
    return 0;
}

EMSCRIPTEN_KEEPALIVE
void urq_web_set_errno(int32_t no) { errno = no; }

EMSCRIPTEN_KEEPALIVE
int urq_web_get_errno(void) { return errno; }

EMSCRIPTEN_KEEPALIVE
void *urq_web_malloc(uint32_t size) { return urq_malloc(size); }

EMSCRIPTEN_KEEPALIVE
void urq_web_free(void *ptr) { free(ptr); }

int main(int argc, char *argv[])
{
    while (true) {
        printf("%s:%d\n", __FILE__, __LINE__);
        urq_sleep(1000);
    }
    return 0;
}

// EMSCRIPTEN_KEEPALIVE
// void urq_web_main(
//     const char *working_folder, int project_id, int width, int height,
//     int project_width, int project_height)
// {
//     log_i(
//         "urq_web_main, working folder: %s, project_id: %d, width: %d, height:
//         %d\n", working_folder, project_id, width, height);
//
//     // urq_fs_mod_init("");
//     _ui_main(project_id, width, height);
// }

EMSCRIPTEN_KEEPALIVE
void urq_web_main(
    const char *working_folder, int project_id, int16_t display_id,
    uint16_t main_page_id, int16_t width, int16_t height, int project_width,
    int project_height)
{
    log_i(
        "urq_web_main, working folder: %s, project_id: %d, width: %d, height: "
        "%d\n",
        working_folder, project_id, width, height);

    // urq_fs_mod_init("");
    _ui_main(project_id, display_id, main_page_id, width, height);
}

/*========================================reload==========================================*/

EMSCRIPTEN_KEEPALIVE
void urq_web_reload_project(
    int project_id, int16_t display_id, uint16_t main_page_id, int16_t width,
    int16_t height)
{
    // if(s_is_running) return ;s_is_running = true;
    urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;
    if (project != NULL) {
        lv_obj_del((lv_obj_t *)project);
        URQ_ROOT_PROJECT = NULL;
        urq_lv_display_deinit();
        if (urq_lv_display_init(project_id, width, height))
            printf("==== urq_lv_display_init failed\n");
        _load_project(project_id, display_id, main_page_id, width, height);
    }
    // s_is_running = false;
}

EMSCRIPTEN_KEEPALIVE
void urq_web_reload_display(int16_t display_id, uint16_t main_page_id)
{
    // if(s_is_running) return ;s_is_running = true;
    urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;
    if (project != NULL) {
        if (display_id != -1)
            project->env.var_sys.display_id = (int8_t)display_id;

        if (main_page_id > 0)
            project->env.var_sys.registers
                .data[URQ_DEVICE_LOCAL_ADDR_BASIC_PAGE_NO] =
                (int16_t)main_page_id;

        urq_project_reload_display(URQ_ROOT_PROJECT);
    }
    // s_is_running = false;
}

EMSCRIPTEN_KEEPALIVE
void urq_web_reload_page(void)
{
    // if(s_is_running) return ;s_is_running = true;
    urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;
    if (project != NULL) {
        urq_page_group_t *group = (urq_page_group_t *)project->page_showing;
        if (group != NULL) {
            uint16_t id = group->id;
            urq_project_close_page(URQ_ROOT_PROJECT);
            urq_project_show_page(URQ_ROOT_PROJECT, id);
        }
    }
    // s_is_running = false;
}

/*========================================set==========================================*/
EMSCRIPTEN_KEEPALIVE
void urq_web_set_page(int16_t page_id, bool force, urq_state_t state)
{
    // if(s_is_running) return ;s_is_running = true;
    if (state != -2) {
        URQ_STATE_DEFAULT = state;
    }
    // urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;

    urq_project_close_page(URQ_ROOT_PROJECT);
    // 执行页面切换
    urq_project_show_page(URQ_ROOT_PROJECT, (urq_page_id_t)page_id);
    // s_is_running = false;
}

EMSCRIPTEN_KEEPALIVE
void urq_web_set_overlay_page_opa(uint8_t opa)
{
    s_overlay_opa = opa;
    urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;
    project->env.var_sys.overlay_opa = opa;
}

EMSCRIPTEN_KEEPALIVE
void urq_web_set_overlay_page(int32_t id, uint8_t opa)
{
    urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;
    urq_project_show_overlay_page((lv_obj_t *)project, (urq_page_id_t)id, opa);
}

EMSCRIPTEN_KEEPALIVE void urq_web_set_widget_size(
    uint16_t page_id, uint16_t widget_id, uint16_t width, uint16_t height)
{
    urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;
    urq_page_group_t *group = (urq_page_group_t *)project->page_showing;
    urq_page_t *page = urq_page_map_get(group->pages, page_id);
    if (page == NULL) {
        return;
    }
    lv_obj_t *widget = urq_lvgl_map_get(page->widget_map, widget_id);
    if (widget == NULL) {
        return;
    }
    lv_obj_set_size(widget, width, height);
}

EMSCRIPTEN_KEEPALIVE
void urq_web_set_widget_pos(
    uint16_t page_id, uint16_t widget_id, uint16_t x, uint16_t y)
{
    urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;
    urq_page_group_t *group = (urq_page_group_t *)project->page_showing;
    urq_page_t *page = urq_page_map_get(group->pages, page_id);
    if (page == NULL) {
        return;
    }
    lv_obj_t *widget = urq_lvgl_map_get(page->widget_map, widget_id);
    if (widget == NULL) {
        return;
    }
    lv_obj_set_pos(widget, x, y);
}

EMSCRIPTEN_KEEPALIVE
void urq_web_set_project_theme(uint16_t theme_id)
{
    // 当前画面
    // if(s_is_running) return ;s_is_running = true;
    urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;
    project->env.var_sys.registers.data[URQ_DEVICE_LOCAL_ADDR_THEME] = theme_id;
    urq_project_load_theme_style((void *)project);

    urq_page_group_t *group_page = (urq_page_group_t *)project->page_showing;
    urq_page_t *page = urq_page_map_get(group_page->pages, group_page->id);

    urq_set_style_page_bg(
        &project->env, (lv_obj_t *)page, group_page->style.bg);

    /// 设置组件页面
    khiter_t k = kh_begin(page->widget_map);
    khiter_t end = kh_end(page->widget_map);
    for (; k != end; ++k) {
        if (kh_exist(page->widget_map, k)) {
            lv_obj_t *widget = kh_value(page->widget_map, k);
            urq_user_data_t *user_data = urq_obj_get_user_data(widget);
            if (user_data->set_state != NULL)
                user_data->set_state(widget, project->env.var_sys.state, 0);
            // urq_set_state_style(project, widget, user_data->style,
            // project->env.var_sys.state); urq_set_style(widget, theme_color,
            // LV_PART_MAIN);
        }
    }
    // s_is_running = false;
}

EMSCRIPTEN_KEEPALIVE
void urq_web_set_project_language(uint16_t language_id)
{
    // 更新所有组件的文本内容
    // 记录当前语言ID，更新当前页面的语言
    // if(s_is_running) return ;s_is_running = true;
    urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;
    if (project->env.language_list.size <= language_id || language_id < 0)
        return;

    URQ_SET_DEVICE_SYSTEM_VAR(
        project->env.var_sys, LANGUAGE,
        project->env.language_list.data[language_id]);

    urq_project_reload_language(project);
    // 更新当前组件内容
    urq_page_group_t *group_page = (urq_page_group_t *)project->page_showing;
    urq_page_t *page = urq_page_map_get(group_page->pages, group_page->id);
    urq_page_refresh_language((lv_obj_t *)page);
    // s_is_running = false;
}

EMSCRIPTEN_KEEPALIVE
void urq_web_set_project_bg_color(uint8_t r, uint8_t g, uint8_t b, uint8_t a)
{
    lv_obj_set_style_bg_color(
        URQ_ROOT_PROJECT, lv_color_make(r, g, b), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(URQ_ROOT_PROJECT, a, LV_PART_MAIN);
}

EMSCRIPTEN_KEEPALIVE
void urq_web_set_state(urq_state_t state, uint8_t platform)
{
    urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;
    urq_page_group_t *group_page = (urq_page_group_t *)project->page_showing;
    urq_page_t *page = urq_page_map_get(group_page->pages, group_page->id);
    project->env.var_sys.state = state;
    urq_page_set_state((lv_obj_t *)page, state, platform);
}

EMSCRIPTEN_KEEPALIVE
const char *urq_web_version(void)
{
    printf("version: %s\n", VERSION);
    return VERSION;
}

EMSCRIPTEN_KEEPALIVE
void urq_web_shutdown(int code) { urq_exit(code); }

EMSCRIPTEN_KEEPALIVE
void urq_web_time_inc() { urq_main_time_inc(); }

EMSCRIPTEN_KEEPALIVE
void urq_web_var_sys_set_u16(urq_device_local_addr_t addr, uint16_t value)
{
    urq_project_t *project = (urq_project_t *)URQ_ROOT_PROJECT;
    // if (urq_conn_sys_u16_write_single(
    //         &project->conn, URQ_DEVICE_LOCAL_ADDR_PAGE, value)) {
    //     log_e("page set error\n");
    // }
}