#pragma once

#include "lvgl.h"
#include "urq/page.h"
#include "urq/widget/common.h"
#include "urq/widget/conf.h"

#ifndef URQ__WIDGET_H
#define URQ__WIDGET_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 配置组件
typedef void (*urq_widget_conf_fn_t)(lv_obj_t *self, urq_widget_conf_t *conf);

typedef void (*urq_widget_set_property_fn_t)(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value);

/// @returns 组件
lv_obj_t *urq_widget_get(
    urq_page_t *page, lv_obj_t *parent, urq_widget_conf_t *conf)
    __attribute__((__nonnull__(1, 2)));

/// @brief 获取克隆 widget
/// @param parent 父节点
/// @param conf   组件配置
/// @returns 组件
lv_obj_t *urq_widget_get_no_cache(
    urq_page_t *page, lv_obj_t *parent, urq_widget_conf_t *conf)
    __attribute__((__nonnull__(1, 2)));

/// @brief 创建 widget
/// @param parent 父节点
/// @param type   组件类型
/// @returns 组件
lv_obj_t *urq_widget_create(lv_obj_t *parent, urq_widget_type_t type)
    __attribute__((__nonnull__(1)));

/// @brief 配置组件
void urq_widget_config(
    lv_obj_t *self, urq_widget_conf_t *conf, urq_widget_conf_fn_t fn)
    __attribute__((__nonnull__(1)));

/// @brief 设置组件属性
void urq_widget_set_property(
    lv_obj_t *self, urq_widget_common_conf_t *common_conf, uint8_t property_id,
    uint8_t index, uint16_t value, urq_widget_set_property_fn_t fn)
    __attribute__((__nonnull__(1)));

#ifdef __cplusplus
}
#endif
#endif // URQ__WIDGET_H
