#include "urq/image/widget.h"
#include "lvgl.h"
#include "urq/preload.h"
#include "urq/user_data.h"
#include "urq/util/control.h"
#include "urq/util/event.h"
#include "urq/util/state.h"
#include "urq/widget.h"
#include "urq/widget/conf.h"
#include "urq/widget/type.h"

#define Self urq_image_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_IMAGE_WIDGET_CLASS = {
    .base_class = &lv_img_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    lv_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_image_widget_config, NULL, urq_image_widget_set_state,
        NULL);

    widget->conf = NULL;
    widget->common_conf = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;
    if (widget->user_data.type != URQ_WIDGET_TYPE_CLONE) {
        urq_user_data_free(&widget->user_data);
        if (widget->conf != NULL) {
            urq_image_widget_conf_free_inplace(widget->conf);
            urq_free(widget->conf);
            widget->conf = NULL;
        }
        if (widget->common_conf != NULL) {
            urq_widget_common_conf_free_inplace(widget->common_conf);
            urq_free(widget->common_conf);
            widget->common_conf = NULL;
        }

        // if (lv_img_get_src(widget->img) != NULL) {
        //     lv_mem_free((void *)lv_img_get_src(widget->img));
        //     lv_img_set_src(widget->img, NULL);
        // }
        widget->img = NULL;
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (urq_event_filter_exit(class_p, e)) {
        return;
    }
}

lv_obj_t *urq_image_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj =
        lv_obj_class_create_obj(&URQ_IMAGE_WIDGET_CLASS, (lv_obj_t *)parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

void urq_image_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *widget = (Self *)self;

    widget->conf = mut_conf->config.image;
    widget->common_conf = mut_conf->common_conf;

    urq_widget_config(self, mut_conf, NULL);
}

void urq_image_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    Self *const widget = (Self *)self;
    urq_set_state(self, widget->common_conf, state, platfrom);
}
