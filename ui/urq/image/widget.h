#pragma once

#include "lvgl.h"
#include "urq/image/conf.h"
#include "urq/user_data.h"

#ifndef URQ__IMAGE__WIDGET_H
#define URQ__IMAGE__WIDGET_H
#ifdef __cplusplus
extern "C" {
#endif

// 替换 image_widget
// 替换 IMAGE_WIDGET

typedef struct {
    /// @brief 父组件
    lv_img_t super;
    /// @brief user data 字段
    urq_user_data_t user_data;
    /// @brief 图片
    lv_obj_t *img;
    /// @brief 配置
    urq_image_widget_conf_t *conf;
    /// @brief 通用配置
    urq_widget_common_conf_t *common_conf;
} urq_image_widget_t;

/// @brief 创建 image_widget
/// @param parent 当前组件所属的页面
/// @returns 组件
lv_obj_t *urq_image_widget_create(lv_obj_t *parent)
    __attribute__((__nonnull__(1)));

/// @brief 配置 image_widget
/// @param self     组件自身
/// @param mut_conf  配置的内容
/// @returns 组件
void urq_image_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
    __attribute__((__nonnull__(1, 2)));

/// @brief 设置状态
/// @param self 组件
/// @param state 状态
/// @returns void
void urq_image_widget_set_state(lv_obj_t *self, urq_state_t state, uint8_t platfrom)
    __attribute__((__nonnull__(1)));

#ifdef __cplusplus
}
#endif
#endif // URQ__IMAGE__WIDGET_H
