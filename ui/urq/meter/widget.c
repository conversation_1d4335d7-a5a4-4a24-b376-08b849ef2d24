#include "urq/meter/widget.h"
#include "lvgl.h"
#include "urq/meter/conf.h"
#include "urq/preload.h"
#include "urq/project/project.h"
#include "urq/user_data.h"
#include "urq/util/color.h"
#include "urq/util/event.h"
#include "urq/util/state.h"
#include "urq/util/style.h"
#include "urq/widget.h"
#include "urq/widget/type.h"

#define Self urq_meter_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_METER_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void urq_meter_widget_draw_cb_event(lv_event_t *e)
{
    lv_obj_draw_part_dsc_t *dsc = lv_event_get_draw_part_dsc(e);
    // lv_obj_t *meter = widget->meter;
    lv_obj_t *obj = (lv_obj_t *)lv_event_get_user_data(e);
    Self *const widget = (Self *)obj;

    if (e->code == LV_EVENT_DRAW_MAIN) {
        // 获取仪表中心
        lv_draw_ctx_t *draw_ctx = lv_event_get_draw_ctx(e);
        lv_coord_t center_x = lv_obj_get_x(obj) + lv_obj_get_width(obj) / 2;
        lv_coord_t center_y = lv_obj_get_y(obj) + lv_obj_get_height(obj) / 2;

        urq_set_style_draw_main_point(
            &widget->user_data.project->env, draw_ctx, center_x, center_y,
            widget->conf->pointer_point_config);
    } else if (e->code == LV_EVENT_DRAW_PART_BEGIN) {

        if (dsc->type == LV_METER_DRAW_PART_ARC) {
            // 弧形指示器的半径、宽度、颜色
            // dsc->radius = 100;
            // if (dsc->arc_dsc != NULL) {
            //     dsc->arc_dsc->width = 10;
            // }
        } else if (dsc->type == LV_METER_DRAW_PART_NEEDLE_LINE) {
            if (dsc->line_dsc != NULL) {
                lv_point_t _p2 = *dsc->p2;
                _p2.x += widget->conf->pointer_config.length;
                *((lv_point_t *)dsc->p2) = _p2;
            }
        } else if (dsc->type == LV_METER_DRAW_PART_TICK) {

            if (dsc->line_dsc != NULL) {
                // dsc->line_dsc->color = lv_color_hex(0x0000FF);
                // dsc->radius = 1;
            }
        }
    }
}

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_meter_widget_config, urq_meter_widget_refresh_language,
        urq_meter_widget_set_state, NULL);

    widget->meter = lv_meter_create(obj);
    widget->conf = NULL;
    widget->common_conf = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    urq_meter_widget_t *widget = (urq_meter_widget_t *)obj;
    urq_meter_widget_conf_t *conf = widget->conf;

    if (widget->user_data.type != URQ_WIDGET_TYPE_CLONE) {
        urq_user_data_free(&widget->user_data);

        if (conf != NULL) {
            urq_meter_widget_conf_free_inplace(conf);
            urq_free(conf);
        }
        if (widget->common_conf != NULL) {
            urq_widget_common_conf_free_inplace(widget->common_conf);
            urq_free(widget->common_conf);
        }
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (urq_event_filter_exit(class_p, e)) {
        return;
    }

    Self *widget = (Self *)lv_event_get_target(e);
    urq_widget_common_conf_t *common_conf = widget->common_conf;
    static uint32_t count = 0;

    // 检查是否执行点击事件
    if (urq_event_check_exec_click(e, &count, common_conf)) {
        // exec
        // printf("可执行\n");
    }

    if (e->code == LV_EVENT_CLICKED) {
    }
}

lv_obj_t *urq_meter_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj;

    obj = lv_obj_class_create_obj(&URQ_METER_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

void urq_meter_widget_conf(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *widget = (Self *)self;
    urq_used(mut_conf);
    urq_project_t *project = widget->user_data.project;
    urq_meter_widget_conf_t *conf = widget->conf;

    urq_curve_scale_t scale_conf = conf->scale;

    lv_meter_scale_t *scale = lv_meter_add_scale(widget->meter);

    // 设置副刻度
    lv_meter_set_scale_ticks(
        widget->meter, scale, scale_conf.sub_tick_count,
        scale_conf.sub_tick_width, scale_conf.sub_tick_len,
        urq_get_color(project->env.theme_color, &scale_conf.sub_color));

    // 设置主刻度
    lv_meter_set_scale_major_ticks(
        widget->meter, scale, scale_conf.main_tick_count,
        scale_conf.main_tick_width, scale_conf.main_tick_len,
        urq_get_color(project->env.theme_color, &scale_conf.main_color),
        conf->label_radius);

    // 设置刻度范围
    lv_meter_set_scale_range(
        widget->meter, scale, scale_conf.min_value, scale_conf.max_value,
        conf->end_rangle, conf->start_rangle);

    lv_meter_set_scale_range(
        widget->meter, scale, 0, 120, conf->end_rangle, conf->start_rangle);

    lv_meter_indicator_t *indic = NULL;

    // 设置下限
    indic = lv_meter_add_arc(
        widget->meter, scale, conf->limit_width,
        // urq_get_color(project->env.theme_color, &conf->lower_limit_color),
        lv_color_make(
            conf->lower_limit_color.rgba.ch.r,
            conf->lower_limit_color.rgba.ch.g,
            conf->lower_limit_color.rgba.ch.b),
        conf->limit_radius);
    lv_meter_set_indicator_start_value(widget->meter, indic, 0);
    lv_meter_set_indicator_end_value(widget->meter, indic, conf->lower_limit);

    // 设置中间
    indic = lv_meter_add_arc(
        widget->meter, scale, conf->limit_width,
        // urq_get_color(project->env.theme_color, &conf->middle_color),
        lv_color_make(
            conf->middle_color.rgba.ch.r, conf->middle_color.rgba.ch.g,
            conf->middle_color.rgba.ch.b),
        conf->limit_radius);
    lv_meter_set_indicator_start_value(widget->meter, indic, conf->lower_limit);
    lv_meter_set_indicator_end_value(widget->meter, indic, conf->upper_limit);

    // 设置上限
    indic = lv_meter_add_arc(
        widget->meter, scale, conf->limit_width,
        // urq_get_color(project->env.theme_color, &conf->upper_limit_color),
        lv_color_make(
            conf->upper_limit_color.rgba.ch.r,
            conf->upper_limit_color.rgba.ch.g,
            conf->upper_limit_color.rgba.ch.b),
        conf->limit_radius);
    lv_meter_set_indicator_start_value(widget->meter, indic, conf->upper_limit);
    lv_meter_set_indicator_end_value(
        widget->meter, indic, conf->scale.max_value);

    // 指针
    indic = lv_meter_add_needle_line(
        widget->meter, scale, (uint16_t)(conf->pointer_config.width),
        urq_get_color(project->env.theme_color, &conf->pointer_config.color),
        0);

    lv_meter_set_indicator_value(
        widget->meter, (lv_meter_indicator_t *)indic, 0);

    lv_obj_add_event_cb(
        widget->meter, urq_meter_widget_draw_cb_event, LV_EVENT_ALL, widget);
}

void urq_meter_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;
    widget->conf = mut_conf->config.meter;
    widget->common_conf = mut_conf->common_conf;
    lv_obj_set_size(widget->meter, mut_conf->size.w, mut_conf->size.h);

    urq_widget_config(self, mut_conf, urq_meter_widget_conf);
}

void urq_meter_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    Self *const widget = (Self *)self;
    urq_set_state(self, widget->common_conf, state, platfrom);
}

void urq_meter_widget_refresh_language(lv_obj_t *self)
{
    Self *const widget = (Self *)self;
    urq_user_data_t *udata = urq_obj_get_user_data((lv_obj_t *)widget);

    urq_state_t state = widget->common_conf->stat_property->cur_state;
    urq_set_state_text(
        (lv_obj_t *)udata, self, widget->common_conf->texts, state);
}
