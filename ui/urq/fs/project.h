//! 工程相关的文件处理

#pragma once

#include "urq/fs/file.h"
#include "urq/preload.h"
#include "urq/project/project.h"

#ifndef URQ__FS__PROJECT_H
#define URQ__FS__PROJECT_H
#ifdef __cplusplus
extern "C" {
#endif

// int urq_fs_project_load_theme(urq_project_t *project, urq_fs_file_t
// *out_file)
//     __attribute__((__nonnull__(1, 2)))
//     __attribute__((__warn_unused_result__()));

/// @brief 加载工程配置
/// @param project  工程
/// @param out_file 输出文件
/// @return 0 成功，-1 失败
int urq_fs_project_load_project(
    urq_project_t *project, urq_fs_file_t **out_file)
    __attribute__((__nonnull__(1, 2)))
    __attribute__((__warn_unused_result__()));

int urq_fs_project_load_project_async(urq_project_t *project)
    __attribute__((__nonnull__(1)));

/// @brief 加载标签列表
/// @param project  工程
/// @param id       设备 id
/// @param out_file 输出文件
/// @return 0 成功，-1 失败
int urq_fs_project_load_atags(
    urq_project_t *project, urq_device_id_t id, urq_fs_file_t **out_file)
    __attribute__((__nonnull__(1, 3), __warn_unused_result__()));

int urq_fs_project_load_atags_async(urq_project_t *project, urq_device_id_t id)
    __attribute__((__nonnull__(1)));

/// @brief 加载 device
///
/// @param project  工程
/// @param out_file 输出文件
/// @return 0 成功，-1 失败
int urq_fs_project_load_device(urq_project_t *project, urq_fs_file_t **out_file)
    __attribute__((__nonnull__(1, 2)))
    __attribute__((__warn_unused_result__()));

int urq_fs_project_load_device_async(urq_project_t *project)
    __attribute__((__nonnull__(1)));

/// @brief 加载 display 配置
///
/// @param project  工程
/// @param out_file 输出文件
/// @return 0 成功，-1 失败
int urq_fs_project_load_display(
    urq_project_t *project, urq_fs_file_t **out_file)
    __attribute__((__nonnull__(1, 2)))
    __attribute__((__warn_unused_result__()));

int urq_fs_project_load_display_async(urq_project_t *project)
    __attribute__((__nonnull__(1)));

/// @brief 加载工程语言
/// @param project  工程
/// @param out_file 输出文件
/// @return 0 成功，-1 失败
int urq_fs_project_load_language(
    urq_project_t *project, urq_page_id_t page_id, urq_fs_file_t **out_file)
    __attribute__((__nonnull__(1, 3), __warn_unused_result__()));

int urq_fs_project_load_language_async(
    urq_project_t *project, urq_page_id_t page_id)
    __attribute__((__nonnull__(1)));

/// @brief 加载主题样式
/// @param project  工程
/// @param out_file 输出文件
/// @return 0 成功，-1 失败
int urq_fs_project_load_theme_style(
    urq_project_t *project, urq_fs_file_t **out_file)
    __attribute__((__nonnull__(1, 2)))
    __attribute__((__warn_unused_result__()));

int urq_fs_project_load_theme_style_async(urq_project_t *project)
    __attribute__((__nonnull__(1)));

/// @brief 加载组件列表
/// @param project  工程
/// @param group    组件组
/// @param out_file 输出文件
/// @return 0 成功，-1 失败
int urq_fs_project_load_widget_group(
    urq_project_t *project, int32_t group, urq_fs_file_t **out_file)
    __attribute__((__nonnull__(1, 3)))
    __attribute__((__warn_unused_result__()));

int urq_fs_project_load_widget_group_async(
    urq_project_t *project, int32_t group, uint8_t type)
    __attribute__((__nonnull__(1)));

/// @brief 加载组件属性
/// @param project  工程
/// @param page_id  页面 id
/// @param out_file 输出文件
/// @return 0 成功，-1 失败
int urq_fs_project_load_widget_property(
    urq_project_t *project, urq_page_id_t page_id, urq_fs_file_t **out_file)
    __attribute__((__nonnull__(1, 3), __warn_unused_result__()));

/// @brief 加载组件属性
/// @param project  工程
/// @param page_id  页面 id
/// @return 0 成功，-1 失败
int urq_fs_project_load_widget_property_async(
    urq_project_t *project, urq_page_id_t page_id)
    __attribute__((__nonnull__(1)));

/// @brief 加载字体
/// @param project  工程
/// @param font_id  字体 id
/// @param out_file 输出文件
/// @return 0 成功，-1 失败
lv_font_t *urq_fs_project_load_font(urq_project_t *project, uint8_t font_id)
    __attribute__((__nonnull__(1))) __attribute__((__warn_unused_result__()));

#ifdef __cplusplus
}
#endif
#endif // URQ__FS__PROJECT_H
