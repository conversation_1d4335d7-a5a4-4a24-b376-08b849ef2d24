#include "urq/fs/project.h"
#include "urq/fs/fs.h"
#include "urq/path.h"
#include "urq/preload.h"
#include "urq/req/file.h"

int urq_fs_project_load_atags_async(urq_project_t *project, urq_device_id_t id)
{
    return urq_req_wirte_set_file_id(
        &project->conn, project->fs.project_root_path,
        URQ_PATH_CONF_FOLDER_ATAG, (int)id, 0);
}

int urq_fs_project_load_atags(
    urq_project_t *project, urq_device_id_t id, urq_fs_file_t **out_file)
{
    char path[URQ_PATH_MAX] = {0};
    if (snprintf(
            path, URQ_PATH_MAX, "%s/%d/%d", project->fs.project_root_path,
            URQ_PATH_CONF_FOLDER_ATAG, id) == -1) {
        return -1;
    }

    if (urq_fs_load_file(&project->fs, path)) {
        return -1;
    }

    if (urq_fs_exists(&project->fs, path, NULL)) {
        return -1;
    }

    return urq_fs_read_file(&project->fs, path, out_file);
}

int urq_fs_project_load_device_async(urq_project_t *project)
{
    return urq_req_wirte_set_file_id(
        &project->conn, project->fs.project_root_path,
        URQ_PATH_CONF_FOLDER_DEVICE, URQ_PATH_CONF_DEFAULT_FILE, 0);
}

int urq_fs_project_load_device(urq_project_t *project, urq_fs_file_t **out_file)
{
    char path[URQ_PATH_MAX] = {0};
    if (snprintf(
            path, URQ_PATH_MAX, "%s/%d/%d", project->fs.project_root_path,
            URQ_PATH_CONF_FOLDER_DEVICE, URQ_PATH_CONF_DEFAULT_FILE) == -1) {
        return -1;
    }

    if (urq_fs_load_file(&project->fs, path)) {
        return -1;
    }

    if (urq_fs_exists(&project->fs, path, NULL)) {
        return -1;
    }
    return urq_fs_read_file(&project->fs, path, out_file);
}

int urq_fs_project_load_display_async(urq_project_t *project)
{
    return urq_req_wirte_set_file_id(
        &project->conn, project->fs.project_root_path,
        URQ_PATH_CONF_FOLDER_DISPLAY, URQ_PATH_CONF_DEFAULT_FILE, 0);
}

int urq_fs_project_load_display(
    urq_project_t *project, urq_fs_file_t **out_file)
{
    char path[URQ_PATH_MAX] = {0};
    if (snprintf(
            path, URQ_PATH_MAX, "%s/%d/%d", project->fs.project_root_path,
            URQ_PATH_CONF_FOLDER_DISPLAY, URQ_PATH_CONF_DEFAULT_FILE) == -1) {
        return -1;
    }
    if (urq_fs_load_file(&project->fs, path)) {
        return -1;
    }

    if (urq_fs_exists(&project->fs, path, NULL)) {
        return -1;
    }
    return urq_fs_read_file(&project->fs, path, out_file);
}

int urq_fs_project_load_language_async(
    urq_project_t *project, urq_page_id_t page_id)
{
    return urq_req_wirte_set_file_id(
        &project->conn, project->fs.project_root_path,
        URQ_PATH_CONF_FOLDER_LANGUAGE, page_id, 0);
}

int urq_fs_project_load_language(
    urq_project_t *project, urq_page_id_t page_id, urq_fs_file_t **out_file)
{
    char path[URQ_PATH_MAX] = {0};
    if (snprintf(
            path, URQ_PATH_MAX, "%s/%d/%d", project->fs.project_root_path,
            URQ_PATH_CONF_FOLDER_LANGUAGE, page_id) == -1) {
        return -1;
    }

    if (urq_fs_load_file(&project->fs, path)) {
        return -1;
    }

    if (urq_fs_exists(&project->fs, path, NULL)) {
        return -1;
    }

    return urq_fs_read_file(&project->fs, path, out_file);
}

int urq_fs_project_load_project_async(urq_project_t *project)
{
    return urq_req_wirte_set_file_id(
        &project->conn, project->fs.project_root_path,
        URQ_PATH_CONF_FOLDER_PROJECT, URQ_PATH_CONF_DEFAULT_FILE, 0);
}

int urq_fs_project_load_project(
    urq_project_t *project, urq_fs_file_t **out_file)
{
    char path[URQ_PATH_MAX] = {0};
    if (snprintf(
            path, URQ_PATH_MAX, "%s/%d/%d", project->fs.project_root_path,
            URQ_PATH_CONF_FOLDER_PROJECT, URQ_PATH_CONF_DEFAULT_FILE) == -1) {
        return -1;
    }
    if (urq_fs_load_file(&project->fs, path)) {
        return -1;
    }

    if (urq_fs_exists(&project->fs, path, NULL)) {
        return -1;
    }

    return urq_fs_read_file(&project->fs, path, out_file);
}

int urq_fs_project_load_widget_group_async(
    urq_project_t *project, int32_t group, uint8_t type)
{
    return urq_req_wirte_set_file_id(
        &project->conn, project->fs.project_root_path,
        URQ_PATH_CONF_FOLDER_WIDGETS, group, type);
}

int urq_fs_project_load_widget_group(
    urq_project_t *project, int32_t group, urq_fs_file_t **out_file)
{
    char path[URQ_PATH_MAX] = {0};
    if (snprintf(
            path, URQ_PATH_MAX, "%s/%d/%d", project->fs.project_root_path,
            URQ_PATH_CONF_FOLDER_WIDGETS, group) == -1) {
        return -1;
    }

    if (urq_fs_load_file(&project->fs, path)) {
        return -1;
    }

    if (urq_fs_exists(&project->fs, path, NULL)) {
        return -1;
    }
    return urq_fs_read_file(&project->fs, path, out_file);
}

int urq_fs_project_load_theme_style_async(urq_project_t *project)
{
    return urq_req_wirte_set_file_id(
        &project->conn, project->fs.project_root_path,
        URQ_PATH_CONF_FOLDER_STYLE, URQ_PATH_CONF_DEFAULT_FILE, 0);
}

int urq_fs_project_load_theme_style(
    urq_project_t *project, urq_fs_file_t **out_file)
{
    char path[URQ_PATH_MAX] = {0};
    if (snprintf(
            path, URQ_PATH_MAX, "%s/%d/%d", project->fs.project_root_path,
            URQ_PATH_CONF_FOLDER_STYLE, URQ_PATH_CONF_DEFAULT_FILE) == -1) {
        return -1;
    }

    if (urq_fs_load_file(&project->fs, path)) {
        return -1;
    }

    return urq_fs_read_file(&project->fs, path, out_file);
}

int urq_fs_project_load_widget_property_async(
    urq_project_t *project, urq_page_id_t page_id)
{
    return urq_req_wirte_set_file_id(
        &project->conn, project->fs.project_root_path,
        URQ_PATH_CONF_FOLDER_PAGE_WIDGET_PROPERTY_ADDRESS, page_id, 0);
}

int urq_fs_project_load_widget_property(
    urq_project_t *project, urq_page_id_t page_id, urq_fs_file_t **out_file)
{
    char path[URQ_PATH_MAX] = {0};
    if (snprintf(
            path, URQ_PATH_MAX, "%s/%d/%d", project->fs.project_root_path,
            URQ_PATH_CONF_FOLDER_PAGE_WIDGET_PROPERTY_ADDRESS, page_id) == -1) {
        return -1;
    }

    if (urq_fs_load_file(&project->fs, path)) {
        return -1;
    }

    if (urq_fs_exists(&project->fs, path, NULL)) {
        return -1;
    }

    return urq_fs_read_file(&project->fs, path, out_file);
}

lv_font_t *urq_fs_project_load_font(urq_project_t *project, uint8_t font_id)
{
    char path[URQ_PATH_MAX];
    snprintf(
        path, URQ_PATH_MAX, "%s/%d/%d", project->fs.project_root_path,
        URQ_PATH_CONF_FOLDER_FONT, font_id);
    if (urq_fs_load_file(&project->fs, path)) {
        return NULL;
    }
    if (urq_fs_exists(&project->fs, path, NULL)) {
        return NULL;
    }

    return lv_font_load(path);
}
