#pragma once

#include "urq/page/group_conf.h"
#include "urq/widget/conf_list.h"
#include <stddef.h>
#include <stdint.h>

#ifndef URQ__PAGE__LOAD_H
#define URQ__PAGE__LOAD_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 加载页面属性
/// @param self 页面
/// @returns void
void urq_page_load_property(void *self);

/// @brief 加载页面语言
/// @param self 页面
/// @returns void
void urq_page_load_language(void *self, const uint8_t *data, size_t size);
void urq_page_load_language_callback(void *self);

/// @brief 加载页面语言
/// @param self 页面
/// @returns void
void urq_page_load_widgets(lv_obj_t *page, const uint8_t *data, size_t size);

void urq_page_load_foreach_widget(lv_obj_t *obj, lv_obj_t* parent, urq_widget_conf_list_t *conf);

/// @brief 加载页面回调
/// @param self 页面
/// @returns void
void urq_page_load_widget_callback(void *self);

#ifdef __cplusplus
}
#endif

#endif
