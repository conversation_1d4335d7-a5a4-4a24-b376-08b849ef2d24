#pragma once

#include "klib/khash.h"
#include "urq/page/page.h"
#include "urq/preload.h"

#ifndef URQ__PAGE__MAP_H
#define URQ__PAGE__MAP_H
#ifdef __cplusplus
extern "C" {
#endif

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告

KHASH_MAP_INIT_INT(urq_page_map, urq_page_t *)
#pragma GCC diagnostic pop // 恢复之前的警告状态

typedef khash_t(urq_page_map) urq_page_map_t;

/// @brief 创建 page map
/// @returns page map
static inline urq_page_map_t *urq_page_map_new(void)
    __attribute__((__warn_unused_result__(), __malloc__()));

/// @brief 释放 page map
/// @details 这个方法不会销毁 page，因为通常来说 page 是由父组件管理的
/// @param self page map
/// @returns void
static inline void urq_page_map_free(urq_page_map_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 添加 page
/// @param self page map
/// @param page page
/// @returns 是否成功
static inline int urq_page_map_add(urq_page_map_t *self, urq_page_t *page)
    __attribute__((__nonnull__(1, 2), __warn_unused_result__()));

/// @brief 执行帧回调
/// @param self page map
/// @returns void
static inline void urq_page_map_frame_cb(urq_page_map_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 获取 page
/// @param self page map
/// @param id page id
/// @returns page
static inline urq_page_t *urq_page_map_get(
    urq_page_map_t *self, urq_page_id_t id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

static inline urq_page_map_t *urq_page_map_new(void)
{
    return kh_init_urq_page_map();
}

static inline void urq_page_map_free(urq_page_map_t *self)
{

    kh_destroy_urq_page_map(self);
}

static inline int urq_page_map_add(urq_page_map_t *self, urq_page_t *page)
{
    int ret;
    khiter_t k = kh_put_urq_page_map(self, (khint32_t)page->id, &ret);
    if (ret < 0) {
        return -1;
    }
    kh_val(self, k) = page;
    return 0;
}

static inline void urq_page_map_frame_cb(urq_page_map_t *self)
{
    urq_page_t *page;
    kh_foreach_value(self, page, { urq_page_frame_cb(page); });
}

static inline urq_page_t *urq_page_map_get(
    urq_page_map_t *self, urq_page_id_t id)
{
    khiter_t k = kh_get_urq_page_map(self, (khint32_t)id);
    if (k == kh_end(self)) {
        return NULL;
    }
    return kh_val(self, k);
}
static inline int urq_page_map_has_key(urq_page_map_t *self, urq_page_id_t id)
{
    return kh_get_urq_page_map(self, (khint32_t)id) != kh_end(self);
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__PAGE__MAP_H
