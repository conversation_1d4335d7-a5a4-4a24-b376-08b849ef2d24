#include "urq/page/group_map.h"
#include "urq/errno.h"
#include "urq/page/group_conf.h"
#include "urq/preload.h"

urq_page_group_map_t *urq_page_group_map_new(void)
{
    return kh_init_urq_page_group_map();
}

void urq_page_group_map_free(urq_page_group_map_t *self)
{
    for (khiter_t k = kh_begin(self); k != kh_end(self); ++k) {
        urq_page_group_conf_t *page_group_conf = kh_value(self, k);
        urq_page_group_conf_free_inplace(page_group_conf);
        free(page_group_conf);
    }
    kh_destroy_urq_page_group_map(self);
}

void urq_page_group_map_resize(urq_page_group_map_t *self, uint32_t size)
{
    kh_resize_urq_page_group_map(self, size);
}

int urq_page_group_map_add_ptr(
    urq_page_group_map_t *const self, uint32_t id,
    urq_page_group_conf_t *page_group_conf)
{
    int ret = 0;
    khiter_t k = kh_put_urq_page_group_map(self, (khint32_t)id, &ret);
    if (ret == 1) {
        // 初始化数据
        kh_val(self, k) = page_group_conf;
        return 0;
    } else {
        switch (ret) {
        case -1:
            errno = URQ_ENOMEM;
            return -1;
        case 0:
            errno = URQ_EEXISTS;
            return -1;
        default:
            errno = EINVAL;
            return -1;
        }
    }
}
int urq_page_group_map_add(
    urq_page_group_map_t *const self, uint32_t id,
    urq_page_group_conf_t page_group_conf)
{
    int ret = 0;
    khiter_t k = kh_put_urq_page_group_map(self, (khint32_t)id, &ret);
    if (ret == 1) {
        // 初始化数据
        urq_page_group_conf_t *tmp = urq_malloc(sizeof(urq_page_group_conf_t));
        *tmp = page_group_conf;
        kh_val(self, k) = tmp;
        return 0;
    } else {
        switch (ret) {
        case -1:
            errno = URQ_ENOMEM;
            return -1;
        case 0:
            errno = URQ_EEXISTS;
            return -1;
        default:
            errno = EINVAL;
            return -1;
        }
    }
}

urq_page_group_conf_t *urq_page_group_map_get(
    const urq_page_group_map_t *self, uint32_t id)
{
    khiter_t k = kh_get_urq_page_group_map(self, (khint32_t)id);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
        return NULL;
    }
    return kh_val(self, k);
}

bool urq_page_group_map_has(const urq_page_group_map_t *self, uint32_t id)
{
    khiter_t k = kh_get_urq_page_group_map(self, (khint32_t)id);
    return k != kh_end(self);
}
