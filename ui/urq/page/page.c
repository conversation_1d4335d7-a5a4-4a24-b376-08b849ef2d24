#include "urq/page/page.h"
#include "lvgl.h"
#include "urq/errno.h"
#include "urq/i18n/map.h"
#include "urq/log/debug.h"
#include "urq/page/cb.h"
#include "urq/page/load.h"
#include "urq/preload.h"
#include "urq/user_data.h"

#define Self urq_page_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_PAGE_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(urq_page_t),
};

lv_obj_t *urq_page_create(lv_obj_t *parent)
{
    lv_obj_t *obj = lv_obj_class_create_obj(&URQ_PAGE_CLASS, parent);
    lv_obj_class_init_obj(obj);

    urq_page_t *page = (urq_page_t *)obj;

    if (page->cb_ring == NULL) {
        lv_obj_del(obj);
        return NULL;
    }
    return obj;
}

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    lv_obj_remove_style_all(obj);

    urq_page_t *page = (urq_page_t *)obj;
    page->id = 0;

    // page->i18n_map = NULL;
    page->cb_ring = urq_page_cb_ring_new();
    page->widget_property_map = urq_widget_property_map_new();
    page->widget_map = urq_lvgl_map_new();
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    urq_page_t *page = (urq_page_t *)obj;
    if (page->cb_ring != NULL) {
        urq_page_cb_ring_free(page->cb_ring);
        page->cb_ring = NULL;
    }
    // if (page->i18n_map != NULL) {
    //     urq_i18n_map_free(page->i18n_map);
    //     page->i18n_map = NULL;
    // }
    if (page->widget_property_map != NULL) {
        urq_widget_property_map_free(page->widget_property_map);
        page->widget_property_map = NULL;
    }
    if (page->widget_map != NULL) {
        urq_lvgl_map_free(page->widget_map);
        page->widget_map = NULL;
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (lv_obj_event_base(class_p, e) != LV_RES_OK) {
        return;
    }
    log_v("page event: %s\n", urq_lv_event_code(e->code));
}

void urq_page_frame_cb(urq_page_t *self)
{
    Self *page = (Self *)self;
    log_v("page half frame\n");
    urq_page_cb_ring_exec(page->cb_ring);
}

int urq_page_conf(
    lv_obj_t *self, urq_project_t *project, const urq_page_conf_t *mv_conf)
{
    Self *page = (Self *)self;
    if (page->id != 0) {
        return -1;
    }
    log_d("conf page: %d\n", mv_conf->id);

    page->id = mv_conf->id;
    page->project = project;
    page->fs = &project->fs;
    page->type = mv_conf->type;

    // async
    // urq_fs_project_load_language_async(page->project, page->id);
    // urq_fs_project_load_widget_group_async(
    //     page->project, page->id, (uint8_t)mv_conf->type);

    // if (urq_page_add_frame_cb1(
    //         page, urq_page_load_language_callback, urq_noop1, self)) {
    //     log_w("add page frame callback error\n");
    //     return -1;
    // }
    lv_obj_clear_flag(self, LV_OBJ_FLAG_CLICKABLE);

    if (urq_page_add_frame_cb1(
            page, urq_page_load_widget_callback, urq_noop1, self)) {
        log_w("add page frame callback error\n");
        return -1;
    }

    return 0;
}

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM

void urq_page_refresh_language(lv_obj_t *self)
{
    Self *page = (Self *)self;

    khiter_t k = kh_begin(page->widget_map);
    khiter_t end = kh_end(page->widget_map);
    for (; k != end; ++k) {
        if (kh_exist(page->widget_map, k)) {
            lv_obj_t *widget = kh_value(page->widget_map, k);
            urq_user_data_t *user_data = urq_obj_get_user_data(widget);
            if (user_data != NULL && user_data->refresh_language != NULL) {
                user_data->refresh_language(widget);
            }
        }
    }
}

void urq_page_set_state(lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    Self *page = (Self *)self;

    khiter_t k = kh_begin(page->widget_map);
    khiter_t end = kh_end(page->widget_map);
    for (; k != end; ++k) {
        if (kh_exist(page->widget_map, k)) {
            lv_obj_t *widget = kh_value(page->widget_map, k);
            urq_user_data_t *user_data = urq_obj_get_user_data(widget);
            if (user_data != NULL && user_data->set_state != NULL) {
                user_data->set_state(widget, state, platfrom);
            }
        }
    }
}

#endif
