#pragma once

#include "lvgl.h"
#include "urq/compile.h"
#include "urq/conf/map/lvgl.h"
#include "urq/fs/fs.h"
#include "urq/i18n/map.h"
#include "urq/page/cb.h"
#include "urq/page/conf.h"
#include "urq/page/win_cb.h"
#include "urq/preload.h"
#include "urq/project/project.h"
#include "urq/widget/map_property.h"

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
#include "urq/widget/state.h"
#endif

#ifndef URQ__PAGE__PAGE_H
#define URQ__PAGE__PAGE_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 页面
typedef struct _urq_page_t {
    /// @brief 继承自 lv_obj_t
    lv_obj_t super;
    /// @brief 画面的唯一 id
    urq_page_id_t id;
    /// @brief 画面类型
    urq_page_type_t type;
    /// @brief 所属工程
    urq_project_t *project;
    /// @brief 文件系统
    urq_fs_t *fs;
    /// @brief 画面相关的回调
    urq_page_cb_ring_t *cb_ring;
    /// @brief 当前画面属性表
    urq_widget_property_map_t *widget_property_map;
    /// @brief 当前画面的组件表
    urq_lvgl_map_t *widget_map;
    /// @brief 多语言文本的表，由 i18n id 映射到文本
    // urq_i18n_map_t *i18n_map;
    /// @brief 页面弹窗回调
    struct {
        void *arg1;
        urq_page_message_win_cb_t cb;
    } message_win_cb;
} urq_page_t;

/// @brief 创建 page 组件
/// @param parent  父组件
/// @returns 组件，如果创建失败将会返回 NULL
lv_obj_t *urq_page_create(lv_obj_t *parent)
    __attribute__((__nonnull__(1), __warn_unused_result__(), __malloc__()));

/// @brief 帧事件
/// @param self 组件自身
/// @returns void
void urq_page_frame_cb(urq_page_t *self) __attribute__((__nonnull__(1)));

/// @brief 配置画面
/// @param self     组件自身
/// @param project  所属工程
/// @param mv_conf 画面配置
/// @returns 配置是否成功
///
/// 这个方法只能被调用一次，再次调用将返回错误
int urq_page_conf(
    lv_obj_t *self, urq_project_t *project, const urq_page_conf_t *mv_conf)
    __attribute__((__nonnull__(1, 2, 3)));

/// @brief 添加帧回调
/// @param page 组件自身
/// @param cb   回调
/// @returns 是否成功
static inline int urq_page_add_frame_cb0(urq_page_t *page, urq_cb0_t cb)
    __attribute__((__nonnull__(1, 2)))
    __attribute__((__warn_unused_result__()));

/// @brief 添加帧回调
/// @param page 组件自身
/// @param cb   回调
/// @param free 释放回调参数的方法
/// @param arg1 回调的参数1
/// @returns 是否成功
static inline int urq_page_add_frame_cb1(
    urq_page_t *page, urq_cb1_t cb, urq_cb1_t free, void *arg1)
    __attribute__((__nonnull__(1, 2, 3)))
    __attribute__((__warn_unused_result__()));

/// @brief 添加帧回调
/// @param page 组件自身
/// @param cb   回调
/// @param free 释放回调参数的方法
/// @param arg1 回调的参数1
/// @param arg2 回调的参数2
/// @returns 是否成功
static inline int urq_page_add_frame_cb2(
    urq_page_t *page, urq_cb2_t cb, urq_cb2_t free, void *arg1, void *arg2)
    __attribute__((__nonnull__(1, 2, 3)))
    __attribute__((__warn_unused_result__()));

/// @brief 添加帧回调
/// @param page 组件自身
/// @param cb   回调
/// @param free 释放回调参数的方法
/// @param arg1 回调的参数1
/// @param arg2 回调的参数2
/// @param arg3 回调的参数3
/// @returns 是否成功
static inline int urq_page_add_frame_cb3(
    urq_page_t *page, urq_cb3_t cb, urq_cb3_t free, void *arg1, void *arg2,
    void *arg3) __attribute__((__nonnull__(1, 2, 3)))
__attribute__((__warn_unused_result__()));

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
/// @brief 设置页面状态
/// @param self 页面
/// @param state 状态
void urq_page_set_state(lv_obj_t *self, urq_state_t state, uint8_t platfrom)
    __attribute__((__nonnull__(1)));

/// @brief 刷新页面语言
/// @param self 页面
void urq_page_refresh_language(lv_obj_t *self) __attribute__((__nonnull__(1)));
#endif

// impl

static inline int urq_page_add_frame_cb0(urq_page_t *page, urq_cb0_t cb)
{
    return urq_page_cb_ring_add0(page->cb_ring, cb);
}

static inline int urq_page_add_frame_cb1(
    urq_page_t *page, urq_cb1_t cb, urq_cb1_t free, void *arg1)
{
    return urq_page_cb_ring_add1(page->cb_ring, cb, free, arg1);
}

static inline int urq_page_add_frame_cb2(
    urq_page_t *page, urq_cb2_t cb, urq_cb2_t free, void *arg1, void *arg2)
{
    return urq_page_cb_ring_add2(page->cb_ring, cb, free, arg1, arg2);
}

static inline int urq_page_add_frame_cb3(
    urq_page_t *page, urq_cb3_t cb, urq_cb3_t free, void *arg1, void *arg2,
    void *arg3)
{
    return urq_page_cb_ring_add3(page->cb_ring, cb, free, arg1, arg2, arg3);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__PAGE__PAGE_H
