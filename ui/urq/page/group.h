#pragma once

#include "urq/keyboard/trigger_data.h"
#include "urq/page/group_conf.h"
#include "urq/page/map.h"
#include "urq/preload.h"
#include "urq/project/project.h"
#include <lvgl.h>

#ifndef URQ__PAGE__GROUP_H
#define URQ__PAGE__GROUP_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct urq_page_group_t {
    lv_obj_t super;
    /// @brief 页面 id
    urq_page_id_t id;
    /// @brief 画面编号
    urq_page_id_t page_no;
    /// @brief 文件系统
    urq_fs_t *fs;
    /// @brief 所属工程
    urq_project_t *project;
    /// @brief 画面列表
    urq_page_map_t *pages;
    /// @brief 样式
    urq_style_t style;
    /// @brief 触发事件的图元
    urq_keyboard_trigger_data_t *trigger_data;
} urq_page_group_t;

/// @brief 创建 page group 组件
/// @param parent  父组件
/// @returns 组件，如果创建失败将会返回 NULL
lv_obj_t *urq_page_group_create(lv_obj_t *parent)
    __attribute__((__nonnull__(1), __warn_unused_result__(), __malloc__()));

/// @brief 关闭页面
/// @param self 组件自身
void urq_page_group_close(lv_obj_t *self);

/// @brief 配置组
/// @param self     组件自身
/// @param project  所属工程
/// @param mv_conf 画面配置
/// @returns 配置是否成功
int urq_page_group_conf(
    lv_obj_t *self, urq_project_t *project, urq_page_group_conf_t *mv_conf)
    __attribute__((__nonnull__(1, 2, 3)));

/// @brief 帧回调
/// @param self 组件自身
/// @returns void
static inline void urq_page_group_frame_cb(lv_obj_t *self)
    __attribute__((__nonnull__(1)));

// impl

static inline void urq_page_group_frame_cb(lv_obj_t *self)
{
    urq_page_group_t *group = (urq_page_group_t *)self;
    urq_page_map_frame_cb(group->pages);
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__PAGE__GROUP_H
