#include "urq/keyboard/widget.h"
#include "lvgl.h"
#include "urq/keyboard/default_keyboard.h"
#include "urq/user_data.h"
#include "urq/util/event.h"
#include "urq/widget.h"
#include "urq/widget/property.h"
#include "urq/widget/type.h"

/// @brief 用户自定义键盘值
static const char *urq_keyboard_user_value[128];
/// @brief 用户自定义键盘控制
static lv_btnmatrix_ctrl_t urq_keyboard_user_ctrl[128];

#define Self urq_keyboard_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_KEYBOARD_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_keyboard_widget_config, NULL, NULL,
        urq_keyboard_widget_set_property);

    widget->keyboard = lv_keyboard_create(obj);
    widget->conf = NULL;
    widget->common_conf = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;

    if (widget->user_data.type != URQ_WIDGET_TYPE_CLONE) {
        urq_user_data_free(&widget->user_data);
        if (widget->conf != NULL) {
            urq_keyboard_widget_conf_free_inplace(widget->conf);
            urq_free(widget->conf);
        }
        if (widget->common_conf != NULL) {
            urq_widget_common_conf_free_inplace(widget->common_conf);
            urq_free(widget->common_conf);
        }
        widget->keyboard = NULL;
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{

    if (urq_event_filter_exit(class_p, e)) {
        return;
    }
    Self *widget = (Self *)lv_event_get_target(e);
    urq_widget_common_conf_t *common_conf = widget->common_conf;
    // urq_project_t *const project = widget->user_data.project;
    static uint32_t count = 0;
    // 检查是否执行点击事件
    if (urq_event_check_exec_click(e, &count, common_conf)) {
        // exec
        // printf("可执行\n");
    }
}

lv_obj_t *urq_keyboard_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj = lv_obj_class_create_obj(&URQ_KEYBOARD_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

static void urq_keyboard_widget_conf(
    lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *widget = (Self *)self;
    urq_keyboard_widget_conf_t *conf = widget->conf;

    lv_obj_set_size(widget->keyboard, mut_conf->size.w, mut_conf->size.h);

    lv_obj_remove_event_cb(widget->keyboard, lv_keyboard_def_event_cb);
    lv_obj_add_event_cb(
        widget->keyboard, urq_event_keyboard_input, LV_EVENT_ALL, self);

    if (conf->type == URQ_KEYBOARD_LAYOUT_LOWER_LETTER) {
        lv_keyboard_set_mode(widget->keyboard, LV_KEYBOARD_MODE_TEXT_LOWER);
        lv_keyboard_set_map(
            widget->keyboard, LV_KEYBOARD_MODE_TEXT_LOWER,
            urq_keyboard_lower_letter, urq_keyboard_lower_letter_ctrl);
    } else if (conf->type == URQ_KEYBOARD_LAYOUT_UPPER_LETTER) {
        lv_keyboard_set_mode(widget->keyboard, LV_KEYBOARD_MODE_TEXT_UPPER);
        lv_keyboard_set_map(
            widget->keyboard, LV_KEYBOARD_MODE_TEXT_UPPER,
            urq_keyboard_upper_letter, urq_keyboard_lower_letter_ctrl);
    } else if (conf->type == URQ_KEYBOARD_LAYOUT_NUMBER) {
        lv_keyboard_set_mode(widget->keyboard, LV_KEYBOARD_MODE_NUMBER);
        lv_keyboard_set_map(
            widget->keyboard, LV_KEYBOARD_MODE_NUMBER, urq_keyboard_number,
            urq_keyboard_number_ctrl);
    } else if (conf->type == URQ_KEYBOARD_LAYOUT_SPECIAL_CHARACTER) {
        lv_keyboard_set_mode(widget->keyboard, LV_KEYBOARD_MODE_SPECIAL);
        // lv_keyboard_set_map(
        //     widget->keyboard, LV_KEYBOARD_MODE_SPECIAL, urq_keyboard_symbol,
        //     urq_keyboard_symbol_ctrl);
    } else {
        lv_keyboard_set_mode(widget->keyboard, LV_KEYBOARD_MODE_USER_1);

        for (size_t i = 0; i < conf->ctrl->size; i++) {
            urq_keyboard_user_value[i] = conf->value->data[i];
            urq_keyboard_user_ctrl[i] = conf->ctrl->keys[i];
        }

        lv_keyboard_set_map(
            widget->keyboard, LV_KEYBOARD_MODE_USER_1, urq_keyboard_user_value,
            urq_keyboard_user_ctrl);
    }
}

void urq_keyboard_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;

    widget->conf = mut_conf->config.keyboard;
    widget->common_conf = mut_conf->common_conf;

    urq_widget_config(self, mut_conf, urq_keyboard_widget_conf);
}

void urq_keyboard_widget_set_property_callback(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    urq_used(self);
    urq_used(index);
    urq_used(value);
    switch (property_id) {
    // case URQ_PROPERTY_ID_TEXT:
    //     lv_label_set_text(widget->label, (const char *)value);
    //     break;
    default:
        break;
    }
}

void urq_keyboard_widget_set_property(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    urq_keyboard_widget_t *const widget = (urq_keyboard_widget_t *)self;
    urq_widget_set_property(
        self, widget->common_conf, property_id, index, value,
        urq_keyboard_widget_set_property_callback);
}
