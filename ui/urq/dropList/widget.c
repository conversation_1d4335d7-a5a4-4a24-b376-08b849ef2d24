#include "urq/dropList/widget.h"
#include "lvgl.h"
#include "urq/page.h"
#include "urq/preload.h"
#include "urq/project/project.h"
#include "urq/req/action.h"
#include "urq/user_data.h"
#include "urq/util/control.h"
#include "urq/util/event.h"
#include "urq/util/font.h"
#include "urq/util/state.h"
#include "urq/util/style.h"
#include "urq/widget.h"
#include "urq/widget/property.h"
#include "urq/widget/type.h"
#include <lvgl/src/core/lv_obj.h>
#include <lvgl/src/widgets/lv_dropdown.h>

#define Self urq_drop_list_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_DROP_LIST_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);

    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_drop_list_widget_config, NULL,
        urq_drop_list_widget_set_state, urq_drop_list_widget_set_property);

    widget->dropdown = lv_dropdown_create(obj);
    widget->conf = NULL;
    widget->common_conf = NULL;
    memset(widget->cur_value, 0, sizeof(widget->cur_value));
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;

    if (widget->user_data.type != URQ_WIDGET_TYPE_CLONE) {
        urq_user_data_free(&widget->user_data);
        if (widget->conf != NULL) {
            urq_dropList_widget_conf_free_inplace(widget->conf);
            urq_free(widget->conf);
            widget->conf = NULL;
        }
        if (widget->common_conf != NULL) {
            urq_widget_common_conf_free_inplace(widget->common_conf);
            urq_free(widget->common_conf);
            widget->common_conf = NULL;
        }
        widget->dropdown = NULL;
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (urq_event_filter_exit(class_p, e)) {
        return;
    }

    Self *widget = (Self *)lv_event_get_target(e);
    urq_widget_common_conf_t *common_conf = widget->common_conf;
    urq_page_t *const page = widget->user_data.page;
    static uint32_t count = 0;
    // 检查是否执行点击事件
    if (e->code == LV_EVENT_VALUE_CHANGED) {
        lv_dropdown_get_selected_str(
            widget->dropdown, widget->cur_value, sizeof(widget->cur_value));
        printf("cur_value: %s\n", widget->cur_value);
    }

    if (urq_event_check_exec_click(e, &count, common_conf)) {
        // exec
        // printf("可执行\n");
    }

    if (common_conf != NULL && common_conf->has_action) {
        if (urq_page_add_frame_cb3(
                page, urq_req_wirte_set_action, urq_noop3, (void *)page,
                (void *)(intptr_t)common_conf->id, (void *)(intptr_t)e->code)) {
            return;
        }
    }
}

static void urq_drop_list_widget_set_cur_value(lv_event_t *e)
{
    Self *widget = (Self *)lv_event_get_user_data(e);
    if (e->code == LV_EVENT_VALUE_CHANGED) {
        lv_dropdown_get_selected_str(
            widget->dropdown, widget->cur_value, sizeof(widget->cur_value));
        printf("cur_value: %s\n", widget->cur_value);
        if (widget->conf->always_open) {
            lv_dropdown_open(widget->dropdown);
        }
    }
}

lv_obj_t *urq_drop_list_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj =
        lv_obj_class_create_obj(&URQ_DROP_LIST_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

static void urq_drop_list_widget_conf(
    lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *widget = (Self *)self;
    urq_dropList_widget_conf_t *conf = widget->conf;
    urq_project_t *project = widget->user_data.project;
    urq_used(mut_conf);

    lv_obj_set_size(widget->dropdown, mut_conf->size.w, mut_conf->size.h);

    lv_obj_add_flag(widget->dropdown, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_clear_flag(
        widget->dropdown, LV_OBJ_FLAG_CLICK_FOCUSABLE); // 移除点击聚焦标志

    lv_dropdown_set_dir(widget->dropdown, widget->conf->direction);

    lv_dropdown_set_options(widget->dropdown, conf->conf.options);
    // lv_dropdown_set_text(widget->dropdown, ""); // 将显示文本设置为空

    // if (conf->conf.font != NULL)
    //     urq_set_font(self, conf->conf.font);

    lv_obj_set_style_text_line_space(
        self, conf->conf.row_spacing, LV_PART_MAIN);

    lv_obj_t *dp_list = lv_dropdown_get_list(widget->dropdown);
    lv_dropdown_set_selected_highlight(widget->dropdown, false);
    urq_set_style_child_bg(
        dp_list, project, conf->conf.selected_color, LV_PART_SELECTED);
    urq_set_style_child_bg(dp_list, project, conf->list_bg_color, LV_PART_MAIN);

    lv_dropdown_set_symbol(
        widget->dropdown, (const void *)conf->droplist_symbol);

    lv_obj_add_event_cb(
        widget->dropdown, urq_drop_list_widget_set_cur_value,
        LV_EVENT_VALUE_CHANGED, widget);
}

void urq_drop_list_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;

    widget->conf = mut_conf->config.drop_list;
    widget->common_conf = mut_conf->common_conf;

    urq_widget_config(self, mut_conf, urq_drop_list_widget_conf);
}

void urq_drop_list_widget_set_property_callback(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    urq_used(self);
    urq_used(index);
    urq_used(value);
    switch (property_id) {
    // case URQ_PROPERTY_ID_TEXT:
    //     lv_label_set_text(widget->label, (const char *)value);
    //     break;
    default:
        break;
    }
}

void urq_drop_list_widget_set_property(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    Self *const widget = (Self *)self;
    urq_widget_set_property(
        self, widget->common_conf, property_id, index, value,
        urq_drop_list_widget_set_property_callback);
}

void urq_drop_list_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    Self *const widget = (Self *)self;
    urq_set_state(self, widget->common_conf, state, platfrom);
}
