#include "urq/widget/create_children.h"
#include "lvgl/src/core/lv_obj.h"
#include "urq/log/verbose.h"
#include "urq/page/page.h"
#include "urq/project/project.h"
#include "urq/user_data.h"
#include "urq/widget.h"
#include "urq/widget/conf.h"

void urq_widget_create_children(
    lv_obj_t *parent, urq_project_t *project, urq_page_t *page,
    urq_widget_conf_list_t *list)
{
    lv_obj_t *obj;
    urq_widget_conf_t *child_conf;
    urq_user_data_t *user_data;

    for (size_t i = 0; i < list->size; i++) {
        child_conf = list->data[i];
        log_d(
            "create child, i: %ld type: %s\n", i,
            urq_widget_type_to_string(child_conf->type));
        obj = urq_widget_create(parent, child_conf->type);
        user_data = urq_obj_get_user_data(obj);
        user_data->project = project;
        user_data->page = page;
        user_data->conf(obj, child_conf);
    }
}
