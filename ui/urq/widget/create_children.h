#pragma once

#include "lvgl.h"
#include "urq/page/page.h"
#include "urq/project/project.h"
#include "urq/widget/conf_list.h"

#ifndef URQ__WIDGET__CREATE_CHILDREN_H
#define URQ__WIDGET__CREATE_CHILDREN_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 创建子节点列表
/// @param parent  父节点
/// @param project 所属工程
/// @param page    所属画面
/// @param list    配置列表
/// @returns void
void urq_widget_create_children(
    lv_obj_t *parent, urq_project_t *project, urq_page_t *page,
    urq_widget_conf_list_t *list) __attribute__((__nonnull__(1, 2, 3, 4)));

#ifdef __cplusplus
}
#endif
#endif // URQ__WIDGET__CREATE_CHILDREN_H
