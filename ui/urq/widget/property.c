#include "urq/widget/property.h"
#include "lvgl.h"
#include "urq/log/debug.h"
#include "urq/preload.h"
#include "urq/user_data.h"

int urq_deal_property(
    lv_obj_t *obj, urq_widget_common_conf_t *conf, urq_property_id_t type,
    uint8_t index, uint64_t value)
{
    urq_used(index);
    urq_used(conf);
    // urq_action_t *action = conf->actions;
    switch (type) {
    case URQ_PROPERTY_ID_LOCATION_LEFT: {
        lv_obj_set_x(obj, (lv_coord_t)value);
        break;
    }
    case URQ_PROPERTY_ID_LOCATION_TOP: {
        lv_obj_set_y(obj, (lv_coord_t)value);
        break;
    }
    case URQ_PROPERTY_ID_SIZE_WIDTH: {
        lv_obj_set_width(obj, (lv_coord_t)value);
        break;
    }
    case URQ_PROPERTY_ID_SIZE_HEIGHT: {
        lv_obj_set_height(obj, (lv_coord_t)value);
    }
    case URQ_PROPERTY_ID_ENABLE_CONTROL_COMPARE_LEFT_VALUE: {
        //        action->enable_control_compare_left_value = value;
        break;
    }
    case URQ_PROPERTY_ID_ENABLE_CONTROL_COMPARE_RIGHT_VALUE1: {
        //        action->enable_control_compare_right_value1 = value;
        break;
    }
    case URQ_PROPERTY_ID_ENABLE_CONTROL_COMPARE_RIGHT_VALUE2: {
        //        action->enable_control_compare_right_value2 = value;
        break;
    }
    case URQ_PROPERTY_ID_STATE_VALUE: {
        // 状态值
        break;
    }
    case URQ_PROPERTY_ID_DISPLAY_VALUE: {
        // 显示值
        break;
    }
    case URQ_PROPERTY_ID_ACTION_PUSH_MQTT_TOPIC: {
        // 推送MQTT主题
        break;
    }
    case URQ_PROPERTY_ID_ACTION_PUSH_MQTT_MESSAGE: {
        // 推送MQTT消息
        break;
    }
    case URQ_PROPERTY_ID_ACTION_HTTP_API: {
        // 推送HTTP API
        break;
    }
    case URQ_PROPERTY_ID_CONFIRM_PAGE_ID: {
        // 确认页面ID
        break;
    }
    case URQ_PROPERTY_ID_PRESS_SOUND_ID: {
        // 按下声音ID
        break;
    }
    case URQ_PROPERTY_ID_STYLE_BACKGROUND_COLOR: {
        // 背景颜色
        break;
    }
    case URQ_PROPERTY_ID_ACTION_PAGE_ID: {
        // 页面ID
        break;
    }
    case URQ_PROPERTY_ID_ACTION_BIT_WRITE_ADDRESS: {
        // 位写地址
        break;
    }
    case URQ_PROPERTY_ID_ACTION_BIT_PULSE_WIDTH: {
        // 位脉冲宽度
        break;
    }
    case URQ_PROPERTY_ID_ACTION_WORD_WRITE_ADDRESS: {
        // 字写地址
        break;
    }
    case URQ_PROPERTY_ID_ACTION_WORD_WRITE_VALUE: {
        // 字写值
        break;
    }
    case URQ_PROPERTY_ID_ACTION_WORD_MAX_VALUE: {
        // 字最大值
        break;
    }
    case URQ_PROPERTY_ID_ACTION_WORD_MIN_VALUE: {
        // 字最小值
        break;
    }
    case URQ_PROPERTY_ID_ACTION_SCREENSHOT_PAGE_ID: {
        // 截图页面ID
        break;
    }
    case URQ_PROPERTY_ID_ACTION_SCREENSHOT_SAVE_PATH: {
        // 截图保存路径
        break;
    }
    case URQ_PROPERTY_ID_ACTION_SCREENSHOT_FILE_NAME: {
        // 截图文件名
        break;
    }
    case URQ_PROPERTY_ID_ACTION_COPY_DATA_SOURCE_ADDRESS: {
        // 复制数据源地址
        break;
    }
    case URQ_PROPERTY_ID_ACTION_COPY_DATA_TARGET_ADDRESS: {
        // 复制数据目标地址
        break;
    }
    case URQ_PROPERTY_ID_ACTION_COPY_DATA_LENGTH: {
        // 复制数据长度
        break;
    }
    case URQ_PROPERTY_ID_ACTION_PLAY_SOUND_SOUND_ID: {
        // 播放声音ID
        break;
    }
    case URQ_PROPERTY_ID_ACTION_PLAY_SOUND_DURATION: {
        // 播放声音时长
        break;
    }
    case URQ_PROPERTY_ID_ACTION_DELAY_MILLISECONDS: {
        // 延迟毫秒
        break;
    }
    case URQ_PROPERTY_ID_ACTION_LOG_TEXT: {
        // 日志文本
        break;
    }
    case URQ_PROPERTY_ID_ACTION_INPUT_WRITE_ADDRESS: {
        // 输入写地址
        break;
    }
    case URQ_PROPERTY_ID_ACTION_INPUT_MAX_VALUE: {
        // 输入最大值
        break;
    }
    case URQ_PROPERTY_ID_ACTION_INPUT_MIN_VALUE: {
        // 输入最小值
        break;
    }
    case URQ_PROPERTY_ID_ACTION_INPUT_MIN_LENGTH: {
        // 输入最小长度
        break;
    }
    case URQ_PROPERTY_ID_ACTION_INPUT_MAX_LENGTH: {
        // 输入最大长度
        break;
    }

    default:
        log_e("unsupported property id, id: %d\n", type);
        return -1;
    }

    return 0;
}
