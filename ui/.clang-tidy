# https://clang.llvm.org/extra/clang-tidy/

Checks: "-*,readability-identifier-naming"
WarningsAsErrors: ""
HeaderFilterRegex: ""
FormatStyle: none
User: ""
CheckOptions:
    # 函数名
    - key: readability-identifier-naming.FunctionPrefix
      value: "urq_"
    - key: readability-identifier-naming.FunctionCase
      value: lower_case
    # 忽略以 _ 开头的函数
    - key: readability-identifier-naming.FunctionIgnoredRegexp
      value: ^_.+

    # 全局 const 变量
    - key: readability-identifier-naming.GlobalConstantCase
      value: UPPER_CASE
    - key: readability-identifier-naming.GlobalConstantPrefix
      value: "URQ_"

    - key: readability-identifier-naming.StaticConstantCase
      value: UPPER_CASE
    - key: readability-identifier-naming.StaticConstantPrefix
      value: "URQ_"
    - key: readability-identifier-naming.StaticConstantIgnoredRegexp
      value: ^_.+

    - key: readability-identifier-naming.ParameterCase
      value: lower_case

    - key: readability-identifier-naming.StructPrefix
      value: "urq_"
    - key: readability-identifier-naming.StructCase
      value: lower_case
    - key: readability-identifier-naming.StructIgnoredRegexp
      value: ^_.+

    # typedef
    - key: readability-identifier-naming.TypedefCase
      value: lower_case
    - key: readability-identifier-naming.TypedefPrefix
      value: "urq_"
    - key: readability-identifier-naming.TypedefSuffix
      value: _t
    - key: readability-identifier-naming.TypedefIgnoredRegexp
      value: ^_.+
