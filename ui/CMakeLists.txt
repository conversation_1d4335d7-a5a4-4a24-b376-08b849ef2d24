cmake_minimum_required(VERSION 3.22)

if(NOT URQ_ROOT_PROJECT)
    message(STATUS "urq_ui  build as top level project")
    project("urq_ui" VERSION 0.0.1)
else()
    message(STATUS "urq_ui  build as sub project")
endif()

# set(C_STANDARD 11)
# include("${CMAKE_CURRENT_LIST_DIR}/../cmake/common.cmake")

file(GLOB_RECURSE urq_ui_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/*.c" "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp")

# 忽略 linux 平台特定的 .c 文件
list(FILTER urq_ui_SOURCES EXCLUDE REGEX ".+\\.[a-z]+\\.c$")

set(URQ_CURRENT_BUILD urq_ui)
add_executable(${URQ_CURRENT_BUILD} ${urq_ui_SOURCES})
set_property(TARGET ${URQ_CURRENT_BUILD} PROPERTY OUTPUT_NAME "urq-ui")
if (URQ_BUILD_PLATFORM STREQUAL "wasm")
    set_property(TARGET ${URQ_CURRENT_BUILD} PROPERTY OUTPUT_NAME "editor.js")
endif()
# target_compile_features(${URQ_CURRENT_BUILD} PUBLIC c_std_11)

target_link_libraries(${URQ_CURRENT_BUILD} urq_core)
target_link_libraries(${URQ_CURRENT_BUILD} urq_lib)
target_link_libraries(${URQ_CURRENT_BUILD} urq_parse)
target_include_directories(${URQ_CURRENT_BUILD} PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})

set(lvgl_DIR "${CMAKE_PREFIX_PATH}/lvgl")
find_package(lvgl 8.4 REQUIRED)
target_include_directories(${URQ_CURRENT_BUILD} PUBLIC "${lvgl_INCLUDE_DIRS}")
target_link_libraries(${URQ_CURRENT_BUILD} ${lvgl_LIBRARIES})


if(${URQ_BUILD_PLATFORM} STREQUAL "linux_64")
    # set(urq_rs_DIR "${CMAKE_PREFIX_PATH}/urq_rs")
    # find_package(urq_rs REQUIRED)
    # target_include_directories(${URQ_CURRENT_BUILD} PUBLIC "${urq_rs_INCLUDE_DIRS}")
    # target_link_libraries(${URQ_CURRENT_BUILD} ${urq_rs_LIBRARIES})

    set(lv_drivers_DIR "${CMAKE_PREFIX_PATH}/lv_drivers")
    find_package(lv_drivers  REQUIRED)
    target_include_directories(${URQ_CURRENT_BUILD} PUBLIC "${lv_drivers_INCLUDE_DIRS}")
    target_link_libraries(${URQ_CURRENT_BUILD} ${lv_drivers_LIBRARIES})

    set(SDL2_DIR "${CMAKE_PREFIX_PATH}/SDL2")
    find_package(SDL2 2.0.20 REQUIRED)
    target_include_directories(${URQ_CURRENT_BUILD} PUBLIC "${SDL2_INCLUDE_DIRS}")
    target_link_libraries(${URQ_CURRENT_BUILD} ${SDL2_LIBRARIES})
endif()


if(${URQ_BUILD_PLATFORM} STREQUAL "wasm")
    set(protobuf_DIR "${CMAKE_PREFIX_PATH}/protobuf")
    find_package(protobuf REQUIRED)
    target_link_libraries(${URQ_CURRENT_BUILD} ${protobuf_LIBRARIES})
endif()

if(${URQ_BUILD_PLATFORM} STREQUAL "arm_v7hf")
    target_link_libraries(${URQ_CURRENT_BUILD} pthread dl)
endif()
