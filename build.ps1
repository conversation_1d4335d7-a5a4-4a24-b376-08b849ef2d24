# defs/build.ps1
param (
    [Parameter(Position=0)]
    [string]$mode = "all" # 默认执行所有操作
)

# 定义函数：执行proto生成
function Execute-Proto {
    # Write-Host "Checking proto compatibility..."
    # $breakingResult = & buf breaking --against '.git#branch=HEAD' 2>&1
    # if ($LASTEXITCODE -ne 0) {
    #     Write-Host "Proto compatibility check failed:"
    #     Write-Host $breakingResult
    #     Write-Host "Exiting due to breaking changes detected."
    #     exit 1
    # }
    # Write-Host "Proto compatibility check passed."
    Write-Host "Executing buf generate..."

    $result = & buf generate 2>&1
    if ($LASTEXITCODE -ne 0 -or $result) {
        Write-Host "Error executing buf generate:"
        Write-Host $result
        exit 1
    }

    Write-Host "buf generate executed successfully, copying files..."

    # Create the destination directory if it doesn't exist
    $dstDir = "../zd-station/src/defs"
    if (-not (Test-Path $dstDir)) {
        New-Item -Path $dstDir -ItemType Directory -Force | Out-Null
    }

    # Copy all proto files to the destination
    Copy-Item -Path "./gen/ts/znd" -Destination $dstDir -Recurse -Force
    # Copy-Item -Path "./capnp" -Destination $dstDir -Recurse -Force

    Write-Host "Files copied successfully to $dstDir"
}

# 定义函数：执行设备配置转换
function Execute-Device {
    Write-Host "Converting all json5 files in devices/config to JSON..."

    $sourceConfigDir = "./devices/config"
    $targetConfigDir = "../zd-station/public/devices/config"
    
    # 处理devices/index.json5文件
    $sourceIndexFile = "./devices/index.json5"
    $targetIndexFile = "../zd-station/public/devices/index.json"
    
    if (Test-Path $sourceIndexFile) {
        # 创建目标devices目录
        $targetDevicesDir = "../zd-station/public/devices"
        if (-not (Test-Path $targetDevicesDir)) {
            New-Item -Path $targetDevicesDir -ItemType Directory -Force | Out-Null
        }
        
        Write-Host "Converting $sourceIndexFile to $targetIndexFile"
        $result = & json5 $sourceIndexFile -o $targetIndexFile
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error converting $sourceIndexFile"
            exit 1
        }
        Write-Host "Successfully converted index.json5"
    } else {
        Write-Host "Warning: $sourceIndexFile not found, skipping..."
    }
    
    # 检查源目录是否存在
    if (-not (Test-Path $sourceConfigDir)) {
        Write-Host "Source config directory not found: $sourceConfigDir"
        return
    }

    # 创建目标根目录
    if (-not (Test-Path $targetConfigDir)) {
        New-Item -Path $targetConfigDir -ItemType Directory -Force | Out-Null
    }

    # 遍历所有子文件夹
    $subDirs = Get-ChildItem -Path $sourceConfigDir -Directory
    
    # 处理根目录下的json5文件
    $json5Files = Get-ChildItem -Path $sourceConfigDir -Filter "*.json5" -File
    foreach ($json5File in $json5Files) {
        $relativePath = $json5File.Name -replace '\.json5$', '.json'
        $targetFile = Join-Path $targetConfigDir $relativePath
        
        Write-Host "Converting $($json5File.FullName) to $targetFile"
        
        $result = & json5 $json5File.FullName -o $targetFile
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error converting $($json5File.FullName)"
            exit 1
        }
    }
    
    # 递归处理所有子目录中的json5文件
    function Process-Directory {
        param(
            [string]$sourceDir,
            [string]$targetDir
        )
        
        # 处理当前目录下的json5文件
        $json5Files = Get-ChildItem -Path $sourceDir -Filter "*.json5" -File
        foreach ($json5File in $json5Files) {
            # 创建目标目录
            if (-not (Test-Path $targetDir)) {
                New-Item -Path $targetDir -ItemType Directory -Force | Out-Null
            }
            
            # 生成目标文件路径
            $jsonFileName = $json5File.Name -replace '\.json5$', '.json'
            $targetFile = Join-Path $targetDir $jsonFileName
            
            Write-Host "Converting $($json5File.FullName) to $targetFile"
            
            $result = & json5 $json5File.FullName -o $targetFile
            if ($LASTEXITCODE -ne 0) {
                Write-Host "Error converting $($json5File.FullName)"
                exit 1
            }
        }
        
        # 递归处理子目录
        $subDirs = Get-ChildItem -Path $sourceDir -Directory
        foreach ($subDir in $subDirs) {
            $targetSubDir = Join-Path $targetDir $subDir.Name
            Process-Directory -sourceDir $subDir.FullName -targetDir $targetSubDir
        }
    }
    
    # 处理所有子目录
    foreach ($subDir in $subDirs) {
        $targetSubDir = Join-Path $targetConfigDir $subDir.Name
        Process-Directory -sourceDir $subDir.FullName -targetDir $targetSubDir
    }

    Write-Host "Successfully converted all json5 files to JSON format"
}

# 根据参数执行相应操作
switch ($mode.ToLower()) {
    "proto" {
        Execute-Proto
    }
    "device" {
        Execute-Device
    }
    "all" {
        Execute-Proto
        Execute-Device
    }
    default {
        Write-Host "Invalid parameter. Use 'proto', 'device', or no parameter for all operations."
        exit 1
    }
}
