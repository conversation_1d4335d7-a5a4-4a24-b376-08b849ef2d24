{"version": "0.2", "ignorePaths": ["src/urq/proto"], "words": ["ccall", "cgen", "cwrap", "dbpp", "emcc", "FBIO", "FBIOGET", "flto", "fseak", "ftest", "FUNCS", "KHASH", "khint", "<PERSON><PERSON><PERSON>", "klib", "MALLOC", "mfloat", "pthread", "pycache", "xoffset"], "ignoreWords": ["clangd", "doxyfile", "dcpm", "dcmake", "emsdk", "emcmake", "fbdev", "fbfd", "genhtml", "gcov", "gcda", "g<PERSON>urce", "indev", "lconv", "liburq", "lvgl", "modbus", "simsun", "protoc", "refr", "deinit", "stdint", "keysym", "umut"], "ignoreRegExpList": ["[A-Z]{8,}", "URQ_E[A-Z_]+"], "import": []}