{
  // 文档格式说明：
  // name：寄存器名称，用这个去取i18n显示名称
  // classify: 分类名称，也用这个取i18n显示分类
  // start: 起始地址，如果有小数位，表示取位，类型为bit，否则为int16
  // length: 长度，如果start为bit，则长度为bit长茺，否则为int16长度
  // access: 权限,rw=读写，r=只读，w=只写
  // label: 用这个调用i18n显示标签，后缀加上_DESC调描述
  // index: 用这个调用i18n显示站号标签，后缀加上_DESC调描述，如果没有定义，表示用不到index

  // Device: {
  //   name: "Local",
  //   protocol: "LOCAL_HMI" // 协议名称，这个也做为RegisterType的前缀，与RegisterType加一块形成RegisterRealType的值
  // },
  DefaultBitRegister:"LB",
  DefaultWordRegister:"LW",
  Registers: {
    LB: {
      text: { "zh-CN": "本机位寄存器", en: "Local Bit Register" },
      registerType: "LB_USR",
      isWord: false,
      showFirst: 0,
      showLast: 65535,
      realFirst: 0,
      needBit: false,
      canWrite: true,
      canRead: true,
      defaultDataType: "bool",
      defaultLength: 1,
      registerBitLength: 1,
    },
    LW: {
      text: { "zh-CN": "本机字寄存器", en: "Local Word Register" },
      registerType: "LW_USR",
      isWord: true,
      showFirst: 0,
      showLast: 65535,
      realFirst: 0,
      needBit: false,
      canWrite: true,
      canRead: true,
      defaultDataType: "u16",
      defaultLength: 1,
      registerBitLength: 16,
    },
    LBH: {
      text: { "zh-CN": "本机保持位寄存器", en: "Local Bit Hold Register" },
      registerType: "LB_HLD",
      isWord: false,
      showFirst: 0,
      showLast: 65535,
      realFirst: 0,
      needBit: false,
      canWrite: true,
      canRead: true,
      defaultDataType: "bool",
      defaultLength: 1,
      registerBitLength: 1,
    },
    LWH: {
      text: { "zh-CN": "本机保持字寄存器", en: "Local Word Hold Register" },
      registerType: "LW_HLD",
      isWord: true,
      showFirst: 0,
      showLast: 65535,
      realFirst: 0,
      needBit: false,
      canWrite: true,
      canRead: true,
      defaultDataType: "u16",
      defaultLength: 1,
      registerBitLength: 16,
    },
    LBS: {
      text: { "zh-CN": "系统位寄存器", en: "System Bit Register" },
      registerType: "LB_SYS",
      isWord: false,
      showFirst: 0,
      showLast: 65535,
      realFirst: 0,
      needBit: false,
      canWrite: true,
      canRead: true,
      defaultDataType: "bool",
      defaultLength: 1,
      registerBitLength: 1,
    },
    LWS: {
      text: { "zh-CN": "系统字寄存器", en: "System Word Register" },
      registerType: "LW_SYS",
      isWord: true,
      showFirst: 0,
      showLast: 65535,
      realFirst: 0,
      needBit: false,
      canWrite: true,
      canRead: true,
      defaultDataType: "u16",
      defaultLength: 1,
      registerBitLength: 16,
    },
    IDX: {
      text: { "zh-CN": "索引寄存器", en: "Index Register" },
      registerType: "IDX",
      isWord: true,
      showFirst: 0,
      showLast: 128,
      realFirst: 0,
      needBit: false,
      canWrite: true,
      canRead: true,
      defaultDataType: "u16",
      defaultLength: 1,
      registerBitLength: 16,
    },
    STA: {
      text: { "zh-CN": "状态寄存器", en: "Status Register" },
      registerType: "STA",
      isWord: true,
      showFirst: 0,
      showLast: 128,
      realFirst: 0,
      needBit: false,
      canWrite: true,
      canRead: true,
      defaultDataType: "u16",
      defaultLength: 1,
      registerBitLength: 16,
    },
  },

  // station:通道号、设备号、网口号、用户、用户组、配方、文件、数据库、MQTT
  
  TagClassifies: {
    STA: {
      label: { "zh-CN": "状态寄存器", en: "Status Register" },
    },
    IDX: {
      label: { "zh-CN": "索引寄存器", en: "Index Register" },
    },
    TIM: {
      label: { "zh-CN": "时间寄存器", en: "Time Register" },
    },
    IOT: {
      label: { "zh-CN": "物联设置", en: "IOT Register" },
    },
    COM: {
      label: { "zh-CN": "通信设置", en: "Communication Setting" },
    },
  },

  Tags:{
    // 状态寄存器
    STA_1:{
      label: { "zh-CN": "当前显示语言", en: "Display Language" },
      classify: "STA",
    },
    STA_2:{
      label: { "zh-CN": "当前显示主题", en: "Display Theme" },
      classify: "STA",
    },
    STA_3:{
      label: { "zh-CN": "当前基本窗口号", en: "Local Basic Page No" },
      classify: "STA",
    },
    STA_4:{
      label: { "zh-CN": "当前公共窗口号", en: "Local Common Page No" },
      classify: "STA",
    },
    STA_5:{
      label: { "zh-CN": "当前弹窗窗口号列表", en: "Local Popup Page No List" },
      dataType: "u16",
      arrayCount: 10,
      length: 10,
      classify: "STA",
    },
    STA_15:{
      label: { "zh-CN": "当前登录用户号", en: "Current User No" },
      classify: "STA",
    },
    STA_16:{
      label: { "zh-CN": "当前登录用户账号", en: "Current User Account" },
      dataType: "utf8",
      length: 20,
      classify: "STA",
    },
    STA_36:{
      label: { "zh-CN": "当前登录用户名", en: "Current User Name" },
      dataType: "utf8",
      length: 20,
      classify: "STA",
    },
    STA_56:{
      label: { "zh-CN": "当前登录用户手机", en: "Current User Mobile" },
      dataType: "utf8",
      length: 10,
      classify: "STA",
    },
    STA_66:{
      label: { "zh-CN": "当前登录云用户ID", en: "Current User Cloud ID" },
      dataType: "u32",
      length: 2,
      classify: "STA",
    },
    STA_68:{
      label: { "zh-CN": "当前登录用户具备的权限", en: "Current User Permission" },
      dataType: "u64",
      length: 4,
      classify: "STA",
    },
    // STA_11:{
    //   label: { "zh-CN": "当前登录用户所属组", en: "Current User Group" },
    //   classify: "STA",
    // },
    // STA_12:{
    //   label: { "zh-CN": "当前登录用户头像", en: "Current User Avatar" },
    //   classify: "STA",
    // },
    // STA_13:{
    //   label: { "zh-CN": "当前登录用户颜色", en: "Current User Color" },
    //   classify: "STA",
    // },
    // 索引寄存器
    "IDX_1~50":{
      label: { "zh-CN": "索引寄存器{N}", en: "Index Register {N}" },
      classify: "IDX",
    },
    // 时间相关
    LWS_1:{
      label: { "zh-CN": "当前日期时间", en: "Current Date Time" },
      length: 4,
      dataType: "dt64",
      classify: "TIM",
    },
    LWS_5:{
      label: { "zh-CN": "当前日期", en: "Current Date" },
      length: 2,
      dataType: "d32",
      classify: "TIM",
    },
    LWS_7:{
      label: { "zh-CN": "当前时间", en: "Current Time" },
      length: 2,
      dataType: "t32",
      classify: "TIM",
    },
    LWS_9:{
      label: { "zh-CN": "当前时间戳（毫秒）", en: "Current Time Stamp (ms)" },
      length: 4,
      dataType: "ts64",
      classify: "TIM",
    },
    LWS_13:{
      label: { "zh-CN": "当前时间戳（秒）", en: "Current Time Stamp (s)" },
      length: 2,
      dataType: "ts32",
      classify: "TIM",
    },
    LWS_15:{
      label: { "zh-CN": "当前年份", en: "Current Year" },
      dataType: "u16",
      classify: "TIM",
    },
    LWS_16:{
      label: { "zh-CN": "当前月份", en: "Current Month" },
      dataType: "u16",
      classify: "TIM",
    },
    LWS_17:{
      label: { "zh-CN": "当前日", en: "Current Day" },
      dataType: "u16",
      classify: "TIM",
    },
    LWS_18:{
      label: { "zh-CN": "当前小时", en: "Current Hour" },
      dataType: "u16",
      classify: "TIM",
    },
    LWS_19:{
      label: { "zh-CN": "当前分钟", en: "Current Minute" },
      dataType: "u16",
      classify: "TIM",
    },
    LWS_20:{
      label: { "zh-CN": "当前秒", en: "Current Second" },
      dataType: "u16",
      classify: "TIM",
    },
    LWS_21:{
      label: { "zh-CN": "当前毫秒", en: "Current Millisecond" },
      dataType: "u16",
      classify: "TIM",
    },
  }
}
