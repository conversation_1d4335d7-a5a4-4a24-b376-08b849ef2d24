{
    "DefaultBitRegister": "0X",
    "DefaultWordRegister": "3X",
    "Registers": {
        "0X": { "name": "0X", "text": { "zh-CN": "离散量输出(DO)", "en": "Digital output" }, "registerType": "0X", "isWord": false, "showFirst": 0, "showLast": 65535, "realFirst": 0, "needBit": false, "canWrite": true, "canRead": true },
        "1X": { "name": "1X", "text": { "zh-CN": "离散量输入(DI)", "en": "Digital input" }, "registerType": "1X", "isWord": false, "showFirst": 0, "showLast": 65535, "realFirst": 0, "needBit": false, "canWrite": false, "canRead": true },
        "3X": { "name": "3X", "text": { "zh-CN": "模拟量输入(AI)", "en": "Analog input" }, "registerType": "3X", "isWord": true, "showFirst": 0, "showLast": 65535, "realFirst": 0, "needBit": false, "canWrite": false, "canRead": true },
        "4X": { "name": "4X", "text": { "zh-CN": "模拟量输出(AO)", "en": "Analog output" }, "registerType": "4X", "isWord": true, "showFirst": 0, "showLast": 65535, "realFirst": 0, "needBit": false, "canWrite": true, "canRead": true },
    }
}
