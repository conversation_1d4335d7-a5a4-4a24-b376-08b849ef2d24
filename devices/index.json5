{
  devices: {
    local: {
      label: { "zh-CN": "本地", en: "Local" },
      tunnel: [],
      configs: {
        hmi: {
          label: { "zh-CN": "本地HMI", en: "Local HMI" },
          configFile: "local/hmi",
          protocol: "LOCAL_HMI",
        },
        plc: {
          label: { "zh-CN": "本地PLC", en: "Local PLC" },
          configFile: "local/plc",
          protocol: "LOCAL_PLC",
        },
      },
    },
    inovance: {
      label: { "zh-CN": "汇川", en: "Inovance" },
      tunnel: ["COM", "ETH"],
      configs: {
        h1u: {
          label: { "zh-CN": "汇川H1U", en: "Inovance H1U" },
          configFile: "inovance/h1u",
          protocol: "MODBUS_MASTER",
        },
        h3u: {
          label: { "zh-CN": "汇川H3U", en: "Inovance H3U" },
          configFile: "inovance/h3u",
          protocol: "MODBUS_MASTER",
        },
        h5u: {
          label: { "zh-CN": "汇川H5U", en: "Inovance H5U" },
          configFile: "inovance/h5u",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    xinje: {
      label: { "zh-CN": "信捷", en: "Xinje" },
      tunnel: ["COM", "ETH"],
      configs: {
        xc: {
          label: { "zh-CN": "信捷XC系列", en: "Xinje XC Series" },
          configFile: "xinje/xc",
          protocol: "MODBUS_MASTER",
        },
        xd: {
          label: { "zh-CN": "信捷XD系列", en: "Xinje XD Series" },
          configFile: "xinje/xd",
          protocol: "MODBUS_MASTER",
        },
        xdm: {
          label: { "zh-CN": "信捷XDM系列", en: "Xinje XDM Series" },
          configFile: "xinje/xdm",
          protocol: "MODBUS_MASTER",
        },
        xd5: {
          label: { "zh-CN": "信捷XD5系列", en: "Xinje XD5 Series" },
          configFile: "xinje/xd5",
          protocol: "MODBUS_MASTER",
        },
        xdh: {
          label: { "zh-CN": "信捷XDH系列", en: "Xinje XDH Series" },
          configFile: "xinje/xdh",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    delta: {
      label: { "zh-CN": "台达", en: "Delta" },
      tunnel: ["COM", "ETH"],
      configs: {
        plc: {
          label: { "zh-CN": "台达PLC", en: "Delta PLC" },
          configFile: "delta/plc",
          protocol: "MODBUS_MASTER",
        },
        as: {
          label: { "zh-CN": "台达AS PLC", en: "Delta AS PLC" },
          configFile: "delta/as",
          protocol: "MODBUS_MASTER",
        },
        dvp: {
          label: { "zh-CN": "台达DVP15MC", en: "Delta DVP15MC" },
          configFile: "delta/dvp",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    fatek: {
      label: { "zh-CN": "永宏", en: "Fatek" },
      tunnel: ["COM", "ETH"],
      configs: {
        plc: {
          label: { "zh-CN": "永宏PLC", en: "Fatek PLC" },
          configFile: "fatek/plc",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    samkoon: {
      label: { "zh-CN": "显控", en: "Samkoon" },
      tunnel: ["COM", "ETH"],
      configs: {
        plc: {
          label: { "zh-CN": "显控PLC", en: "Samkoon PLC" },
          configFile: "samkoon/plc",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    megmeet: {
      label: { "zh-CN": "麦格米特", en: "Megmeet" },
      tunnel: ["COM", "ETH"],
      configs: {
        plc: {
          label: { "zh-CN": "麦格米特PLC", en: "Megmeet PLC" },
          configFile: "megmeet/plc",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    outseal: {
      label: { "zh-CN": "Outseal", en: "Outseal" },
      tunnel: ["COM"],
      configs: {
        plc: {
          label: { "zh-CN": "Outseal PLC", en: "Outseal PLC" },
          configFile: "outseal/plc",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    panasonic: {
      label: { "zh-CN": "松下", en: "Panasonic" },
      tunnel: ["COM"],
      configs: {
        "fp-xh": {
          label: { "zh-CN": "松下FP-XH", en: "Panasonic FP-XH" },
          configFile: "panasonic/fp-xh",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    schneider: {
      label: { "zh-CN": "施耐德", en: "Schneider" },
      tunnel: ["COM", "ETH"],
      configs: {
        modbus: {
          label: { "zh-CN": "施耐德Modbus", en: "Schneider Modbus" },
          configFile: "schneider/modbus",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    sinovo: {
      label: { "zh-CN": "西林", en: "Sinovo" },
      tunnel: ["COM", "ETH"],
      configs: {
        plc: {
          label: { "zh-CN": "西林PLC", en: "Sinovo PLC" },
          configFile: "sinovo/plc",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    teco: {
      label: { "zh-CN": "东元", en: "Teco" },
      tunnel: ["COM", "ETH"],
      configs: {
        plc: {
          label: { "zh-CN": "东元PLC", en: "Teco PLC" },
          configFile: "teco/plc",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    veichi: {
      label: { "zh-CN": "伟创", en: "Veichi" },
      tunnel: ["COM", "ETH"],
      configs: {
        vc: {
          label: { "zh-CN": "伟创VC系列", en: "Veichi VC Series" },
          configFile: "veichi/vc",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    junc: {
      label: { "zh-CN": "军创", en: "JUNCAUTO" },
      tunnel: ["COM", "ETH"],
      configs: {
        plc: {
          label: { "zh-CN": "军创PLC", en: "JUNCAUTO PLC" },
          configFile: "junc/plc",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    mcgs: {
      label: { "zh-CN": "MCGS", en: "MCGS" },
      tunnel: ["COM", "ETH"],
      configs: {
        mcgs: {
          label: { "zh-CN": "MCGS", en: "MCGS" },
          configFile: "mcgs/mcgs",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    haiwell: {
      label: { "zh-CN": "海为", en: "Haiwell" },
      tunnel: ["COM", "ETH"],
      configs: {
        plc: {
          label: { "zh-CN": "海为PLC", en: "Haiwell PLC" },
          configFile: "haiwell/plc",
          protocol: "MODBUS_MASTER",
        },
        ext: {
          label: { "zh-CN": "海为扩展模块", en: "Haiwell EXT" },
          configFile: "haiwell/ext",
          protocol: "MODBUS_MASTER",
        },
      },
    },
    modbus: {
      label: { "zh-CN": "Modbus", en: "Modbus" },
      tunnel: ["COM", "ETH"],
      configs: {
        master: {
          label: { "zh-CN": "Modbus主站", en: "Modbus Master" },
          configFile: "modbus/master",
          protocol: "MODBUS_MASTER",
        },
        master_ascii: {
          label: { "zh-CN": "Modbus主站ASCII", en: "Modbus Master ASCII" },
          configFile: "modbus/master",
          protocol: "MODBUS_MASTER_ASCII",
          tunnel: ["COM"],
        },
        slave: {
          label: { "zh-CN": "Modbus从站", en: "Modbus Slave" },
          configFile: "modbus/slave",
          protocol: "MODBUS_SLAVE",
        },
        slave_ascii: {
          label: { "zh-CN": "Modbus从站ASCII", en: "Modbus Slave ASCII" },
          configFile: "modbus/slave",
          protocol: "MODBUS_SLAVE_ASCII",
          tunnel: ["COM"],
        },
      },
    },
  },
}
