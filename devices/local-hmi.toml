# 文档格式说明：
# name：寄存器名称，用这个去取i18n显示名称
# classify: 分类名称，也用这个取i18n显示分类
# start: 起始地址，如果有小数位，表示取位，类型为bit，否则为int16
# length: 长度，如果start为bit，则长度为bit长茺，否则为int16长度
# access: 权限,rw=读写，r=只读，w=只写
# label: 用这个调用i18n显示标签，后缀加上_DESC调描述
# index: 用这个调用i18n显示站号标签，后缀加上_DESC调描述，如果没有定义，表示用不到index
# [Device]
# name = "Local"
# protocol = "LOCAL_HMI" # 协议名称，这个也做为RegisterType的前缀，与RegisterType加一块形成RegisterRealType的值
Registers = [
    { name = "LB", text = { zh-CN = "本机位寄存器", en = "Local Bit Register" }, registerType = "LB", defaultDataType="bool", registerBitLength=1, defaultLength=1, isWord = false, showFirst = 0, showLast = 65535, realFirst = 0, needBit = false, canWrite = true, canRead = true },
    { name = "LW", text = { zh-CN = "本机字寄存器", en = "Local Word Register" }, registerType = "LW", defaultDataType="u16", registerBitLength=16, defaultLength=1, isWord = true, showFirst = 0, showLast = 65535, realFirst = 0, needBit = false, canWrite = true, canRead = true },
    { name = "LHB", text = { zh-CN = "本机保持位寄存器", en = "Local Bit Hold Register" }, registerType = "LB_SAV", defaultDataType='bool', registerBitLength=1, defaultLength=1, isWord = false, showFirst = 0, showLast = 65535, realFirst = 0, needBit = false, canWrite = true, canRead = true },
    { name = "LHW", text = { zh-CN = "本机保持字寄存器", en = "Local Word Hold Register" }, registerType = "LW_SAV", defaultDataType='u16', registerBitLength=16, defaultLength=1, isWord = true, showFirst = 0, showLast = 65535, realFirst = 0, needBit = false, canWrite = true, canRead = true },
    { name = "LSB", text = { zh-CN = "本机系统位寄存器", en = "System Bit Register" }, registerType = "LB_SYS", defaultDataType='bool', registerBitLength=1, defaultLength=1, isWord = false, showFirst = 0, showLast = 65535, realFirst = 0, needBit = false, canWrite = true, canRead = true },
    { name = "LSW", text = { zh-CN = "本机系统字寄存器", en = "System Word Register" }, registerType = "LW_SYS", defaultDataType='u16', registerBitLength=16, defaultLength=1, isWord = true, showFirst = 0, showLast = 65535, realFirst = 0, needBit = false, canWrite = true, canRead = true },
    { name = "IDX", text = { zh-CN = "索引寄存器", en = "Index Register" }, registerType = "IDX", defaultDataType='u16', registerBitLength=16, defaultLength=1, isWord = true, showFirst = 0, showLast = 128, realFirst = 0, needBit = false, canWrite = true, canRead = true },
    { name = "STA", text = { zh-CN = "状态寄存器", en = "Status Register" }, registerType = "STA", defaultDataType='u16', registerBitLength=16, defaultLength=1, isWord = true, showFirst = 0, showLast = 128, realFirst = 0, needBit = false, canWrite = true, canRead = true },

    # { name = "RP_DAT", text = { zh-CN = "配方数据", en = "Recipe Data" }, register_type = "RP_DAT", is_word = true, show_first = 0, show_last = 65535, real_first = 0, need_bit = false, can_write = true, can_read = true },
    # { name = "RP_CUR", text = { zh-CN = "配方当前组数据", en = "Recipe Current Group Data" }, register_type = "RP_CUR", is_word = true, show_first = 0, show_last = 65535, real_first = 0, need_bit = false, can_write = true, can_read = true },
    # { name = "RP_OPA", text = { zh-CN = "配方操作", en = "Recipe Operation" }, register_type = "RP_OPA", is_word = true, show_first = 0, show_last = 65535, real_first = 0, need_bit = false, can_write = true, can_read = true },
    # { name = "FS_DAT", text = { zh-CN = "文件数据", en = "File Data" }, register_type = "FS_DAT", is_word = true, show_first = 0, show_last = 65535, real_first = 0, need_bit = false, can_write = true, can_read = true },
    # { name = "FS_OPA", text = { zh-CN = "文件操作", en = "File Operation" }, register_type = "FS_OPA", is_word = true, show_first = 0, show_last = 65535, real_first = 0, need_bit = false, can_write = true, can_read = true },
    # { name = "DB_SERVER", text = { zh-CN = "数据库服务器", en = "Database Server" }, register_type = "DB_SERVER", is_word = true, show_first = 0, show_last = 65535, real_first = 0, need_bit = false, can_write = true, can_read = true },
    # { name = "DB_QUERY", text = { zh-CN = "数据库查询数据", en = "Database Query Data" }, register_type = "DB_QUERY", is_word = true, show_first = 0, show_last = 65535000, real_first = 0, need_bit = false, can_write = true, can_read = true },
    # { name = "DB_DAT", text = { zh-CN = "数据库数据", en = "Database Data" }, register_type = "DB_DAT", is_word = true, show_first = 0, show_last = 65535, real_first = 0, need_bit = false, can_write = true, can_read = true },
    # { name = "DB_OPA", text = { zh-CN = "数据库操作", en = "Database Operation" }, register_type = "DB_OPA", is_word = true, show_first = 0, show_last = 65535, real_first = 0, need_bit = false, can_write = true, can_read = true },
]

#station:通道号、设备号、网口号、用户、用户组、配方、文件、数据库、MQTT

# # 地址标签（通讯相关）
# [[Tags]]
# register_type = "SYS_WORD" # 寄存器的名称（对应Register的name），classify及tags如果未定义，以这里为准
# can_write = true    # 继承用
# can_read = true     # 继承用
# data_type = "u16"    # 继承用
# length = 1      # 继承用

[[TagClassifies]]
text = { zh-CN = "状态寄存器", en = "Status Register" }
#使用寄存器
registerName = "STA"
tags = [
    # 语言
    { id = 1, address = 1, label = { zh-CN = "当前显示语言", en = "Display Language" } },
    # 主题
    { id = 2, address = 2, label = { zh-CN = "当前显示主题", en = "Display Theme" } },
    # 本机基本窗口号
    { id = 3, address = 3, label = { zh-CN = "当前基本窗口号", en = "Local Basic Page No" } },
    # 本机公共窗口号
    { id = 4, address = 4, label = { zh-CN = "当前公共窗口号", en = "Local Common Page No" } },
    # 本机弹窗窗口号列表(用于弹窗同步,最大10级弹窗)
    { id = 5, address = 5, length = 10, label = { zh-CN = "当前弹窗窗口号列表", en = "Local Popup Page No List" } },
    # 当前登录用户号
    { id = 6, address = 6, label = { zh-CN = "当前登录用户号", en = "Current User No" } },
    # 当前登录用户账号
    { id = 7, address = 7, label = { zh-CN = "当前登录用户账号", en = "Current User Account" } },
    # 当前登录用户名
    { id = 8, address = 8, label = { zh-CN = "当前登录用户名", en = "Current User Name" } },
    # 当前登录用户手机
    { id = 9, address = 9, label = { zh-CN = "当前登录用户手机", en = "Current User Mobile" } },
    # 当前登录用户具备的权限(这是把所有组权限和用户权限做或运算后的结果)
    { id = 10, address = 10, label = { zh-CN = "当前登录用户具备的权限", en = "Current User Permission" } },
    # 当前登录用户所属组
    { id = 11, address = 11, label = { zh-CN = "当前登录用户所属组", en = "Current User Group" } },
    # 当前登录用户头像(MDI-ICON)
    { id = 12, address = 12, label = { zh-CN = "当前登录用户头像", en = "Current User Avatar" } },
    # 当前登录用户颜色
    { id = 13, address = 13, label = { zh-CN = "当前登录用户颜色", en = "Current User Color" } },
    # 当前输入法
    { id = 14, address = 14, label = { zh-CN = "当前输入法", en = "Current Input Method" }, desc = { zh-CN = "0-ASCII,1-拼音,2-五笔", en = "0-ASCII, 1-Pinyin, 2-Wu bi" } },
    # 当前键盘窗口号
    { id = 15, address = 15, label = { zh-CN = "当前键盘窗口号", en = "Current Keyboard Window No" } },
    # 当前按键
    # { id = 15, address = 15, length = 2, label = { zh-CN = "当前按键", en = "Current Key" } },
    # 键盘显示状态
    # { id = 16, address = 16, label = { zh-CN = "ASCII键盘显示状态", en = "ASCII Keyboard Display State" }, desc = { zh-CN = "0-小写,1-大写,2-符号/数字", en = "0-Lowercase, 1-Uppercase, 2-Symbol/Number" } },
    # 输入过程中的内容
    { id = 17, address = 17, array_count = -1, label = { zh-CN = "输入过程中的内容", en = "Input Content" } },
    # 输入候选字
    { id = 18, address = 18, array_count = -1, label = { zh-CN = "输入候选字", en = "Input Candidate" } },
    # 输入时是否显示为密码模式
    { id = 19, address = 19, data_type = "BOOL", bit_index = 0, label = { zh-CN = "输入时是否显示为密码模式", en = "Input Password Mode" } },
    # 数值输入最大值
    { id = 20, address = 20, label = { zh-CN = "数值输入最大值", en = "Numeric Input Max" } },
    # 数值输入最小值
    { id = 21, address = 21, label = { zh-CN = "数值输入最小值", en = "Numeric Input Min" } },
    # 字符输入最长长度
    { id = 21, address = 21, label = { zh-CN = "ASCII字符输入最长长度", en = "ASCII Character Input Max Length" } },
    # 字符输入最短长度
    { id = 22, address = 22, label = { zh-CN = "ASCII字符输入最短长度", en = "ASCII Character Input Min Length" } },

    # 操作确认
    { id = 30, address = 30, label = { zh-CN = "操作确认", en = "Operation Confirm" }, desc = { zh-CN = "1-确认,2-取消", en = "1-Confirm, 2-Cancel" } },
    # 操作确认等待时间
    { id = 31, address = 31, label = { zh-CN = "操作确认等待时间", en = "Operation Confirm Wait Time" }, min = 0, max = 65535 },
]

[[TagClassifies]]
text = { zh-CN = "索引寄存器", en = "Index Register" }
#使用寄存器
registerName = "IDX"
tags = [
    # 索引寄存器
    { id = 32, address = 1, batchStart = 1, batchCount = 50, label = { zh-CN = "索引寄存器{N}", en = "Index Register {N}" } },
    
    # 在线访问者暂时不做，可能直接做在服务端更好，系统寄存器只要增加一个是否有远程访问者在访问
    # 在线互联网访问者用户账号ID
    # { id = 62, address = 84, length = 8, array_count = -1, label = { zh-CN = "访问者用户账号", en = "Remote User Account" } },
    # 在线互联网访问者访问开始时间
    # { id = 63, address = 84, length = 8, array_count = -1, label = { zh-CN = "访问者访问开始时间", en = "Remote Access Start Time" } },
    # 当前在线的远程访问者数量（具体名单在服务端找）
    # { id = 64, address = 84, length = 1, label = { zh-CN = "互联网远程访问者数量", en = "Internet Remote User Count" } },
    # { id = 65, address = 84, length = 1, label = { zh-CN = "局域网远程访问者数量", en = "Local Area Network Remote User Count" } },


    # 关于远程访问的同步：在远程访问HMI时，需要先在应用层获得同步参数，分为完全同步模式、画面同步模式、完全不同步模式
    # 完全同步：语言、主题、窗口、键盘、用户全部同步，画面同步：语言、主题、窗口同步，键盘、用户不同步，完全不同步：语言、主题、窗口、键盘、用户完全不同步
    # 同步模式（本地址不会同步，初始值是接收应用传入值，如果应用没有传入值，则取HMI的值，HMI端则由工程初始化，默认为0）
    # { id = 0, address = 0, data_type="u8", byte_index = 0, label = { zh-CN = "同步模式", en = "Synchronization Mode" }, desc = { zh-CN = "0-完全同步/远程协助模式，1-画面同步模式，2-不同步模式", en = "0-Full Synchronization Mode, 1-Partial Synchronization Mode, 2-Non Synchronization Mode" }, options = [
    #     { label = { zh-CN = '完全同步/远程协助', en = 'Full Synchronization/Remote Assistance' }, value = 0 },
    #     { label = { zh-CN = '画面同步', en = 'Page Synchronization' }, value = 1 },
    #     { label = { zh-CN = '不同步', en = 'Non Synchronization' }, value = 2 },
    # ] },
    # # 完全同步是否允许
    # { id = 1, address = 0, bit_index = 8, data_type="bool", length=1, label = { zh-CN = "是否禁用完全同步", en = "Full Synchronization Disable" } },
    # # 画面同步是否允许
    # { id = 2, address = 0, bit_index = 9, data_type="bool", length=1, label = { zh-CN = "是否禁用画面同步", en = "Page Synchronization Disable" } },
    # # 不同步是否允许
    # { id = 3, address = 0, bit_index = 10, data_type="bool", length=1, label = { zh-CN = "是否禁用完全不同步", en = "Non Synchronization Disable" } },
]


# IOT基本设置
[[TagClassifies]]
text = { zh-CN = "物联设置", en = "IOT" }
register_type = "LSW"
tags = [
    # 设备代号
    { id = 40, address = 40, type = "string", length = 16, label = { zh-CN = "设备代号", en = "Device Code" } },
    # 设备名称
    { id = 41, address = 41, type = "string", length = 32, label = { zh-CN = "设备名称", en = "Device Name" } },
    # 设备群组(支持以/分隔的群组路径)
    { id = 42, address = 42, type = "string", length = 128, label = { zh-CN = "设备群组", en = "Device Group" } },
    # 物联接入服务器地址（支持IP和域名，域名最长128个字符，用于私有化部署）
    { id = 43, address = 43, type = "string", length = 128, label = { zh-CN = "物联接入服务器地址", en = "IOT Access Server Address" } },
    # 物联接入服务器端口
    { id = 44, address = 44, label = { zh-CN = "物联接入服务器端口", en = "IOT Access Server Port" } },
    # 物联数据服务器地址（支持IP和域名，域名最长128个字符，用于私有化部署）
    { id = 45, address = 45, type = "string", length = 128, label = { zh-CN = "物联数据服务器地址", en = "IOT Data Server Address" } },
    # 物联数据服务器端口
    { id = 46, address = 46, label = { zh-CN = "物联数据服务器端口", en = "IOT Data Server Port" } },
    # 物联接入状态：0-未连接，1-已连接，2-连接中，3-连接失败，等待重连
    { id = 47, address = 47, write = "false", label = { zh-CN = "物联接入状态", en = "IOT Access Status" } },
    # 物联数据状态：0-未连接，1-已连接，2-连接中，3-连接失败，等待重连
    { id = 48, address = 48, write = "false", label = { zh-CN = "物联数据状态", en = "IOT Data Status" } },
    # 上次连接失败原因
    { id = 49, address = 49, type = "u8", label = { zh-CN = "上次连接失败原因", en = "IOT Last Connect Fail Reason" } },
    # 是否允许物联在线
    { id = 50, address = 50, bit_index = 0, type = "bool", label = { zh-CN = "是否允许物联在线", en = "IOT Enable Online" } },
    # 是否开放互联网访问
    { id = 51, address = 50, bit_index = 1, type = "bool", label = { zh-CN = "是否开放互联网访问", en = "IOT Enable Internet Access" } },
    # 是否开放API
    { id = 52, address = 50, bit_index = 2, type = "bool", label = { zh-CN = "是否开放API", en = "IOT Enable API" } },
    # 是否开放MQTT服务端
    { id = 53, address = 50, bit_index = 3, type = "bool", label = { zh-CN = "是否开放MQTT代理", en = "IOT Enable MQTT" } },
    # 是否开启MQTT客户端
    { id = 54, address = 50, bit_index = 4, type = "bool", label = { zh-CN = "是否开放MQTT代理", en = "IOT Enable MQTT" } },
    # 是否开放远程查看
    { id = 55, address = 50, bit_index = 5, type = "bool", label = { zh-CN = "是否开放远程查看", en = "IOT Enable View" } },
    # 是否开放数据上报
    { id = 56, address = 50, bit_index = 6, type = "bool", label = { zh-CN = "是否开放数据上报", en = "IOT Enable Up Data" } },
    # 是否开放远程写入
    { id = 57, address = 50, bit_index = 7, type = "bool", label = { zh-CN = "是否开放远程写入", en = "IOT Enable Write" } },
    # 是否开放文件管理
    { id = 58, address = 50, bit_index = 8, type = "bool", label = { zh-CN = "是否开放文件管理", en = "IOT Enable File Manage" } },
    # 是否允许远程重启
    { id = 59, address = 50, bit_index = 9, type = "bool", label = { zh-CN = "是否允许远程重启", en = "IOT Enable Reboot" } },
    # 是否允许远程更新工程
    { id = 60, address = 50, bit_index = 10, type = "bool", label = { zh-CN = "是否允许远程更新工程", en = "IOT Enable Update Project" } },
    # 是否允许远程透传
    { id = 61, address = 50, bit_index = 11, type = "bool", label = { zh-CN = "是否允许远程更新配方", en = "IOT Enable Update Recipe" } },
    # 是否允许远程更新文件
    { id = 62, address = 50, bit_index = 12, type = "bool", label = { zh-CN = "是否允许远程更新文件", en = "IOT Enable Update File" } },
    # 是否允许画面同步
    { id = 63, address = 51, bit_index = 0, data_type = "bool", length = 1, label = { zh-CN = "是否禁用画面同步", en = "Page Synchronization Disable" } },
    # 是否允许语言同步
    { id = 64, address = 51, bit_index = 1, data_type = "bool", length = 1, label = { zh-CN = "是否禁用语言同步", en = "Language Synchronization Disable" } },
    # 是否允许主题同步
    { id = 65, address = 51, bit_index = 2, data_type = "bool", length = 1, label = { zh-CN = "是否禁用主题同步", en = "Theme Synchronization Disable" } },
    # 是否允许用户同步
    { id = 66, address = 51, bit_index = 3, data_type = "bool", length = 1, label = { zh-CN = "是否禁用用户同步", en = "User Synchronization Disable" } },
    # 是否允许键盘同步
    { id = 67, address = 51, bit_index = 4, data_type = "bool", length = 1, label = { zh-CN = "是否禁用键盘同步", en = "Keyboard Synchronization Disable" } },
    # API接入IP白名单
    { id = 68, address = 52, data_type = "u32", length = 1, array_count = -1, label = { zh-CN = "API接入IP白名单", en = "API Access IP Whitelist" } },
    # 画面是否同步
    { id = 69, address = 53, bit_index = 0, data_type = "bool", length = 1, label = { zh-CN = "访问端画面是否同步", en = "Page Synchronization" } },
    # 语言是否同步
    { id = 70, address = 53, bit_index = 1, data_type = "bool", length = 1, label = { zh-CN = "访问端语言是否同步", en = "Language Synchronization" } },
    # 主题是否同步
    { id = 71, address = 53, bit_index = 2, data_type = "bool", length = 1, label = { zh-CN = "访问端主题是否同步", en = "Theme Synchronization" } },
    # 用户是否同步
    { id = 72, address = 53, bit_index = 3, data_type = "bool", length = 1, label = { zh-CN = "访问端用户是否同步", en = "User Synchronization" } },
    # 键盘是否同步
    { id = 73, address = 53, bit_index = 4, data_type = "bool", length = 1, label = { zh-CN = "访问端键盘是否同步", en = "Keyboard Synchronization" } },
]

# 时间相关
[[TagClassifies]]
text = { zh-CN = "时间相关", en = "Time" }
register = "LSW"
tags = [
    # 年
    { id = 80, address = 80, label = { zh-CN = "年", en = "Year" } },
    # 月
    { id = 81, address = 81, label = { zh-CN = "月", en = "Month" } },
    # 日
    { id = 82, address = 82, label = { zh-CN = "日", en = "Day" } },
    # 时
    { id = 83, address = 83, label = { zh-CN = "时", en = "Hour" } },
    # 分
    { id = 84, address = 84, label = { zh-CN = "分", en = "Minute" } },
    # 秒
    { id = 85, address = 85, label = { zh-CN = "秒", en = "Second" } },
    # 毫秒
    { id = 86, address = 86, label = { zh-CN = "毫秒", en = "Millisecond" } },
    # 星期几
    { id = 87, address = 87, label = { zh-CN = "星期几", en = "Weekday" } },
    # 今年第几周
    { id = 88, address = 88, label = { zh-CN = "今年第几周", en = "Week of Year" } },
    # 时区
    { id = 89, address = 89, label = { zh-CN = "时区", en = "Time Zone" } },
    # 农历年月日
    { id = 90, address = 90, label = { zh-CN = "农历年", en = "Lunar Year" } },
    { id = 91, address = 91, label = { zh-CN = "农历月", en = "Lunar Month" } },
    { id = 92, address = 92, label = { zh-CN = "农历日", en = "Lunar Day" } },
    # 是否启用NTP
    { id = 93, address = 93, type = "bool", label = { zh-CN = "是否启用NTP", en = "NTP Enable" } },
    # 是否立即更新或者同步时间(1-不管有没有NTP，都立即写入时间，如果是NTP，再同步时间)
    { id = 94, address = 94, type = "bool", label = { zh-CN = "是否立即更新或者同步时间", en = "NTP Update" } },
]

# 地址标签分类
[[TagClassifies]]
text = { zh-CN = "通信接口参数", en = "Port Parameter" }
#站号作用
station = { label = { zh-CN = "通信接口号", en = "Tunnel No" }, from = "TUNNEL_NO" }
#使用寄存器
register = "LSW"
tags = [
    #串口方式：0=RS232,1=RS485,2=RS422
    { id = 100, address = 100, label = { zh-CN = "串口方式", en = "Serial Port Type" }, desc = "0-RS232,1-RS485", options = [
        { label = 'RS232', value = 0 },
        { label = 'RS485', value = 1 },
    ] },
    #波特率
    { id = 101, address = 101, label = { zh-CN = "波特率", en = "Baud Rate" }, desc = { zh-CN = "直接用数字，9600就表示9600波特率", en = "Use number directly, 9600 means 9600 baud rate" }, options = [
        300,
        600,
        1200,
        2400,
        4800,
        9600,
        14400,
        19200,
        28800,
        38400,
        57600,
        76800,
        115200,
        187500,
    ] },
    #数据位
    { id = 102, address = 102, label = { zh-CN = "数据位", en = "Data Bits" }, desc = { zh-CN = "7-数据位7，8-数据位8", en = "7-Data bits 7, 8-Data bits 8" }, items = [
        7,
        8,
    ] },
    #停止位
    { id = 103, address = 103, label = { zh-CN = "停止位", en = "Stop Bits" }, desc = { zh-CN = "1-1个停止位，2-2个停止位", en = "1-1 stop bit, 2-2 stop bits" }, items = [
        1,
        2,
    ] },
    #校验位
    { id = 104, address = 104, label = { zh-CN = "校验位", en = "Parity" }, desc = { zh-CN = "0-无校验，1-奇校验，2-偶校验", en = "0-No parity, 1-Odd parity, 2-Even parity" }, items = [
        { label = { zh-CN = '无校验', en = 'No parity' }, value = 0 },
        { label = { zh-CN = '奇校验(Odd)', en = 'Odd parity' }, value = 1 },
        { label = { zh-CN = '偶校验(Even)', en = 'Even parity' }, value = 2 },
    ] },
    #是否启用
    { id = 105, address = 105, type = "bool", label = { zh-CN = "是否启用", en = "Enable" }, desc = { zh-CN = "0-不启用，1-启用", en = "0-Disabled, 1-Enabled" }, items = [
        { label = { zh-CN = '禁用', en = 'Disabled' }, value = 0 },
        { label = { zh-CN = '启用', en = 'Enabled' }, value = 1 },
    ] },
    #通讯状态
    { id = 106, address = 106, type = "bool", write = false, label = { zh-CN = "通讯状态", en = "Communication Status" }, desc = { zh-CN = "0-离线,1-在线", en = "0-Offline,1-Online" }, items = [
        { label = { zh-CN = "离线", en = "Offline" }, value = 0 },
        { label = { zh-CN = "在线", en = "Online" }, value = 1 },
    ] },
]

[[TagClassifies]]
text = { zh-CN = "设备通信参数", en = "Device Parameter" }
register = "LSW"
station = { label = { zh-CN = "设备号", en = "Device No" }, from = "DEVICE_NO" }
tags = [
    # 设备COM号（仅串口设备，用于动态切换串口，以及远程HMI的子设备对应串口，考虑到PC端，串口号允许到255）
    { id = 8, address = 10, label = { zh-CN = "设备COM号", en = "Device COM No" }, desc = "1-COM1,2-COM2,3-COM3", min = 0, max = 255 },
    # #设备自定义波特率
    # { id = 9, address = 11, label = { zh-CN = "设备自定义波特率", en = "Device Custom Baud Rate" } },
    # #设备自定义数据位
    # { id = 10, address = 12, label = { zh-CN = "设备自定义数据位", en = "Device Custom Data Bits" } },
    # #设备自定义停止位
    # { id = 11, address = 13, label = { zh-CN = "设备自定义停止位", en = "Device Custom Stop Bits" } },
    # #设备自定义校验位
    # { id = 12, address = 14, label = { zh-CN = "设备自定义校验位", en = "Device Custom Parity" } },
    #设备IPV4地址
    { id = 110, address = 110, label = { zh-CN = "设备IP地址1段", en = "Device IP Address 1st" }, min = 0, max = 255 },
    { id = 111, address = 111, label = { zh-CN = "设备IP地址2段", en = "Device IP Address 2nd" }, min = 0, max = 255 },
    { id = 112, address = 112, label = { zh-CN = "设备IP地址3段", en = "Device IP Address 3rd" }, min = 0, max = 255 },
    { id = 113, address = 113, label = { zh-CN = "设备IP地址4段", en = "Device IP Address 4th" }, min = 0, max = 255 },
    #设备IPV6地址
    # { id = 13, address = 15, length = 8, access = "rw", label = { zh-CN = "设备IP(V6)地址", en = "Device IP(V6) Address" }, desc = { zh-CN = "8个地址，分别表示IPV6的8个段" }, index = "DEVICE_NO" },
    #设备端口号
    { id = 130, address = 130, label = { zh-CN = "设备端口号", en = "Device Port" }, min = 0, max = 65535 },
    #设备所在的互联网HMI 云KEY
    { id = 140, address = 140, type = "string", length = 32, label = { zh-CN = "互联网HMI 云KEY", en = "Internet HMI Cloud Key" } },
    #设备所在的局域网HMI IP
    { id = 150, address = 150, label = { zh-CN = "局域网HMI IP段1", en = "LAN HMI IP Address 1st" }, min = 0, max = 255 },
    { id = 160, address = 160, label = { zh-CN = "局域网HMI IP段2", en = "LAN HMI IP Address 2nd" }, min = 0, max = 255 },
    { id = 170, address = 170, label = { zh-CN = "局域网HMI IP段3", en = "LAN HMI IP Address 3rd" }, min = 0, max = 255 },
    { id = 180, address = 180, label = { zh-CN = "局域网HMI IP段4", en = "LAN HMI IP Address 4th" }, min = 0, max = 255 },
    #设备所在的局域网HMI端口号
    { id = 190, address = 190, label = { zh-CN = "局域网HMI端口号", en = "LAN HMI Port" }, min = 0, max = 65535 },
    #设备目标站号
    { id = 200, address = 200, label = { zh-CN = "设备站号", en = "Device Station" }, min = 0, max = 255 },
    #设备广播站号（-1表示不广播）
    { id = 210, address = 210, type = "i16", label = { zh-CN = "设备广播站号", en = "Device Broadcast Station" }, desc = { zh-CN = "-1:无广播,0-255表示广播端口号", en = "-1:No broadcast, 0~255:Broadcast port" }, min = -1, max = 255 },
    #设备超时时间
    { id = 220, address = 220, label = { zh-CN = "设备超时时间", en = "Device Timeout" }, min = 0, max = 65535 },
    #设备通讯间隔
    { id = 230, address = 230, label = { zh-CN = "设备通讯间隔", en = "Device Interval" }, min = 0, max = 65535 },
    #通讯尝试次数
    { id = 240, address = 240, label = { zh-CN = "通讯尝试次数", en = "Device Try Count" }, min = 0, max = 65535 },
    #通讯尝试间隔
    { id = 25, address = 58, label = { zh-CN = "通讯尝试间隔", en = "Device Try Interval" }, min = 0, max = 65535 },
    #通讯异常提示时长(-1-不显示，0-一直提示，单位秒)
    { id = 26, address = 59, type = "i16", label = { zh-CN = "通讯异常提示时长", en = "Device Error Prompt Time" }, desc = { zh-CN = "-1:不显示,0-一直提示,1~32767表示显示相应的秒数", en = "-1:Not display, 0-Always prompt, 1~32767:show seconds" }, min = -1, max = 32767 },
    #位组包间隔
    { id = 27, address = 60, label = { zh-CN = "位组包间隔", en = "Device Pack Dist Bit" }, min = 0, max = 65535 },
    #位组包最大个数
    { id = 28, address = 61, label = { zh-CN = "位组包最大个数", en = "Device Pack Max Bit" }, min = 0, max = 65535 },
    #字组包间隔
    { id = 29, address = 62, label = { zh-CN = "字组包间隔", en = "Device Pack Dist Word" }, min = 0, max = 65535 },
    #字组包最大个数
    { id = 30, address = 63, label = { zh-CN = "字组包最大个数", en = "Device Pack Max Word" }, min = 0, max = 65535 },
    #设备是否启用
    { id = 31, address = 64.0, type = "bool", label = { zh-CN = "设备是否启用", en = "Device Enable" } },
    #设备通讯状态
    { id = 32, address = 64.1, type = "bool", write = false, label = { zh-CN = "设备通讯状态", en = "Device Status" } },
    #让设备的参数生效
    { id = 33, address = 64.2, type = "bool", label = { zh-CN = "让设备的参数生效", en = "Device Update Param" }, desc = { zh-CN = "0-不生效/生效完成,1-立即生效", en = "0-Not effective/Effective completed, 1-Immediate effective" } },
]

[[TagClassifies]]
text = { zh-CN = "网络相关", en = "Network" }
register = "LSW"
offset = 80
station = { label = { zh-CN = "网口号", en = "Network No" }, from = "NETWORK_NO" }
# 其中index为网口号，0-LAN1，1-LAN2，10-WIFI，11-4G
tags = [
    # MAC地址
    { id = 49, address = 80, write = false, label = { zh-CN = "MAC地址第1段", en = "MAC Address 1st" } },
    { id = 50, address = 81, write = false, label = { zh-CN = "MAC地址第2段", en = "MAC Address 2nd" } },
    { id = 51, address = 82, write = false, label = { zh-CN = "MAC地址第3段", en = "MAC Address 3rd" } },
    { id = 52, address = 83, write = false, label = { zh-CN = "MAC地址第4段", en = "MAC Address 4th" } },
    { id = 53, address = 84, write = false, label = { zh-CN = "MAC地址第5段", en = "MAC Address 5th" } },
    { id = 54, address = 85, write = false, label = { zh-CN = "MAC地址第6段", en = "MAC Address 6th" } },
    # IP地址
    { id = 55, address = 86, label = { zh-CN = "IP地址第1段", en = "IP Address 1st" } },
    { id = 56, address = 87, label = { zh-CN = "IP地址第2段", en = "IP Address 2nd" } },
    { id = 57, address = 88, label = { zh-CN = "IP地址第3段", en = "IP Address 3rd" } },
    { id = 58, address = 89, label = { zh-CN = "IP地址第4段", en = "IP Address 4th" } },
    # 子网掩码
    { id = 59, address = 90, label = { zh-CN = "子网掩码第1段", en = "Subnet Mask 1st" } },
    { id = 60, address = 91, label = { zh-CN = "子网掩码第2段", en = "Subnet Mask 2nd" } },
    { id = 61, address = 92, label = { zh-CN = "子网掩码第3段", en = "Subnet Mask 3rd" } },
    { id = 62, address = 93, label = { zh-CN = "子网掩码第4段", en = "Subnet Mask 4th" } },
    # 网关
    { id = 63, address = 94, label = { zh-CN = "网关第1段", en = "Gateway 1st" } },
    { id = 64, address = 95, label = { zh-CN = "网关第2段", en = "Gateway 2nd" } },
    { id = 65, address = 96, label = { zh-CN = "网关第3段", en = "Gateway 3rd" } },
    { id = 66, address = 97, label = { zh-CN = "网关第4段", en = "Gateway 4th" } },
    # DNS1
    { id = 67, address = 98, label = { zh-CN = "DNS1第1段", en = "DNS1 1st" } },
    { id = 68, address = 99, label = { zh-CN = "DNS1第2段", en = "DNS1 2nd" } },
    { id = 69, address = 100, label = { zh-CN = "DNS1第3段", en = "DNS1 3rd" } },
    { id = 70, address = 101, label = { zh-CN = "DNS1第4段", en = "DNS1 4th" } },
    # DNS2
    { id = 71, address = 102, label = { zh-CN = "DNS2第1段", en = "DNS2 1st" } },
    { id = 72, address = 103, label = { zh-CN = "DNS2第2段", en = "DNS2 2nd" } },
    { id = 73, address = 104, label = { zh-CN = "DNS2第3段", en = "DNS2 3rd" } },
    { id = 74, address = 105, label = { zh-CN = "DNS2第4段", en = "DNS2 4th" } },
    # # IPV6地址
    # { id = 75, address = 106, label = { zh-CN = "IPV6地址第1段", en = "IPV6 Address 1st" } },
    # { id = 76, address = 107, label = { zh-CN = "IPV6地址第2段", en = "IPV6 Address 2nd" } },
    # { id = 77, address = 108, label = { zh-CN = "IPV6地址第3段", en = "IPV6 Address 3rd" } },
    # { id = 78, address = 109, label = { zh-CN = "IPV6地址第4段", en = "IPV6 Address 4th" } },
    # { id = 79, address = 110, label = { zh-CN = "IPV6地址第5段", en = "IPV6 Address 5th" } },
    # { id = 80, address = 111, label = { zh-CN = "IPV6地址第6段", en = "IPV6 Address 6th" } },
    # { id = 81, address = 112, label = { zh-CN = "IPV6地址第7段", en = "IPV6 Address 7th" } },
    # { id = 82, address = 113, label = { zh-CN = "IPV6地址第8段", en = "IPV6 Address 8th" } },
    # # IPV6前缀长度
    # { id = 83, address = 114, label = { zh-CN = "IPV6前缀长度", en = "IPV6 Prefix Length" } },
    # # IPV6网关
    # { id = 84, address = 115, label = { zh-CN = "IPV6网关第1段", en = "IPV6 Gateway 1st" } },
    # { id = 85, address = 116, label = { zh-CN = "IPV6网关第2段", en = "IPV6 Gateway 2nd" } },
    # { id = 86, address = 117, label = { zh-CN = "IPV6网关第3段", en = "IPV6 Gateway 3rd" } },
    # { id = 87, address = 118, label = { zh-CN = "IPV6网关第4段", en = "IPV6 Gateway 4th" } },
    # { id = 88, address = 119, label = { zh-CN = "IPV6网关第5段", en = "IPV6 Gateway 5th" } },
    # { id = 89, address = 120, label = { zh-CN = "IPV6网关第6段", en = "IPV6 Gateway 6th" } },
    # { id = 90, address = 121, label = { zh-CN = "IPV6网关第7段", en = "IPV6 Gateway 7th" } },
    # { id = 91, address = 122, label = { zh-CN = "IPV6网关第8段", en = "IPV6 Gateway 8th" } },
    # # IPV6 DNS1
    # { id = 92, address = 123, label = { zh-CN = "IPV6 DNS1第1段", en = "IPV6 DNS1 1st" } },
    # { id = 93, address = 124, label = { zh-CN = "IPV6 DNS1第2段", en = "IPV6 DNS1 2nd" } },
    # { id = 94, address = 125, label = { zh-CN = "IPV6 DNS1第3段", en = "IPV6 DNS1 3rd" } },
    # { id = 95, address = 126, label = { zh-CN = "IPV6 DNS1第4段", en = "IPV6 DNS1 4th" } },
    # { id = 96, address = 127, label = { zh-CN = "IPV6 DNS1第5段", en = "IPV6 DNS1 5th" } },
    # { id = 97, address = 128, label = { zh-CN = "IPV6 DNS1第6段", en = "IPV6 DNS1 6th" } },
    # { id = 98, address = 129, label = { zh-CN = "IPV6 DNS1第7段", en = "IPV6 DNS1 7th" } },
    # { id = 99, address = 130, label = { zh-CN = "IPV6 DNS1第8段", en = "IPV6 DNS1 8th" } },
    # # IPV6 DNS2
    # { id = 100, address = 131, label = { zh-CN = "IPV6 DNS2第1段", en = "IPV6 DNS2 1st" } },
    # { id = 101, address = 132, label = { zh-CN = "IPV6 DNS2第2段", en = "IPV6 DNS2 2nd" } },
    # { id = 102, address = 133, label = { zh-CN = "IPV6 DNS2第3段", en = "IPV6 DNS2 3rd" } },
    # { id = 103, address = 134, label = { zh-CN = "IPV6 DNS2第4段", en = "IPV6 DNS2 4th" } },
    # { id = 104, address = 135, label = { zh-CN = "IPV6 DNS2第5段", en = "IPV6 DNS2 5th" } },
    # { id = 105, address = 136, label = { zh-CN = "IPV6 DNS2第6段", en = "IPV6 DNS2 6th" } },
    # { id = 106, address = 137, label = { zh-CN = "IPV6 DNS2第7段", en = "IPV6 DNS2 7th" } },
    # { id = 107, address = 138, label = { zh-CN = "IPV6 DNS2第8段", en = "IPV6 DNS2 8th" } },
    # 联网方式：0-手动IP，1-DHCP，2-IP手动，DNS自动，10-IPV6手动，11-IPV6DHCP，12-IPV6手动，DNS自动
    { id = 75, address = 106, label = { zh-CN = "联网方式", en = "Network Mode" } },
    # 网络命令：0-无，1-写入设置/重新获取IP，2-不写入，从当前配置重新获取IP，3-不写入，网络重置，4-停用此网络，5-启用此网络
    { id = 76, address = 107, label = { zh-CN = "网络命令", en = "Network Command" } },
    # 网络状态：0-无连接，1-有连接但无互联网，2-有连接且有互联网，3-被停用
    { id = 77, address = 108, write = false, label = { zh-CN = "网络状态", en = "Network Status" } },
]

[[TagClassifies]]
text = { zh-CN = "用户信息", en = "User Information" }
register = "LSW"
station = { label = { zh-CN = "用户组", en = "User Group" }, from = "USER_GROUP_NO" }
tags = [
    # 用户组名称
    { id = 78, address = 160, type = "string", length = 16, label = { zh-CN = "用户组名称", en = "User Group Name" }, index = "USER_GROUP_NO" },
    # 用户组权限号
    { id = 79, address = 176, type = "u64", length = 4, label = { zh-CN = "用户组权限号", en = "User Group Permission" }, desc = { zh-CN = "4个word,共64位,对应64个权限号", en = "4 words, 64 bits, corresponding to 64 permission numbers" }, index = "USER_GROUP_NO" },
    # 用户组状态：0-正常，1-列表隐藏，2-仅远程隐藏，3-仅本地隐藏，4-禁用，5-远程禁用，6-本地禁用（用户状态为0时继承组的状态）
    { id = 80, address = 180, label = { zh-CN = "用户组状态", en = "User Group Status" } },
    # 用户组控制系统子码(与中心控制系统子码一致的可被同步)
    { id = 81, address = 184, type = "string", length = 2, label = { zh-CN = "用户组控制系统子码", en = "User Group CCS Slave Code" } },
    # 用户账号，为了方便在屏上输入用的，账号和名称只允许一个为空
    { id = 82, address = 186, type = "string", length = 16, label = { zh-CN = "用户账号", en = "User Account" } },
    # 用户名称，账号和名称只允许一个为空
    { id = 83, address = 202, type = "string", length = 16, label = { zh-CN = "用户名称", en = "User Name" } },
    # 用户密码，密码允许为空
    { id = 84, address = 218, type = "string", length = 16, label = { zh-CN = "用户密码", en = "User Password" } },
    # 用户PIN码，为空表示未启用
    { id = 85, address = 234, type = "string", length = 16, label = { zh-CN = "用户PIN码", en = "User PIN" } },
    # 用户权限
    { id = 86, address = 250, type = "u64", length = 4, label = { zh-CN = "用户权限", en = "User Permission" }, desc = { zh-CN = "4个word,共64位,对应64个权限号", en = "4 words, 64 bits, corresponding to 64 permission numbers" } },
    # 用户所属组
    { id = 87, address = 254, type = "u64", length = 4, label = { zh-CN = "用户所属组", en = "User Group" }, desc = { zh-CN = "4个word,共64位,对应64个用户组", en = "4 words, 64 bits, corresponding to 64 user groups" } },
    # 用户绑定手机号(考虑到用户使用方便性，用ASCII字符串而不是数值分段)
    { id = 88, address = 258, type = "string", length = 8, label = { zh-CN = "用户绑定手机号", en = "User Mobile" } },
    # 用户绑定邮箱
    { id = 89, address = 266, type = "string", length = 32, label = { zh-CN = "用户绑定邮箱", en = "User Email" } },
    # 用户头像(MDI图标码)
    { id = 90, address = 298, type = "u32", length = 2, label = { zh-CN = "用户头像", en = "User Avatar" } },
    # 用户颜色
    { id = 91, address = 300, type = "u32", length = 2, label = { zh-CN = "用户颜色", en = "User Color" } },
    # 用户控制系统子码(与中心控制系统子码一致的可被同步)
    { id = 92, address = 302, type = "string", length = 2, label = { zh-CN = "用户控制系统子码", en = "User CCS Slave Code" } },
    # 设置用户状态：0-正常，1-列表隐藏，2-仅远程隐藏，3-仅本地隐藏，4-禁用，5-远程禁用，6-本地禁用，7-要求修改密码/PIN码，8-要求修改密码，9-要求修改PIN码
    { id = 93, address = 304, type = "u16", label = { zh-CN = "用户状态", en = "User Status" }, desc = { zh-CN = "0-正常，1-列表隐藏，2-仅远程隐藏，3-仅本地隐藏，4-禁用，5-远程禁用，6-本地禁用，7-要求修改密码/PIN码，8-要求修改密码，9-要求修改PIN码", en = "0-normal, 1-list hidden, 2-only remote hidden, 3-only local hidden, 4-disabled, 5-remote disabled, 6-local disabled, 7-require change password/PIN, 8-require change password, 9-require change PIN" }, items = [
        { value = 0, label = { zh-CN = "正常", en = "Normal" } },
        { value = 1, label = { zh-CN = "列表隐藏", en = "List Hidden" } },
        { value = 2, label = { zh-CN = "仅远程隐藏", en = "Only Remote Hidden" } },
        { value = 3, label = { zh-CN = "仅本地隐藏", en = "Only Local Hidden" } },
        { value = 4, label = { zh-CN = "禁用", en = "Disabled" } },
        { value = 5, label = { zh-CN = "远程禁用", en = "Remote Disabled" } },
        { value = 6, label = { zh-CN = "本地禁用", en = "Local Disabled" } },
        { value = 7, label = { zh-CN = "要求修改密码/PIN码", en = "Require Change Password/PIN" } },
        { value = 8, label = { zh-CN = "要求修改密码", en = "Require Change Password" } },
        { value = 9, label = { zh-CN = "要求修改PIN码", en = "Require Change PIN" } },
    ] },
    # 登录成功次数(超过最大值就从0开始)
    { id = 94, address = 306, write = false, label = { zh-CN = "登录成功次数", en = "User Login Count" } },
    # 从上次登录成功至今登录失败次数
    { id = 95, address = 308, write = false, label = { zh-CN = "登录失败次数", en = "User Login Fail Count" } },
    # 最近登录时间
    { id = 96, address = 312, write = false, type = "u64", length = 4, label = { zh-CN = "最近登录时间", en = "User Last Login Time" }, desc = { zh-CN = "自1970年1月1日(UTC)以来的秒数", en = "The number of seconds since January 1, 1970 (UTC)." } },
    # 最近登录IP,为0表示本地登录
    { id = 97, address = 316, write = false, label = { zh-CN = "最近登录IP第1段", en = "User Last Login IP 1st" } },
    { id = 98, address = 317, write = false, label = { zh-CN = "最近登录IP第2段", en = "User Last Login IP 2nd" } },
    { id = 99, address = 318, write = false, label = { zh-CN = "最近登录IP第3段", en = "User Last Login IP 3rd" } },
    { id = 100, address = 319, write = false, label = { zh-CN = "最近登录IP第4段", en = "User Last Login IP 4th" } },
]

[[TagClassifies]]
text = { zh-CN = "用户操作", en = "User Operation" }
register = "LSW"
a101 = { address = 250, write = false, label = { zh-CN = "用户组数量", en = "User Group Count" } }
102 = { address = 251, write = false, label = { zh-CN = "用户数量", en = "User Count" } }
tags = [
    # 用户组数量
    { id = 101, address = 250, write = false, label = { zh-CN = "用户组数量", en = "User Group Count" } },
    # 用户数量
    { id = 102, address = 251, write = false, label = { zh-CN = "用户数量", en = "User Count" } },
    # 操作命令
    # 用户操作：1-用户名登录，2-用户手机号登录，3-用户注销，4-追加用户，5-插入用户，6-更新用户，7-获取用户，8-删除用户，9-删除所有用户（除了当前的管理员），10-上移，11-下移，12-置顶，13-置底
    # 用户组操作：21-追加用户组，22-插入用户组，23-更新用户组，24-获取用户组，25-删除用户组，26-删除所有用户组（除了当前的管理员组），27-上移用户组，28-下移用户组，29-置顶用户组，30-置底用户组
    # 用户登录时，从index为0的提取用户名和密码进行对比，追加和插入用户，也从index为0的提取相关信息写入到对应index
    { id = 103, address = 252, label = { zh-CN = "用户操作命令", en = "User Command" }, items = [
        { value = 1, label = { zh-CN = "用户号登录", en = "User No Login" }, desc = { zh-CN = "需要参数先写入系统标签：[准备登录的用户号]和[准备登录的密码/PIN码]", en = "The parameter needs to be written first: [Prepare User No] and [Prepare Password/PIN]" } },
        { value = 2, label = { zh-CN = "用户账号/用户名/手机号/邮箱登录", en = "User Account/Name/Mobile/Email Login" }, desc = { zh-CN = "需要参数先写入系统标签：[准备登录的用户账号/用户名/手机号/邮箱]和[准备登录的密码/PIN码]", en = "The parameter needs to be written first: [Prepare User Account/Name/Mobile/Email] and [Prepare Password/PIN]" } },
        { value = 3, label = { zh-CN = "用户注销", en = "User Logout" } },
        { value = 5, label = { zh-CN = "写入/更新用户信息/密码", en = "Write/Update User Information/Password" }, desc = { zh-CN = "需要参数先写入系统标签：[准备操作的用户号],系统会将该用户号对应的用户信息相关地址更新到设备中", en = "The parameter needs to be written first: [Prepare User No], the system will update the user information related address corresponding to the user number to the device." } },
        { value = 6, label = { zh-CN = "删除用户", en = "Delete User" }, desc = { zh-CN = "需要参数先写入系统标签：[准备操作的用户号]", en = "The parameter needs to be written first: [Prepare User No]" } },
        { value = 7, label = { zh-CN = "删除所有用户（仅管理员可执行，并保留当前的管理员）", en = "Delete All Users (except the current administrator)" } },
        { value = 8, label = { zh-CN = "上移", en = "Move Up" }, desc = { zh-CN = "需要参数先写入系统标签：[准备操作的用户号]", en = "The parameter needs to be written first: [Prepare User No]" } },
        { value = 9, label = { zh-CN = "下移", en = "Move Down" }, desc = { zh-CN = "需要参数先写入系统标签：[准备操作的用户号]", en = "The parameter needs to be written first: [Prepare User No]" } },
        { value = 10, label = { zh-CN = "置顶", en = "Top" }, desc = { zh-CN = "需要参数先写入系统标签：[准备操作的用户号]", en = "The parameter needs to be written first: [Prepare User No]" } },
        { value = 11, label = { zh-CN = "置底", en = "Bottom" }, desc = { zh-CN = "需要参数先写入系统标签：[准备操作的用户号]", en = "The parameter needs to be written first: [Prepare User No]" } },
    ] },
    # 准备操作的用户号
    { id = 104, address = 253, label = { zh-CN = "准备操作的用户号", en = "Prepare User No" } },
    # 准备操作的用户名/手机号/账号/邮箱
    { id = 105, address = 254, type = "string", length = 32, label = { zh-CN = "准备操作的用户账号", en = "Prepare User Account" } },
    # 准备操作的用户密码/PIN码
    { id = 106, address = 286, type = "string", length = 16, label = { zh-CN = "准备操作的用户密码", en = "Prepare User Password" } },
    # 校验方式：0-自动校验，1-校验用户名和密码，2-校验手机号和密码，3-校验账号和密码，4-校验用户名和PIN码，5-校验手机号和PIN码，6-校验账号和PIN码
    { id = 107, address = 302, label = { zh-CN = "准备操作的校验方式", en = "Prepare Check Type" } },
    # 操作结果：0-未执行或执行成功，1-执行中，2-执行成功，>2执行出错（3表示权限不足，4表示用户不存在，5表示用户已存在，6表示用户被禁用，7表示登录校验错误）
    { id = 108, address = 303, label = { zh-CN = "操作结果", en = "Operation Result" } },
    # 用户安全选项：0-禁用登录，1-启用登录，2-仅远程启用登录，3-仅本地启用登录
    { id = 109, address = 304, label = { zh-CN = "用户安全选项", en = "User Security Option" } },
    # 自动注销时间，单位秒
    { id = 110, address = 305, label = { zh-CN = "自动注销时间", en = "Auto Logout Time" } },

]

# 硬件相关系统寄存器
[[TagClassifies]]
text = { zh-CN = "硬件相关", en = "Hardware" }
register = "LSW"
tags = [
    # 产品型号编号
    { id = 119, address = 340, type = "u32", length = 2, write = "false", label = { zh-CN = "产品型号", en = "Product Model" } },
    # 产品序列号
    { id = 120, address = 342, type = "string", length = 4, write = "false", label = { zh-CN = "产品序列号", en = "Product Serial Number" } },
    # 软件版本号
    { id = 121, address = 346, write = "false", label = { zh-CN = "软件版本号1段", en = "Software Version 1st" } },
    { id = 122, address = 347, write = "false", label = { zh-CN = "软件版本号2段", en = "Software Version 2nd" } },
    { id = 123, address = 348, write = "false", label = { zh-CN = "软件版本号3段", en = "Software Version 3rd" } },
    { id = 124, address = 349, write = "false", label = { zh-CN = "软件版本号4段", en = "Software Version 4th" } },
    # 硬件版本号
    { id = 125, address = 350, write = "false", label = { zh-CN = "硬件版本号", en = "Hardware Version" } },
    # 工程ID
    { id = 126, address = 351, length = 4, write = "false", label = { zh-CN = "工程ID", en = "Project ID" } },
    # 工程版本号
    { id = 127, address = 352, length = 2, write = "false", label = { zh-CN = "工程版本号", en = "Project Version" } },
    # 分辨率：0-无屏幕，然后从4寸普清到15寸高清
    { id = 128, address = 353, write = "false", label = { zh-CN = "分辨率", en = "Resolution" } },
    # 屏幕旋转：0-0度，1-90度，2-180度，3-270度
    { id = 129, address = 354, write = "false", label = { zh-CN = "屏幕旋转", en = "Screen Rotation" } },
    # 屏幕色深：16、24
    { id = 128, address = 354, write = "false", label = { zh-CN = "屏幕色深", en = "Screen Color Depth" } },
    # 屏幕厂商（仅开发用）
    { id = 129, address = 355, write = "false", label = { zh-CN = "屏幕厂商", en = "Screen Manufacturer" } },
    # 触摸类型：0-无触摸，1-电阻，2-电容
    { id = 130, address = 356, write = "false", label = { zh-CN = "触摸类型", en = "Touch Type" } },
    # 取位（0-7）是否有串口，（8-15）是否485，0-COM1，1-COM2,8-COM1,9-COM2
    { id = 131, address = 357.0, type = "bool", label = { zh-CN = "是否有COM1", en = "Has COM1" } },
    { id = 132, address = 357.1, type = "bool", label = { zh-CN = "是否有COM2", en = "Has COM2" } },
    { id = 133, address = 357.2, type = "bool", label = { zh-CN = "是否有COM3", en = "Has COM3" } },
    { id = 134, address = 357.3, type = "bool", label = { zh-CN = "是否有COM4", en = "Has COM4" } },
    { id = 135, address = 357.4, type = "bool", label = { zh-CN = "是否有COM5", en = "Has COM5" } },
    { id = 136, address = 357.5, type = "bool", label = { zh-CN = "是否有COM6", en = "Has COM6" } },
    { id = 137, address = 357.6, type = "bool", label = { zh-CN = "是否有COM7", en = "Has COM7" } },
    { id = 138, address = 357.7, type = "bool", label = { zh-CN = "是否有COM8", en = "Has COM8" } },
    { id = 139, address = 357.8, type = "bool", label = { zh-CN = "COM1是否RS485", en = "COM1 is RS485" } },
    { id = 140, address = 357.9, type = "bool", label = { zh-CN = "COM2是否RS485", en = "COM2 is RS485" } },
    { id = 141, address = 357.10, type = "bool", label = { zh-CN = "COM3是否RS485", en = "COM3 is RS485" } },
    { id = 142, address = 357.11, type = "bool", label = { zh-CN = "COM4是否RS485", en = "COM4 is RS485" } },
    { id = 143, address = 357.12, type = "bool", label = { zh-CN = "COM5是否RS485", en = "COM5 is RS485" } },
    { id = 144, address = 357.13, type = "bool", label = { zh-CN = "COM6是否RS485", en = "COM6 is RS485" } },
    { id = 145, address = 357.14, type = "bool", label = { zh-CN = "COM7是否RS485", en = "COM7 is RS485" } },
    { id = 146, address = 357.15, type = "bool", label = { zh-CN = "COM8是否RS485", en = "COM8 is RS485" } },
    # 取位（0-5）是否有网口，WIFI，4G,蓝牙，麦克风，喇叭，RFID
    { id = 147, address = 358.0, type = "bool", write = "false", label = { zh-CN = "是否有网口1", en = "Has Network Port 1" } },
    { id = 148, address = 358.1, type = "bool", write = "false", label = { zh-CN = "是否有网口2", en = "Has Network Port 2" } },
    { id = 149, address = 358.2, type = "bool", write = "false", label = { zh-CN = "是否有网口3", en = "Has Network Port 3" } },
    { id = 150, address = 358.3, type = "bool", write = "false", label = { zh-CN = "是否有网口4", en = "Has Network Port 4" } },
    { id = 151, address = 358.4, type = "bool", write = "false", label = { zh-CN = "是否有网口5", en = "Has Network Port 5" } },
    { id = 152, address = 358.5, type = "bool", write = "false", label = { zh-CN = "是否有WIFI", en = "Has WIFI" } },
    { id = 153, address = 358.6, type = "bool", write = "false", label = { zh-CN = "是否有4G", en = "Has 4G" } },
    { id = 154, address = 358.7, type = "bool", write = "false", label = { zh-CN = "是否有蓝牙", en = "Has Bluetooth" } },
    { id = 155, address = 358.8, type = "bool", write = "false", label = { zh-CN = "是否有麦克风", en = "Has Microphone" } },
    { id = 156, address = 358.9, type = "bool", write = "false", label = { zh-CN = "是否有喇叭", en = "Has Speaker" } },
    { id = 157, address = 358.10, type = "bool", write = "false", label = { zh-CN = "是否有RFID", en = "Has RFID" } },
    # 预留硬件取位
    { id = 158, address = 359, write = "false", label = { zh-CN = "预留硬件取位", en = "Reserved Hardware Bit" } },
    # FLASH/EMMC总空间
    { id = 159, address = 360, write = "false", label = { zh-CN = "存储总空间", en = "Storage Total Space" } },
    # FLASH/EMMC可用空间
    { id = 160, address = 362, write = "false", label = { zh-CN = "存储可用空间", en = "Storage Available Space" } },
    # DDR总空间
    { id = 161, address = 364, write = "false", label = { zh-CN = "内存总空间", en = "Memory Total Space" } },
    # DDR可用空间
    { id = 162, address = 366, write = "false", label = { zh-CN = "内存可用空间", en = "Memory Available Space" } },
    # CPU类型（仅开发用）
    { id = 163, address = 368, write = "false", label = { zh-CN = "CPU类型", en = "CPU Type" } },
    # CPU占用（仅开发用）
    { id = 164, address = 369, write = "false", label = { zh-CN = "CPU占用", en = "CPU Usage" } },
    # 帧率
    { id = 165, address = 370, write = "false", label = { zh-CN = "帧率", en = "Frame Rate" } },
    # 屏保时间
    { id = 166, address = 371, label = { zh-CN = "屏保时间(秒)", en = "Screen Saver Time(s)" } },
    # 屏幕正常背光亮度
    { id = 167, address = 372, label = { zh-CN = "屏幕正常背光亮度", en = "Screen Normal Brightness" }, min = 0, max = 100 },
    # 屏幕调低背光亮度
    { id = 168, address = 373, label = { zh-CN = "屏幕调低背光亮度", en = "Screen High Brightness" }, min = 0, max = 100 },
    # 屏幕调低亮度时间
    { id = 169, address = 374, label = { zh-CN = "屏幕调低亮度时间", en = "Screen Dim Time" } },
    # 屏幕关闭背光时间
    { id = 170, address = 375, label = { zh-CN = "屏幕关闭背光时间", en = "Screen Off Time" } },
    # 音量
    { id = 171, address = 376, label = { zh-CN = "音量", en = "Volume" }, min = 0, max = 100 },
    # 触摸X
    { id = 172, address = 377, write = "false", label = { zh-CN = "触摸X", en = "Touch X" } },
    # 触摸Y
    { id = 173, address = 378, write = "false", label = { zh-CN = "触摸Y", en = "Touch Y" } },
    # 开机密码
    { id = 174, address = 380, type = "string", length = 16, write = "false", label = { zh-CN = "开机密码", en = "Boot Password" } },
    # 下载密码
    { id = 175, address = 384, type = "string", length = 16, write = "false", label = { zh-CN = "下载密码", en = "Download Password" } },
    # 上载密码
    { id = 176, address = 388, type = "string", length = 16, write = "false", label = { zh-CN = "上载密码", en = "Upload Password" } },
    # 关机/休眠/睡眠密码
    { id = 177, address = 392, type = "string", length = 16, write = "false", label = { zh-CN = "关机/休眠/睡眠密码", en = "Shutdown Password" } },
    # 蜂鸣器开关
    { id = 178, address = 396.0, type = "bool", label = { zh-CN = "蜂鸣器开关", en = "Beep Enable" } },
    # 语音是否静音
    { id = 179, address = 396.1, type = "bool", label = { zh-CN = "语音是否静音", en = "Voice Mute" } },
    # 是否有触摸
    { id = 180, address = 396.2, type = "bool", write = "false", label = { zh-CN = "是否有触摸", en = "Has Touch" } },
    # 是否降低亮度
    { id = 181, address = 396.3, type = "bool", label = { zh-CN = "是否降低亮度", en = "DIM_BRIGHTNESS" } },
    # 是否关闭背光
    { id = 182, address = 396.4, type = "bool", label = { zh-CN = "是否关闭背光", en = "OFF_BRIGHTNESS" } },
    # 是否允许上载
    { id = 183, address = 396.5, type = "bool", label = { zh-CN = "是否允许上载", en = "ALLOW_UPLOAD" } },
    # 是否允许下载
    { id = 184, address = 396.6, type = "bool", label = { zh-CN = "是否允许下载", en = "ALLOW_DOWNLOAD" } },
    # 重启HMI
    { id = 185, address = 396.7, type = "bool", label = { zh-CN = "重启HMI", en = "REBOOT_DEVICE" } },
    # 进入触摸校准
    { id = 186, address = 397, type = "bool", label = { zh-CN = "进入触摸校准", en = "ENTER_TOUCH_CALIBRATION" } },
    # 更新工程及固件(1-更新工程及固件,2-仅更新固件,3-回滚更新前工程和固件,4-恢复HMI出厂设置,5-恢复设备厂商的出厂设置)
    { id = 187, address = 398, type = "bool", label = { zh-CN = "更新工程及固件", en = "UPDATE_PROJECT_AND_FIRMWARE" } },
    # 进入关机/休眠/睡眠
    { id = 186, address = 396.8, type = "bool", label = { zh-CN = "进入关机/休眠/睡眠", en = "ENTER_SHUTDOWN" } },
    # # 截屏操作(截屏保存在本地/USB/云端/是否立即打印的组合,本地始终只存最后5张)
    # { id = 188, address = 399, type = "bool", label = { zh-CN = "截屏操作", en = "SCREEN_SHOT_OPERATION" } },
    # # 截屏坐标X
    # { id = 189, address = 400, type = "bool", label = { zh-CN = "截屏坐标X", en = "SCREEN_SHOT_X" } },
    # # 截屏坐标Y
    # { id = 190, address = 401, type = "bool", label = { zh-CN = "截屏坐标Y", en = "SCREEN_SHOT_Y" } },
]


# # 中心控制系统
# [[Tags.classify]]
# text = { zh-CN = "中心控制系统", en = "IOT CCS" }
# offset = 500
# tags = [
#     # 中心控制系统服务器地址（支持IP和域名，域名最长128个字符，用于私有化部署）
#     { id = 209, address = 485, type = "string", length = 128, label = { zh-CN = "中心控制系统服务器地址", en = "IOT CCS Server Address" } },
#     # 中心控制系统服务器端口
#     { id = 210, address = 549, label = { zh-CN = "中心控制系统服务器端口", en = "IOT CCS Server Port" } },
#     # 中心控制类型: 0-未启用，1-被控端，2-主控端
#     { id = 211, address = 548, label = { zh-CN = "中心控制类型", en = "IOT CCS Type" }, items = [
#         { label = { zh-CN = "未启用", en = "Not Enabled" }, value = 0 },
#         { label = { zh-CN = "被控端", en = "Slave" }, value = 1 },
#         { label = { zh-CN = "主控端", en = "Master" }, value = 2 },
#     ] },
#     # 控制状态：0-未连接，1-已连接，2-连接中，3-连接失败，等待重连
#     { id = 212, address = 549, write = "false", label = { zh-CN = "控制状态", en = "IOT CCS Status" }, items = [
#         { label = { zh-CN = "未连接", en = "Not Connected" }, value = 0 },
#         { label = { zh-CN = "已连接", en = "Connected" }, value = 1 },
#         { label = { zh-CN = "连接中", en = "Connecting" }, value = 2 },
#         { label = { zh-CN = "连接失败", en = "Connect Failed" }, value = 3 },
#         { label = { zh-CN = "等待重连", en = "Waiting Reconnect" }, value = 4 },
#     ] },
#     # 上次连接失败原因
#     { id = 213, address = 550, write = "false", label = { zh-CN = "上次连接失败原因", en = "IOT CCS Last Connect Fail Reason" } },
#     # 中心控制主码（相同主码的组成一个中心控制系统，包括服务端和客户端都要相同主码，如果是跨互联网，则需要在一个企业账号下主码相同）
#     { id = 214, address = 551, type = "string", length = 16, label = { zh-CN = "中心控制主码", en = "IOT CCS Master Code" } },
#     # 中心控制子码（用于同一个CCS下，不同组之间的区分，比如推送用户、配方，可选择子码推送）
#     { id = 215, address = 552, type = "string", length = 16, label = { zh-CN = "中心控制子码", en = "IOT CCS Slave Code" } },
#     # 中心控制被控工程识别码(识别码相同工程才能被主控更新工程，防止更新工程错误)
#     { id = 216, address = 555, type = "string", length = 16, label = { zh-CN = "中心控制被控工程识别码", en = "IOT CCS Project Code" } },
#     # 是否允许控制在线
#     { id = 217, address = 556.0, type = "bool", label = { zh-CN = "是否允许控制在线", en = "IOT CCS Enable Online" } },
#     # 是否开放用户管理
#     { id = 218, address = 556.1, type = "bool", label = { zh-CN = "是否开放用户管理", en = "IOT CCS Enable User Manage" } },
#     # 是否开放配方管理
#     { id = 219, address = 556.2, type = "bool", label = { zh-CN = "是否开放配方管理", en = "IOT CCS Enable Recipe Manage" } },
#     # 是否开放远程查看
#     { id = 220, address = 556.3, type = "bool", label = { zh-CN = "是否开放远程查看", en = "IOT CCS Enable View" } },
#     # 是否开放数据上报
#     { id = 221, address = 556.4, type = "bool", label = { zh-CN = "是否开放数据上报", en = "IOT CCS Enable Up Data" } },
#     # 是否开放数据远程写入
#     { id = 222, address = 556.5, type = "bool", label = { zh-CN = "是否开放数据远程写入", en = "IOT CCS Enable Write" } },
#     # 是否开放文件管理
#     { id = 223, address = 556.6, type = "bool", label = { zh-CN = "是否开放文件管理", en = "IOT CCS Enable File Manage" } },
#     # 是否允许远程重启
#     { id = 224, address = 556.7, type = "bool", label = { zh-CN = "是否允许远程重启", en = "IOT CCS Enable Reboot" } },
#     # 是否允许远程更新工程
#     { id = 225, address = 556.8, type = "bool", label = { zh-CN = "是否允许远程更新工程", en = "IOT CCS Enable Update Project" } },
# ]

# [[Tags.classify]]
# text = { zh-CN = "配方操作", en = "Recipe Operation" }
# index = "RECIPE_NO"
# tags = [
#     # 操作命令
#     # 配方操作
#     #   - 1：下载当前行配方到PLC（目标地址在配方的属性中预先指定传输地址，如果未指定，则返回错误代码）
#     #   - 2：从PLC上载到当前行配方（源地址在配方的属性中预先指定传输地址，如果未指定，则返回错误代码）
#     #   - 3：新增一行配方（10001为1时，表示复制最后一组配方数据）
#     #   - 4：插入一行配方（在当前组位置插入，10001为1时，表示复制当前组配方数据）
#     #   - 5：删除当前组配方
#     #   - 6：清除当前组配方值
#     #   - 7：把当前组配方上移
#     #   - 8：把当前组配方下移
#     #   - 9：把当前组配方置顶
#     #   - 10：把当前组配方置底
#     #   - 11：复制配方到指定站号（先往10001写入目标站号）
#     #   - 12：从指定站号复制配方（先往10001写入源站号）
#     #   - 13：把配方导出到USB
#     #     - 地址10001：表示源文件站号，0则表示当前站号
#     #     - 地址10002
#     #       - 0-目标文件默认为RECIPE[站号].rcp但如果重复加后缀
#     #       - 1-固定RECIPE[站号].rcp可覆盖
#     #       - 2-固定RECIPE[站号].rcp不可覆盖，已存在则报错
#     #   - 14：从USB导入配方（源文件名固定为RECIPE[站号].rcp）
#     #     - 地址10001：表示源文件站号，0则表示当前站号
#     #   - 15：把配方指定组导出到USB（地址10001和10002为源组号和长度，地址10003为目标组号）
#     #   - 16：从USB导入配方指定组（地址10001和10002为源组号和长度，地址10003为目标组号）
#     #   - 99：清除当前站号配方数据，站号0则清除所有配方
#     #   - 100：立即将内存写入FLASH(避免1分钟的缓存丢数据)
#     #     - 地址10001：0表示所有站号，否则只写当前站号
#     { id = 300, address = 0, label = { zh-CN = "操作命令", en = "Operation Command" }, items = [
#         { label = { zh-CN = "下载当前行配方到PLC", en = "Download Current Line Recipe to PLC" }, value = 1 },
#         { label = { zh-CN = "从PLC上载到当前行配方", en = "Upload from PLC to Current Line Recipe" }, value = 2 },
#         { label = { zh-CN = "新增一行配方", en = "Add a New Line Recipe" }, value = 3 },
#         { label = { zh-CN = "插入一行配方", en = "Insert a New Line Recipe" }, value = 4 },
#         { label = { zh-CN = "删除当前组配方", en = "Delete Current Group Recipe" }, value = 5 },
#         { label = { zh-CN = "清除当前组配方值", en = "Clear Current Group Recipe Value" }, value = 6 },
#         { label = { zh-CN = "把当前组配方上移", en = "Move Current Group Recipe Up" }, value = 7 },
#         { label = { zh-CN = "把当前组配方下移", en = "Move Current Group Recipe Down" }, value = 8 },
#     ] },
#     # 操作参数
#     { id = 301, address = 1, label = { zh-CN = "操作参数", en = "Operation Parameter" } },
#     # 配方操作结果
#     # 0：无操作或操作成功
#     # 1：操作中
#     # <0：错误代码
#     { id = 302, address = 2, label = { zh-CN = "配方操作结果", en = "Recipe Operation Result" }, items = [
#         { label = { zh-CN = "无操作或操作成功", en = "No Operation or Operation Success" }, value = 0 },
#         { label = { zh-CN = "操作中", en = "Operation in Progress" }, value = 1 },
#         { label = { zh-CN = "错误代码", en = "Error Code" }, value = -1 },
#     ] },
#     # 配方索引
#     { id = 303, address = 3, label = { zh-CN = "配方索引", en = "Recipe Index" } },
# ]

# [[Tags.classify]]
# text = { zh-CN = "文件操作", en = "File Operation" }
# register = "FS_OPA"
# index = "FILE_NO"
# tags = [
#     # 文件存储位置: 0-DEV，1-USB
#     { id = 304, address = 0, label = { zh-CN = "文件存储位置", en = "File Storage Location" }, items = [
#         { label = { zh-CN = "本机", en = "Local" }, value = 0 },
#         { label = { zh-CN = "USB", en = "USB" }, value = 1 },
#     ] },
#     # 文件操作
#     # - 1：操作命令
#     #   - 1：复制文件到指定站号（先往2写入目标站号）
#     #   - 12：从指定站号复制文件（先往2写入源站号）
#     #   - 13：把文件导出到USB
#     #     - 地址2：表示源文件站号，0则表示当前站号
#     #     - 地址3
#     #       - 0-目标文件默认为file_store[站号].data但如果重复加后缀
#     #       - 1-固定file_store[站号].data可覆盖
#     #       - 2-固定file_store[站号].data不可覆盖，已存在则报错
#     #   - 14：从USB导入内部（源文件名固定为file_store[站号].rcp）
#     #     - 地址2：表示源文件站号，0则表示当前站号
#     #   - 99：删除当前站号文件数据，站号0表示清除所有
#     #   - 100：立即将内存写入FLASH(避免1分钟的缓存丢数据)
#     #     - 地址2：0表示所有站号，否则只写当前站号
#     { id = 305, address = 1, label = { zh-CN = "文件操作", en = "File Operation" }, items = [

#     ] },

#     # 文件操作参数
#     { id = 306, address = 2, label = { zh-CN = "文件操作参数", en = "File Operation Parameter" } },
#     # 文件操作结果
#     # 0：无操作或操作成功
#     # 1：操作中
#     # <0：错误代码
#     { id = 307, address = 3, label = { zh-CN = "文件操作结果", en = "File Operation Result" } },
# ]

# # DB寄存器
# [[Register]]
# name = "DB_SERVER"
# register_type = "DB_SERVER"
# text = { zh-CN = "数据库服务器", en = "Database Server" }
# register_start = 0
# address = [
#     # 数据库服务器类型(0-Sqlite, 1-MySQL，2-PostgreSQL，3-SQLServer)
#     { id = 308, start = 0, length = 1, access = "rw", label = "DB_SERVER_TYPE", index = "DB_SERVER_NO" },
#     # 服务器地址，支持IP和域名，域名最长128个字符(sqlite就是文件名了)
#     { id = 309, start = 1, length = 64, access = "rw", label = "DB_SERVER_ADDRESS", index = "DB_SERVER_NO" },
#     # SQL服务器端口(sqlite默认5432)
#     { id = 310, start = 65, length = 2, access = "rw", label = "DB_SERVER_PORT", index = "DB_SERVER_NO" },
#     # 数据库名称
#     { id = 311, start = 67, length = 64, access = "rw", label = "DB_DATABASE", index = "DB_SERVER_NO" },
#     # 数据库用户名
#     { id = 312, start = 131, length = 32, access = "rw", label = "DB_USER_NAME", index = "DB_SERVER_NO" },
#     # 数据库密码
#     { id = 313, start = 163, length = 32, access = "rw", label = "DB_PASSWORD", index = "DB_SERVER_NO" },
#     # 其他连接参数，字符串存储（GB2312编码）
#     { id = 314, start = 199, length = 64, access = "rw", label = "DB_OTHER_PARAM", index = "DB_SERVER_NO" },
#     # 超时时间，单位秒
#     { id = 315, start = 263, length = 2, access = "rw", label = "DB_TIMEOUT", index = "DB_SERVER_NO" },
#     # 数据库操作(0-不连接，1-开始连接，2-断开连接)，设置2可以表示禁用
#     { id = 316, start = 265, length = 1, access = "rw", label = "DB_OPERATION", index = "DB_SERVER_NO" },
#     # 数据库状态（0-未连接，1-已连接，2-连接中，3-连接失败）
#     { id = 317, start = 266, length = 1, access = "r", label = "DB_STATUS", index = "DB_SERVER_NO" },
#     # 数据库错误代码
#     { id = 318, start = 267, length = 1, access = "r", label = "DB_ERROR_CODE", index = "DB_SERVER_NO" },
# ]

# # DB查询数据（也可能为可编辑的表数据）
# [[Register]]
# name = "DB_QUERY"
# text = { zh-CN = "数据库查询数据", en = "Database Query Data" }
# register_type = "DB_QUERY"
# register_start = 0
# address = [
#     # 数据库表数据
#     { id = 319, start = 0, length = 65535000, access = "rw", label = "DB_QUERY_DATA", index = "QUERY_NO" },
# ]

# # DB当前行数据
# [[Register]]
# name = "DB_ROW"
# text = { zh-CN = "数据库当前行数据", en = "Database Current Row Data" }
# register_type = "DB_ROW"
# register_start = 0
# address = [
#     # 数据库表当前行数据
#     { id = 320, start = 0, length = 1999, access = "rw", label = "DB_CURRENT_ROW", index = "QUERY_NO" },
# ]

# # DB表操作
# #   - 1：执行查询（一个QUERY支持多个SQL，从0开始，1-指定查询SQL，2-199为SQL拼接参数，如果是可翻页的，则可以定义2-3为页数，4-为每页行数）
# #   - 2：更新数据到数据库(自动记录好哪些数据有更新，包括DB_DATA和DB_ROW)
# #   - 3：新增行（索引会加1，当前行数据留空），如果1-2不为0，则表示复制指定行数据
# #   - 4：删除当前行数据
# #   - 5：删除表的所有行数据
# #   - 6：针对已经查询出来的数据在界面端进行一下简单过滤（提前设计好过滤参数，同样1-指定过滤配置，2-199为过滤SQL拼接参数）
# #   - 7：针对已经查询出来的数据在界面端进行一下简单排序（提前设计好排序参数，同样1-指定排序配置，2-199为排序SQL拼接参数）
# [[Register]]
# name = "DB_OPA"
# text = { zh-CN = "数据库操作", en = "Database Operation" }
# register_type = "DB_OPA"
# register_start = 0
# address = [
#     # 操作命令
#     { id = 321, start = 0, length = 1, access = "rw", label = "DB_OPERATION", index = "QUERY_NO" },
#     # 操作参数
#     { id = 322, start = 1, length = 199, access = "rw", label = "DB_OPERATION_PARAM", index = "QUERY_NO" },
#     # 操作结果
#     { id = 323, start = 200, length = 1, access = "r", label = "DB_OPERATION_RESULT", index = "QUERY_NO" },
#     # 表指定行
#     { id = 324, start = 201, length = 2, access = "rw", label = "DB_TABLE_ROW_INDEX", index = "QUERY_NO" },
# ]
