#!/bin/bash

# set -e
# set -o pipefail

root_dir=$(realpath $(dirname $0))

# 编译
function urq_build(){
    local platform=$1
    local type=$2
    local editor=$3

    local build_dir="${root_dir}/build/${platform}/${type}"
    local args="-S ${root_dir} -B $build_dir -DCMAKE_TOOLCHAIN_FILE='${root_dir}/cmake/${platform}-${type}.cmake'"
    if [ "$platform" == "wasm" ]; then

        source "${HOME}/toolchain/emsdk/emsdk_env.sh" 
        if [ "$editor" == "editor" ]; then
            build_dir="${root_dir}/build/editor/${type}"
            args="-S ${root_dir} -B $build_dir -DCMAKE_TOOLCHAIN_FILE='${root_dir}/cmake/editor-${type}.cmake'"
        fi
    fi

    if [ "$platform" == "wasm" ]; then
        emcmake cmake $args
    else
        cmake $args
    fi

    cmake --build $build_dir -j 
}

# 修改 .clangd 文件
function change_clangd(){
    if [ -f "$root_dir/.clangd" ]; then
        echo "rm $root_dir/.clangd"
        # rm $root_dir/.clangd
    fi
    echo "cp $root_dir/scripts/$1.clangd $root_dir/.clangd"

    # 复制 .clangd 文件，并脱敏用户个人信息
    cat $root_dir/scripts/$1.clangd | sed "s|{HOME}|${HOME}|g" > $root_dir/.clangd
}

# 复制 wasm 文件
function copy_to_web(){
    dst=/mnt/d/code/urq/urq_ts/public
    files=("urq-ui.js" "urq-ui.wasm" "urq-ui.wasm.map")

    for file in ${files[@]}; do
        if [ -f "$dst/$file" ]; then
            echo "rm $dst/$file"
            rm $dst/$file
        fi
        echo "cp ${root_dir}/build/wasm/debug/ui/$file $dst/$file"
        cp ${root_dir}/build/wasm/debug/ui/$file $dst/$file
    done
}

# 执行测试
function run_test(){
    urq_build linux_64 debug
    find $root_dir/build/linux_64/debug -name "*.gcda" -exec rm {} \;

    # 遍历所有测试用例
    for file in $(find $root_dir/build -name "urq-*-test-*" -type f -perm /a+x); do
        echo "run test: $(basename $file)"
        $file
    done

    lcov --directory $root_dir/build/linux_64/debug \
        --capture \
        --output-file $root_dir/.tmp/coverage.info \
        >> /dev/null

    genhtml $root_dir/.tmp/coverage.info \
        --output-directory $root_dir/.tmp/coverage\
        >> /dev/null
}

case "$1" in
    "arm_v7hf")
        urq_build arm_v7hf debug
        exit 0
        ;;
    "clangd")
        change_clangd $2
        exit 0
        ;;
    "help")
        echo "Usage: urq {server|wasm|asset|proto|clangd|help}"
        echo "asset : 复制工程文件"
        echo "clangd: 修改 .clangd 文件"
        echo "help  : 显示这条帮助"
        echo "proto : 复制 proto 文件"
        echo "server: 启动界面服务器"
        echo "wasm  : 编译 wasm"
        exit 0
        ;;
    "run")
        $root_dir/build/linux_64/debug/ui/urq-ui --project $root_dir/asset
        exit 0
        ;;
    "server")
        $root_dir/build/linux_64/debug/example/urq-ui-server
        exit 0
        ;;
    "test")
        run_test
        exit 0
        ;;
    "wasm")
        urq_build wasm debug
        exit 0
        ;;
    "editor")
        urq_build wasm debug editor
        exit 0
        ;;
esac

urq_build linux_64 debug
#urq_build linux_64 release
#urq_build arm_v7hf release
# find $root_dir/build/linux_64/debug -name "*.gcda" -exec rm {} \;

# urq_build arm_v7hf debug
# ${root_dir}/build/linux_64/debug/example/server
# ${root_dir}/build/linux_64/debug/ui/urq_ui --project ${root_dir}/asset

