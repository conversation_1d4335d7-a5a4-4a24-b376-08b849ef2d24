$CurrentDir = Get-Location

$RuntimeDir = $PSScriptRoot
$ZdStationDir = "${PSScriptRoot}/../zd-station"

# # 不再需要切换到父目录

# if (-not (Test-Path $RuntimeDir)) {
#     git clone https://gitee.com/urq/urq_ts.git zendia-runtime
#     pnpm i
#     Set-Location $RuntimeDir
#     npm i
# }
# else {
#     Set-Location $RuntimeDir
#     git pull
#     npm i
#     npm run build
# }

git pull

$Files = @("editor.js", "editor.wasm")

foreach ($File in $Files) {
    if (Test-Path "${ZdStationDir}/public/${File}") {
        Write-Host "Remove ${ZdStationDir}/public/${File}"
        Remove-Item "${ZdStationDir}/public/${File}"
    }
    Write-Host "Copy ${RuntimeDir}/public/${File} to ${ZdStationDir}/public/${File}"
    Copy-Item -Path "${RuntimeDir}/public/${File}" -Destination "${ZdStationDir}/public/${File}"
}

Write-Host "Copy ${RuntimeDir}/src/runtime/UrqModule.ts to ${ZdStationDir}/src/zd-ui-ts/src/UrqModule.ts"
Copy-Item -Path "${RuntimeDir}/src/runtime/UrqModule.ts" -Destination "${ZdStationDir}/src/zd-ui-ts/src/UrqModule.ts"

Set-Location $CurrentDir