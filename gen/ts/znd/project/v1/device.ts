// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/device.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  AllTypeValue,
  DataFormatType,
  dataFormatTypeFromJSON,
  dataFormatTypeToJSON,
  DataReferenceInt16,
  DataReferenceUInt16,
  DataReferenceUInt32,
  DataReferenceUInt8,
  NumberValue,
  StructType,
  VariableReference,
} from "./variable";

export const protobufPackage = "znd.project.v1";

/** 协议参数类型 */
export enum ProtocolParamType {
  PROTOCOL_PARAM_TYPE_UNSPECIFIED = 0,
  /** PROTOCOL_PARAM_TYPE_MODBUS_BASE - MODBUS的基址（0-基址0，1-基址1） */
  PROTOCOL_PARAM_TYPE_MODBUS_BASE = 1,
  /** PROTOCOL_PARAM_TYPE_MODBUS_WRITE_0X - MODBUS的0区写功能码（0-自动判断、1-只用05、2-只用15） */
  PROTOCOL_PARAM_TYPE_MODBUS_WRITE_0X = 2,
  /** PROTOCOL_PARAM_TYPE_MODBUS_WRITE_4X - MODBUS的4区写功能码（0-自动判断、1-只用06、2-只用16） */
  PROTOCOL_PARAM_TYPE_MODBUS_WRITE_4X = 3,
  /** PROTOCOL_PARAM_TYPE_MODBUS_ADDRESS_SYSTEM - MODBUS的地址进制（0-十进制、8-八进制、16-十六进制） */
  PROTOCOL_PARAM_TYPE_MODBUS_ADDRESS_SYSTEM = 4,
  UNRECOGNIZED = -1,
}

export function protocolParamTypeFromJSON(object: any): ProtocolParamType {
  switch (object) {
    case 0:
    case "PROTOCOL_PARAM_TYPE_UNSPECIFIED":
      return ProtocolParamType.PROTOCOL_PARAM_TYPE_UNSPECIFIED;
    case 1:
    case "PROTOCOL_PARAM_TYPE_MODBUS_BASE":
      return ProtocolParamType.PROTOCOL_PARAM_TYPE_MODBUS_BASE;
    case 2:
    case "PROTOCOL_PARAM_TYPE_MODBUS_WRITE_0X":
      return ProtocolParamType.PROTOCOL_PARAM_TYPE_MODBUS_WRITE_0X;
    case 3:
    case "PROTOCOL_PARAM_TYPE_MODBUS_WRITE_4X":
      return ProtocolParamType.PROTOCOL_PARAM_TYPE_MODBUS_WRITE_4X;
    case 4:
    case "PROTOCOL_PARAM_TYPE_MODBUS_ADDRESS_SYSTEM":
      return ProtocolParamType.PROTOCOL_PARAM_TYPE_MODBUS_ADDRESS_SYSTEM;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ProtocolParamType.UNRECOGNIZED;
  }
}

export function protocolParamTypeToJSON(object: ProtocolParamType): string {
  switch (object) {
    case ProtocolParamType.PROTOCOL_PARAM_TYPE_UNSPECIFIED:
      return "PROTOCOL_PARAM_TYPE_UNSPECIFIED";
    case ProtocolParamType.PROTOCOL_PARAM_TYPE_MODBUS_BASE:
      return "PROTOCOL_PARAM_TYPE_MODBUS_BASE";
    case ProtocolParamType.PROTOCOL_PARAM_TYPE_MODBUS_WRITE_0X:
      return "PROTOCOL_PARAM_TYPE_MODBUS_WRITE_0X";
    case ProtocolParamType.PROTOCOL_PARAM_TYPE_MODBUS_WRITE_4X:
      return "PROTOCOL_PARAM_TYPE_MODBUS_WRITE_4X";
    case ProtocolParamType.PROTOCOL_PARAM_TYPE_MODBUS_ADDRESS_SYSTEM:
      return "PROTOCOL_PARAM_TYPE_MODBUS_ADDRESS_SYSTEM";
    case ProtocolParamType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 协议类型 */
export enum ProtocolType {
  PROTOCOL_TYPE_UNSPECIFIED = 0,
  /** PROTOCOL_TYPE_MODBUS_MASTER - 主站，如果是COM，则使用RTU，否则使用以太网TCP */
  PROTOCOL_TYPE_MODBUS_MASTER = 1,
  /** PROTOCOL_TYPE_MODBUS_SLAVE - 从站，如果是COM，则使用RTU，否则使用以太网TCP */
  PROTOCOL_TYPE_MODBUS_SLAVE = 2,
  PROTOCOL_TYPE_MODBUS_MASTER_ASCII = 3,
  /** PROTOCOL_TYPE_MODBUS_SLAVE_ASCII - MODBUS从站 */
  PROTOCOL_TYPE_MODBUS_SLAVE_ASCII = 4,
  /** PROTOCOL_TYPE_SIEMENS_PPI - 西门子 */
  PROTOCOL_TYPE_SIEMENS_PPI = 10,
  /** PROTOCOL_TYPE_SIEMENS_MPI2 - 西门子 */
  PROTOCOL_TYPE_SIEMENS_MPI2 = 11,
  /** PROTOCOL_TYPE_SIEMENS_MPI3 - 西门子 */
  PROTOCOL_TYPE_SIEMENS_MPI3 = 12,
  /** PROTOCOL_TYPE_SIEMENS_MPI4 - 西门子 */
  PROTOCOL_TYPE_SIEMENS_MPI4 = 13,
  /** PROTOCOL_TYPE_SIEMENS_S7COMM - 西门子 */
  PROTOCOL_TYPE_SIEMENS_S7COMM = 14,
  /** PROTOCOL_TYPE_MITSUBISHI_MC - 三菱 */
  PROTOCOL_TYPE_MITSUBISHI_MC = 20,
  /** PROTOCOL_TYPE_MITSUBISHI_MC1E - 三菱 */
  PROTOCOL_TYPE_MITSUBISHI_MC1E = 21,
  /** PROTOCOL_TYPE_MITSUBISHI_MC2E - 三菱 */
  PROTOCOL_TYPE_MITSUBISHI_MC2E = 22,
  /** PROTOCOL_TYPE_MITSUBISHI_MC3E - 三菱 */
  PROTOCOL_TYPE_MITSUBISHI_MC3E = 23,
  /** PROTOCOL_TYPE_MITSUBISHI_MC4C - 三菱 */
  PROTOCOL_TYPE_MITSUBISHI_MC4C = 24,
  /** PROTOCOL_TYPE_MITSUBISHI_SLMP - 三菱 */
  PROTOCOL_TYPE_MITSUBISHI_SLMP = 25,
  /** PROTOCOL_TYPE_OMRON_FINS - 欧姆龙 */
  PROTOCOL_TYPE_OMRON_FINS = 30,
  /** PROTOCOL_TYPE_OMRON_HOSTLINK - 欧姆龙 */
  PROTOCOL_TYPE_OMRON_HOSTLINK = 31,
  /** PROTOCOL_TYPE_OMRON_ENTERNET_CIP - 欧姆龙 */
  PROTOCOL_TYPE_OMRON_ENTERNET_CIP = 32,
  /** PROTOCOL_TYPE_FATEK - 永宏 */
  PROTOCOL_TYPE_FATEK = 40,
  /** PROTOCOL_TYPE_VIGOR_VSPROTOCOL - 丰炜 */
  PROTOCOL_TYPE_VIGOR_VSPROTOCOL = 41,
  /** PROTOCOL_TYPE_PANASONIC_NEWTOCOL - 松下 */
  PROTOCOL_TYPE_PANASONIC_NEWTOCOL = 42,
  /** PROTOCOL_TYPE_ROCKWELL_IDEC_SERIAL - 罗克韦尔 */
  PROTOCOL_TYPE_ROCKWELL_IDEC_SERIAL = 43,
  /** PROTOCOL_TYPE_AIBUS - 宇电 */
  PROTOCOL_TYPE_AIBUS = 44,
  /** PROTOCOL_TYPE_MAXCOMM - MAXCOMM，连接另一台HMI设备 */
  PROTOCOL_TYPE_MAXCOMM = 90,
  /** PROTOCOL_TYPE_FREE - 自由协议 */
  PROTOCOL_TYPE_FREE = 98,
  /** PROTOCOL_TYPE_PASS_THROUGH - 透传协议（仅透传用） */
  PROTOCOL_TYPE_PASS_THROUGH = 99,
  /** PROTOCOL_TYPE_LOCAL_HMI - 本地设备 */
  PROTOCOL_TYPE_LOCAL_HMI = 100,
  UNRECOGNIZED = -1,
}

export function protocolTypeFromJSON(object: any): ProtocolType {
  switch (object) {
    case 0:
    case "PROTOCOL_TYPE_UNSPECIFIED":
      return ProtocolType.PROTOCOL_TYPE_UNSPECIFIED;
    case 1:
    case "PROTOCOL_TYPE_MODBUS_MASTER":
      return ProtocolType.PROTOCOL_TYPE_MODBUS_MASTER;
    case 2:
    case "PROTOCOL_TYPE_MODBUS_SLAVE":
      return ProtocolType.PROTOCOL_TYPE_MODBUS_SLAVE;
    case 3:
    case "PROTOCOL_TYPE_MODBUS_MASTER_ASCII":
      return ProtocolType.PROTOCOL_TYPE_MODBUS_MASTER_ASCII;
    case 4:
    case "PROTOCOL_TYPE_MODBUS_SLAVE_ASCII":
      return ProtocolType.PROTOCOL_TYPE_MODBUS_SLAVE_ASCII;
    case 10:
    case "PROTOCOL_TYPE_SIEMENS_PPI":
      return ProtocolType.PROTOCOL_TYPE_SIEMENS_PPI;
    case 11:
    case "PROTOCOL_TYPE_SIEMENS_MPI2":
      return ProtocolType.PROTOCOL_TYPE_SIEMENS_MPI2;
    case 12:
    case "PROTOCOL_TYPE_SIEMENS_MPI3":
      return ProtocolType.PROTOCOL_TYPE_SIEMENS_MPI3;
    case 13:
    case "PROTOCOL_TYPE_SIEMENS_MPI4":
      return ProtocolType.PROTOCOL_TYPE_SIEMENS_MPI4;
    case 14:
    case "PROTOCOL_TYPE_SIEMENS_S7COMM":
      return ProtocolType.PROTOCOL_TYPE_SIEMENS_S7COMM;
    case 20:
    case "PROTOCOL_TYPE_MITSUBISHI_MC":
      return ProtocolType.PROTOCOL_TYPE_MITSUBISHI_MC;
    case 21:
    case "PROTOCOL_TYPE_MITSUBISHI_MC1E":
      return ProtocolType.PROTOCOL_TYPE_MITSUBISHI_MC1E;
    case 22:
    case "PROTOCOL_TYPE_MITSUBISHI_MC2E":
      return ProtocolType.PROTOCOL_TYPE_MITSUBISHI_MC2E;
    case 23:
    case "PROTOCOL_TYPE_MITSUBISHI_MC3E":
      return ProtocolType.PROTOCOL_TYPE_MITSUBISHI_MC3E;
    case 24:
    case "PROTOCOL_TYPE_MITSUBISHI_MC4C":
      return ProtocolType.PROTOCOL_TYPE_MITSUBISHI_MC4C;
    case 25:
    case "PROTOCOL_TYPE_MITSUBISHI_SLMP":
      return ProtocolType.PROTOCOL_TYPE_MITSUBISHI_SLMP;
    case 30:
    case "PROTOCOL_TYPE_OMRON_FINS":
      return ProtocolType.PROTOCOL_TYPE_OMRON_FINS;
    case 31:
    case "PROTOCOL_TYPE_OMRON_HOSTLINK":
      return ProtocolType.PROTOCOL_TYPE_OMRON_HOSTLINK;
    case 32:
    case "PROTOCOL_TYPE_OMRON_ENTERNET_CIP":
      return ProtocolType.PROTOCOL_TYPE_OMRON_ENTERNET_CIP;
    case 40:
    case "PROTOCOL_TYPE_FATEK":
      return ProtocolType.PROTOCOL_TYPE_FATEK;
    case 41:
    case "PROTOCOL_TYPE_VIGOR_VSPROTOCOL":
      return ProtocolType.PROTOCOL_TYPE_VIGOR_VSPROTOCOL;
    case 42:
    case "PROTOCOL_TYPE_PANASONIC_NEWTOCOL":
      return ProtocolType.PROTOCOL_TYPE_PANASONIC_NEWTOCOL;
    case 43:
    case "PROTOCOL_TYPE_ROCKWELL_IDEC_SERIAL":
      return ProtocolType.PROTOCOL_TYPE_ROCKWELL_IDEC_SERIAL;
    case 44:
    case "PROTOCOL_TYPE_AIBUS":
      return ProtocolType.PROTOCOL_TYPE_AIBUS;
    case 90:
    case "PROTOCOL_TYPE_MAXCOMM":
      return ProtocolType.PROTOCOL_TYPE_MAXCOMM;
    case 98:
    case "PROTOCOL_TYPE_FREE":
      return ProtocolType.PROTOCOL_TYPE_FREE;
    case 99:
    case "PROTOCOL_TYPE_PASS_THROUGH":
      return ProtocolType.PROTOCOL_TYPE_PASS_THROUGH;
    case 100:
    case "PROTOCOL_TYPE_LOCAL_HMI":
      return ProtocolType.PROTOCOL_TYPE_LOCAL_HMI;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ProtocolType.UNRECOGNIZED;
  }
}

export function protocolTypeToJSON(object: ProtocolType): string {
  switch (object) {
    case ProtocolType.PROTOCOL_TYPE_UNSPECIFIED:
      return "PROTOCOL_TYPE_UNSPECIFIED";
    case ProtocolType.PROTOCOL_TYPE_MODBUS_MASTER:
      return "PROTOCOL_TYPE_MODBUS_MASTER";
    case ProtocolType.PROTOCOL_TYPE_MODBUS_SLAVE:
      return "PROTOCOL_TYPE_MODBUS_SLAVE";
    case ProtocolType.PROTOCOL_TYPE_MODBUS_MASTER_ASCII:
      return "PROTOCOL_TYPE_MODBUS_MASTER_ASCII";
    case ProtocolType.PROTOCOL_TYPE_MODBUS_SLAVE_ASCII:
      return "PROTOCOL_TYPE_MODBUS_SLAVE_ASCII";
    case ProtocolType.PROTOCOL_TYPE_SIEMENS_PPI:
      return "PROTOCOL_TYPE_SIEMENS_PPI";
    case ProtocolType.PROTOCOL_TYPE_SIEMENS_MPI2:
      return "PROTOCOL_TYPE_SIEMENS_MPI2";
    case ProtocolType.PROTOCOL_TYPE_SIEMENS_MPI3:
      return "PROTOCOL_TYPE_SIEMENS_MPI3";
    case ProtocolType.PROTOCOL_TYPE_SIEMENS_MPI4:
      return "PROTOCOL_TYPE_SIEMENS_MPI4";
    case ProtocolType.PROTOCOL_TYPE_SIEMENS_S7COMM:
      return "PROTOCOL_TYPE_SIEMENS_S7COMM";
    case ProtocolType.PROTOCOL_TYPE_MITSUBISHI_MC:
      return "PROTOCOL_TYPE_MITSUBISHI_MC";
    case ProtocolType.PROTOCOL_TYPE_MITSUBISHI_MC1E:
      return "PROTOCOL_TYPE_MITSUBISHI_MC1E";
    case ProtocolType.PROTOCOL_TYPE_MITSUBISHI_MC2E:
      return "PROTOCOL_TYPE_MITSUBISHI_MC2E";
    case ProtocolType.PROTOCOL_TYPE_MITSUBISHI_MC3E:
      return "PROTOCOL_TYPE_MITSUBISHI_MC3E";
    case ProtocolType.PROTOCOL_TYPE_MITSUBISHI_MC4C:
      return "PROTOCOL_TYPE_MITSUBISHI_MC4C";
    case ProtocolType.PROTOCOL_TYPE_MITSUBISHI_SLMP:
      return "PROTOCOL_TYPE_MITSUBISHI_SLMP";
    case ProtocolType.PROTOCOL_TYPE_OMRON_FINS:
      return "PROTOCOL_TYPE_OMRON_FINS";
    case ProtocolType.PROTOCOL_TYPE_OMRON_HOSTLINK:
      return "PROTOCOL_TYPE_OMRON_HOSTLINK";
    case ProtocolType.PROTOCOL_TYPE_OMRON_ENTERNET_CIP:
      return "PROTOCOL_TYPE_OMRON_ENTERNET_CIP";
    case ProtocolType.PROTOCOL_TYPE_FATEK:
      return "PROTOCOL_TYPE_FATEK";
    case ProtocolType.PROTOCOL_TYPE_VIGOR_VSPROTOCOL:
      return "PROTOCOL_TYPE_VIGOR_VSPROTOCOL";
    case ProtocolType.PROTOCOL_TYPE_PANASONIC_NEWTOCOL:
      return "PROTOCOL_TYPE_PANASONIC_NEWTOCOL";
    case ProtocolType.PROTOCOL_TYPE_ROCKWELL_IDEC_SERIAL:
      return "PROTOCOL_TYPE_ROCKWELL_IDEC_SERIAL";
    case ProtocolType.PROTOCOL_TYPE_AIBUS:
      return "PROTOCOL_TYPE_AIBUS";
    case ProtocolType.PROTOCOL_TYPE_MAXCOMM:
      return "PROTOCOL_TYPE_MAXCOMM";
    case ProtocolType.PROTOCOL_TYPE_FREE:
      return "PROTOCOL_TYPE_FREE";
    case ProtocolType.PROTOCOL_TYPE_PASS_THROUGH:
      return "PROTOCOL_TYPE_PASS_THROUGH";
    case ProtocolType.PROTOCOL_TYPE_LOCAL_HMI:
      return "PROTOCOL_TYPE_LOCAL_HMI";
    case ProtocolType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum SerialType {
  /** SERIAL_TYPE_UNSPECIFIED - 未定义 */
  SERIAL_TYPE_UNSPECIFIED = 0,
  /** SERIAL_TYPE_RS232 - RS232 */
  SERIAL_TYPE_RS232 = 1,
  /** SERIAL_TYPE_RS422 - RS422 */
  SERIAL_TYPE_RS422 = 2,
  /** SERIAL_TYPE_RS485 - RS485 */
  SERIAL_TYPE_RS485 = 3,
  UNRECOGNIZED = -1,
}

export function serialTypeFromJSON(object: any): SerialType {
  switch (object) {
    case 0:
    case "SERIAL_TYPE_UNSPECIFIED":
      return SerialType.SERIAL_TYPE_UNSPECIFIED;
    case 1:
    case "SERIAL_TYPE_RS232":
      return SerialType.SERIAL_TYPE_RS232;
    case 2:
    case "SERIAL_TYPE_RS422":
      return SerialType.SERIAL_TYPE_RS422;
    case 3:
    case "SERIAL_TYPE_RS485":
      return SerialType.SERIAL_TYPE_RS485;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SerialType.UNRECOGNIZED;
  }
}

export function serialTypeToJSON(object: SerialType): string {
  switch (object) {
    case SerialType.SERIAL_TYPE_UNSPECIFIED:
      return "SERIAL_TYPE_UNSPECIFIED";
    case SerialType.SERIAL_TYPE_RS232:
      return "SERIAL_TYPE_RS232";
    case SerialType.SERIAL_TYPE_RS422:
      return "SERIAL_TYPE_RS422";
    case SerialType.SERIAL_TYPE_RS485:
      return "SERIAL_TYPE_RS485";
    case SerialType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum ParityType {
  /** PARITY_TYPE_UNSPECIFIED - 未定义 */
  PARITY_TYPE_UNSPECIFIED = 0,
  /** PARITY_TYPE_EVEN - 偶校验 */
  PARITY_TYPE_EVEN = 1,
  /** PARITY_TYPE_ODD - 奇校验 */
  PARITY_TYPE_ODD = 2,
  UNRECOGNIZED = -1,
}

export function parityTypeFromJSON(object: any): ParityType {
  switch (object) {
    case 0:
    case "PARITY_TYPE_UNSPECIFIED":
      return ParityType.PARITY_TYPE_UNSPECIFIED;
    case 1:
    case "PARITY_TYPE_EVEN":
      return ParityType.PARITY_TYPE_EVEN;
    case 2:
    case "PARITY_TYPE_ODD":
      return ParityType.PARITY_TYPE_ODD;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ParityType.UNRECOGNIZED;
  }
}

export function parityTypeToJSON(object: ParityType): string {
  switch (object) {
    case ParityType.PARITY_TYPE_UNSPECIFIED:
      return "PARITY_TYPE_UNSPECIFIED";
    case ParityType.PARITY_TYPE_EVEN:
      return "PARITY_TYPE_EVEN";
    case ParityType.PARITY_TYPE_ODD:
      return "PARITY_TYPE_ODD";
    case ParityType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum StopBitsType {
  /** STOP_BITS_TYPE_UNSPECIFIED - 未定义 */
  STOP_BITS_TYPE_UNSPECIFIED = 0,
  /** STOP_BITS_TYPE_1 - 1个停止位 */
  STOP_BITS_TYPE_1 = 1,
  /** STOP_BITS_TYPE_2 - 2个停止位(不需要1.5个停止位，因为同行都没有) */
  STOP_BITS_TYPE_2 = 2,
  UNRECOGNIZED = -1,
}

export function stopBitsTypeFromJSON(object: any): StopBitsType {
  switch (object) {
    case 0:
    case "STOP_BITS_TYPE_UNSPECIFIED":
      return StopBitsType.STOP_BITS_TYPE_UNSPECIFIED;
    case 1:
    case "STOP_BITS_TYPE_1":
      return StopBitsType.STOP_BITS_TYPE_1;
    case 2:
    case "STOP_BITS_TYPE_2":
      return StopBitsType.STOP_BITS_TYPE_2;
    case -1:
    case "UNRECOGNIZED":
    default:
      return StopBitsType.UNRECOGNIZED;
  }
}

export function stopBitsTypeToJSON(object: StopBitsType): string {
  switch (object) {
    case StopBitsType.STOP_BITS_TYPE_UNSPECIFIED:
      return "STOP_BITS_TYPE_UNSPECIFIED";
    case StopBitsType.STOP_BITS_TYPE_1:
      return "STOP_BITS_TYPE_1";
    case StopBitsType.STOP_BITS_TYPE_2:
      return "STOP_BITS_TYPE_2";
    case StopBitsType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum DataBitsType {
  /** DATA_BITS_TYPE_UNSPECIFIED - 未定义 */
  DATA_BITS_TYPE_UNSPECIFIED = 0,
  /** DATA_BITS_TYPE_7 - 7个数据位 */
  DATA_BITS_TYPE_7 = 7,
  /** DATA_BITS_TYPE_8 - 8个数据位 */
  DATA_BITS_TYPE_8 = 8,
  UNRECOGNIZED = -1,
}

export function dataBitsTypeFromJSON(object: any): DataBitsType {
  switch (object) {
    case 0:
    case "DATA_BITS_TYPE_UNSPECIFIED":
      return DataBitsType.DATA_BITS_TYPE_UNSPECIFIED;
    case 7:
    case "DATA_BITS_TYPE_7":
      return DataBitsType.DATA_BITS_TYPE_7;
    case 8:
    case "DATA_BITS_TYPE_8":
      return DataBitsType.DATA_BITS_TYPE_8;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DataBitsType.UNRECOGNIZED;
  }
}

export function dataBitsTypeToJSON(object: DataBitsType): string {
  switch (object) {
    case DataBitsType.DATA_BITS_TYPE_UNSPECIFIED:
      return "DATA_BITS_TYPE_UNSPECIFIED";
    case DataBitsType.DATA_BITS_TYPE_7:
      return "DATA_BITS_TYPE_7";
    case DataBitsType.DATA_BITS_TYPE_8:
      return "DATA_BITS_TYPE_8";
    case DataBitsType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum Word16Order {
  /** WORD16_ORDER_UNSPECIFIED - 未定义 */
  WORD16_ORDER_UNSPECIFIED = 0,
  /** WORD16_ORDER_1_2 - 12 */
  WORD16_ORDER_1_2 = 1,
  /** WORD16_ORDER_2_1 - 21 */
  WORD16_ORDER_2_1 = 2,
  UNRECOGNIZED = -1,
}

export function word16OrderFromJSON(object: any): Word16Order {
  switch (object) {
    case 0:
    case "WORD16_ORDER_UNSPECIFIED":
      return Word16Order.WORD16_ORDER_UNSPECIFIED;
    case 1:
    case "WORD16_ORDER_1_2":
      return Word16Order.WORD16_ORDER_1_2;
    case 2:
    case "WORD16_ORDER_2_1":
      return Word16Order.WORD16_ORDER_2_1;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Word16Order.UNRECOGNIZED;
  }
}

export function word16OrderToJSON(object: Word16Order): string {
  switch (object) {
    case Word16Order.WORD16_ORDER_UNSPECIFIED:
      return "WORD16_ORDER_UNSPECIFIED";
    case Word16Order.WORD16_ORDER_1_2:
      return "WORD16_ORDER_1_2";
    case Word16Order.WORD16_ORDER_2_1:
      return "WORD16_ORDER_2_1";
    case Word16Order.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum Word32Order {
  /** WORD32_ORDER_UNSPECIFIED - 未定义 */
  WORD32_ORDER_UNSPECIFIED = 0,
  /** WORD32_ORDER_1_2_3_4 - 1234 */
  WORD32_ORDER_1_2_3_4 = 1,
  /** WORD32_ORDER_2_1_4_3 - 2143 */
  WORD32_ORDER_2_1_4_3 = 2,
  /** WORD32_ORDER_3_4_1_2 - 3412 */
  WORD32_ORDER_3_4_1_2 = 3,
  /** WORD32_ORDER_4_3_2_1 - 4321 */
  WORD32_ORDER_4_3_2_1 = 4,
  UNRECOGNIZED = -1,
}

export function word32OrderFromJSON(object: any): Word32Order {
  switch (object) {
    case 0:
    case "WORD32_ORDER_UNSPECIFIED":
      return Word32Order.WORD32_ORDER_UNSPECIFIED;
    case 1:
    case "WORD32_ORDER_1_2_3_4":
      return Word32Order.WORD32_ORDER_1_2_3_4;
    case 2:
    case "WORD32_ORDER_2_1_4_3":
      return Word32Order.WORD32_ORDER_2_1_4_3;
    case 3:
    case "WORD32_ORDER_3_4_1_2":
      return Word32Order.WORD32_ORDER_3_4_1_2;
    case 4:
    case "WORD32_ORDER_4_3_2_1":
      return Word32Order.WORD32_ORDER_4_3_2_1;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Word32Order.UNRECOGNIZED;
  }
}

export function word32OrderToJSON(object: Word32Order): string {
  switch (object) {
    case Word32Order.WORD32_ORDER_UNSPECIFIED:
      return "WORD32_ORDER_UNSPECIFIED";
    case Word32Order.WORD32_ORDER_1_2_3_4:
      return "WORD32_ORDER_1_2_3_4";
    case Word32Order.WORD32_ORDER_2_1_4_3:
      return "WORD32_ORDER_2_1_4_3";
    case Word32Order.WORD32_ORDER_3_4_1_2:
      return "WORD32_ORDER_3_4_1_2";
    case Word32Order.WORD32_ORDER_4_3_2_1:
      return "WORD32_ORDER_4_3_2_1";
    case Word32Order.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 串口参数全部设备存储，为了序列化 */
export interface TunnelDeviceList {
  /** 按通讯隧道编号存储(key是通讯号，u8) */
  tunnels: { [key: number]: Tunnel };
  /** 按设备ID存储(key是设备ID，u8，从1开始，0表示工程用) */
  devices: { [key: number]: Device };
  /** 设备编号映射，key是设备号，value是设备ID，都是u8 */
  deviceNoMap: { [key: number]: number };
  /** 自定义结构体数据类型，key是u8 */
  structDataTypes: { [key: number]: StructType };
  /** 索引寄存器信息(key是u8) */
  indexRegisterInfo: { [key: number]: IndexRegisterInfo };
  /** 变量分类（key是u16） */
  variableClassifies: { [key: number]: VariableClassify };
}

export interface TunnelDeviceList_TunnelsEntry {
  key: number;
  value: Tunnel | undefined;
}

export interface TunnelDeviceList_DevicesEntry {
  key: number;
  value: Device | undefined;
}

export interface TunnelDeviceList_DeviceNoMapEntry {
  key: number;
  value: number;
}

export interface TunnelDeviceList_StructDataTypesEntry {
  key: number;
  value: StructType | undefined;
}

export interface TunnelDeviceList_IndexRegisterInfoEntry {
  key: number;
  value: IndexRegisterInfo | undefined;
}

export interface TunnelDeviceList_VariableClassifiesEntry {
  key: number;
  value: VariableClassify | undefined;
}

/** 变量分类 */
export interface VariableClassify {
  /** 名称 */
  name: string;
  /** 允许多个上级（用途不明确，只是估计有些场景有用） */
  parentIdU16: number[];
  /** 是否禁用 */
  disable: boolean;
  /** 备注 */
  memo: string;
}

/** 索引寄存器信息 */
export interface IndexRegisterInfo {
  name: string;
  /** 类型分为：bool、int8、uint8、int16、uint16、int32、uint32、float32,无值则默认为int16 */
  dataFormat: DataFormatType;
  /** 初始值 */
  initValue:
    | NumberValue
    | undefined;
  /** 同步PLC地址（双向同步） */
  syncVariable?:
    | VariableReference
    | undefined;
  /** 是否禁用 */
  disable: boolean;
  /** 备注 */
  memo: string;
}

/** 通讯隧道 */
export interface Tunnel {
  /** 通道类型：COM、ETH */
  tunnelType: string;
  /** 通道口 */
  no?:
    | number
    | undefined;
  /** 正常可由应用自动生成，这边默认为无设置 */
  name: string;
  /** 备注 */
  memo: string;
  /** 是否禁用 */
  disable: boolean;
  /** 通讯参数 */
  communicationParams:
    | CommunicationParams
    | undefined;
  /** 串口参数 */
  serialParam?:
    | SerialTunnelParam
    | undefined;
  /** 仿真时的串口参数 */
  simulatedSerialParam?: SerialTunnelParam | undefined;
}

/** 设备 */
export interface Device {
  /** 设备名称 */
  name: string;
  /** 通讯隧道编号 */
  tunnelIdU8: number;
  /** 设备品牌(设计端用) */
  deviceBrand: string;
  /** 设备协议配置(设计端用) */
  deviceConfig: string;
  /** 协议类型 */
  protocol: ProtocolType;
  /** 协议参数 */
  protocolParams: ProtocolParam[];
  /** 是否禁用 */
  disable: boolean;
  /** 备注 */
  memo: string;
  /** 设备参数 */
  deviceParams?:
    | //
    /** 以太网设备参数(以太网设备一定有这个，不然没有地址) */
    { $case: "ethernetParam"; value: EthernetDeviceParam }
    | //
    /** 串口设备参数(串口设备不一定有，因为通道有了) */
    { $case: "serialParam"; value: SerialDeviceParam }
    | //
    /** 云设备参数(云设备一定有这个，不然没有地址) */
    { $case: "cloudParam"; value: CloudDeviceParam }
    | //
    /** 子设备列表及参数(如果有子设备，表示是动态设备) */
    { $case: "multiDeviceParam"; value: MultiDeviceParam }
    | undefined;
  /** 站号 */
  station:
    | DataReferenceUInt8
    | undefined;
  /** 广播端口(可能不启用) */
  broadcastPort?:
    | DataReferenceUInt8
    | undefined;
  /** 其他参数（比如西门子的机架号、插槽号） */
  extendParams: { [key: number]: DataReferenceInt16 };
  /** 通讯参数(未设置则继承自通道) */
  communicationParams?:
    | CommunicationParams
    | undefined;
  /** 初始值列表 */
  initValues: VariableInitValue[];
  /** 父设备(如果有父设备，表示这是动态设备下的子设备) */
  parentDeviceId?:
    | number
    | undefined;
  /** 仿真时的设备参数(可能都没值，都没值时以真实参数为准，有可能真实设备是串口，仿真时却是以太网或者云设备) */
  simulatedDeviceParams?:
    | //
    /** 以太网设备参数(以太网设备一定有这个，不然没有地址) */
    { $case: "simulatedEthernetParam"; value: EthernetDeviceParam }
    | //
    /** 串口设备参数(串口设备不一定有，因为通道有了) */
    { $case: "simulatedSerialParam"; value: SerialDeviceParam }
    | //
    /** 云设备参数(云设备一定有这个，不然没有地址) */
    { $case: "simulatedCloudParam"; value: CloudDeviceParam }
    | undefined;
}

export interface Device_ExtendParamsEntry {
  key: number;
  value: DataReferenceInt16 | undefined;
}

/** 设置初始值 */
export interface VariableInitValue {
  variable: VariableReference | undefined;
  value: AllTypeValue | undefined;
}

/** 协议本身特定参数 */
export interface ProtocolParam {
  paramType: ProtocolParamType;
  paramValue: DataReferenceInt16 | undefined;
}

export interface EthernetDeviceParam {
  /** 用4个字节表示ip地址 */
  ipAddress:
    | DataReferenceUInt32
    | undefined;
  /** 端口号 */
  ipPort:
    | DataReferenceUInt16
    | undefined;
  /** 是否UDP,否则为TCP */
  isUdp: boolean;
}

/** 多设备集合 */
export interface MultiDeviceParam {
  /** 子设备ID的列表，Tag上用站号指向此数组下标 */
  subDeviceIds: number[];
  /** 子设备索引，用于全局统一切换该设备，如果有了这个，图元上可以不选，如果图元有选，以图元为准 */
  subDeviceIndexRegisterU16?:
    | number
    | undefined;
  /** 索引偏移值，也就是把子设备索引的值，再加上这个值，才是子设备ID数组的下标 */
  subDeviceIndexOffsetI16: number;
}

/** 串口设备参数 */
export interface SerialDeviceParam {
  /** 波特率 */
  baudrate:
    | DataReferenceUInt16
    | undefined;
  /** 数据位(DataBitsType) */
  databits:
    | DataReferenceUInt8
    | undefined;
  /** 校验位(ParityType) */
  parity:
    | DataReferenceUInt8
    | undefined;
  /** 停止位(StopBitsType) */
  stopbits: DataReferenceUInt8 | undefined;
}

/** 串口通道参数 */
export interface SerialTunnelParam {
  /** 串口编号，从1开始 */
  serialNo:
    | DataReferenceUInt8
    | undefined;
  /** 串口类型（SerialType） */
  serialType:
    | DataReferenceUInt8
    | undefined;
  /** 波特率 */
  baudrate:
    | DataReferenceUInt16
    | undefined;
  /** 数据位(DataBitsType) */
  databits:
    | DataReferenceUInt8
    | undefined;
  /** 校验位(ParityType) */
  parity:
    | DataReferenceUInt8
    | undefined;
  /** 停止位(StopBitsType) */
  stopbits: DataReferenceUInt8 | undefined;
}

/** 云设备参数 */
export interface CloudDeviceParam {
  /** 云设备KEY */
  cloudDeviceKey: string;
}

/** 通讯参数(所有参数可选，未设置作用对应定义的枚举常量DEFAULT值) */
export interface CommunicationParams {
  /** 超时时间，单位毫秒(默认1000) */
  timeoutU16?:
    | number
    | undefined;
  /** 重试次数，尝试次数之后再提示失败，失败之后还是会连(默认3次) */
  retryCountU8?:
    | number
    | undefined;
  /** 重试间隔时间，单位毫秒(默认1000) */
  retryIntervalU16?:
    | number
    | undefined;
  /** 最小读取间隔时间，单位毫秒(默认1000) */
  minReadIntervalU16?:
    | number
    | undefined;
  /** 位组间隔,指间隔多少后，放弃组在一包(默认5) */
  bitGroupDistanceU16?:
    | number
    | undefined;
  /** 位组最大数(默认128) */
  bitGroupMaxCountU16?:
    | number
    | undefined;
  /** 位写入最大长度(默认128) */
  bitWriteMaxLengthU16?:
    | number
    | undefined;
  /** 字组间隔，指间隔多少后，放弃组在一包(默认5) */
  wordGroupDistanceU16?:
    | number
    | undefined;
  /** 字组最大长度(默认64) */
  wordGroupMaxCountU16?:
    | number
    | undefined;
  /** 字写入最大长度(默认64) */
  wordWriteMaxLengthU16?:
    | number
    | undefined;
  /** 错误提示时间，单位秒(-1表示不提示，0表示一直提示) */
  errorPromptTimeI16?:
    | number
    | undefined;
  /** 16位字组顺序(默认21) */
  int16Order?:
    | Word16Order
    | undefined;
  /** 32位整型字组顺序(默认4321) */
  int32Order?:
    | Word32Order
    | undefined;
  /** 32位浮点字组顺序(默认4321) */
  float32Order?: Word32Order | undefined;
}

export enum CommunicationParams_TimeoutDefault {
  TIMEOUT_DEFAULT_UNSPECIFIED = 0,
  TIMEOUT_DEFAULT_VALUE = 1000,
  UNRECOGNIZED = -1,
}

export function communicationParams_TimeoutDefaultFromJSON(object: any): CommunicationParams_TimeoutDefault {
  switch (object) {
    case 0:
    case "TIMEOUT_DEFAULT_UNSPECIFIED":
      return CommunicationParams_TimeoutDefault.TIMEOUT_DEFAULT_UNSPECIFIED;
    case 1000:
    case "TIMEOUT_DEFAULT_VALUE":
      return CommunicationParams_TimeoutDefault.TIMEOUT_DEFAULT_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_TimeoutDefault.UNRECOGNIZED;
  }
}

export function communicationParams_TimeoutDefaultToJSON(object: CommunicationParams_TimeoutDefault): string {
  switch (object) {
    case CommunicationParams_TimeoutDefault.TIMEOUT_DEFAULT_UNSPECIFIED:
      return "TIMEOUT_DEFAULT_UNSPECIFIED";
    case CommunicationParams_TimeoutDefault.TIMEOUT_DEFAULT_VALUE:
      return "TIMEOUT_DEFAULT_VALUE";
    case CommunicationParams_TimeoutDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommunicationParams_RetryCountDefault {
  RETRY_COUNT_DEFAULT_UNSPECIFIED = 0,
  RETRY_COUNT_DEFAULT_VALUE = 3,
  UNRECOGNIZED = -1,
}

export function communicationParams_RetryCountDefaultFromJSON(object: any): CommunicationParams_RetryCountDefault {
  switch (object) {
    case 0:
    case "RETRY_COUNT_DEFAULT_UNSPECIFIED":
      return CommunicationParams_RetryCountDefault.RETRY_COUNT_DEFAULT_UNSPECIFIED;
    case 3:
    case "RETRY_COUNT_DEFAULT_VALUE":
      return CommunicationParams_RetryCountDefault.RETRY_COUNT_DEFAULT_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_RetryCountDefault.UNRECOGNIZED;
  }
}

export function communicationParams_RetryCountDefaultToJSON(object: CommunicationParams_RetryCountDefault): string {
  switch (object) {
    case CommunicationParams_RetryCountDefault.RETRY_COUNT_DEFAULT_UNSPECIFIED:
      return "RETRY_COUNT_DEFAULT_UNSPECIFIED";
    case CommunicationParams_RetryCountDefault.RETRY_COUNT_DEFAULT_VALUE:
      return "RETRY_COUNT_DEFAULT_VALUE";
    case CommunicationParams_RetryCountDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommunicationParams_RetryIntervalDefault {
  RETRY_INTERVAL_DEFAULT_UNSPECIFIED = 0,
  RETRY_INTERVAL_DEFAULT_VALUE = 1000,
  UNRECOGNIZED = -1,
}

export function communicationParams_RetryIntervalDefaultFromJSON(
  object: any,
): CommunicationParams_RetryIntervalDefault {
  switch (object) {
    case 0:
    case "RETRY_INTERVAL_DEFAULT_UNSPECIFIED":
      return CommunicationParams_RetryIntervalDefault.RETRY_INTERVAL_DEFAULT_UNSPECIFIED;
    case 1000:
    case "RETRY_INTERVAL_DEFAULT_VALUE":
      return CommunicationParams_RetryIntervalDefault.RETRY_INTERVAL_DEFAULT_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_RetryIntervalDefault.UNRECOGNIZED;
  }
}

export function communicationParams_RetryIntervalDefaultToJSON(
  object: CommunicationParams_RetryIntervalDefault,
): string {
  switch (object) {
    case CommunicationParams_RetryIntervalDefault.RETRY_INTERVAL_DEFAULT_UNSPECIFIED:
      return "RETRY_INTERVAL_DEFAULT_UNSPECIFIED";
    case CommunicationParams_RetryIntervalDefault.RETRY_INTERVAL_DEFAULT_VALUE:
      return "RETRY_INTERVAL_DEFAULT_VALUE";
    case CommunicationParams_RetryIntervalDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommunicationParams_MinReadIntervalDefault {
  MIN_READ_INTERVAL_DEFAULT_UNSPECIFIED = 0,
  MIN_READ_INTERVAL_DEFAULT_VALUE = 1000,
  UNRECOGNIZED = -1,
}

export function communicationParams_MinReadIntervalDefaultFromJSON(
  object: any,
): CommunicationParams_MinReadIntervalDefault {
  switch (object) {
    case 0:
    case "MIN_READ_INTERVAL_DEFAULT_UNSPECIFIED":
      return CommunicationParams_MinReadIntervalDefault.MIN_READ_INTERVAL_DEFAULT_UNSPECIFIED;
    case 1000:
    case "MIN_READ_INTERVAL_DEFAULT_VALUE":
      return CommunicationParams_MinReadIntervalDefault.MIN_READ_INTERVAL_DEFAULT_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_MinReadIntervalDefault.UNRECOGNIZED;
  }
}

export function communicationParams_MinReadIntervalDefaultToJSON(
  object: CommunicationParams_MinReadIntervalDefault,
): string {
  switch (object) {
    case CommunicationParams_MinReadIntervalDefault.MIN_READ_INTERVAL_DEFAULT_UNSPECIFIED:
      return "MIN_READ_INTERVAL_DEFAULT_UNSPECIFIED";
    case CommunicationParams_MinReadIntervalDefault.MIN_READ_INTERVAL_DEFAULT_VALUE:
      return "MIN_READ_INTERVAL_DEFAULT_VALUE";
    case CommunicationParams_MinReadIntervalDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommunicationParams_BitGroupDistanceDefault {
  BIT_GROUP_DISTANCE_DEFAULT_UNSPECIFIED = 0,
  BIT_GROUP_DISTANCE_DEFAULT_VALUE = 5,
  UNRECOGNIZED = -1,
}

export function communicationParams_BitGroupDistanceDefaultFromJSON(
  object: any,
): CommunicationParams_BitGroupDistanceDefault {
  switch (object) {
    case 0:
    case "BIT_GROUP_DISTANCE_DEFAULT_UNSPECIFIED":
      return CommunicationParams_BitGroupDistanceDefault.BIT_GROUP_DISTANCE_DEFAULT_UNSPECIFIED;
    case 5:
    case "BIT_GROUP_DISTANCE_DEFAULT_VALUE":
      return CommunicationParams_BitGroupDistanceDefault.BIT_GROUP_DISTANCE_DEFAULT_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_BitGroupDistanceDefault.UNRECOGNIZED;
  }
}

export function communicationParams_BitGroupDistanceDefaultToJSON(
  object: CommunicationParams_BitGroupDistanceDefault,
): string {
  switch (object) {
    case CommunicationParams_BitGroupDistanceDefault.BIT_GROUP_DISTANCE_DEFAULT_UNSPECIFIED:
      return "BIT_GROUP_DISTANCE_DEFAULT_UNSPECIFIED";
    case CommunicationParams_BitGroupDistanceDefault.BIT_GROUP_DISTANCE_DEFAULT_VALUE:
      return "BIT_GROUP_DISTANCE_DEFAULT_VALUE";
    case CommunicationParams_BitGroupDistanceDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommunicationParams_BitGroupMaxCountDefault {
  BIT_GROUP_MAX_COUNT_DEFAULT_UNSPECIFIED = 0,
  BIT_GROUP_MAX_COUNT_DEFAULT_VALUE = 128,
  UNRECOGNIZED = -1,
}

export function communicationParams_BitGroupMaxCountDefaultFromJSON(
  object: any,
): CommunicationParams_BitGroupMaxCountDefault {
  switch (object) {
    case 0:
    case "BIT_GROUP_MAX_COUNT_DEFAULT_UNSPECIFIED":
      return CommunicationParams_BitGroupMaxCountDefault.BIT_GROUP_MAX_COUNT_DEFAULT_UNSPECIFIED;
    case 128:
    case "BIT_GROUP_MAX_COUNT_DEFAULT_VALUE":
      return CommunicationParams_BitGroupMaxCountDefault.BIT_GROUP_MAX_COUNT_DEFAULT_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_BitGroupMaxCountDefault.UNRECOGNIZED;
  }
}

export function communicationParams_BitGroupMaxCountDefaultToJSON(
  object: CommunicationParams_BitGroupMaxCountDefault,
): string {
  switch (object) {
    case CommunicationParams_BitGroupMaxCountDefault.BIT_GROUP_MAX_COUNT_DEFAULT_UNSPECIFIED:
      return "BIT_GROUP_MAX_COUNT_DEFAULT_UNSPECIFIED";
    case CommunicationParams_BitGroupMaxCountDefault.BIT_GROUP_MAX_COUNT_DEFAULT_VALUE:
      return "BIT_GROUP_MAX_COUNT_DEFAULT_VALUE";
    case CommunicationParams_BitGroupMaxCountDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommunicationParams_BitWriteMaxLengthDefault {
  BIT_WRITE_MAX_LENGTH_DEFAULT_UNSPECIFIED = 0,
  BIT_WRITE_MAX_LENGTH_DEFAULT_VALUE = 128,
  UNRECOGNIZED = -1,
}

export function communicationParams_BitWriteMaxLengthDefaultFromJSON(
  object: any,
): CommunicationParams_BitWriteMaxLengthDefault {
  switch (object) {
    case 0:
    case "BIT_WRITE_MAX_LENGTH_DEFAULT_UNSPECIFIED":
      return CommunicationParams_BitWriteMaxLengthDefault.BIT_WRITE_MAX_LENGTH_DEFAULT_UNSPECIFIED;
    case 128:
    case "BIT_WRITE_MAX_LENGTH_DEFAULT_VALUE":
      return CommunicationParams_BitWriteMaxLengthDefault.BIT_WRITE_MAX_LENGTH_DEFAULT_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_BitWriteMaxLengthDefault.UNRECOGNIZED;
  }
}

export function communicationParams_BitWriteMaxLengthDefaultToJSON(
  object: CommunicationParams_BitWriteMaxLengthDefault,
): string {
  switch (object) {
    case CommunicationParams_BitWriteMaxLengthDefault.BIT_WRITE_MAX_LENGTH_DEFAULT_UNSPECIFIED:
      return "BIT_WRITE_MAX_LENGTH_DEFAULT_UNSPECIFIED";
    case CommunicationParams_BitWriteMaxLengthDefault.BIT_WRITE_MAX_LENGTH_DEFAULT_VALUE:
      return "BIT_WRITE_MAX_LENGTH_DEFAULT_VALUE";
    case CommunicationParams_BitWriteMaxLengthDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommunicationParams_WordGroupDistanceDefault {
  WORD_GROUP_DISTANCE_DEFAULT_UNSPECIFIED = 0,
  WORD_GROUP_DISTANCE_DEFAULT_VALUE = 5,
  UNRECOGNIZED = -1,
}

export function communicationParams_WordGroupDistanceDefaultFromJSON(
  object: any,
): CommunicationParams_WordGroupDistanceDefault {
  switch (object) {
    case 0:
    case "WORD_GROUP_DISTANCE_DEFAULT_UNSPECIFIED":
      return CommunicationParams_WordGroupDistanceDefault.WORD_GROUP_DISTANCE_DEFAULT_UNSPECIFIED;
    case 5:
    case "WORD_GROUP_DISTANCE_DEFAULT_VALUE":
      return CommunicationParams_WordGroupDistanceDefault.WORD_GROUP_DISTANCE_DEFAULT_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_WordGroupDistanceDefault.UNRECOGNIZED;
  }
}

export function communicationParams_WordGroupDistanceDefaultToJSON(
  object: CommunicationParams_WordGroupDistanceDefault,
): string {
  switch (object) {
    case CommunicationParams_WordGroupDistanceDefault.WORD_GROUP_DISTANCE_DEFAULT_UNSPECIFIED:
      return "WORD_GROUP_DISTANCE_DEFAULT_UNSPECIFIED";
    case CommunicationParams_WordGroupDistanceDefault.WORD_GROUP_DISTANCE_DEFAULT_VALUE:
      return "WORD_GROUP_DISTANCE_DEFAULT_VALUE";
    case CommunicationParams_WordGroupDistanceDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommunicationParams_WordGroupMaxCountDefault {
  WORD_GROUP_MAX_COUNT_DEFAULT_UNSPECIFIED = 0,
  WORD_GROUP_MAX_COUNT_DEFAULT_VALUE = 64,
  UNRECOGNIZED = -1,
}

export function communicationParams_WordGroupMaxCountDefaultFromJSON(
  object: any,
): CommunicationParams_WordGroupMaxCountDefault {
  switch (object) {
    case 0:
    case "WORD_GROUP_MAX_COUNT_DEFAULT_UNSPECIFIED":
      return CommunicationParams_WordGroupMaxCountDefault.WORD_GROUP_MAX_COUNT_DEFAULT_UNSPECIFIED;
    case 64:
    case "WORD_GROUP_MAX_COUNT_DEFAULT_VALUE":
      return CommunicationParams_WordGroupMaxCountDefault.WORD_GROUP_MAX_COUNT_DEFAULT_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_WordGroupMaxCountDefault.UNRECOGNIZED;
  }
}

export function communicationParams_WordGroupMaxCountDefaultToJSON(
  object: CommunicationParams_WordGroupMaxCountDefault,
): string {
  switch (object) {
    case CommunicationParams_WordGroupMaxCountDefault.WORD_GROUP_MAX_COUNT_DEFAULT_UNSPECIFIED:
      return "WORD_GROUP_MAX_COUNT_DEFAULT_UNSPECIFIED";
    case CommunicationParams_WordGroupMaxCountDefault.WORD_GROUP_MAX_COUNT_DEFAULT_VALUE:
      return "WORD_GROUP_MAX_COUNT_DEFAULT_VALUE";
    case CommunicationParams_WordGroupMaxCountDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommunicationParams_WordWriteMaxLengthDefault {
  WORD_WRITE_MAX_LENGTH_DEFAULT_UNSPECIFIED = 0,
  WORD_WRITE_MAX_LENGTH_DEFAULT_VALUE = 64,
  UNRECOGNIZED = -1,
}

export function communicationParams_WordWriteMaxLengthDefaultFromJSON(
  object: any,
): CommunicationParams_WordWriteMaxLengthDefault {
  switch (object) {
    case 0:
    case "WORD_WRITE_MAX_LENGTH_DEFAULT_UNSPECIFIED":
      return CommunicationParams_WordWriteMaxLengthDefault.WORD_WRITE_MAX_LENGTH_DEFAULT_UNSPECIFIED;
    case 64:
    case "WORD_WRITE_MAX_LENGTH_DEFAULT_VALUE":
      return CommunicationParams_WordWriteMaxLengthDefault.WORD_WRITE_MAX_LENGTH_DEFAULT_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_WordWriteMaxLengthDefault.UNRECOGNIZED;
  }
}

export function communicationParams_WordWriteMaxLengthDefaultToJSON(
  object: CommunicationParams_WordWriteMaxLengthDefault,
): string {
  switch (object) {
    case CommunicationParams_WordWriteMaxLengthDefault.WORD_WRITE_MAX_LENGTH_DEFAULT_UNSPECIFIED:
      return "WORD_WRITE_MAX_LENGTH_DEFAULT_UNSPECIFIED";
    case CommunicationParams_WordWriteMaxLengthDefault.WORD_WRITE_MAX_LENGTH_DEFAULT_VALUE:
      return "WORD_WRITE_MAX_LENGTH_DEFAULT_VALUE";
    case CommunicationParams_WordWriteMaxLengthDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommunicationParams_ErrorPromptTimeDefault {
  ERROR_PROMPT_TIME_DEFAULT_UNSPECIFIED = 0,
  UNRECOGNIZED = -1,
}

export function communicationParams_ErrorPromptTimeDefaultFromJSON(
  object: any,
): CommunicationParams_ErrorPromptTimeDefault {
  switch (object) {
    case 0:
    case "ERROR_PROMPT_TIME_DEFAULT_UNSPECIFIED":
      return CommunicationParams_ErrorPromptTimeDefault.ERROR_PROMPT_TIME_DEFAULT_UNSPECIFIED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_ErrorPromptTimeDefault.UNRECOGNIZED;
  }
}

export function communicationParams_ErrorPromptTimeDefaultToJSON(
  object: CommunicationParams_ErrorPromptTimeDefault,
): string {
  switch (object) {
    case CommunicationParams_ErrorPromptTimeDefault.ERROR_PROMPT_TIME_DEFAULT_UNSPECIFIED:
      return "ERROR_PROMPT_TIME_DEFAULT_UNSPECIFIED";
    case CommunicationParams_ErrorPromptTimeDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommunicationParams_Int16OrderDefault {
  INT16_ORDER_DEFAULT_UNSPECIFIED = 0,
  INT16_ORDER_DEFAULT_VALUE = 2,
  UNRECOGNIZED = -1,
}

export function communicationParams_Int16OrderDefaultFromJSON(object: any): CommunicationParams_Int16OrderDefault {
  switch (object) {
    case 0:
    case "INT16_ORDER_DEFAULT_UNSPECIFIED":
      return CommunicationParams_Int16OrderDefault.INT16_ORDER_DEFAULT_UNSPECIFIED;
    case 2:
    case "INT16_ORDER_DEFAULT_VALUE":
      return CommunicationParams_Int16OrderDefault.INT16_ORDER_DEFAULT_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_Int16OrderDefault.UNRECOGNIZED;
  }
}

export function communicationParams_Int16OrderDefaultToJSON(object: CommunicationParams_Int16OrderDefault): string {
  switch (object) {
    case CommunicationParams_Int16OrderDefault.INT16_ORDER_DEFAULT_UNSPECIFIED:
      return "INT16_ORDER_DEFAULT_UNSPECIFIED";
    case CommunicationParams_Int16OrderDefault.INT16_ORDER_DEFAULT_VALUE:
      return "INT16_ORDER_DEFAULT_VALUE";
    case CommunicationParams_Int16OrderDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommunicationParams_Int32OrderDefault {
  INT32_ORDER_DEFAULT_UNSPECIFIED = 0,
  INT32_ORDER_DEFAULT_VALUE = 4,
  UNRECOGNIZED = -1,
}

export function communicationParams_Int32OrderDefaultFromJSON(object: any): CommunicationParams_Int32OrderDefault {
  switch (object) {
    case 0:
    case "INT32_ORDER_DEFAULT_UNSPECIFIED":
      return CommunicationParams_Int32OrderDefault.INT32_ORDER_DEFAULT_UNSPECIFIED;
    case 4:
    case "INT32_ORDER_DEFAULT_VALUE":
      return CommunicationParams_Int32OrderDefault.INT32_ORDER_DEFAULT_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_Int32OrderDefault.UNRECOGNIZED;
  }
}

export function communicationParams_Int32OrderDefaultToJSON(object: CommunicationParams_Int32OrderDefault): string {
  switch (object) {
    case CommunicationParams_Int32OrderDefault.INT32_ORDER_DEFAULT_UNSPECIFIED:
      return "INT32_ORDER_DEFAULT_UNSPECIFIED";
    case CommunicationParams_Int32OrderDefault.INT32_ORDER_DEFAULT_VALUE:
      return "INT32_ORDER_DEFAULT_VALUE";
    case CommunicationParams_Int32OrderDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommunicationParams_Float32OrderDefault {
  FLOAT32_ORDER_DEFAULT_UNSPECIFIED = 0,
  FLOAT32_ORDER_DEFAULT_VALUE = 4,
  UNRECOGNIZED = -1,
}

export function communicationParams_Float32OrderDefaultFromJSON(object: any): CommunicationParams_Float32OrderDefault {
  switch (object) {
    case 0:
    case "FLOAT32_ORDER_DEFAULT_UNSPECIFIED":
      return CommunicationParams_Float32OrderDefault.FLOAT32_ORDER_DEFAULT_UNSPECIFIED;
    case 4:
    case "FLOAT32_ORDER_DEFAULT_VALUE":
      return CommunicationParams_Float32OrderDefault.FLOAT32_ORDER_DEFAULT_VALUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommunicationParams_Float32OrderDefault.UNRECOGNIZED;
  }
}

export function communicationParams_Float32OrderDefaultToJSON(object: CommunicationParams_Float32OrderDefault): string {
  switch (object) {
    case CommunicationParams_Float32OrderDefault.FLOAT32_ORDER_DEFAULT_UNSPECIFIED:
      return "FLOAT32_ORDER_DEFAULT_UNSPECIFIED";
    case CommunicationParams_Float32OrderDefault.FLOAT32_ORDER_DEFAULT_VALUE:
      return "FLOAT32_ORDER_DEFAULT_VALUE";
    case CommunicationParams_Float32OrderDefault.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

function createBaseTunnelDeviceList(): TunnelDeviceList {
  return {
    tunnels: {},
    devices: {},
    deviceNoMap: {},
    structDataTypes: {},
    indexRegisterInfo: {},
    variableClassifies: {},
  };
}

export const TunnelDeviceList: MessageFns<TunnelDeviceList> = {
  encode(message: TunnelDeviceList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.tunnels).forEach(([key, value]) => {
      TunnelDeviceList_TunnelsEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    Object.entries(message.devices).forEach(([key, value]) => {
      TunnelDeviceList_DevicesEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    Object.entries(message.deviceNoMap).forEach(([key, value]) => {
      TunnelDeviceList_DeviceNoMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    Object.entries(message.structDataTypes).forEach(([key, value]) => {
      TunnelDeviceList_StructDataTypesEntry.encode({ key: key as any, value }, writer.uint32(50).fork()).join();
    });
    Object.entries(message.indexRegisterInfo).forEach(([key, value]) => {
      TunnelDeviceList_IndexRegisterInfoEntry.encode({ key: key as any, value }, writer.uint32(58).fork()).join();
    });
    Object.entries(message.variableClassifies).forEach(([key, value]) => {
      TunnelDeviceList_VariableClassifiesEntry.encode({ key: key as any, value }, writer.uint32(66).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TunnelDeviceList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTunnelDeviceList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = TunnelDeviceList_TunnelsEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.tunnels[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = TunnelDeviceList_DevicesEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.devices[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = TunnelDeviceList_DeviceNoMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.deviceNoMap[entry5.key] = entry5.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          const entry6 = TunnelDeviceList_StructDataTypesEntry.decode(reader, reader.uint32());
          if (entry6.value !== undefined) {
            message.structDataTypes[entry6.key] = entry6.value;
          }
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          const entry7 = TunnelDeviceList_IndexRegisterInfoEntry.decode(reader, reader.uint32());
          if (entry7.value !== undefined) {
            message.indexRegisterInfo[entry7.key] = entry7.value;
          }
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          const entry8 = TunnelDeviceList_VariableClassifiesEntry.decode(reader, reader.uint32());
          if (entry8.value !== undefined) {
            message.variableClassifies[entry8.key] = entry8.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TunnelDeviceList {
    return {
      tunnels: isObject(object.tunnels)
        ? Object.entries(object.tunnels).reduce<{ [key: number]: Tunnel }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = Tunnel.fromJSON(value);
          return acc;
        }, {})
        : {},
      devices: isObject(object.devices)
        ? Object.entries(object.devices).reduce<{ [key: number]: Device }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = Device.fromJSON(value);
          return acc;
        }, {})
        : {},
      deviceNoMap: isObject(object.deviceNoMap)
        ? Object.entries(object.deviceNoMap).reduce<{ [key: number]: number }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = Number(value);
          return acc;
        }, {})
        : {},
      structDataTypes: isObject(object.structDataTypes)
        ? Object.entries(object.structDataTypes).reduce<{ [key: number]: StructType }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = StructType.fromJSON(value);
          return acc;
        }, {})
        : {},
      indexRegisterInfo: isObject(object.indexRegisterInfo)
        ? Object.entries(object.indexRegisterInfo).reduce<{ [key: number]: IndexRegisterInfo }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = IndexRegisterInfo.fromJSON(value);
          return acc;
        }, {})
        : {},
      variableClassifies: isObject(object.variableClassifies)
        ? Object.entries(object.variableClassifies).reduce<{ [key: number]: VariableClassify }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = VariableClassify.fromJSON(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: TunnelDeviceList): unknown {
    const obj: any = {};
    if (message.tunnels) {
      const entries = Object.entries(message.tunnels);
      if (entries.length > 0) {
        obj.tunnels = {};
        entries.forEach(([k, v]) => {
          obj.tunnels[k] = Tunnel.toJSON(v);
        });
      }
    }
    if (message.devices) {
      const entries = Object.entries(message.devices);
      if (entries.length > 0) {
        obj.devices = {};
        entries.forEach(([k, v]) => {
          obj.devices[k] = Device.toJSON(v);
        });
      }
    }
    if (message.deviceNoMap) {
      const entries = Object.entries(message.deviceNoMap);
      if (entries.length > 0) {
        obj.deviceNoMap = {};
        entries.forEach(([k, v]) => {
          obj.deviceNoMap[k] = Math.round(v);
        });
      }
    }
    if (message.structDataTypes) {
      const entries = Object.entries(message.structDataTypes);
      if (entries.length > 0) {
        obj.structDataTypes = {};
        entries.forEach(([k, v]) => {
          obj.structDataTypes[k] = StructType.toJSON(v);
        });
      }
    }
    if (message.indexRegisterInfo) {
      const entries = Object.entries(message.indexRegisterInfo);
      if (entries.length > 0) {
        obj.indexRegisterInfo = {};
        entries.forEach(([k, v]) => {
          obj.indexRegisterInfo[k] = IndexRegisterInfo.toJSON(v);
        });
      }
    }
    if (message.variableClassifies) {
      const entries = Object.entries(message.variableClassifies);
      if (entries.length > 0) {
        obj.variableClassifies = {};
        entries.forEach(([k, v]) => {
          obj.variableClassifies[k] = VariableClassify.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TunnelDeviceList>, I>>(base?: I): TunnelDeviceList {
    return TunnelDeviceList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TunnelDeviceList>, I>>(object: I): TunnelDeviceList {
    const message = createBaseTunnelDeviceList();
    message.tunnels = Object.entries(object.tunnels ?? {}).reduce<{ [key: number]: Tunnel }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = Tunnel.fromPartial(value);
      }
      return acc;
    }, {});
    message.devices = Object.entries(object.devices ?? {}).reduce<{ [key: number]: Device }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = Device.fromPartial(value);
      }
      return acc;
    }, {});
    message.deviceNoMap = Object.entries(object.deviceNoMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.structDataTypes = Object.entries(object.structDataTypes ?? {}).reduce<{ [key: number]: StructType }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = StructType.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.indexRegisterInfo = Object.entries(object.indexRegisterInfo ?? {}).reduce<
      { [key: number]: IndexRegisterInfo }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = IndexRegisterInfo.fromPartial(value);
      }
      return acc;
    }, {});
    message.variableClassifies = Object.entries(object.variableClassifies ?? {}).reduce<
      { [key: number]: VariableClassify }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = VariableClassify.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseTunnelDeviceList_TunnelsEntry(): TunnelDeviceList_TunnelsEntry {
  return { key: 0, value: undefined };
}

export const TunnelDeviceList_TunnelsEntry: MessageFns<TunnelDeviceList_TunnelsEntry> = {
  encode(message: TunnelDeviceList_TunnelsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      Tunnel.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TunnelDeviceList_TunnelsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTunnelDeviceList_TunnelsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = Tunnel.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TunnelDeviceList_TunnelsEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? Tunnel.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: TunnelDeviceList_TunnelsEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = Tunnel.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TunnelDeviceList_TunnelsEntry>, I>>(base?: I): TunnelDeviceList_TunnelsEntry {
    return TunnelDeviceList_TunnelsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TunnelDeviceList_TunnelsEntry>, I>>(
    object: I,
  ): TunnelDeviceList_TunnelsEntry {
    const message = createBaseTunnelDeviceList_TunnelsEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? Tunnel.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseTunnelDeviceList_DevicesEntry(): TunnelDeviceList_DevicesEntry {
  return { key: 0, value: undefined };
}

export const TunnelDeviceList_DevicesEntry: MessageFns<TunnelDeviceList_DevicesEntry> = {
  encode(message: TunnelDeviceList_DevicesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      Device.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TunnelDeviceList_DevicesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTunnelDeviceList_DevicesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = Device.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TunnelDeviceList_DevicesEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? Device.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: TunnelDeviceList_DevicesEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = Device.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TunnelDeviceList_DevicesEntry>, I>>(base?: I): TunnelDeviceList_DevicesEntry {
    return TunnelDeviceList_DevicesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TunnelDeviceList_DevicesEntry>, I>>(
    object: I,
  ): TunnelDeviceList_DevicesEntry {
    const message = createBaseTunnelDeviceList_DevicesEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? Device.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseTunnelDeviceList_DeviceNoMapEntry(): TunnelDeviceList_DeviceNoMapEntry {
  return { key: 0, value: 0 };
}

export const TunnelDeviceList_DeviceNoMapEntry: MessageFns<TunnelDeviceList_DeviceNoMapEntry> = {
  encode(message: TunnelDeviceList_DeviceNoMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).uint32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TunnelDeviceList_DeviceNoMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTunnelDeviceList_DeviceNoMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TunnelDeviceList_DeviceNoMapEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
    };
  },

  toJSON(message: TunnelDeviceList_DeviceNoMapEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== 0) {
      obj.value = Math.round(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TunnelDeviceList_DeviceNoMapEntry>, I>>(
    base?: I,
  ): TunnelDeviceList_DeviceNoMapEntry {
    return TunnelDeviceList_DeviceNoMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TunnelDeviceList_DeviceNoMapEntry>, I>>(
    object: I,
  ): TunnelDeviceList_DeviceNoMapEntry {
    const message = createBaseTunnelDeviceList_DeviceNoMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseTunnelDeviceList_StructDataTypesEntry(): TunnelDeviceList_StructDataTypesEntry {
  return { key: 0, value: undefined };
}

export const TunnelDeviceList_StructDataTypesEntry: MessageFns<TunnelDeviceList_StructDataTypesEntry> = {
  encode(message: TunnelDeviceList_StructDataTypesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      StructType.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TunnelDeviceList_StructDataTypesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTunnelDeviceList_StructDataTypesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = StructType.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TunnelDeviceList_StructDataTypesEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? StructType.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: TunnelDeviceList_StructDataTypesEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = StructType.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TunnelDeviceList_StructDataTypesEntry>, I>>(
    base?: I,
  ): TunnelDeviceList_StructDataTypesEntry {
    return TunnelDeviceList_StructDataTypesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TunnelDeviceList_StructDataTypesEntry>, I>>(
    object: I,
  ): TunnelDeviceList_StructDataTypesEntry {
    const message = createBaseTunnelDeviceList_StructDataTypesEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? StructType.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseTunnelDeviceList_IndexRegisterInfoEntry(): TunnelDeviceList_IndexRegisterInfoEntry {
  return { key: 0, value: undefined };
}

export const TunnelDeviceList_IndexRegisterInfoEntry: MessageFns<TunnelDeviceList_IndexRegisterInfoEntry> = {
  encode(message: TunnelDeviceList_IndexRegisterInfoEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      IndexRegisterInfo.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TunnelDeviceList_IndexRegisterInfoEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTunnelDeviceList_IndexRegisterInfoEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = IndexRegisterInfo.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TunnelDeviceList_IndexRegisterInfoEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? IndexRegisterInfo.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: TunnelDeviceList_IndexRegisterInfoEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = IndexRegisterInfo.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TunnelDeviceList_IndexRegisterInfoEntry>, I>>(
    base?: I,
  ): TunnelDeviceList_IndexRegisterInfoEntry {
    return TunnelDeviceList_IndexRegisterInfoEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TunnelDeviceList_IndexRegisterInfoEntry>, I>>(
    object: I,
  ): TunnelDeviceList_IndexRegisterInfoEntry {
    const message = createBaseTunnelDeviceList_IndexRegisterInfoEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? IndexRegisterInfo.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseTunnelDeviceList_VariableClassifiesEntry(): TunnelDeviceList_VariableClassifiesEntry {
  return { key: 0, value: undefined };
}

export const TunnelDeviceList_VariableClassifiesEntry: MessageFns<TunnelDeviceList_VariableClassifiesEntry> = {
  encode(message: TunnelDeviceList_VariableClassifiesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      VariableClassify.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TunnelDeviceList_VariableClassifiesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTunnelDeviceList_VariableClassifiesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = VariableClassify.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TunnelDeviceList_VariableClassifiesEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? VariableClassify.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: TunnelDeviceList_VariableClassifiesEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = VariableClassify.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TunnelDeviceList_VariableClassifiesEntry>, I>>(
    base?: I,
  ): TunnelDeviceList_VariableClassifiesEntry {
    return TunnelDeviceList_VariableClassifiesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TunnelDeviceList_VariableClassifiesEntry>, I>>(
    object: I,
  ): TunnelDeviceList_VariableClassifiesEntry {
    const message = createBaseTunnelDeviceList_VariableClassifiesEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? VariableClassify.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseVariableClassify(): VariableClassify {
  return { name: "", parentIdU16: [], disable: false, memo: "" };
}

export const VariableClassify: MessageFns<VariableClassify> = {
  encode(message: VariableClassify, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    writer.uint32(18).fork();
    for (const v of message.parentIdU16) {
      writer.uint32(v);
    }
    writer.join();
    if (message.disable !== false) {
      writer.uint32(24).bool(message.disable);
    }
    if (message.memo !== "") {
      writer.uint32(34).string(message.memo);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VariableClassify {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVariableClassify();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.parentIdU16.push(reader.uint32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.parentIdU16.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.disable = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VariableClassify {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      parentIdU16: globalThis.Array.isArray(object?.parentIdU16)
        ? object.parentIdU16.map((e: any) => globalThis.Number(e))
        : [],
      disable: isSet(object.disable) ? globalThis.Boolean(object.disable) : false,
      memo: isSet(object.memo) ? globalThis.String(object.memo) : "",
    };
  },

  toJSON(message: VariableClassify): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.parentIdU16?.length) {
      obj.parentIdU16 = message.parentIdU16.map((e) => Math.round(e));
    }
    if (message.disable !== false) {
      obj.disable = message.disable;
    }
    if (message.memo !== "") {
      obj.memo = message.memo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VariableClassify>, I>>(base?: I): VariableClassify {
    return VariableClassify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VariableClassify>, I>>(object: I): VariableClassify {
    const message = createBaseVariableClassify();
    message.name = object.name ?? "";
    message.parentIdU16 = object.parentIdU16?.map((e) => e) || [];
    message.disable = object.disable ?? false;
    message.memo = object.memo ?? "";
    return message;
  },
};

function createBaseIndexRegisterInfo(): IndexRegisterInfo {
  return { name: "", dataFormat: 0, initValue: undefined, syncVariable: undefined, disable: false, memo: "" };
}

export const IndexRegisterInfo: MessageFns<IndexRegisterInfo> = {
  encode(message: IndexRegisterInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.dataFormat !== 0) {
      writer.uint32(16).int32(message.dataFormat);
    }
    if (message.initValue !== undefined) {
      NumberValue.encode(message.initValue, writer.uint32(26).fork()).join();
    }
    if (message.syncVariable !== undefined) {
      VariableReference.encode(message.syncVariable, writer.uint32(34).fork()).join();
    }
    if (message.disable !== false) {
      writer.uint32(40).bool(message.disable);
    }
    if (message.memo !== "") {
      writer.uint32(50).string(message.memo);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): IndexRegisterInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIndexRegisterInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.dataFormat = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.initValue = NumberValue.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.syncVariable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.disable = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IndexRegisterInfo {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      dataFormat: isSet(object.dataFormat) ? dataFormatTypeFromJSON(object.dataFormat) : 0,
      initValue: isSet(object.initValue) ? NumberValue.fromJSON(object.initValue) : undefined,
      syncVariable: isSet(object.syncVariable) ? VariableReference.fromJSON(object.syncVariable) : undefined,
      disable: isSet(object.disable) ? globalThis.Boolean(object.disable) : false,
      memo: isSet(object.memo) ? globalThis.String(object.memo) : "",
    };
  },

  toJSON(message: IndexRegisterInfo): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.dataFormat !== 0) {
      obj.dataFormat = dataFormatTypeToJSON(message.dataFormat);
    }
    if (message.initValue !== undefined) {
      obj.initValue = NumberValue.toJSON(message.initValue);
    }
    if (message.syncVariable !== undefined) {
      obj.syncVariable = VariableReference.toJSON(message.syncVariable);
    }
    if (message.disable !== false) {
      obj.disable = message.disable;
    }
    if (message.memo !== "") {
      obj.memo = message.memo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IndexRegisterInfo>, I>>(base?: I): IndexRegisterInfo {
    return IndexRegisterInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IndexRegisterInfo>, I>>(object: I): IndexRegisterInfo {
    const message = createBaseIndexRegisterInfo();
    message.name = object.name ?? "";
    message.dataFormat = object.dataFormat ?? 0;
    message.initValue = (object.initValue !== undefined && object.initValue !== null)
      ? NumberValue.fromPartial(object.initValue)
      : undefined;
    message.syncVariable = (object.syncVariable !== undefined && object.syncVariable !== null)
      ? VariableReference.fromPartial(object.syncVariable)
      : undefined;
    message.disable = object.disable ?? false;
    message.memo = object.memo ?? "";
    return message;
  },
};

function createBaseTunnel(): Tunnel {
  return {
    tunnelType: "",
    no: undefined,
    name: "",
    memo: "",
    disable: false,
    communicationParams: undefined,
    serialParam: undefined,
    simulatedSerialParam: undefined,
  };
}

export const Tunnel: MessageFns<Tunnel> = {
  encode(message: Tunnel, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tunnelType !== "") {
      writer.uint32(10).string(message.tunnelType);
    }
    if (message.no !== undefined) {
      writer.uint32(16).int32(message.no);
    }
    if (message.name !== "") {
      writer.uint32(26).string(message.name);
    }
    if (message.memo !== "") {
      writer.uint32(34).string(message.memo);
    }
    if (message.disable !== false) {
      writer.uint32(40).bool(message.disable);
    }
    if (message.communicationParams !== undefined) {
      CommunicationParams.encode(message.communicationParams, writer.uint32(50).fork()).join();
    }
    if (message.serialParam !== undefined) {
      SerialTunnelParam.encode(message.serialParam, writer.uint32(58).fork()).join();
    }
    if (message.simulatedSerialParam !== undefined) {
      SerialTunnelParam.encode(message.simulatedSerialParam, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Tunnel {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTunnel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tunnelType = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.no = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.disable = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.communicationParams = CommunicationParams.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.serialParam = SerialTunnelParam.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.simulatedSerialParam = SerialTunnelParam.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Tunnel {
    return {
      tunnelType: isSet(object.tunnelType) ? globalThis.String(object.tunnelType) : "",
      no: isSet(object.no) ? globalThis.Number(object.no) : undefined,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      memo: isSet(object.memo) ? globalThis.String(object.memo) : "",
      disable: isSet(object.disable) ? globalThis.Boolean(object.disable) : false,
      communicationParams: isSet(object.communicationParams)
        ? CommunicationParams.fromJSON(object.communicationParams)
        : undefined,
      serialParam: isSet(object.serialParam) ? SerialTunnelParam.fromJSON(object.serialParam) : undefined,
      simulatedSerialParam: isSet(object.simulatedSerialParam)
        ? SerialTunnelParam.fromJSON(object.simulatedSerialParam)
        : undefined,
    };
  },

  toJSON(message: Tunnel): unknown {
    const obj: any = {};
    if (message.tunnelType !== "") {
      obj.tunnelType = message.tunnelType;
    }
    if (message.no !== undefined) {
      obj.no = Math.round(message.no);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.memo !== "") {
      obj.memo = message.memo;
    }
    if (message.disable !== false) {
      obj.disable = message.disable;
    }
    if (message.communicationParams !== undefined) {
      obj.communicationParams = CommunicationParams.toJSON(message.communicationParams);
    }
    if (message.serialParam !== undefined) {
      obj.serialParam = SerialTunnelParam.toJSON(message.serialParam);
    }
    if (message.simulatedSerialParam !== undefined) {
      obj.simulatedSerialParam = SerialTunnelParam.toJSON(message.simulatedSerialParam);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Tunnel>, I>>(base?: I): Tunnel {
    return Tunnel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Tunnel>, I>>(object: I): Tunnel {
    const message = createBaseTunnel();
    message.tunnelType = object.tunnelType ?? "";
    message.no = object.no ?? undefined;
    message.name = object.name ?? "";
    message.memo = object.memo ?? "";
    message.disable = object.disable ?? false;
    message.communicationParams = (object.communicationParams !== undefined && object.communicationParams !== null)
      ? CommunicationParams.fromPartial(object.communicationParams)
      : undefined;
    message.serialParam = (object.serialParam !== undefined && object.serialParam !== null)
      ? SerialTunnelParam.fromPartial(object.serialParam)
      : undefined;
    message.simulatedSerialParam = (object.simulatedSerialParam !== undefined && object.simulatedSerialParam !== null)
      ? SerialTunnelParam.fromPartial(object.simulatedSerialParam)
      : undefined;
    return message;
  },
};

function createBaseDevice(): Device {
  return {
    name: "",
    tunnelIdU8: 0,
    deviceBrand: "",
    deviceConfig: "",
    protocol: 0,
    protocolParams: [],
    disable: false,
    memo: "",
    deviceParams: undefined,
    station: undefined,
    broadcastPort: undefined,
    extendParams: {},
    communicationParams: undefined,
    initValues: [],
    parentDeviceId: undefined,
    simulatedDeviceParams: undefined,
  };
}

export const Device: MessageFns<Device> = {
  encode(message: Device, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.tunnelIdU8 !== 0) {
      writer.uint32(24).uint32(message.tunnelIdU8);
    }
    if (message.deviceBrand !== "") {
      writer.uint32(34).string(message.deviceBrand);
    }
    if (message.deviceConfig !== "") {
      writer.uint32(42).string(message.deviceConfig);
    }
    if (message.protocol !== 0) {
      writer.uint32(48).int32(message.protocol);
    }
    for (const v of message.protocolParams) {
      ProtocolParam.encode(v!, writer.uint32(58).fork()).join();
    }
    if (message.disable !== false) {
      writer.uint32(64).bool(message.disable);
    }
    if (message.memo !== "") {
      writer.uint32(74).string(message.memo);
    }
    switch (message.deviceParams?.$case) {
      case "ethernetParam":
        EthernetDeviceParam.encode(message.deviceParams.value, writer.uint32(90).fork()).join();
        break;
      case "serialParam":
        SerialDeviceParam.encode(message.deviceParams.value, writer.uint32(98).fork()).join();
        break;
      case "cloudParam":
        CloudDeviceParam.encode(message.deviceParams.value, writer.uint32(106).fork()).join();
        break;
      case "multiDeviceParam":
        MultiDeviceParam.encode(message.deviceParams.value, writer.uint32(114).fork()).join();
        break;
    }
    if (message.station !== undefined) {
      DataReferenceUInt8.encode(message.station, writer.uint32(122).fork()).join();
    }
    if (message.broadcastPort !== undefined) {
      DataReferenceUInt8.encode(message.broadcastPort, writer.uint32(130).fork()).join();
    }
    Object.entries(message.extendParams).forEach(([key, value]) => {
      Device_ExtendParamsEntry.encode({ key: key as any, value }, writer.uint32(138).fork()).join();
    });
    if (message.communicationParams !== undefined) {
      CommunicationParams.encode(message.communicationParams, writer.uint32(146).fork()).join();
    }
    for (const v of message.initValues) {
      VariableInitValue.encode(v!, writer.uint32(154).fork()).join();
    }
    if (message.parentDeviceId !== undefined) {
      writer.uint32(168).int32(message.parentDeviceId);
    }
    switch (message.simulatedDeviceParams?.$case) {
      case "simulatedEthernetParam":
        EthernetDeviceParam.encode(message.simulatedDeviceParams.value, writer.uint32(178).fork()).join();
        break;
      case "simulatedSerialParam":
        SerialDeviceParam.encode(message.simulatedDeviceParams.value, writer.uint32(186).fork()).join();
        break;
      case "simulatedCloudParam":
        CloudDeviceParam.encode(message.simulatedDeviceParams.value, writer.uint32(194).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Device {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDevice();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.tunnelIdU8 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.deviceBrand = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.deviceConfig = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.protocol = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.protocolParams.push(ProtocolParam.decode(reader, reader.uint32()));
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.disable = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.deviceParams = { $case: "ethernetParam", value: EthernetDeviceParam.decode(reader, reader.uint32()) };
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.deviceParams = { $case: "serialParam", value: SerialDeviceParam.decode(reader, reader.uint32()) };
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.deviceParams = { $case: "cloudParam", value: CloudDeviceParam.decode(reader, reader.uint32()) };
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.deviceParams = { $case: "multiDeviceParam", value: MultiDeviceParam.decode(reader, reader.uint32()) };
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.station = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.broadcastPort = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          const entry17 = Device_ExtendParamsEntry.decode(reader, reader.uint32());
          if (entry17.value !== undefined) {
            message.extendParams[entry17.key] = entry17.value;
          }
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.communicationParams = CommunicationParams.decode(reader, reader.uint32());
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.initValues.push(VariableInitValue.decode(reader, reader.uint32()));
          continue;
        }
        case 21: {
          if (tag !== 168) {
            break;
          }

          message.parentDeviceId = reader.int32();
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.simulatedDeviceParams = {
            $case: "simulatedEthernetParam",
            value: EthernetDeviceParam.decode(reader, reader.uint32()),
          };
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.simulatedDeviceParams = {
            $case: "simulatedSerialParam",
            value: SerialDeviceParam.decode(reader, reader.uint32()),
          };
          continue;
        }
        case 24: {
          if (tag !== 194) {
            break;
          }

          message.simulatedDeviceParams = {
            $case: "simulatedCloudParam",
            value: CloudDeviceParam.decode(reader, reader.uint32()),
          };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Device {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      tunnelIdU8: isSet(object.tunnelIdU8) ? globalThis.Number(object.tunnelIdU8) : 0,
      deviceBrand: isSet(object.deviceBrand) ? globalThis.String(object.deviceBrand) : "",
      deviceConfig: isSet(object.deviceConfig) ? globalThis.String(object.deviceConfig) : "",
      protocol: isSet(object.protocol) ? protocolTypeFromJSON(object.protocol) : 0,
      protocolParams: globalThis.Array.isArray(object?.protocolParams)
        ? object.protocolParams.map((e: any) => ProtocolParam.fromJSON(e))
        : [],
      disable: isSet(object.disable) ? globalThis.Boolean(object.disable) : false,
      memo: isSet(object.memo) ? globalThis.String(object.memo) : "",
      deviceParams: isSet(object.ethernetParam)
        ? { $case: "ethernetParam", value: EthernetDeviceParam.fromJSON(object.ethernetParam) }
        : isSet(object.serialParam)
        ? { $case: "serialParam", value: SerialDeviceParam.fromJSON(object.serialParam) }
        : isSet(object.cloudParam)
        ? { $case: "cloudParam", value: CloudDeviceParam.fromJSON(object.cloudParam) }
        : isSet(object.multiDeviceParam)
        ? { $case: "multiDeviceParam", value: MultiDeviceParam.fromJSON(object.multiDeviceParam) }
        : undefined,
      station: isSet(object.station) ? DataReferenceUInt8.fromJSON(object.station) : undefined,
      broadcastPort: isSet(object.broadcastPort) ? DataReferenceUInt8.fromJSON(object.broadcastPort) : undefined,
      extendParams: isObject(object.extendParams)
        ? Object.entries(object.extendParams).reduce<{ [key: number]: DataReferenceInt16 }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = DataReferenceInt16.fromJSON(value);
          return acc;
        }, {})
        : {},
      communicationParams: isSet(object.communicationParams)
        ? CommunicationParams.fromJSON(object.communicationParams)
        : undefined,
      initValues: globalThis.Array.isArray(object?.initValues)
        ? object.initValues.map((e: any) => VariableInitValue.fromJSON(e))
        : [],
      parentDeviceId: isSet(object.parentDeviceId) ? globalThis.Number(object.parentDeviceId) : undefined,
      simulatedDeviceParams: isSet(object.simulatedEthernetParam)
        ? { $case: "simulatedEthernetParam", value: EthernetDeviceParam.fromJSON(object.simulatedEthernetParam) }
        : isSet(object.simulatedSerialParam)
        ? { $case: "simulatedSerialParam", value: SerialDeviceParam.fromJSON(object.simulatedSerialParam) }
        : isSet(object.simulatedCloudParam)
        ? { $case: "simulatedCloudParam", value: CloudDeviceParam.fromJSON(object.simulatedCloudParam) }
        : undefined,
    };
  },

  toJSON(message: Device): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.tunnelIdU8 !== 0) {
      obj.tunnelIdU8 = Math.round(message.tunnelIdU8);
    }
    if (message.deviceBrand !== "") {
      obj.deviceBrand = message.deviceBrand;
    }
    if (message.deviceConfig !== "") {
      obj.deviceConfig = message.deviceConfig;
    }
    if (message.protocol !== 0) {
      obj.protocol = protocolTypeToJSON(message.protocol);
    }
    if (message.protocolParams?.length) {
      obj.protocolParams = message.protocolParams.map((e) => ProtocolParam.toJSON(e));
    }
    if (message.disable !== false) {
      obj.disable = message.disable;
    }
    if (message.memo !== "") {
      obj.memo = message.memo;
    }
    if (message.deviceParams?.$case === "ethernetParam") {
      obj.ethernetParam = EthernetDeviceParam.toJSON(message.deviceParams.value);
    } else if (message.deviceParams?.$case === "serialParam") {
      obj.serialParam = SerialDeviceParam.toJSON(message.deviceParams.value);
    } else if (message.deviceParams?.$case === "cloudParam") {
      obj.cloudParam = CloudDeviceParam.toJSON(message.deviceParams.value);
    } else if (message.deviceParams?.$case === "multiDeviceParam") {
      obj.multiDeviceParam = MultiDeviceParam.toJSON(message.deviceParams.value);
    }
    if (message.station !== undefined) {
      obj.station = DataReferenceUInt8.toJSON(message.station);
    }
    if (message.broadcastPort !== undefined) {
      obj.broadcastPort = DataReferenceUInt8.toJSON(message.broadcastPort);
    }
    if (message.extendParams) {
      const entries = Object.entries(message.extendParams);
      if (entries.length > 0) {
        obj.extendParams = {};
        entries.forEach(([k, v]) => {
          obj.extendParams[k] = DataReferenceInt16.toJSON(v);
        });
      }
    }
    if (message.communicationParams !== undefined) {
      obj.communicationParams = CommunicationParams.toJSON(message.communicationParams);
    }
    if (message.initValues?.length) {
      obj.initValues = message.initValues.map((e) => VariableInitValue.toJSON(e));
    }
    if (message.parentDeviceId !== undefined) {
      obj.parentDeviceId = Math.round(message.parentDeviceId);
    }
    if (message.simulatedDeviceParams?.$case === "simulatedEthernetParam") {
      obj.simulatedEthernetParam = EthernetDeviceParam.toJSON(message.simulatedDeviceParams.value);
    } else if (message.simulatedDeviceParams?.$case === "simulatedSerialParam") {
      obj.simulatedSerialParam = SerialDeviceParam.toJSON(message.simulatedDeviceParams.value);
    } else if (message.simulatedDeviceParams?.$case === "simulatedCloudParam") {
      obj.simulatedCloudParam = CloudDeviceParam.toJSON(message.simulatedDeviceParams.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Device>, I>>(base?: I): Device {
    return Device.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Device>, I>>(object: I): Device {
    const message = createBaseDevice();
    message.name = object.name ?? "";
    message.tunnelIdU8 = object.tunnelIdU8 ?? 0;
    message.deviceBrand = object.deviceBrand ?? "";
    message.deviceConfig = object.deviceConfig ?? "";
    message.protocol = object.protocol ?? 0;
    message.protocolParams = object.protocolParams?.map((e) => ProtocolParam.fromPartial(e)) || [];
    message.disable = object.disable ?? false;
    message.memo = object.memo ?? "";
    switch (object.deviceParams?.$case) {
      case "ethernetParam": {
        if (object.deviceParams?.value !== undefined && object.deviceParams?.value !== null) {
          message.deviceParams = {
            $case: "ethernetParam",
            value: EthernetDeviceParam.fromPartial(object.deviceParams.value),
          };
        }
        break;
      }
      case "serialParam": {
        if (object.deviceParams?.value !== undefined && object.deviceParams?.value !== null) {
          message.deviceParams = {
            $case: "serialParam",
            value: SerialDeviceParam.fromPartial(object.deviceParams.value),
          };
        }
        break;
      }
      case "cloudParam": {
        if (object.deviceParams?.value !== undefined && object.deviceParams?.value !== null) {
          message.deviceParams = {
            $case: "cloudParam",
            value: CloudDeviceParam.fromPartial(object.deviceParams.value),
          };
        }
        break;
      }
      case "multiDeviceParam": {
        if (object.deviceParams?.value !== undefined && object.deviceParams?.value !== null) {
          message.deviceParams = {
            $case: "multiDeviceParam",
            value: MultiDeviceParam.fromPartial(object.deviceParams.value),
          };
        }
        break;
      }
    }
    message.station = (object.station !== undefined && object.station !== null)
      ? DataReferenceUInt8.fromPartial(object.station)
      : undefined;
    message.broadcastPort = (object.broadcastPort !== undefined && object.broadcastPort !== null)
      ? DataReferenceUInt8.fromPartial(object.broadcastPort)
      : undefined;
    message.extendParams = Object.entries(object.extendParams ?? {}).reduce<{ [key: number]: DataReferenceInt16 }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = DataReferenceInt16.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.communicationParams = (object.communicationParams !== undefined && object.communicationParams !== null)
      ? CommunicationParams.fromPartial(object.communicationParams)
      : undefined;
    message.initValues = object.initValues?.map((e) => VariableInitValue.fromPartial(e)) || [];
    message.parentDeviceId = object.parentDeviceId ?? undefined;
    switch (object.simulatedDeviceParams?.$case) {
      case "simulatedEthernetParam": {
        if (object.simulatedDeviceParams?.value !== undefined && object.simulatedDeviceParams?.value !== null) {
          message.simulatedDeviceParams = {
            $case: "simulatedEthernetParam",
            value: EthernetDeviceParam.fromPartial(object.simulatedDeviceParams.value),
          };
        }
        break;
      }
      case "simulatedSerialParam": {
        if (object.simulatedDeviceParams?.value !== undefined && object.simulatedDeviceParams?.value !== null) {
          message.simulatedDeviceParams = {
            $case: "simulatedSerialParam",
            value: SerialDeviceParam.fromPartial(object.simulatedDeviceParams.value),
          };
        }
        break;
      }
      case "simulatedCloudParam": {
        if (object.simulatedDeviceParams?.value !== undefined && object.simulatedDeviceParams?.value !== null) {
          message.simulatedDeviceParams = {
            $case: "simulatedCloudParam",
            value: CloudDeviceParam.fromPartial(object.simulatedDeviceParams.value),
          };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseDevice_ExtendParamsEntry(): Device_ExtendParamsEntry {
  return { key: 0, value: undefined };
}

export const Device_ExtendParamsEntry: MessageFns<Device_ExtendParamsEntry> = {
  encode(message: Device_ExtendParamsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      DataReferenceInt16.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Device_ExtendParamsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDevice_ExtendParamsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = DataReferenceInt16.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Device_ExtendParamsEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? DataReferenceInt16.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: Device_ExtendParamsEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = DataReferenceInt16.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Device_ExtendParamsEntry>, I>>(base?: I): Device_ExtendParamsEntry {
    return Device_ExtendParamsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Device_ExtendParamsEntry>, I>>(object: I): Device_ExtendParamsEntry {
    const message = createBaseDevice_ExtendParamsEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? DataReferenceInt16.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseVariableInitValue(): VariableInitValue {
  return { variable: undefined, value: undefined };
}

export const VariableInitValue: MessageFns<VariableInitValue> = {
  encode(message: VariableInitValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.variable !== undefined) {
      VariableReference.encode(message.variable, writer.uint32(10).fork()).join();
    }
    if (message.value !== undefined) {
      AllTypeValue.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VariableInitValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVariableInitValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.variable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = AllTypeValue.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VariableInitValue {
    return {
      variable: isSet(object.variable) ? VariableReference.fromJSON(object.variable) : undefined,
      value: isSet(object.value) ? AllTypeValue.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: VariableInitValue): unknown {
    const obj: any = {};
    if (message.variable !== undefined) {
      obj.variable = VariableReference.toJSON(message.variable);
    }
    if (message.value !== undefined) {
      obj.value = AllTypeValue.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VariableInitValue>, I>>(base?: I): VariableInitValue {
    return VariableInitValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VariableInitValue>, I>>(object: I): VariableInitValue {
    const message = createBaseVariableInitValue();
    message.variable = (object.variable !== undefined && object.variable !== null)
      ? VariableReference.fromPartial(object.variable)
      : undefined;
    message.value = (object.value !== undefined && object.value !== null)
      ? AllTypeValue.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseProtocolParam(): ProtocolParam {
  return { paramType: 0, paramValue: undefined };
}

export const ProtocolParam: MessageFns<ProtocolParam> = {
  encode(message: ProtocolParam, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.paramType !== 0) {
      writer.uint32(8).int32(message.paramType);
    }
    if (message.paramValue !== undefined) {
      DataReferenceInt16.encode(message.paramValue, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProtocolParam {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProtocolParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.paramType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.paramValue = DataReferenceInt16.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProtocolParam {
    return {
      paramType: isSet(object.paramType) ? protocolParamTypeFromJSON(object.paramType) : 0,
      paramValue: isSet(object.paramValue) ? DataReferenceInt16.fromJSON(object.paramValue) : undefined,
    };
  },

  toJSON(message: ProtocolParam): unknown {
    const obj: any = {};
    if (message.paramType !== 0) {
      obj.paramType = protocolParamTypeToJSON(message.paramType);
    }
    if (message.paramValue !== undefined) {
      obj.paramValue = DataReferenceInt16.toJSON(message.paramValue);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProtocolParam>, I>>(base?: I): ProtocolParam {
    return ProtocolParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProtocolParam>, I>>(object: I): ProtocolParam {
    const message = createBaseProtocolParam();
    message.paramType = object.paramType ?? 0;
    message.paramValue = (object.paramValue !== undefined && object.paramValue !== null)
      ? DataReferenceInt16.fromPartial(object.paramValue)
      : undefined;
    return message;
  },
};

function createBaseEthernetDeviceParam(): EthernetDeviceParam {
  return { ipAddress: undefined, ipPort: undefined, isUdp: false };
}

export const EthernetDeviceParam: MessageFns<EthernetDeviceParam> = {
  encode(message: EthernetDeviceParam, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ipAddress !== undefined) {
      DataReferenceUInt32.encode(message.ipAddress, writer.uint32(10).fork()).join();
    }
    if (message.ipPort !== undefined) {
      DataReferenceUInt16.encode(message.ipPort, writer.uint32(18).fork()).join();
    }
    if (message.isUdp !== false) {
      writer.uint32(32).bool(message.isUdp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EthernetDeviceParam {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEthernetDeviceParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.ipAddress = DataReferenceUInt32.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.ipPort = DataReferenceUInt16.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.isUdp = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EthernetDeviceParam {
    return {
      ipAddress: isSet(object.ipAddress) ? DataReferenceUInt32.fromJSON(object.ipAddress) : undefined,
      ipPort: isSet(object.ipPort) ? DataReferenceUInt16.fromJSON(object.ipPort) : undefined,
      isUdp: isSet(object.isUdp) ? globalThis.Boolean(object.isUdp) : false,
    };
  },

  toJSON(message: EthernetDeviceParam): unknown {
    const obj: any = {};
    if (message.ipAddress !== undefined) {
      obj.ipAddress = DataReferenceUInt32.toJSON(message.ipAddress);
    }
    if (message.ipPort !== undefined) {
      obj.ipPort = DataReferenceUInt16.toJSON(message.ipPort);
    }
    if (message.isUdp !== false) {
      obj.isUdp = message.isUdp;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EthernetDeviceParam>, I>>(base?: I): EthernetDeviceParam {
    return EthernetDeviceParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EthernetDeviceParam>, I>>(object: I): EthernetDeviceParam {
    const message = createBaseEthernetDeviceParam();
    message.ipAddress = (object.ipAddress !== undefined && object.ipAddress !== null)
      ? DataReferenceUInt32.fromPartial(object.ipAddress)
      : undefined;
    message.ipPort = (object.ipPort !== undefined && object.ipPort !== null)
      ? DataReferenceUInt16.fromPartial(object.ipPort)
      : undefined;
    message.isUdp = object.isUdp ?? false;
    return message;
  },
};

function createBaseMultiDeviceParam(): MultiDeviceParam {
  return { subDeviceIds: [], subDeviceIndexRegisterU16: undefined, subDeviceIndexOffsetI16: 0 };
}

export const MultiDeviceParam: MessageFns<MultiDeviceParam> = {
  encode(message: MultiDeviceParam, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.subDeviceIds) {
      writer.int32(v);
    }
    writer.join();
    if (message.subDeviceIndexRegisterU16 !== undefined) {
      writer.uint32(16).int32(message.subDeviceIndexRegisterU16);
    }
    if (message.subDeviceIndexOffsetI16 !== 0) {
      writer.uint32(24).int32(message.subDeviceIndexOffsetI16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MultiDeviceParam {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMultiDeviceParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.subDeviceIds.push(reader.int32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.subDeviceIds.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.subDeviceIndexRegisterU16 = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.subDeviceIndexOffsetI16 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MultiDeviceParam {
    return {
      subDeviceIds: globalThis.Array.isArray(object?.subDeviceIds)
        ? object.subDeviceIds.map((e: any) => globalThis.Number(e))
        : [],
      subDeviceIndexRegisterU16: isSet(object.subDeviceIndexRegisterU16)
        ? globalThis.Number(object.subDeviceIndexRegisterU16)
        : undefined,
      subDeviceIndexOffsetI16: isSet(object.subDeviceIndexOffsetI16)
        ? globalThis.Number(object.subDeviceIndexOffsetI16)
        : 0,
    };
  },

  toJSON(message: MultiDeviceParam): unknown {
    const obj: any = {};
    if (message.subDeviceIds?.length) {
      obj.subDeviceIds = message.subDeviceIds.map((e) => Math.round(e));
    }
    if (message.subDeviceIndexRegisterU16 !== undefined) {
      obj.subDeviceIndexRegisterU16 = Math.round(message.subDeviceIndexRegisterU16);
    }
    if (message.subDeviceIndexOffsetI16 !== 0) {
      obj.subDeviceIndexOffsetI16 = Math.round(message.subDeviceIndexOffsetI16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MultiDeviceParam>, I>>(base?: I): MultiDeviceParam {
    return MultiDeviceParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MultiDeviceParam>, I>>(object: I): MultiDeviceParam {
    const message = createBaseMultiDeviceParam();
    message.subDeviceIds = object.subDeviceIds?.map((e) => e) || [];
    message.subDeviceIndexRegisterU16 = object.subDeviceIndexRegisterU16 ?? undefined;
    message.subDeviceIndexOffsetI16 = object.subDeviceIndexOffsetI16 ?? 0;
    return message;
  },
};

function createBaseSerialDeviceParam(): SerialDeviceParam {
  return { baudrate: undefined, databits: undefined, parity: undefined, stopbits: undefined };
}

export const SerialDeviceParam: MessageFns<SerialDeviceParam> = {
  encode(message: SerialDeviceParam, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.baudrate !== undefined) {
      DataReferenceUInt16.encode(message.baudrate, writer.uint32(26).fork()).join();
    }
    if (message.databits !== undefined) {
      DataReferenceUInt8.encode(message.databits, writer.uint32(34).fork()).join();
    }
    if (message.parity !== undefined) {
      DataReferenceUInt8.encode(message.parity, writer.uint32(42).fork()).join();
    }
    if (message.stopbits !== undefined) {
      DataReferenceUInt8.encode(message.stopbits, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SerialDeviceParam {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSerialDeviceParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.baudrate = DataReferenceUInt16.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.databits = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.parity = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.stopbits = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SerialDeviceParam {
    return {
      baudrate: isSet(object.baudrate) ? DataReferenceUInt16.fromJSON(object.baudrate) : undefined,
      databits: isSet(object.databits) ? DataReferenceUInt8.fromJSON(object.databits) : undefined,
      parity: isSet(object.parity) ? DataReferenceUInt8.fromJSON(object.parity) : undefined,
      stopbits: isSet(object.stopbits) ? DataReferenceUInt8.fromJSON(object.stopbits) : undefined,
    };
  },

  toJSON(message: SerialDeviceParam): unknown {
    const obj: any = {};
    if (message.baudrate !== undefined) {
      obj.baudrate = DataReferenceUInt16.toJSON(message.baudrate);
    }
    if (message.databits !== undefined) {
      obj.databits = DataReferenceUInt8.toJSON(message.databits);
    }
    if (message.parity !== undefined) {
      obj.parity = DataReferenceUInt8.toJSON(message.parity);
    }
    if (message.stopbits !== undefined) {
      obj.stopbits = DataReferenceUInt8.toJSON(message.stopbits);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SerialDeviceParam>, I>>(base?: I): SerialDeviceParam {
    return SerialDeviceParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SerialDeviceParam>, I>>(object: I): SerialDeviceParam {
    const message = createBaseSerialDeviceParam();
    message.baudrate = (object.baudrate !== undefined && object.baudrate !== null)
      ? DataReferenceUInt16.fromPartial(object.baudrate)
      : undefined;
    message.databits = (object.databits !== undefined && object.databits !== null)
      ? DataReferenceUInt8.fromPartial(object.databits)
      : undefined;
    message.parity = (object.parity !== undefined && object.parity !== null)
      ? DataReferenceUInt8.fromPartial(object.parity)
      : undefined;
    message.stopbits = (object.stopbits !== undefined && object.stopbits !== null)
      ? DataReferenceUInt8.fromPartial(object.stopbits)
      : undefined;
    return message;
  },
};

function createBaseSerialTunnelParam(): SerialTunnelParam {
  return {
    serialNo: undefined,
    serialType: undefined,
    baudrate: undefined,
    databits: undefined,
    parity: undefined,
    stopbits: undefined,
  };
}

export const SerialTunnelParam: MessageFns<SerialTunnelParam> = {
  encode(message: SerialTunnelParam, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.serialNo !== undefined) {
      DataReferenceUInt8.encode(message.serialNo, writer.uint32(10).fork()).join();
    }
    if (message.serialType !== undefined) {
      DataReferenceUInt8.encode(message.serialType, writer.uint32(18).fork()).join();
    }
    if (message.baudrate !== undefined) {
      DataReferenceUInt16.encode(message.baudrate, writer.uint32(26).fork()).join();
    }
    if (message.databits !== undefined) {
      DataReferenceUInt8.encode(message.databits, writer.uint32(34).fork()).join();
    }
    if (message.parity !== undefined) {
      DataReferenceUInt8.encode(message.parity, writer.uint32(42).fork()).join();
    }
    if (message.stopbits !== undefined) {
      DataReferenceUInt8.encode(message.stopbits, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SerialTunnelParam {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSerialTunnelParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.serialNo = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.serialType = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.baudrate = DataReferenceUInt16.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.databits = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.parity = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.stopbits = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SerialTunnelParam {
    return {
      serialNo: isSet(object.serialNo) ? DataReferenceUInt8.fromJSON(object.serialNo) : undefined,
      serialType: isSet(object.serialType) ? DataReferenceUInt8.fromJSON(object.serialType) : undefined,
      baudrate: isSet(object.baudrate) ? DataReferenceUInt16.fromJSON(object.baudrate) : undefined,
      databits: isSet(object.databits) ? DataReferenceUInt8.fromJSON(object.databits) : undefined,
      parity: isSet(object.parity) ? DataReferenceUInt8.fromJSON(object.parity) : undefined,
      stopbits: isSet(object.stopbits) ? DataReferenceUInt8.fromJSON(object.stopbits) : undefined,
    };
  },

  toJSON(message: SerialTunnelParam): unknown {
    const obj: any = {};
    if (message.serialNo !== undefined) {
      obj.serialNo = DataReferenceUInt8.toJSON(message.serialNo);
    }
    if (message.serialType !== undefined) {
      obj.serialType = DataReferenceUInt8.toJSON(message.serialType);
    }
    if (message.baudrate !== undefined) {
      obj.baudrate = DataReferenceUInt16.toJSON(message.baudrate);
    }
    if (message.databits !== undefined) {
      obj.databits = DataReferenceUInt8.toJSON(message.databits);
    }
    if (message.parity !== undefined) {
      obj.parity = DataReferenceUInt8.toJSON(message.parity);
    }
    if (message.stopbits !== undefined) {
      obj.stopbits = DataReferenceUInt8.toJSON(message.stopbits);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SerialTunnelParam>, I>>(base?: I): SerialTunnelParam {
    return SerialTunnelParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SerialTunnelParam>, I>>(object: I): SerialTunnelParam {
    const message = createBaseSerialTunnelParam();
    message.serialNo = (object.serialNo !== undefined && object.serialNo !== null)
      ? DataReferenceUInt8.fromPartial(object.serialNo)
      : undefined;
    message.serialType = (object.serialType !== undefined && object.serialType !== null)
      ? DataReferenceUInt8.fromPartial(object.serialType)
      : undefined;
    message.baudrate = (object.baudrate !== undefined && object.baudrate !== null)
      ? DataReferenceUInt16.fromPartial(object.baudrate)
      : undefined;
    message.databits = (object.databits !== undefined && object.databits !== null)
      ? DataReferenceUInt8.fromPartial(object.databits)
      : undefined;
    message.parity = (object.parity !== undefined && object.parity !== null)
      ? DataReferenceUInt8.fromPartial(object.parity)
      : undefined;
    message.stopbits = (object.stopbits !== undefined && object.stopbits !== null)
      ? DataReferenceUInt8.fromPartial(object.stopbits)
      : undefined;
    return message;
  },
};

function createBaseCloudDeviceParam(): CloudDeviceParam {
  return { cloudDeviceKey: "" };
}

export const CloudDeviceParam: MessageFns<CloudDeviceParam> = {
  encode(message: CloudDeviceParam, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cloudDeviceKey !== "") {
      writer.uint32(42).string(message.cloudDeviceKey);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CloudDeviceParam {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCloudDeviceParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.cloudDeviceKey = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CloudDeviceParam {
    return { cloudDeviceKey: isSet(object.cloudDeviceKey) ? globalThis.String(object.cloudDeviceKey) : "" };
  },

  toJSON(message: CloudDeviceParam): unknown {
    const obj: any = {};
    if (message.cloudDeviceKey !== "") {
      obj.cloudDeviceKey = message.cloudDeviceKey;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CloudDeviceParam>, I>>(base?: I): CloudDeviceParam {
    return CloudDeviceParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CloudDeviceParam>, I>>(object: I): CloudDeviceParam {
    const message = createBaseCloudDeviceParam();
    message.cloudDeviceKey = object.cloudDeviceKey ?? "";
    return message;
  },
};

function createBaseCommunicationParams(): CommunicationParams {
  return {
    timeoutU16: undefined,
    retryCountU8: undefined,
    retryIntervalU16: undefined,
    minReadIntervalU16: undefined,
    bitGroupDistanceU16: undefined,
    bitGroupMaxCountU16: undefined,
    bitWriteMaxLengthU16: undefined,
    wordGroupDistanceU16: undefined,
    wordGroupMaxCountU16: undefined,
    wordWriteMaxLengthU16: undefined,
    errorPromptTimeI16: undefined,
    int16Order: undefined,
    int32Order: undefined,
    float32Order: undefined,
  };
}

export const CommunicationParams: MessageFns<CommunicationParams> = {
  encode(message: CommunicationParams, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.timeoutU16 !== undefined) {
      writer.uint32(8).uint32(message.timeoutU16);
    }
    if (message.retryCountU8 !== undefined) {
      writer.uint32(16).uint32(message.retryCountU8);
    }
    if (message.retryIntervalU16 !== undefined) {
      writer.uint32(24).uint32(message.retryIntervalU16);
    }
    if (message.minReadIntervalU16 !== undefined) {
      writer.uint32(32).uint32(message.minReadIntervalU16);
    }
    if (message.bitGroupDistanceU16 !== undefined) {
      writer.uint32(40).uint32(message.bitGroupDistanceU16);
    }
    if (message.bitGroupMaxCountU16 !== undefined) {
      writer.uint32(48).uint32(message.bitGroupMaxCountU16);
    }
    if (message.bitWriteMaxLengthU16 !== undefined) {
      writer.uint32(56).uint32(message.bitWriteMaxLengthU16);
    }
    if (message.wordGroupDistanceU16 !== undefined) {
      writer.uint32(64).uint32(message.wordGroupDistanceU16);
    }
    if (message.wordGroupMaxCountU16 !== undefined) {
      writer.uint32(72).uint32(message.wordGroupMaxCountU16);
    }
    if (message.wordWriteMaxLengthU16 !== undefined) {
      writer.uint32(80).uint32(message.wordWriteMaxLengthU16);
    }
    if (message.errorPromptTimeI16 !== undefined) {
      writer.uint32(96).int32(message.errorPromptTimeI16);
    }
    if (message.int16Order !== undefined) {
      writer.uint32(104).int32(message.int16Order);
    }
    if (message.int32Order !== undefined) {
      writer.uint32(112).int32(message.int32Order);
    }
    if (message.float32Order !== undefined) {
      writer.uint32(120).int32(message.float32Order);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CommunicationParams {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommunicationParams();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.timeoutU16 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.retryCountU8 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.retryIntervalU16 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.minReadIntervalU16 = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.bitGroupDistanceU16 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.bitGroupMaxCountU16 = reader.uint32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.bitWriteMaxLengthU16 = reader.uint32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.wordGroupDistanceU16 = reader.uint32();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.wordGroupMaxCountU16 = reader.uint32();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.wordWriteMaxLengthU16 = reader.uint32();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.errorPromptTimeI16 = reader.int32();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.int16Order = reader.int32() as any;
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.int32Order = reader.int32() as any;
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.float32Order = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CommunicationParams {
    return {
      timeoutU16: isSet(object.timeoutU16) ? globalThis.Number(object.timeoutU16) : undefined,
      retryCountU8: isSet(object.retryCountU8) ? globalThis.Number(object.retryCountU8) : undefined,
      retryIntervalU16: isSet(object.retryIntervalU16) ? globalThis.Number(object.retryIntervalU16) : undefined,
      minReadIntervalU16: isSet(object.minReadIntervalU16) ? globalThis.Number(object.minReadIntervalU16) : undefined,
      bitGroupDistanceU16: isSet(object.bitGroupDistanceU16)
        ? globalThis.Number(object.bitGroupDistanceU16)
        : undefined,
      bitGroupMaxCountU16: isSet(object.bitGroupMaxCountU16)
        ? globalThis.Number(object.bitGroupMaxCountU16)
        : undefined,
      bitWriteMaxLengthU16: isSet(object.bitWriteMaxLengthU16)
        ? globalThis.Number(object.bitWriteMaxLengthU16)
        : undefined,
      wordGroupDistanceU16: isSet(object.wordGroupDistanceU16)
        ? globalThis.Number(object.wordGroupDistanceU16)
        : undefined,
      wordGroupMaxCountU16: isSet(object.wordGroupMaxCountU16)
        ? globalThis.Number(object.wordGroupMaxCountU16)
        : undefined,
      wordWriteMaxLengthU16: isSet(object.wordWriteMaxLengthU16)
        ? globalThis.Number(object.wordWriteMaxLengthU16)
        : undefined,
      errorPromptTimeI16: isSet(object.errorPromptTimeI16) ? globalThis.Number(object.errorPromptTimeI16) : undefined,
      int16Order: isSet(object.int16Order) ? word16OrderFromJSON(object.int16Order) : undefined,
      int32Order: isSet(object.int32Order) ? word32OrderFromJSON(object.int32Order) : undefined,
      float32Order: isSet(object.float32Order) ? word32OrderFromJSON(object.float32Order) : undefined,
    };
  },

  toJSON(message: CommunicationParams): unknown {
    const obj: any = {};
    if (message.timeoutU16 !== undefined) {
      obj.timeoutU16 = Math.round(message.timeoutU16);
    }
    if (message.retryCountU8 !== undefined) {
      obj.retryCountU8 = Math.round(message.retryCountU8);
    }
    if (message.retryIntervalU16 !== undefined) {
      obj.retryIntervalU16 = Math.round(message.retryIntervalU16);
    }
    if (message.minReadIntervalU16 !== undefined) {
      obj.minReadIntervalU16 = Math.round(message.minReadIntervalU16);
    }
    if (message.bitGroupDistanceU16 !== undefined) {
      obj.bitGroupDistanceU16 = Math.round(message.bitGroupDistanceU16);
    }
    if (message.bitGroupMaxCountU16 !== undefined) {
      obj.bitGroupMaxCountU16 = Math.round(message.bitGroupMaxCountU16);
    }
    if (message.bitWriteMaxLengthU16 !== undefined) {
      obj.bitWriteMaxLengthU16 = Math.round(message.bitWriteMaxLengthU16);
    }
    if (message.wordGroupDistanceU16 !== undefined) {
      obj.wordGroupDistanceU16 = Math.round(message.wordGroupDistanceU16);
    }
    if (message.wordGroupMaxCountU16 !== undefined) {
      obj.wordGroupMaxCountU16 = Math.round(message.wordGroupMaxCountU16);
    }
    if (message.wordWriteMaxLengthU16 !== undefined) {
      obj.wordWriteMaxLengthU16 = Math.round(message.wordWriteMaxLengthU16);
    }
    if (message.errorPromptTimeI16 !== undefined) {
      obj.errorPromptTimeI16 = Math.round(message.errorPromptTimeI16);
    }
    if (message.int16Order !== undefined) {
      obj.int16Order = word16OrderToJSON(message.int16Order);
    }
    if (message.int32Order !== undefined) {
      obj.int32Order = word32OrderToJSON(message.int32Order);
    }
    if (message.float32Order !== undefined) {
      obj.float32Order = word32OrderToJSON(message.float32Order);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CommunicationParams>, I>>(base?: I): CommunicationParams {
    return CommunicationParams.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommunicationParams>, I>>(object: I): CommunicationParams {
    const message = createBaseCommunicationParams();
    message.timeoutU16 = object.timeoutU16 ?? undefined;
    message.retryCountU8 = object.retryCountU8 ?? undefined;
    message.retryIntervalU16 = object.retryIntervalU16 ?? undefined;
    message.minReadIntervalU16 = object.minReadIntervalU16 ?? undefined;
    message.bitGroupDistanceU16 = object.bitGroupDistanceU16 ?? undefined;
    message.bitGroupMaxCountU16 = object.bitGroupMaxCountU16 ?? undefined;
    message.bitWriteMaxLengthU16 = object.bitWriteMaxLengthU16 ?? undefined;
    message.wordGroupDistanceU16 = object.wordGroupDistanceU16 ?? undefined;
    message.wordGroupMaxCountU16 = object.wordGroupMaxCountU16 ?? undefined;
    message.wordWriteMaxLengthU16 = object.wordWriteMaxLengthU16 ?? undefined;
    message.errorPromptTimeI16 = object.errorPromptTimeI16 ?? undefined;
    message.int16Order = object.int16Order ?? undefined;
    message.int32Order = object.int32Order ?? undefined;
    message.float32Order = object.float32Order ?? undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
