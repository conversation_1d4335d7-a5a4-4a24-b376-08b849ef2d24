// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/script.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { ProtectType, protectTypeFromJSON, protectTypeToJSON } from "./common";
import { VariableReference } from "./variable";

export const protobufPackage = "znd.project.v1";

/** 数据变化类型 */
export enum DataChangeType {
  DATA_CHANGE_TYPE_UNSPECIFIED = 0,
  /** DATA_CHANGE_TYPE_ON_CHANGE - 值改变执行一次(位切换) */
  DATA_CHANGE_TYPE_ON_CHANGE = 1,
  /** DATA_CHANGE_TYPE_ON_ZERO - 值从非0变为0执行一次(位从ON变为OFF) */
  DATA_CHANGE_TYPE_ON_ZERO = 2,
  /** DATA_CHANGE_TYPE_ON_NOT_ZERO - 值从0变为非零执行一次(位从OFF变为ON) */
  DATA_CHANGE_TYPE_ON_NOT_ZERO = 3,
  /** DATA_CHANGE_TYPE_ON_MINUS - 值从正数变为负数执行一次(位地址无此选项) */
  DATA_CHANGE_TYPE_ON_MINUS = 4,
  /** DATA_CHANGE_TYPE_ON_PLUS - 值从负数变为正数执行一次(位地址无此选项) */
  DATA_CHANGE_TYPE_ON_PLUS = 5,
  /** DATA_CHANGE_TYPE_ON_ADD - 只在值增加时执行(位地址无此选项) */
  DATA_CHANGE_TYPE_ON_ADD = 8,
  /** DATA_CHANGE_TYPE_ON_SUB - 只在值减少时执行(位地址无此选项) */
  DATA_CHANGE_TYPE_ON_SUB = 9,
  UNRECOGNIZED = -1,
}

export function dataChangeTypeFromJSON(object: any): DataChangeType {
  switch (object) {
    case 0:
    case "DATA_CHANGE_TYPE_UNSPECIFIED":
      return DataChangeType.DATA_CHANGE_TYPE_UNSPECIFIED;
    case 1:
    case "DATA_CHANGE_TYPE_ON_CHANGE":
      return DataChangeType.DATA_CHANGE_TYPE_ON_CHANGE;
    case 2:
    case "DATA_CHANGE_TYPE_ON_ZERO":
      return DataChangeType.DATA_CHANGE_TYPE_ON_ZERO;
    case 3:
    case "DATA_CHANGE_TYPE_ON_NOT_ZERO":
      return DataChangeType.DATA_CHANGE_TYPE_ON_NOT_ZERO;
    case 4:
    case "DATA_CHANGE_TYPE_ON_MINUS":
      return DataChangeType.DATA_CHANGE_TYPE_ON_MINUS;
    case 5:
    case "DATA_CHANGE_TYPE_ON_PLUS":
      return DataChangeType.DATA_CHANGE_TYPE_ON_PLUS;
    case 8:
    case "DATA_CHANGE_TYPE_ON_ADD":
      return DataChangeType.DATA_CHANGE_TYPE_ON_ADD;
    case 9:
    case "DATA_CHANGE_TYPE_ON_SUB":
      return DataChangeType.DATA_CHANGE_TYPE_ON_SUB;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DataChangeType.UNRECOGNIZED;
  }
}

export function dataChangeTypeToJSON(object: DataChangeType): string {
  switch (object) {
    case DataChangeType.DATA_CHANGE_TYPE_UNSPECIFIED:
      return "DATA_CHANGE_TYPE_UNSPECIFIED";
    case DataChangeType.DATA_CHANGE_TYPE_ON_CHANGE:
      return "DATA_CHANGE_TYPE_ON_CHANGE";
    case DataChangeType.DATA_CHANGE_TYPE_ON_ZERO:
      return "DATA_CHANGE_TYPE_ON_ZERO";
    case DataChangeType.DATA_CHANGE_TYPE_ON_NOT_ZERO:
      return "DATA_CHANGE_TYPE_ON_NOT_ZERO";
    case DataChangeType.DATA_CHANGE_TYPE_ON_MINUS:
      return "DATA_CHANGE_TYPE_ON_MINUS";
    case DataChangeType.DATA_CHANGE_TYPE_ON_PLUS:
      return "DATA_CHANGE_TYPE_ON_PLUS";
    case DataChangeType.DATA_CHANGE_TYPE_ON_ADD:
      return "DATA_CHANGE_TYPE_ON_ADD";
    case DataChangeType.DATA_CHANGE_TYPE_ON_SUB:
      return "DATA_CHANGE_TYPE_ON_SUB";
    case DataChangeType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 代码类型 */
export enum ScriptType {
  SCRIPT_TYPE_UNSPECIFIED = 0,
  SCRIPT_TYPE_LUA = 1,
  SCRIPT_TYPE_PYTHON = 2,
  SCRIPT_TYPE_JS = 3,
  SCRIPT_TYPE_C = 4,
  SCRIPT_TYPE_LD = 5,
  SCRIPT_TYPE_ST = 6,
  UNRECOGNIZED = -1,
}

export function scriptTypeFromJSON(object: any): ScriptType {
  switch (object) {
    case 0:
    case "SCRIPT_TYPE_UNSPECIFIED":
      return ScriptType.SCRIPT_TYPE_UNSPECIFIED;
    case 1:
    case "SCRIPT_TYPE_LUA":
      return ScriptType.SCRIPT_TYPE_LUA;
    case 2:
    case "SCRIPT_TYPE_PYTHON":
      return ScriptType.SCRIPT_TYPE_PYTHON;
    case 3:
    case "SCRIPT_TYPE_JS":
      return ScriptType.SCRIPT_TYPE_JS;
    case 4:
    case "SCRIPT_TYPE_C":
      return ScriptType.SCRIPT_TYPE_C;
    case 5:
    case "SCRIPT_TYPE_LD":
      return ScriptType.SCRIPT_TYPE_LD;
    case 6:
    case "SCRIPT_TYPE_ST":
      return ScriptType.SCRIPT_TYPE_ST;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ScriptType.UNRECOGNIZED;
  }
}

export function scriptTypeToJSON(object: ScriptType): string {
  switch (object) {
    case ScriptType.SCRIPT_TYPE_UNSPECIFIED:
      return "SCRIPT_TYPE_UNSPECIFIED";
    case ScriptType.SCRIPT_TYPE_LUA:
      return "SCRIPT_TYPE_LUA";
    case ScriptType.SCRIPT_TYPE_PYTHON:
      return "SCRIPT_TYPE_PYTHON";
    case ScriptType.SCRIPT_TYPE_JS:
      return "SCRIPT_TYPE_JS";
    case ScriptType.SCRIPT_TYPE_C:
      return "SCRIPT_TYPE_C";
    case ScriptType.SCRIPT_TYPE_LD:
      return "SCRIPT_TYPE_LD";
    case ScriptType.SCRIPT_TYPE_ST:
      return "SCRIPT_TYPE_ST";
    case ScriptType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 启动事件 */
export interface StartEvent {
  /** 延迟时间，单位毫秒 */
  delay?: number | undefined;
}

/** 定时事件 */
export interface TimerEvent {
  /** 间隔时间，单位毫秒 */
  interval?:
    | number
    | undefined;
  /** 执行次数，未设置时无限次 */
  count?:
    | number
    | undefined;
  /** 启用/禁用 */
  enable: VariableReference | undefined;
}

/** 退出事件 */
export interface ExitEvent {
  /** 延迟时间，单位毫秒 */
  delay?: number | undefined;
}

/** 页面打开事件 */
export interface PageOpenEvent {
  /** 页面ID */
  pageId?:
    | number
    | undefined;
  /** 延迟时间，单位毫秒 */
  delay?:
    | number
    | undefined;
  /** 循环间隔时间，单位毫秒 */
  loopInterval?:
    | number
    | undefined;
  /** 循环次数，未设置时为无限次,直到窗口关闭 */
  loopCount?: number | undefined;
}

/** 页面关闭事件 */
export interface PageCloseEvent {
  /** 页面ID */
  pageId?:
    | number
    | undefined;
  /** 延迟时间，单位毫秒 */
  delay?: number | undefined;
}

/** 触发事件 */
export interface DataChangeEvent {
  /** 触发数据 */
  data:
    | VariableReference
    | undefined;
  /** 数据变化类型 */
  changeType?:
    | DataChangeType
    | undefined;
  /** 误差范围，仅在变化时有效 */
  tolerance?:
    | number
    | undefined;
  /** 延迟时间，单位毫秒 */
  delay?: number | undefined;
}

/** 程序 */
export interface Program {
  id: number;
  name: string;
  /** 开始事件 */
  startEvent?:
    | StartEvent
    | undefined;
  /** 结束事件 */
  exitEvent?:
    | ExitEvent
    | undefined;
  /** 页面打开事件 */
  pageOpenEvent?:
    | PageOpenEvent
    | undefined;
  /** 页面关闭事件 */
  pageCloseEvent?:
    | PageCloseEvent
    | undefined;
  /** 定时事件 */
  timerEvent?:
    | TimerEvent
    | undefined;
  /** 数据变化事件 */
  dataChangeEvent?:
    | DataChangeEvent
    | undefined;
  /** 执行脚本 */
  scripts: Script[];
  /** 执行结束后再调用程序ID列表 */
  afterProgramIds: number[];
  /** 备注 */
  memo?:
    | string
    | undefined;
  /** 是否禁用 */
  isDisable?:
    | boolean
    | undefined;
  /** 是否保护 */
  protectType?:
    | ProtectType
    | undefined;
  /** 保护密码 */
  protectPasswd?:
    | string
    | undefined;
  /** 是否是子程序 */
  isSubProgram?:
    | boolean
    | undefined;
  /** 脚本分类 */
  categoryId?: number | undefined;
}

/** 脚本 */
export interface Script {
  /** 执行前根据值决定是否执行 */
  enable: VariableReference | undefined;
  type?: ScriptType | undefined;
  code?: string | undefined;
}

function createBaseStartEvent(): StartEvent {
  return { delay: undefined };
}

export const StartEvent: MessageFns<StartEvent> = {
  encode(message: StartEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.delay !== undefined) {
      writer.uint32(8).int32(message.delay);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StartEvent {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStartEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.delay = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StartEvent {
    return { delay: isSet(object.delay) ? globalThis.Number(object.delay) : undefined };
  },

  toJSON(message: StartEvent): unknown {
    const obj: any = {};
    if (message.delay !== undefined) {
      obj.delay = Math.round(message.delay);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StartEvent>, I>>(base?: I): StartEvent {
    return StartEvent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StartEvent>, I>>(object: I): StartEvent {
    const message = createBaseStartEvent();
    message.delay = object.delay ?? undefined;
    return message;
  },
};

function createBaseTimerEvent(): TimerEvent {
  return { interval: undefined, count: undefined, enable: undefined };
}

export const TimerEvent: MessageFns<TimerEvent> = {
  encode(message: TimerEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.interval !== undefined) {
      writer.uint32(8).int32(message.interval);
    }
    if (message.count !== undefined) {
      writer.uint32(16).int32(message.count);
    }
    if (message.enable !== undefined) {
      VariableReference.encode(message.enable, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TimerEvent {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTimerEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.interval = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.enable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TimerEvent {
    return {
      interval: isSet(object.interval) ? globalThis.Number(object.interval) : undefined,
      count: isSet(object.count) ? globalThis.Number(object.count) : undefined,
      enable: isSet(object.enable) ? VariableReference.fromJSON(object.enable) : undefined,
    };
  },

  toJSON(message: TimerEvent): unknown {
    const obj: any = {};
    if (message.interval !== undefined) {
      obj.interval = Math.round(message.interval);
    }
    if (message.count !== undefined) {
      obj.count = Math.round(message.count);
    }
    if (message.enable !== undefined) {
      obj.enable = VariableReference.toJSON(message.enable);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TimerEvent>, I>>(base?: I): TimerEvent {
    return TimerEvent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TimerEvent>, I>>(object: I): TimerEvent {
    const message = createBaseTimerEvent();
    message.interval = object.interval ?? undefined;
    message.count = object.count ?? undefined;
    message.enable = (object.enable !== undefined && object.enable !== null)
      ? VariableReference.fromPartial(object.enable)
      : undefined;
    return message;
  },
};

function createBaseExitEvent(): ExitEvent {
  return { delay: undefined };
}

export const ExitEvent: MessageFns<ExitEvent> = {
  encode(message: ExitEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.delay !== undefined) {
      writer.uint32(8).int32(message.delay);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ExitEvent {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseExitEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.delay = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ExitEvent {
    return { delay: isSet(object.delay) ? globalThis.Number(object.delay) : undefined };
  },

  toJSON(message: ExitEvent): unknown {
    const obj: any = {};
    if (message.delay !== undefined) {
      obj.delay = Math.round(message.delay);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ExitEvent>, I>>(base?: I): ExitEvent {
    return ExitEvent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExitEvent>, I>>(object: I): ExitEvent {
    const message = createBaseExitEvent();
    message.delay = object.delay ?? undefined;
    return message;
  },
};

function createBasePageOpenEvent(): PageOpenEvent {
  return { pageId: undefined, delay: undefined, loopInterval: undefined, loopCount: undefined };
}

export const PageOpenEvent: MessageFns<PageOpenEvent> = {
  encode(message: PageOpenEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageId !== undefined) {
      writer.uint32(8).int32(message.pageId);
    }
    if (message.delay !== undefined) {
      writer.uint32(16).int32(message.delay);
    }
    if (message.loopInterval !== undefined) {
      writer.uint32(24).int32(message.loopInterval);
    }
    if (message.loopCount !== undefined) {
      writer.uint32(32).int32(message.loopCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PageOpenEvent {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePageOpenEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pageId = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.delay = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.loopInterval = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.loopCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PageOpenEvent {
    return {
      pageId: isSet(object.pageId) ? globalThis.Number(object.pageId) : undefined,
      delay: isSet(object.delay) ? globalThis.Number(object.delay) : undefined,
      loopInterval: isSet(object.loopInterval) ? globalThis.Number(object.loopInterval) : undefined,
      loopCount: isSet(object.loopCount) ? globalThis.Number(object.loopCount) : undefined,
    };
  },

  toJSON(message: PageOpenEvent): unknown {
    const obj: any = {};
    if (message.pageId !== undefined) {
      obj.pageId = Math.round(message.pageId);
    }
    if (message.delay !== undefined) {
      obj.delay = Math.round(message.delay);
    }
    if (message.loopInterval !== undefined) {
      obj.loopInterval = Math.round(message.loopInterval);
    }
    if (message.loopCount !== undefined) {
      obj.loopCount = Math.round(message.loopCount);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PageOpenEvent>, I>>(base?: I): PageOpenEvent {
    return PageOpenEvent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageOpenEvent>, I>>(object: I): PageOpenEvent {
    const message = createBasePageOpenEvent();
    message.pageId = object.pageId ?? undefined;
    message.delay = object.delay ?? undefined;
    message.loopInterval = object.loopInterval ?? undefined;
    message.loopCount = object.loopCount ?? undefined;
    return message;
  },
};

function createBasePageCloseEvent(): PageCloseEvent {
  return { pageId: undefined, delay: undefined };
}

export const PageCloseEvent: MessageFns<PageCloseEvent> = {
  encode(message: PageCloseEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageId !== undefined) {
      writer.uint32(8).int32(message.pageId);
    }
    if (message.delay !== undefined) {
      writer.uint32(16).int32(message.delay);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PageCloseEvent {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePageCloseEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pageId = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.delay = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PageCloseEvent {
    return {
      pageId: isSet(object.pageId) ? globalThis.Number(object.pageId) : undefined,
      delay: isSet(object.delay) ? globalThis.Number(object.delay) : undefined,
    };
  },

  toJSON(message: PageCloseEvent): unknown {
    const obj: any = {};
    if (message.pageId !== undefined) {
      obj.pageId = Math.round(message.pageId);
    }
    if (message.delay !== undefined) {
      obj.delay = Math.round(message.delay);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PageCloseEvent>, I>>(base?: I): PageCloseEvent {
    return PageCloseEvent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageCloseEvent>, I>>(object: I): PageCloseEvent {
    const message = createBasePageCloseEvent();
    message.pageId = object.pageId ?? undefined;
    message.delay = object.delay ?? undefined;
    return message;
  },
};

function createBaseDataChangeEvent(): DataChangeEvent {
  return { data: undefined, changeType: undefined, tolerance: undefined, delay: undefined };
}

export const DataChangeEvent: MessageFns<DataChangeEvent> = {
  encode(message: DataChangeEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.data !== undefined) {
      VariableReference.encode(message.data, writer.uint32(10).fork()).join();
    }
    if (message.changeType !== undefined) {
      writer.uint32(16).int32(message.changeType);
    }
    if (message.tolerance !== undefined) {
      writer.uint32(29).float(message.tolerance);
    }
    if (message.delay !== undefined) {
      writer.uint32(32).int32(message.delay);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataChangeEvent {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataChangeEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.changeType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 29) {
            break;
          }

          message.tolerance = reader.float();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.delay = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataChangeEvent {
    return {
      data: isSet(object.data) ? VariableReference.fromJSON(object.data) : undefined,
      changeType: isSet(object.changeType) ? dataChangeTypeFromJSON(object.changeType) : undefined,
      tolerance: isSet(object.tolerance) ? globalThis.Number(object.tolerance) : undefined,
      delay: isSet(object.delay) ? globalThis.Number(object.delay) : undefined,
    };
  },

  toJSON(message: DataChangeEvent): unknown {
    const obj: any = {};
    if (message.data !== undefined) {
      obj.data = VariableReference.toJSON(message.data);
    }
    if (message.changeType !== undefined) {
      obj.changeType = dataChangeTypeToJSON(message.changeType);
    }
    if (message.tolerance !== undefined) {
      obj.tolerance = message.tolerance;
    }
    if (message.delay !== undefined) {
      obj.delay = Math.round(message.delay);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataChangeEvent>, I>>(base?: I): DataChangeEvent {
    return DataChangeEvent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataChangeEvent>, I>>(object: I): DataChangeEvent {
    const message = createBaseDataChangeEvent();
    message.data = (object.data !== undefined && object.data !== null)
      ? VariableReference.fromPartial(object.data)
      : undefined;
    message.changeType = object.changeType ?? undefined;
    message.tolerance = object.tolerance ?? undefined;
    message.delay = object.delay ?? undefined;
    return message;
  },
};

function createBaseProgram(): Program {
  return {
    id: 0,
    name: "",
    startEvent: undefined,
    exitEvent: undefined,
    pageOpenEvent: undefined,
    pageCloseEvent: undefined,
    timerEvent: undefined,
    dataChangeEvent: undefined,
    scripts: [],
    afterProgramIds: [],
    memo: undefined,
    isDisable: undefined,
    protectType: undefined,
    protectPasswd: undefined,
    isSubProgram: undefined,
    categoryId: undefined,
  };
}

export const Program: MessageFns<Program> = {
  encode(message: Program, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int32(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.startEvent !== undefined) {
      StartEvent.encode(message.startEvent, writer.uint32(26).fork()).join();
    }
    if (message.exitEvent !== undefined) {
      ExitEvent.encode(message.exitEvent, writer.uint32(34).fork()).join();
    }
    if (message.pageOpenEvent !== undefined) {
      PageOpenEvent.encode(message.pageOpenEvent, writer.uint32(42).fork()).join();
    }
    if (message.pageCloseEvent !== undefined) {
      PageCloseEvent.encode(message.pageCloseEvent, writer.uint32(50).fork()).join();
    }
    if (message.timerEvent !== undefined) {
      TimerEvent.encode(message.timerEvent, writer.uint32(58).fork()).join();
    }
    if (message.dataChangeEvent !== undefined) {
      DataChangeEvent.encode(message.dataChangeEvent, writer.uint32(66).fork()).join();
    }
    for (const v of message.scripts) {
      Script.encode(v!, writer.uint32(74).fork()).join();
    }
    writer.uint32(82).fork();
    for (const v of message.afterProgramIds) {
      writer.int32(v);
    }
    writer.join();
    if (message.memo !== undefined) {
      writer.uint32(90).string(message.memo);
    }
    if (message.isDisable !== undefined) {
      writer.uint32(96).bool(message.isDisable);
    }
    if (message.protectType !== undefined) {
      writer.uint32(104).int32(message.protectType);
    }
    if (message.protectPasswd !== undefined) {
      writer.uint32(114).string(message.protectPasswd);
    }
    if (message.isSubProgram !== undefined) {
      writer.uint32(120).bool(message.isSubProgram);
    }
    if (message.categoryId !== undefined) {
      writer.uint32(128).int32(message.categoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Program {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProgram();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.startEvent = StartEvent.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.exitEvent = ExitEvent.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.pageOpenEvent = PageOpenEvent.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.pageCloseEvent = PageCloseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.timerEvent = TimerEvent.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.dataChangeEvent = DataChangeEvent.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.scripts.push(Script.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag === 80) {
            message.afterProgramIds.push(reader.int32());

            continue;
          }

          if (tag === 82) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.afterProgramIds.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.isDisable = reader.bool();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.protectType = reader.int32() as any;
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.protectPasswd = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.isSubProgram = reader.bool();
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.categoryId = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Program {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      startEvent: isSet(object.startEvent) ? StartEvent.fromJSON(object.startEvent) : undefined,
      exitEvent: isSet(object.exitEvent) ? ExitEvent.fromJSON(object.exitEvent) : undefined,
      pageOpenEvent: isSet(object.pageOpenEvent) ? PageOpenEvent.fromJSON(object.pageOpenEvent) : undefined,
      pageCloseEvent: isSet(object.pageCloseEvent) ? PageCloseEvent.fromJSON(object.pageCloseEvent) : undefined,
      timerEvent: isSet(object.timerEvent) ? TimerEvent.fromJSON(object.timerEvent) : undefined,
      dataChangeEvent: isSet(object.dataChangeEvent) ? DataChangeEvent.fromJSON(object.dataChangeEvent) : undefined,
      scripts: globalThis.Array.isArray(object?.scripts) ? object.scripts.map((e: any) => Script.fromJSON(e)) : [],
      afterProgramIds: globalThis.Array.isArray(object?.afterProgramIds)
        ? object.afterProgramIds.map((e: any) => globalThis.Number(e))
        : [],
      memo: isSet(object.memo) ? globalThis.String(object.memo) : undefined,
      isDisable: isSet(object.isDisable) ? globalThis.Boolean(object.isDisable) : undefined,
      protectType: isSet(object.protectType) ? protectTypeFromJSON(object.protectType) : undefined,
      protectPasswd: isSet(object.protectPasswd) ? globalThis.String(object.protectPasswd) : undefined,
      isSubProgram: isSet(object.isSubProgram) ? globalThis.Boolean(object.isSubProgram) : undefined,
      categoryId: isSet(object.categoryId) ? globalThis.Number(object.categoryId) : undefined,
    };
  },

  toJSON(message: Program): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.startEvent !== undefined) {
      obj.startEvent = StartEvent.toJSON(message.startEvent);
    }
    if (message.exitEvent !== undefined) {
      obj.exitEvent = ExitEvent.toJSON(message.exitEvent);
    }
    if (message.pageOpenEvent !== undefined) {
      obj.pageOpenEvent = PageOpenEvent.toJSON(message.pageOpenEvent);
    }
    if (message.pageCloseEvent !== undefined) {
      obj.pageCloseEvent = PageCloseEvent.toJSON(message.pageCloseEvent);
    }
    if (message.timerEvent !== undefined) {
      obj.timerEvent = TimerEvent.toJSON(message.timerEvent);
    }
    if (message.dataChangeEvent !== undefined) {
      obj.dataChangeEvent = DataChangeEvent.toJSON(message.dataChangeEvent);
    }
    if (message.scripts?.length) {
      obj.scripts = message.scripts.map((e) => Script.toJSON(e));
    }
    if (message.afterProgramIds?.length) {
      obj.afterProgramIds = message.afterProgramIds.map((e) => Math.round(e));
    }
    if (message.memo !== undefined) {
      obj.memo = message.memo;
    }
    if (message.isDisable !== undefined) {
      obj.isDisable = message.isDisable;
    }
    if (message.protectType !== undefined) {
      obj.protectType = protectTypeToJSON(message.protectType);
    }
    if (message.protectPasswd !== undefined) {
      obj.protectPasswd = message.protectPasswd;
    }
    if (message.isSubProgram !== undefined) {
      obj.isSubProgram = message.isSubProgram;
    }
    if (message.categoryId !== undefined) {
      obj.categoryId = Math.round(message.categoryId);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Program>, I>>(base?: I): Program {
    return Program.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Program>, I>>(object: I): Program {
    const message = createBaseProgram();
    message.id = object.id ?? 0;
    message.name = object.name ?? "";
    message.startEvent = (object.startEvent !== undefined && object.startEvent !== null)
      ? StartEvent.fromPartial(object.startEvent)
      : undefined;
    message.exitEvent = (object.exitEvent !== undefined && object.exitEvent !== null)
      ? ExitEvent.fromPartial(object.exitEvent)
      : undefined;
    message.pageOpenEvent = (object.pageOpenEvent !== undefined && object.pageOpenEvent !== null)
      ? PageOpenEvent.fromPartial(object.pageOpenEvent)
      : undefined;
    message.pageCloseEvent = (object.pageCloseEvent !== undefined && object.pageCloseEvent !== null)
      ? PageCloseEvent.fromPartial(object.pageCloseEvent)
      : undefined;
    message.timerEvent = (object.timerEvent !== undefined && object.timerEvent !== null)
      ? TimerEvent.fromPartial(object.timerEvent)
      : undefined;
    message.dataChangeEvent = (object.dataChangeEvent !== undefined && object.dataChangeEvent !== null)
      ? DataChangeEvent.fromPartial(object.dataChangeEvent)
      : undefined;
    message.scripts = object.scripts?.map((e) => Script.fromPartial(e)) || [];
    message.afterProgramIds = object.afterProgramIds?.map((e) => e) || [];
    message.memo = object.memo ?? undefined;
    message.isDisable = object.isDisable ?? undefined;
    message.protectType = object.protectType ?? undefined;
    message.protectPasswd = object.protectPasswd ?? undefined;
    message.isSubProgram = object.isSubProgram ?? undefined;
    message.categoryId = object.categoryId ?? undefined;
    return message;
  },
};

function createBaseScript(): Script {
  return { enable: undefined, type: undefined, code: undefined };
}

export const Script: MessageFns<Script> = {
  encode(message: Script, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.enable !== undefined) {
      VariableReference.encode(message.enable, writer.uint32(10).fork()).join();
    }
    if (message.type !== undefined) {
      writer.uint32(16).int32(message.type);
    }
    if (message.code !== undefined) {
      writer.uint32(26).string(message.code);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Script {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseScript();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.enable = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.code = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Script {
    return {
      enable: isSet(object.enable) ? VariableReference.fromJSON(object.enable) : undefined,
      type: isSet(object.type) ? scriptTypeFromJSON(object.type) : undefined,
      code: isSet(object.code) ? globalThis.String(object.code) : undefined,
    };
  },

  toJSON(message: Script): unknown {
    const obj: any = {};
    if (message.enable !== undefined) {
      obj.enable = VariableReference.toJSON(message.enable);
    }
    if (message.type !== undefined) {
      obj.type = scriptTypeToJSON(message.type);
    }
    if (message.code !== undefined) {
      obj.code = message.code;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Script>, I>>(base?: I): Script {
    return Script.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Script>, I>>(object: I): Script {
    const message = createBaseScript();
    message.enable = (object.enable !== undefined && object.enable !== null)
      ? VariableReference.fromPartial(object.enable)
      : undefined;
    message.type = object.type ?? undefined;
    message.code = object.code ?? undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
