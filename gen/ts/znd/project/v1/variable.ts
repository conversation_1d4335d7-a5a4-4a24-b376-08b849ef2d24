// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/variable.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "znd.project.v1";

/** 数据格式 */
export enum DataFormatType {
  DATA_FORMAT_TYPE_UNSPECIFIED = 0,
  /** DATA_FORMAT_TYPE_BOOL - 布尔型 */
  DATA_FORMAT_TYPE_BOOL = 1,
  /** DATA_FORMAT_TYPE_U8 - 8位无符号整型 */
  DATA_FORMAT_TYPE_U8 = 2,
  /** DATA_FORMAT_TYPE_I8 - 8位有符号整型 */
  DATA_FORMAT_TYPE_I8 = 3,
  /** DATA_FORMAT_TYPE_U16 - 16位无符号整型 */
  DATA_FORMAT_TYPE_U16 = 4,
  /** DATA_FORMAT_TYPE_I16 - 16位有符号整型 */
  DATA_FORMAT_TYPE_I16 = 5,
  /** DATA_FORMAT_TYPE_U32 - 32位无符号整型 */
  DATA_FORMAT_TYPE_U32 = 6,
  /** DATA_FORMAT_TYPE_I32 - 32位有符号整型 */
  DATA_FORMAT_TYPE_I32 = 7,
  /** DATA_FORMAT_TYPE_FLOAT - 32位浮点型 */
  DATA_FORMAT_TYPE_FLOAT = 8,
  /** DATA_FORMAT_TYPE_BCD16 - BCD16 */
  DATA_FORMAT_TYPE_BCD16 = 11,
  /** DATA_FORMAT_TYPE_BCD32 - BCD32 */
  DATA_FORMAT_TYPE_BCD32 = 13,
  /** DATA_FORMAT_TYPE_HEX16 - 16位十六进制 */
  DATA_FORMAT_TYPE_HEX16 = 15,
  /** DATA_FORMAT_TYPE_HEX32 - 32位十六进制 */
  DATA_FORMAT_TYPE_HEX32 = 17,
  /** DATA_FORMAT_TYPE_BIN16 - 16位二进制 */
  DATA_FORMAT_TYPE_BIN16 = 18,
  /** DATA_FORMAT_TYPE_BIN32 - 32位二进制 */
  DATA_FORMAT_TYPE_BIN32 = 21,
  /** DATA_FORMAT_TYPE_U64 - 64位无符号整型 */
  DATA_FORMAT_TYPE_U64 = 22,
  /** DATA_FORMAT_TYPE_I64 - 64位有符号整型 */
  DATA_FORMAT_TYPE_I64 = 23,
  /** DATA_FORMAT_TYPE_DOUBLE - 64位浮点型 */
  DATA_FORMAT_TYPE_DOUBLE = 25,
  /** DATA_FORMAT_TYPE_DT64 - 日期时间，占8字节，64位(年2个字节，月1个字节，日1个字节，时1个字节，分1个字节，秒1个字节，10毫秒1个字节) */
  DATA_FORMAT_TYPE_DT64 = 26,
  /** DATA_FORMAT_TYPE_DT32 - 短日期时间，不含年份且分钟级别，占4字节，32位(月1个字节，日1个字节，时1个字节，分1个字节) */
  DATA_FORMAT_TYPE_DT32 = 27,
  /** DATA_FORMAT_TYPE_D32 - 日期，占4字节，UINT32位(年2个字节，月1个字节，日1个字节各1个字节的U8) */
  DATA_FORMAT_TYPE_D32 = 28,
  /** DATA_FORMAT_TYPE_T32 - 时间，占4字节，UINT32位（时、分、秒各1个字节的U8） */
  DATA_FORMAT_TYPE_T32 = 29,
  /** DATA_FORMAT_TYPE_TS32 - 秒时间戳，32位无符号整型(1970-01-01 00:00:00 到现在的秒数) */
  DATA_FORMAT_TYPE_TS32 = 30,
  /** DATA_FORMAT_TYPE_TS64 - 毫秒时间戳，64位无符号整型(1970-01-01 00:00:00 到现在的毫秒数) */
  DATA_FORMAT_TYPE_TS64 = 31,
  /** DATA_FORMAT_TYPE_UTF8 - 字符串，UTF8 */
  DATA_FORMAT_TYPE_UTF8 = 32,
  /** DATA_FORMAT_TYPE_UNICODE - 字符串，Unicode */
  DATA_FORMAT_TYPE_UNICODE = 33,
  /** DATA_FORMAT_TYPE_GBK - 字符串，GBK */
  DATA_FORMAT_TYPE_GBK = 34,
  /** DATA_FORMAT_TYPE_UTF8_LH - 字符串，UTF8高低互换 */
  DATA_FORMAT_TYPE_UTF8_LH = 35,
  /** DATA_FORMAT_TYPE_UNICODE_LH - 字符串，Unicode高低互换 */
  DATA_FORMAT_TYPE_UNICODE_LH = 36,
  /** DATA_FORMAT_TYPE_GBK_LH - 字符串，GBK高低互换 */
  DATA_FORMAT_TYPE_GBK_LH = 37,
  /** DATA_FORMAT_TYPE_IP - IP地址(4字节) */
  DATA_FORMAT_TYPE_IP = 38,
  /** DATA_FORMAT_TYPE_STRUCT - 结构体 */
  DATA_FORMAT_TYPE_STRUCT = 39,
  UNRECOGNIZED = -1,
}

export function dataFormatTypeFromJSON(object: any): DataFormatType {
  switch (object) {
    case 0:
    case "DATA_FORMAT_TYPE_UNSPECIFIED":
      return DataFormatType.DATA_FORMAT_TYPE_UNSPECIFIED;
    case 1:
    case "DATA_FORMAT_TYPE_BOOL":
      return DataFormatType.DATA_FORMAT_TYPE_BOOL;
    case 2:
    case "DATA_FORMAT_TYPE_U8":
      return DataFormatType.DATA_FORMAT_TYPE_U8;
    case 3:
    case "DATA_FORMAT_TYPE_I8":
      return DataFormatType.DATA_FORMAT_TYPE_I8;
    case 4:
    case "DATA_FORMAT_TYPE_U16":
      return DataFormatType.DATA_FORMAT_TYPE_U16;
    case 5:
    case "DATA_FORMAT_TYPE_I16":
      return DataFormatType.DATA_FORMAT_TYPE_I16;
    case 6:
    case "DATA_FORMAT_TYPE_U32":
      return DataFormatType.DATA_FORMAT_TYPE_U32;
    case 7:
    case "DATA_FORMAT_TYPE_I32":
      return DataFormatType.DATA_FORMAT_TYPE_I32;
    case 8:
    case "DATA_FORMAT_TYPE_FLOAT":
      return DataFormatType.DATA_FORMAT_TYPE_FLOAT;
    case 11:
    case "DATA_FORMAT_TYPE_BCD16":
      return DataFormatType.DATA_FORMAT_TYPE_BCD16;
    case 13:
    case "DATA_FORMAT_TYPE_BCD32":
      return DataFormatType.DATA_FORMAT_TYPE_BCD32;
    case 15:
    case "DATA_FORMAT_TYPE_HEX16":
      return DataFormatType.DATA_FORMAT_TYPE_HEX16;
    case 17:
    case "DATA_FORMAT_TYPE_HEX32":
      return DataFormatType.DATA_FORMAT_TYPE_HEX32;
    case 18:
    case "DATA_FORMAT_TYPE_BIN16":
      return DataFormatType.DATA_FORMAT_TYPE_BIN16;
    case 21:
    case "DATA_FORMAT_TYPE_BIN32":
      return DataFormatType.DATA_FORMAT_TYPE_BIN32;
    case 22:
    case "DATA_FORMAT_TYPE_U64":
      return DataFormatType.DATA_FORMAT_TYPE_U64;
    case 23:
    case "DATA_FORMAT_TYPE_I64":
      return DataFormatType.DATA_FORMAT_TYPE_I64;
    case 25:
    case "DATA_FORMAT_TYPE_DOUBLE":
      return DataFormatType.DATA_FORMAT_TYPE_DOUBLE;
    case 26:
    case "DATA_FORMAT_TYPE_DT64":
      return DataFormatType.DATA_FORMAT_TYPE_DT64;
    case 27:
    case "DATA_FORMAT_TYPE_DT32":
      return DataFormatType.DATA_FORMAT_TYPE_DT32;
    case 28:
    case "DATA_FORMAT_TYPE_D32":
      return DataFormatType.DATA_FORMAT_TYPE_D32;
    case 29:
    case "DATA_FORMAT_TYPE_T32":
      return DataFormatType.DATA_FORMAT_TYPE_T32;
    case 30:
    case "DATA_FORMAT_TYPE_TS32":
      return DataFormatType.DATA_FORMAT_TYPE_TS32;
    case 31:
    case "DATA_FORMAT_TYPE_TS64":
      return DataFormatType.DATA_FORMAT_TYPE_TS64;
    case 32:
    case "DATA_FORMAT_TYPE_UTF8":
      return DataFormatType.DATA_FORMAT_TYPE_UTF8;
    case 33:
    case "DATA_FORMAT_TYPE_UNICODE":
      return DataFormatType.DATA_FORMAT_TYPE_UNICODE;
    case 34:
    case "DATA_FORMAT_TYPE_GBK":
      return DataFormatType.DATA_FORMAT_TYPE_GBK;
    case 35:
    case "DATA_FORMAT_TYPE_UTF8_LH":
      return DataFormatType.DATA_FORMAT_TYPE_UTF8_LH;
    case 36:
    case "DATA_FORMAT_TYPE_UNICODE_LH":
      return DataFormatType.DATA_FORMAT_TYPE_UNICODE_LH;
    case 37:
    case "DATA_FORMAT_TYPE_GBK_LH":
      return DataFormatType.DATA_FORMAT_TYPE_GBK_LH;
    case 38:
    case "DATA_FORMAT_TYPE_IP":
      return DataFormatType.DATA_FORMAT_TYPE_IP;
    case 39:
    case "DATA_FORMAT_TYPE_STRUCT":
      return DataFormatType.DATA_FORMAT_TYPE_STRUCT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DataFormatType.UNRECOGNIZED;
  }
}

export function dataFormatTypeToJSON(object: DataFormatType): string {
  switch (object) {
    case DataFormatType.DATA_FORMAT_TYPE_UNSPECIFIED:
      return "DATA_FORMAT_TYPE_UNSPECIFIED";
    case DataFormatType.DATA_FORMAT_TYPE_BOOL:
      return "DATA_FORMAT_TYPE_BOOL";
    case DataFormatType.DATA_FORMAT_TYPE_U8:
      return "DATA_FORMAT_TYPE_U8";
    case DataFormatType.DATA_FORMAT_TYPE_I8:
      return "DATA_FORMAT_TYPE_I8";
    case DataFormatType.DATA_FORMAT_TYPE_U16:
      return "DATA_FORMAT_TYPE_U16";
    case DataFormatType.DATA_FORMAT_TYPE_I16:
      return "DATA_FORMAT_TYPE_I16";
    case DataFormatType.DATA_FORMAT_TYPE_U32:
      return "DATA_FORMAT_TYPE_U32";
    case DataFormatType.DATA_FORMAT_TYPE_I32:
      return "DATA_FORMAT_TYPE_I32";
    case DataFormatType.DATA_FORMAT_TYPE_FLOAT:
      return "DATA_FORMAT_TYPE_FLOAT";
    case DataFormatType.DATA_FORMAT_TYPE_BCD16:
      return "DATA_FORMAT_TYPE_BCD16";
    case DataFormatType.DATA_FORMAT_TYPE_BCD32:
      return "DATA_FORMAT_TYPE_BCD32";
    case DataFormatType.DATA_FORMAT_TYPE_HEX16:
      return "DATA_FORMAT_TYPE_HEX16";
    case DataFormatType.DATA_FORMAT_TYPE_HEX32:
      return "DATA_FORMAT_TYPE_HEX32";
    case DataFormatType.DATA_FORMAT_TYPE_BIN16:
      return "DATA_FORMAT_TYPE_BIN16";
    case DataFormatType.DATA_FORMAT_TYPE_BIN32:
      return "DATA_FORMAT_TYPE_BIN32";
    case DataFormatType.DATA_FORMAT_TYPE_U64:
      return "DATA_FORMAT_TYPE_U64";
    case DataFormatType.DATA_FORMAT_TYPE_I64:
      return "DATA_FORMAT_TYPE_I64";
    case DataFormatType.DATA_FORMAT_TYPE_DOUBLE:
      return "DATA_FORMAT_TYPE_DOUBLE";
    case DataFormatType.DATA_FORMAT_TYPE_DT64:
      return "DATA_FORMAT_TYPE_DT64";
    case DataFormatType.DATA_FORMAT_TYPE_DT32:
      return "DATA_FORMAT_TYPE_DT32";
    case DataFormatType.DATA_FORMAT_TYPE_D32:
      return "DATA_FORMAT_TYPE_D32";
    case DataFormatType.DATA_FORMAT_TYPE_T32:
      return "DATA_FORMAT_TYPE_T32";
    case DataFormatType.DATA_FORMAT_TYPE_TS32:
      return "DATA_FORMAT_TYPE_TS32";
    case DataFormatType.DATA_FORMAT_TYPE_TS64:
      return "DATA_FORMAT_TYPE_TS64";
    case DataFormatType.DATA_FORMAT_TYPE_UTF8:
      return "DATA_FORMAT_TYPE_UTF8";
    case DataFormatType.DATA_FORMAT_TYPE_UNICODE:
      return "DATA_FORMAT_TYPE_UNICODE";
    case DataFormatType.DATA_FORMAT_TYPE_GBK:
      return "DATA_FORMAT_TYPE_GBK";
    case DataFormatType.DATA_FORMAT_TYPE_UTF8_LH:
      return "DATA_FORMAT_TYPE_UTF8_LH";
    case DataFormatType.DATA_FORMAT_TYPE_UNICODE_LH:
      return "DATA_FORMAT_TYPE_UNICODE_LH";
    case DataFormatType.DATA_FORMAT_TYPE_GBK_LH:
      return "DATA_FORMAT_TYPE_GBK_LH";
    case DataFormatType.DATA_FORMAT_TYPE_IP:
      return "DATA_FORMAT_TYPE_IP";
    case DataFormatType.DATA_FORMAT_TYPE_STRUCT:
      return "DATA_FORMAT_TYPE_STRUCT";
    case DataFormatType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 结构体类型枚举 */
export enum StructTypeMode {
  /** STRUCT_TYPE_MODE_UNSPECIFIED - 未设置(默认跟1相同，也是连续地址字，因为这个用的最多，节省空间) */
  STRUCT_TYPE_MODE_UNSPECIFIED = 0,
  /** STRUCT_TYPE_MODE_WORD - 连续地址字（这个跟未设置一样，目前只支持这个，保留这个的原因是方便理解） */
  STRUCT_TYPE_MODE_WORD = 1,
  /** STRUCT_TYPE_MODE_BOOL - 连续地址布尔（暂时不支持） */
  STRUCT_TYPE_MODE_BOOL = 2,
  /** STRUCT_TYPE_MODE_COMPOSITE - 不连续地址的复合类型（暂时不支持） */
  STRUCT_TYPE_MODE_COMPOSITE = 3,
  UNRECOGNIZED = -1,
}

export function structTypeModeFromJSON(object: any): StructTypeMode {
  switch (object) {
    case 0:
    case "STRUCT_TYPE_MODE_UNSPECIFIED":
      return StructTypeMode.STRUCT_TYPE_MODE_UNSPECIFIED;
    case 1:
    case "STRUCT_TYPE_MODE_WORD":
      return StructTypeMode.STRUCT_TYPE_MODE_WORD;
    case 2:
    case "STRUCT_TYPE_MODE_BOOL":
      return StructTypeMode.STRUCT_TYPE_MODE_BOOL;
    case 3:
    case "STRUCT_TYPE_MODE_COMPOSITE":
      return StructTypeMode.STRUCT_TYPE_MODE_COMPOSITE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return StructTypeMode.UNRECOGNIZED;
  }
}

export function structTypeModeToJSON(object: StructTypeMode): string {
  switch (object) {
    case StructTypeMode.STRUCT_TYPE_MODE_UNSPECIFIED:
      return "STRUCT_TYPE_MODE_UNSPECIFIED";
    case StructTypeMode.STRUCT_TYPE_MODE_WORD:
      return "STRUCT_TYPE_MODE_WORD";
    case StructTypeMode.STRUCT_TYPE_MODE_BOOL:
      return "STRUCT_TYPE_MODE_BOOL";
    case StructTypeMode.STRUCT_TYPE_MODE_COMPOSITE:
      return "STRUCT_TYPE_MODE_COMPOSITE";
    case StructTypeMode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 系统内部寄存器类型 */
export enum RegisterTypeLocalHmi {
  REGISTER_TYPE_LOCAL_HMI_UNSPECIFIED = 0,
  /** REGISTER_TYPE_LOCAL_HMI_STA - 状态寄存器 */
  REGISTER_TYPE_LOCAL_HMI_STA = 1,
  /** REGISTER_TYPE_LOCAL_HMI_IDX - 索引寄存器 */
  REGISTER_TYPE_LOCAL_HMI_IDX = 2,
  /** REGISTER_TYPE_LOCAL_HMI_LB_USR - 用户使用位寄存器 */
  REGISTER_TYPE_LOCAL_HMI_LB_USR = 3,
  /** REGISTER_TYPE_LOCAL_HMI_LW_USR - 用户使用字寄存器 */
  REGISTER_TYPE_LOCAL_HMI_LW_USR = 4,
  /** REGISTER_TYPE_LOCAL_HMI_LB_HLD - 用户使用保持位寄存器 */
  REGISTER_TYPE_LOCAL_HMI_LB_HLD = 5,
  /** REGISTER_TYPE_LOCAL_HMI_LW_HLD - 用户使用保持字寄存器 */
  REGISTER_TYPE_LOCAL_HMI_LW_HLD = 6,
  /** REGISTER_TYPE_LOCAL_HMI_LB_SYS - 系统使用位寄存器 */
  REGISTER_TYPE_LOCAL_HMI_LB_SYS = 7,
  /** REGISTER_TYPE_LOCAL_HMI_LW_SYS - 系统使用字寄存器 */
  REGISTER_TYPE_LOCAL_HMI_LW_SYS = 8,
  UNRECOGNIZED = -1,
}

export function registerTypeLocalHmiFromJSON(object: any): RegisterTypeLocalHmi {
  switch (object) {
    case 0:
    case "REGISTER_TYPE_LOCAL_HMI_UNSPECIFIED":
      return RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_UNSPECIFIED;
    case 1:
    case "REGISTER_TYPE_LOCAL_HMI_STA":
      return RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_STA;
    case 2:
    case "REGISTER_TYPE_LOCAL_HMI_IDX":
      return RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_IDX;
    case 3:
    case "REGISTER_TYPE_LOCAL_HMI_LB_USR":
      return RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_LB_USR;
    case 4:
    case "REGISTER_TYPE_LOCAL_HMI_LW_USR":
      return RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_LW_USR;
    case 5:
    case "REGISTER_TYPE_LOCAL_HMI_LB_HLD":
      return RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_LB_HLD;
    case 6:
    case "REGISTER_TYPE_LOCAL_HMI_LW_HLD":
      return RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_LW_HLD;
    case 7:
    case "REGISTER_TYPE_LOCAL_HMI_LB_SYS":
      return RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_LB_SYS;
    case 8:
    case "REGISTER_TYPE_LOCAL_HMI_LW_SYS":
      return RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_LW_SYS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RegisterTypeLocalHmi.UNRECOGNIZED;
  }
}

export function registerTypeLocalHmiToJSON(object: RegisterTypeLocalHmi): string {
  switch (object) {
    case RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_UNSPECIFIED:
      return "REGISTER_TYPE_LOCAL_HMI_UNSPECIFIED";
    case RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_STA:
      return "REGISTER_TYPE_LOCAL_HMI_STA";
    case RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_IDX:
      return "REGISTER_TYPE_LOCAL_HMI_IDX";
    case RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_LB_USR:
      return "REGISTER_TYPE_LOCAL_HMI_LB_USR";
    case RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_LW_USR:
      return "REGISTER_TYPE_LOCAL_HMI_LW_USR";
    case RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_LB_HLD:
      return "REGISTER_TYPE_LOCAL_HMI_LB_HLD";
    case RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_LW_HLD:
      return "REGISTER_TYPE_LOCAL_HMI_LW_HLD";
    case RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_LB_SYS:
      return "REGISTER_TYPE_LOCAL_HMI_LB_SYS";
    case RegisterTypeLocalHmi.REGISTER_TYPE_LOCAL_HMI_LW_SYS:
      return "REGISTER_TYPE_LOCAL_HMI_LW_SYS";
    case RegisterTypeLocalHmi.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** MODBUS寄存器类型 */
export enum RegisterTypeModbus {
  REGISTER_TYPE_MODBUS_UNSPECIFIED = 0,
  REGISTER_TYPE_MODBUS_0X = 1,
  REGISTER_TYPE_MODBUS_1X = 2,
  REGISTER_TYPE_MODBUS_3X = 3,
  REGISTER_TYPE_MODBUS_4X = 4,
  UNRECOGNIZED = -1,
}

export function registerTypeModbusFromJSON(object: any): RegisterTypeModbus {
  switch (object) {
    case 0:
    case "REGISTER_TYPE_MODBUS_UNSPECIFIED":
      return RegisterTypeModbus.REGISTER_TYPE_MODBUS_UNSPECIFIED;
    case 1:
    case "REGISTER_TYPE_MODBUS_0X":
      return RegisterTypeModbus.REGISTER_TYPE_MODBUS_0X;
    case 2:
    case "REGISTER_TYPE_MODBUS_1X":
      return RegisterTypeModbus.REGISTER_TYPE_MODBUS_1X;
    case 3:
    case "REGISTER_TYPE_MODBUS_3X":
      return RegisterTypeModbus.REGISTER_TYPE_MODBUS_3X;
    case 4:
    case "REGISTER_TYPE_MODBUS_4X":
      return RegisterTypeModbus.REGISTER_TYPE_MODBUS_4X;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RegisterTypeModbus.UNRECOGNIZED;
  }
}

export function registerTypeModbusToJSON(object: RegisterTypeModbus): string {
  switch (object) {
    case RegisterTypeModbus.REGISTER_TYPE_MODBUS_UNSPECIFIED:
      return "REGISTER_TYPE_MODBUS_UNSPECIFIED";
    case RegisterTypeModbus.REGISTER_TYPE_MODBUS_0X:
      return "REGISTER_TYPE_MODBUS_0X";
    case RegisterTypeModbus.REGISTER_TYPE_MODBUS_1X:
      return "REGISTER_TYPE_MODBUS_1X";
    case RegisterTypeModbus.REGISTER_TYPE_MODBUS_3X:
      return "REGISTER_TYPE_MODBUS_3X";
    case RegisterTypeModbus.REGISTER_TYPE_MODBUS_4X:
      return "REGISTER_TYPE_MODBUS_4X";
    case RegisterTypeModbus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 结构体数据项 */
export interface StructTypeField {
  /** 字段ID，不用数组下标的原因，是考虑会被调整字段顺序，以及方便检查是否被引用 */
  fieldIdU8: number;
  /** 字段名称 */
  name: string;
  /** 数据格式 */
  dataFormat: DataFormatType;
  /** 从结构体开始算的起始地址，方便快速计算偏移 */
  startU16: number;
  /** 地址长度 */
  lengthU8: number;
  /** 数组个数（0表示不是数组） */
  arrayCountU8: number;
  /** 位索引 */
  bitIndex?:
    | number
    | undefined;
  /** 字节索引 */
  byteIndex?:
    | number
    | undefined;
  /** 备注 */
  memo: string;
}

/** 结构体数据 */
export interface StructType {
  /** 结构体名称 */
  name: string;
  /** 字段列表 */
  fields: StructTypeField[];
  /** 结构体总长度 */
  lengthU16: number;
  /** 结构体模式，默认连续字地址(其他暂不支持) */
  mode?:
    | StructTypeMode
    | undefined;
  /** 备注 */
  memo?: string | undefined;
}

/** 变量标签库及初始值(根据设备id分开存放) */
export interface VariableLibrary {
  /** 按tag_id存储,key是u16 */
  variables: { [key: number]: Variable };
}

export interface VariableLibrary_VariablesEntry {
  key: number;
  value: Variable | undefined;
}

/** 变量引用 */
export interface VariableReference {
  from?:
    | //
    /**
     * //索引寄存器地址,范围1-255，0保留不用（没引用表示空值）
     * uint32 index_register_u8 = 1;
     * //状态寄存器地址
     * uint32 status_register_u8 = 2;
     * 变量引用
     */
    { $case: "varDefined"; value: VariableReference_UserDefined }
    | //
    /** 手动输入 */
    { $case: "varInput"; value: Variable }
    | undefined;
  /** 地址动态偏移(优先偏移位地址、再主地址) */
  addressDynamicOffset?:
    | VariableReference
    | undefined;
  /** 子设备下标(仅针对动态设备有效，动态设备根据这个下标来引用不同的设备) */
  subDeviceIndex?:
    | DataReferenceUInt8
    | undefined;
  /** 系统变量的索引号(仅针对部分系统变量，如设备通讯参数用来指定设备号等) */
  localHmiIndex?:
    | DataReferenceUInt8
    | undefined;
  /** 地址扩展参数(为不同PLC预留，方便扩展，用map，key类型用u8，不同协议不同枚举) */
  extendParams: { [key: number]: DataReferenceInt16 };
  /** 把变量得到的值，再进行加上这个固定值 */
  valueAddI16: number;
}

/** 引用用户定义变量的结构 */
export interface VariableReference_UserDefined {
  /** 设备ID(由设计端保证不超过255) */
  deviceIdU8: number;
  /** 地址标签ID（根据设备ID，从地址标签库中取） */
  varIdU16: number;
  /** 地址固定偏移(优先偏移位地址、再主地址)，有单独固定地址偏移的原因，是为了让引用变量后再偏移，省去创建变量的工作 */
  addressFixedOffsetI16: number;
  /** 变量下标(依次展开可能是结构体字段id也可能是数组下标) */
  varSubscripts: DataReferenceUInt8[];
  /** 变量下标偏移（得到上面的值之后 再偏移得到真正的下标） */
  varSubscriptOffsetI16: number;
}

export interface VariableReference_ExtendParamsEntry {
  key: number;
  value: DataReferenceInt16 | undefined;
}

/** 数据引用 */
export interface DataReference {
  from?:
    | //
    /** 常量 */
    { $case: "constant"; value: AllTypeValue }
    | //
    /** 变量 */
    { $case: "variable"; value: VariableReference }
    | undefined;
}

/** 数值型数据引用 */
export interface DataReferenceNumber {
  from?:
    | //
    /** 固定值 */
    { $case: "constantNumber"; value: NumberValue }
    | //
    /** 变量引用 */
    { $case: "variable"; value: VariableReference }
    | undefined;
}

/** 8位整型引用 */
export interface DataReferenceUInt8 {
  from?:
    | //
    /** 固定值 */
    { $case: "constantU8"; value: number }
    | //
    /** 变量引用 */
    { $case: "variable"; value: VariableReference }
    | undefined;
}

/** 8位整型引用 */
export interface DataReferenceInt8 {
  from?:
    | //
    /** 固定值 */
    { $case: "constantI8"; value: number }
    | //
    /** 变量引用 */
    { $case: "variable"; value: VariableReference }
    | undefined;
}

/** 16位整型引用 */
export interface DataReferenceInt16 {
  from?:
    | //
    /** 固定值 */
    { $case: "constantI16"; value: number }
    | //
    /** 变量引用 */
    { $case: "variable"; value: VariableReference }
    | undefined;
}

/** uint16位整型引用 */
export interface DataReferenceUInt16 {
  from?:
    | //
    /** 固定值 */
    { $case: "constantU16"; value: number }
    | //
    /** 变量引用 */
    { $case: "variable"; value: VariableReference }
    | undefined;
}

/** 32位整型引用 */
export interface DataReferenceInt32 {
  from?:
    | //
    /** 固定值 */
    { $case: "constantI32"; value: number }
    | //
    /** 变量引用 */
    { $case: "variable"; value: VariableReference }
    | undefined;
}

/** uint32位整型引用 */
export interface DataReferenceUInt32 {
  from?:
    | //
    /** 固定值 */
    { $case: "constantU32"; value: number }
    | //
    /** 变量引用 */
    { $case: "variable"; value: VariableReference }
    | undefined;
}

/** 64位有符号整型引用 */
export interface DataReferenceInt64 {
  from?:
    | //
    /** 固定值 */
    { $case: "constantI64"; value: number }
    | //
    /** 变量引用 */
    { $case: "variable"; value: VariableReference }
    | undefined;
}

/** 64位无符号整型引用 */
export interface DataReferenceUInt64 {
  from?:
    | //
    /** 固定值 */
    { $case: "constantU64"; value: number }
    | //
    /** 变量引用 */
    { $case: "variable"; value: VariableReference }
    | undefined;
}

/** 32位浮点型引用 */
export interface DataReferenceFloat {
  from?:
    | //
    /** 固定值 */
    { $case: "constantFloat"; value: number }
    | //
    /** 变量引用 */
    { $case: "variable"; value: VariableReference }
    | undefined;
}

/** 64位浮点型引用 */
export interface DataReferenceDouble {
  from?:
    | //
    /** 固定值 */
    { $case: "constantDouble"; value: number }
    | //
    /** 变量引用 */
    { $case: "variable"; value: VariableReference }
    | undefined;
}

/** bool型引用 */
export interface DataReferenceBool {
  from?:
    | //
    /** 固定值 */
    { $case: "constantBool"; value: boolean }
    | //
    /** 变量引用 */
    { $case: "variable"; value: VariableReference }
    | undefined;
}

/** 变量定义 */
export interface Variable {
  /** 名称 */
  name: string;
  /** 标签备注 */
  memo: string;
  /** 所属分类,支持多个(设计端用) */
  varClassifyIdsU16: number[];
  /** 所属设备（设计端用，是用户归类用的，设计端会约束所有定义中的变量都来自该设备，主要用于在同一个设备中用动态变量偏移地址时使用） */
  varDeviceIdU8: number;
  /** 定义 */
  definition?:
    | //
    /** 寄存器地址 */
    { $case: "registerAddress"; value: RegisterAddress }
    | //
    /** 动态变量 */
    { $case: "dynamicVariable"; value: DynamicVariable }
    | //
    /** 表达式 */
    { $case: "expression"; value: Expression }
    | undefined;
  /**
   * 最终输出的目标数据类型(不管做不做转换，都一定有值，不做转换，就是寄存器数据格式，做转换就是转换后的数据格式)
   * 基本类型规定枚举值是在100以下，100及以上表示结构体，结构体ID一定是从100开始，允许的结构体类型不超过150个，足够用了
   */
  dataFormat: DataFormatType;
}

/** 寄存器地址 */
export interface RegisterAddress {
  /**
   * (不用了，直接用地址拼出对应KEY)对应的系统预置变量（用户选择了系统变量，保存时会转化地址，0表示没有，设计端使用）
   * uint32 preset_variable_id_u16 = 1;
   * 是否来自预置地址标签（如果是，则名称从预置地址标签库中取）
   */
  isPresetAddressTag: boolean;
  /** 显示寄存器类型 */
  registerDisplayType: string;
  /** 显示PLC地址 */
  registerDisplayAddressU32: number;
  /** 设备ID */
  deviceIdU8: number;
  /** 真实寄存器类型(不同协议不一样的定义, 最长255，用u8) */
  registerTypeU8: number;
  /** 真实PLC地址（对于有些地方浮点型地址的，拆分小数部分到bit_address，目的是为了节省最常用于整型地址占用空间） */
  registerAddressU32: number;
  /** 寄存器单地址位长度，0或者16都表示为16，也有8和32（16最常见，用0节省存储空间） */
  registerBitLengthU8: number;
  /** 取位u8(西门子Ix.y中的y，也用这个) */
  bitIndexU8?:
    | number
    | undefined;
  /** 字节索引（8位整型用） */
  byteIndexU8?:
    | number
    | undefined;
  /**
   * （暂时不用，由引用变量时处理）地址动态偏移(优先偏移位地址、再主地址)
   * optional VariableReference address_dynamic_offset = 10;
   * 位是否反转，默认不反转(仅位类型使用，含字取位的反转)
   */
  bitReverse: boolean;
  /** 地址长度(如果没值则为1) */
  addressLengthU16: number;
  /**
   * 把这个字段注释掉的原因，是不管在创建变量时，还是在引用输入地址，都不会去选择结构体字段，只有在引用结构体变量才会选择
   * 结构体字段ID（仅在图元引用地址时才用到，不用数组下标的原因是因为结构体字段会更改）
   * uint32 struct_field_id_u8 = 16;
   * 数组第1维长度（零表示不是数组，大于0表示定长数组，变长数组暂不实现，如果后续要实现，另外加个字段标记，这个字段就可以表示最长数组）
   */
  array1dLengthU8: number;
  /** 数组第1维起始下标 */
  array1dStartIndexU8: number;
  /** 数组第2维长度（如果为0，则表示不是数组） */
  array2dLengthU8: number;
  /** 数组第2维起始下标 */
  array2dStartIndexU8: number;
  /** 数据换算 */
  dataTransforms: DataTransform[];
  /** 转换前的寄存器的原始寄存器数据格式(只在有数据转换时才有值，如果没有数据转换，这个值是0) */
  registerDataFormat: DataFormatType;
}

/** 动态变量 */
export interface DynamicVariable {
  /** 变量列表的下标(可以只支持索引寄存器，以后再考虑支持其他变量) */
  subVariableIndex:
    | VariableReference
    | undefined;
  /** 变量列表 */
  subVariables: VariableReference[];
  /** 起始下标 */
  subVariableIndexOffsetI16: number;
}

/** 表达式 */
export interface Expression {
  /** 引用地址的map，key由编辑器生成，运行端直接使用，如果有写入功能，则要写入的值固定用v，这里不会嵌套引用表达式地址 */
  refVariables: { [key: string]: VariableReference };
  /** 读取表达式(如果有地址引用，直接使用引用变量的key) */
  readExpression: string;
  /**
   * key是ref_variables中的key，是针对指定地址的写入表达式
   * 写入时机，是指针对该tag写入时，将写入值赋给map变量v，然后遍历write_expressions计算，依次写入key对应的地址
   */
  writeExpressions: { [key: string]: string };
}

export interface Expression_RefVariablesEntry {
  key: string;
  value: VariableReference | undefined;
}

export interface Expression_WriteExpressionsEntry {
  key: string;
  value: string;
}

/** 计算表达式，用于只读计算使用 */
export interface CalculateExpression {
  /** 读取引用地址的map，key由编辑器生成，运行端直接使用 */
  refVariables: { [key: string]: VariableReference };
  /** 读取表达式(如果有地址引用，直接使用引用变量的key) */
  expression: string;
}

export interface CalculateExpression_RefVariablesEntry {
  key: string;
  value: VariableReference | undefined;
}

/** 数据换算(如果每种都有，按以下顺序转换) */
export interface DataTransform {
  /** 如果为空，则检查下转换后数据格式是否为0，如果为0，则表示不转换，不为0则转换类型 */
  transform?:
    | //
    /** 偏移值，整型或浮点型，用原值加偏移值后输出 */
    { $case: "offset"; value: DataReferenceNumber }
    | //
    /** 系数，整型或浮点型，用原值*系数后输出 */
    { $case: "factor"; value: DataReferenceNumber }
    | //
    /** 线性转换 */
    { $case: "linear"; value: LinearTransform }
    | //
    /** 表达式转换 */
    { $case: "expression"; value: Expression }
    | //
    /** 差值转换 */
    { $case: "diff"; value: DiffTransform }
    | undefined;
  /** 转换后数据格式（如果为0，则表示不转换） */
  dataFormat: DataFormatType;
}

/** 差值换算(主要用于产能提取，当前输入值减去该变量上一次存起来的值，做为新值) */
export interface DiffTransform {
  /** 两次值的计算时间间隔（单位100毫秒），如果是0，表示跟上次的值对比，如果是数据采样需要的，则跟上次采样对比 */
  timeIntervalU16: number;
  /**
   * 是否判断范围
   * 要判断时，如果输入值更小，则新值=（输入值-最小值）+（最大值-上一次值）
   */
  isRange: boolean;
  /** 最小值 */
  minValue?:
    | DataReferenceNumber
    | undefined;
  /** 最大值 */
  maxValue?: DataReferenceNumber | undefined;
}

/** 数据线性转换 */
export interface LinearTransform {
  /** 原值最小值 */
  orgValueMin:
    | DataReferenceNumber
    | undefined;
  /** 原值最大值 */
  orgValueMax:
    | DataReferenceNumber
    | undefined;
  /** 目标值最小值 */
  targetValueMin:
    | DataReferenceNumber
    | undefined;
  /** 目标值最大值 */
  targetValueMax: DataReferenceNumber | undefined;
}

export interface NumberValue {
  /** 值 */
  from?:
    | { $case: "valueI8"; value: number }
    | { $case: "valueU8"; value: number }
    | { $case: "valueI16"; value: number }
    | { $case: "valueU16"; value: number }
    | { $case: "valueI32"; value: number }
    | { $case: "valueU32"; value: number }
    | { $case: "valueI64"; value: number }
    | { $case: "valueU64"; value: number }
    | { $case: "valueFloat"; value: number }
    | { $case: "valueDouble"; value: number }
    | undefined;
}

export interface AllTypeValue {
  from?:
    | { $case: "valueI8"; value: number }
    | { $case: "valueU8"; value: number }
    | { $case: "valueI16"; value: number }
    | { $case: "valueU16"; value: number }
    | { $case: "valueI32"; value: number }
    | { $case: "valueU32"; value: number }
    | { $case: "valueI64"; value: number }
    | { $case: "valueU64"; value: number }
    | { $case: "valueFloat"; value: number }
    | { $case: "valueDouble"; value: number }
    | { $case: "valueBool"; value: boolean }
    | { $case: "valueString"; value: string }
    | undefined;
  /** 数据类型(只针对字符串，标注字符编码) */
  dataFormat?: DataFormatType | undefined;
}

/** 变量触发 */
export interface VariableTrigger {
  /** 变量(数据类型为bool型) */
  variableBool: VariableReference | undefined;
  triggerType: VariableTrigger_Type;
  /** 自动复位（如果为true，则触发后自动复位到变动之前的状态） */
  autoReset: boolean;
}

/** 触发方式 */
export enum VariableTrigger_Type {
  TYPE_UNSPECIFIED = 0,
  TYPE_OFF_TO_ON = 1,
  TYPE_ON_TO_OFF = 2,
  TYPE_CHANGE = 3,
  UNRECOGNIZED = -1,
}

export function variableTrigger_TypeFromJSON(object: any): VariableTrigger_Type {
  switch (object) {
    case 0:
    case "TYPE_UNSPECIFIED":
      return VariableTrigger_Type.TYPE_UNSPECIFIED;
    case 1:
    case "TYPE_OFF_TO_ON":
      return VariableTrigger_Type.TYPE_OFF_TO_ON;
    case 2:
    case "TYPE_ON_TO_OFF":
      return VariableTrigger_Type.TYPE_ON_TO_OFF;
    case 3:
    case "TYPE_CHANGE":
      return VariableTrigger_Type.TYPE_CHANGE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return VariableTrigger_Type.UNRECOGNIZED;
  }
}

export function variableTrigger_TypeToJSON(object: VariableTrigger_Type): string {
  switch (object) {
    case VariableTrigger_Type.TYPE_UNSPECIFIED:
      return "TYPE_UNSPECIFIED";
    case VariableTrigger_Type.TYPE_OFF_TO_ON:
      return "TYPE_OFF_TO_ON";
    case VariableTrigger_Type.TYPE_ON_TO_OFF:
      return "TYPE_ON_TO_OFF";
    case VariableTrigger_Type.TYPE_CHANGE:
      return "TYPE_CHANGE";
    case VariableTrigger_Type.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

function createBaseStructTypeField(): StructTypeField {
  return {
    fieldIdU8: 0,
    name: "",
    dataFormat: 0,
    startU16: 0,
    lengthU8: 0,
    arrayCountU8: 0,
    bitIndex: undefined,
    byteIndex: undefined,
    memo: "",
  };
}

export const StructTypeField: MessageFns<StructTypeField> = {
  encode(message: StructTypeField, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fieldIdU8 !== 0) {
      writer.uint32(8).uint32(message.fieldIdU8);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.dataFormat !== 0) {
      writer.uint32(24).int32(message.dataFormat);
    }
    if (message.startU16 !== 0) {
      writer.uint32(32).uint32(message.startU16);
    }
    if (message.lengthU8 !== 0) {
      writer.uint32(40).uint32(message.lengthU8);
    }
    if (message.arrayCountU8 !== 0) {
      writer.uint32(48).uint32(message.arrayCountU8);
    }
    if (message.bitIndex !== undefined) {
      writer.uint32(56).int32(message.bitIndex);
    }
    if (message.byteIndex !== undefined) {
      writer.uint32(64).int32(message.byteIndex);
    }
    if (message.memo !== "") {
      writer.uint32(74).string(message.memo);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StructTypeField {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStructTypeField();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.fieldIdU8 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.dataFormat = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.startU16 = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.lengthU8 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.arrayCountU8 = reader.uint32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.bitIndex = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.byteIndex = reader.int32();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StructTypeField {
    return {
      fieldIdU8: isSet(object.fieldIdU8) ? globalThis.Number(object.fieldIdU8) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      dataFormat: isSet(object.dataFormat) ? dataFormatTypeFromJSON(object.dataFormat) : 0,
      startU16: isSet(object.startU16) ? globalThis.Number(object.startU16) : 0,
      lengthU8: isSet(object.lengthU8) ? globalThis.Number(object.lengthU8) : 0,
      arrayCountU8: isSet(object.arrayCountU8) ? globalThis.Number(object.arrayCountU8) : 0,
      bitIndex: isSet(object.bitIndex) ? globalThis.Number(object.bitIndex) : undefined,
      byteIndex: isSet(object.byteIndex) ? globalThis.Number(object.byteIndex) : undefined,
      memo: isSet(object.memo) ? globalThis.String(object.memo) : "",
    };
  },

  toJSON(message: StructTypeField): unknown {
    const obj: any = {};
    if (message.fieldIdU8 !== 0) {
      obj.fieldIdU8 = Math.round(message.fieldIdU8);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.dataFormat !== 0) {
      obj.dataFormat = dataFormatTypeToJSON(message.dataFormat);
    }
    if (message.startU16 !== 0) {
      obj.startU16 = Math.round(message.startU16);
    }
    if (message.lengthU8 !== 0) {
      obj.lengthU8 = Math.round(message.lengthU8);
    }
    if (message.arrayCountU8 !== 0) {
      obj.arrayCountU8 = Math.round(message.arrayCountU8);
    }
    if (message.bitIndex !== undefined) {
      obj.bitIndex = Math.round(message.bitIndex);
    }
    if (message.byteIndex !== undefined) {
      obj.byteIndex = Math.round(message.byteIndex);
    }
    if (message.memo !== "") {
      obj.memo = message.memo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StructTypeField>, I>>(base?: I): StructTypeField {
    return StructTypeField.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StructTypeField>, I>>(object: I): StructTypeField {
    const message = createBaseStructTypeField();
    message.fieldIdU8 = object.fieldIdU8 ?? 0;
    message.name = object.name ?? "";
    message.dataFormat = object.dataFormat ?? 0;
    message.startU16 = object.startU16 ?? 0;
    message.lengthU8 = object.lengthU8 ?? 0;
    message.arrayCountU8 = object.arrayCountU8 ?? 0;
    message.bitIndex = object.bitIndex ?? undefined;
    message.byteIndex = object.byteIndex ?? undefined;
    message.memo = object.memo ?? "";
    return message;
  },
};

function createBaseStructType(): StructType {
  return { name: "", fields: [], lengthU16: 0, mode: undefined, memo: undefined };
}

export const StructType: MessageFns<StructType> = {
  encode(message: StructType, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    for (const v of message.fields) {
      StructTypeField.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.lengthU16 !== 0) {
      writer.uint32(24).uint32(message.lengthU16);
    }
    if (message.mode !== undefined) {
      writer.uint32(32).int32(message.mode);
    }
    if (message.memo !== undefined) {
      writer.uint32(42).string(message.memo);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StructType {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStructType();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fields.push(StructTypeField.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.lengthU16 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.mode = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StructType {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      fields: globalThis.Array.isArray(object?.fields)
        ? object.fields.map((e: any) => StructTypeField.fromJSON(e))
        : [],
      lengthU16: isSet(object.lengthU16) ? globalThis.Number(object.lengthU16) : 0,
      mode: isSet(object.mode) ? structTypeModeFromJSON(object.mode) : undefined,
      memo: isSet(object.memo) ? globalThis.String(object.memo) : undefined,
    };
  },

  toJSON(message: StructType): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.fields?.length) {
      obj.fields = message.fields.map((e) => StructTypeField.toJSON(e));
    }
    if (message.lengthU16 !== 0) {
      obj.lengthU16 = Math.round(message.lengthU16);
    }
    if (message.mode !== undefined) {
      obj.mode = structTypeModeToJSON(message.mode);
    }
    if (message.memo !== undefined) {
      obj.memo = message.memo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StructType>, I>>(base?: I): StructType {
    return StructType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StructType>, I>>(object: I): StructType {
    const message = createBaseStructType();
    message.name = object.name ?? "";
    message.fields = object.fields?.map((e) => StructTypeField.fromPartial(e)) || [];
    message.lengthU16 = object.lengthU16 ?? 0;
    message.mode = object.mode ?? undefined;
    message.memo = object.memo ?? undefined;
    return message;
  },
};

function createBaseVariableLibrary(): VariableLibrary {
  return { variables: {} };
}

export const VariableLibrary: MessageFns<VariableLibrary> = {
  encode(message: VariableLibrary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.variables).forEach(([key, value]) => {
      VariableLibrary_VariablesEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VariableLibrary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVariableLibrary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = VariableLibrary_VariablesEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.variables[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VariableLibrary {
    return {
      variables: isObject(object.variables)
        ? Object.entries(object.variables).reduce<{ [key: number]: Variable }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = Variable.fromJSON(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: VariableLibrary): unknown {
    const obj: any = {};
    if (message.variables) {
      const entries = Object.entries(message.variables);
      if (entries.length > 0) {
        obj.variables = {};
        entries.forEach(([k, v]) => {
          obj.variables[k] = Variable.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VariableLibrary>, I>>(base?: I): VariableLibrary {
    return VariableLibrary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VariableLibrary>, I>>(object: I): VariableLibrary {
    const message = createBaseVariableLibrary();
    message.variables = Object.entries(object.variables ?? {}).reduce<{ [key: number]: Variable }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = Variable.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseVariableLibrary_VariablesEntry(): VariableLibrary_VariablesEntry {
  return { key: 0, value: undefined };
}

export const VariableLibrary_VariablesEntry: MessageFns<VariableLibrary_VariablesEntry> = {
  encode(message: VariableLibrary_VariablesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      Variable.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VariableLibrary_VariablesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVariableLibrary_VariablesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = Variable.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VariableLibrary_VariablesEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? Variable.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: VariableLibrary_VariablesEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = Variable.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VariableLibrary_VariablesEntry>, I>>(base?: I): VariableLibrary_VariablesEntry {
    return VariableLibrary_VariablesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VariableLibrary_VariablesEntry>, I>>(
    object: I,
  ): VariableLibrary_VariablesEntry {
    const message = createBaseVariableLibrary_VariablesEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? Variable.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseVariableReference(): VariableReference {
  return {
    from: undefined,
    addressDynamicOffset: undefined,
    subDeviceIndex: undefined,
    localHmiIndex: undefined,
    extendParams: {},
    valueAddI16: 0,
  };
}

export const VariableReference: MessageFns<VariableReference> = {
  encode(message: VariableReference, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "varDefined":
        VariableReference_UserDefined.encode(message.from.value, writer.uint32(26).fork()).join();
        break;
      case "varInput":
        Variable.encode(message.from.value, writer.uint32(34).fork()).join();
        break;
    }
    if (message.addressDynamicOffset !== undefined) {
      VariableReference.encode(message.addressDynamicOffset, writer.uint32(42).fork()).join();
    }
    if (message.subDeviceIndex !== undefined) {
      DataReferenceUInt8.encode(message.subDeviceIndex, writer.uint32(50).fork()).join();
    }
    if (message.localHmiIndex !== undefined) {
      DataReferenceUInt8.encode(message.localHmiIndex, writer.uint32(58).fork()).join();
    }
    Object.entries(message.extendParams).forEach(([key, value]) => {
      VariableReference_ExtendParamsEntry.encode({ key: key as any, value }, writer.uint32(66).fork()).join();
    });
    if (message.valueAddI16 !== 0) {
      writer.uint32(72).int32(message.valueAddI16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VariableReference {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVariableReference();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.from = { $case: "varDefined", value: VariableReference_UserDefined.decode(reader, reader.uint32()) };
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.from = { $case: "varInput", value: Variable.decode(reader, reader.uint32()) };
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.addressDynamicOffset = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.subDeviceIndex = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.localHmiIndex = DataReferenceUInt8.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          const entry8 = VariableReference_ExtendParamsEntry.decode(reader, reader.uint32());
          if (entry8.value !== undefined) {
            message.extendParams[entry8.key] = entry8.value;
          }
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.valueAddI16 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VariableReference {
    return {
      from: isSet(object.varDefined)
        ? { $case: "varDefined", value: VariableReference_UserDefined.fromJSON(object.varDefined) }
        : isSet(object.varInput)
        ? { $case: "varInput", value: Variable.fromJSON(object.varInput) }
        : undefined,
      addressDynamicOffset: isSet(object.addressDynamicOffset)
        ? VariableReference.fromJSON(object.addressDynamicOffset)
        : undefined,
      subDeviceIndex: isSet(object.subDeviceIndex) ? DataReferenceUInt8.fromJSON(object.subDeviceIndex) : undefined,
      localHmiIndex: isSet(object.localHmiIndex) ? DataReferenceUInt8.fromJSON(object.localHmiIndex) : undefined,
      extendParams: isObject(object.extendParams)
        ? Object.entries(object.extendParams).reduce<{ [key: number]: DataReferenceInt16 }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = DataReferenceInt16.fromJSON(value);
          return acc;
        }, {})
        : {},
      valueAddI16: isSet(object.valueAddI16) ? globalThis.Number(object.valueAddI16) : 0,
    };
  },

  toJSON(message: VariableReference): unknown {
    const obj: any = {};
    if (message.from?.$case === "varDefined") {
      obj.varDefined = VariableReference_UserDefined.toJSON(message.from.value);
    } else if (message.from?.$case === "varInput") {
      obj.varInput = Variable.toJSON(message.from.value);
    }
    if (message.addressDynamicOffset !== undefined) {
      obj.addressDynamicOffset = VariableReference.toJSON(message.addressDynamicOffset);
    }
    if (message.subDeviceIndex !== undefined) {
      obj.subDeviceIndex = DataReferenceUInt8.toJSON(message.subDeviceIndex);
    }
    if (message.localHmiIndex !== undefined) {
      obj.localHmiIndex = DataReferenceUInt8.toJSON(message.localHmiIndex);
    }
    if (message.extendParams) {
      const entries = Object.entries(message.extendParams);
      if (entries.length > 0) {
        obj.extendParams = {};
        entries.forEach(([k, v]) => {
          obj.extendParams[k] = DataReferenceInt16.toJSON(v);
        });
      }
    }
    if (message.valueAddI16 !== 0) {
      obj.valueAddI16 = Math.round(message.valueAddI16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VariableReference>, I>>(base?: I): VariableReference {
    return VariableReference.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VariableReference>, I>>(object: I): VariableReference {
    const message = createBaseVariableReference();
    switch (object.from?.$case) {
      case "varDefined": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "varDefined", value: VariableReference_UserDefined.fromPartial(object.from.value) };
        }
        break;
      }
      case "varInput": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "varInput", value: Variable.fromPartial(object.from.value) };
        }
        break;
      }
    }
    message.addressDynamicOffset = (object.addressDynamicOffset !== undefined && object.addressDynamicOffset !== null)
      ? VariableReference.fromPartial(object.addressDynamicOffset)
      : undefined;
    message.subDeviceIndex = (object.subDeviceIndex !== undefined && object.subDeviceIndex !== null)
      ? DataReferenceUInt8.fromPartial(object.subDeviceIndex)
      : undefined;
    message.localHmiIndex = (object.localHmiIndex !== undefined && object.localHmiIndex !== null)
      ? DataReferenceUInt8.fromPartial(object.localHmiIndex)
      : undefined;
    message.extendParams = Object.entries(object.extendParams ?? {}).reduce<{ [key: number]: DataReferenceInt16 }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = DataReferenceInt16.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.valueAddI16 = object.valueAddI16 ?? 0;
    return message;
  },
};

function createBaseVariableReference_UserDefined(): VariableReference_UserDefined {
  return { deviceIdU8: 0, varIdU16: 0, addressFixedOffsetI16: 0, varSubscripts: [], varSubscriptOffsetI16: 0 };
}

export const VariableReference_UserDefined: MessageFns<VariableReference_UserDefined> = {
  encode(message: VariableReference_UserDefined, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.deviceIdU8 !== 0) {
      writer.uint32(8).uint32(message.deviceIdU8);
    }
    if (message.varIdU16 !== 0) {
      writer.uint32(16).uint32(message.varIdU16);
    }
    if (message.addressFixedOffsetI16 !== 0) {
      writer.uint32(32).int32(message.addressFixedOffsetI16);
    }
    for (const v of message.varSubscripts) {
      DataReferenceUInt8.encode(v!, writer.uint32(42).fork()).join();
    }
    if (message.varSubscriptOffsetI16 !== 0) {
      writer.uint32(48).int32(message.varSubscriptOffsetI16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VariableReference_UserDefined {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVariableReference_UserDefined();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.deviceIdU8 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.varIdU16 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.addressFixedOffsetI16 = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.varSubscripts.push(DataReferenceUInt8.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.varSubscriptOffsetI16 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VariableReference_UserDefined {
    return {
      deviceIdU8: isSet(object.deviceIdU8) ? globalThis.Number(object.deviceIdU8) : 0,
      varIdU16: isSet(object.varIdU16) ? globalThis.Number(object.varIdU16) : 0,
      addressFixedOffsetI16: isSet(object.addressFixedOffsetI16) ? globalThis.Number(object.addressFixedOffsetI16) : 0,
      varSubscripts: globalThis.Array.isArray(object?.varSubscripts)
        ? object.varSubscripts.map((e: any) => DataReferenceUInt8.fromJSON(e))
        : [],
      varSubscriptOffsetI16: isSet(object.varSubscriptOffsetI16) ? globalThis.Number(object.varSubscriptOffsetI16) : 0,
    };
  },

  toJSON(message: VariableReference_UserDefined): unknown {
    const obj: any = {};
    if (message.deviceIdU8 !== 0) {
      obj.deviceIdU8 = Math.round(message.deviceIdU8);
    }
    if (message.varIdU16 !== 0) {
      obj.varIdU16 = Math.round(message.varIdU16);
    }
    if (message.addressFixedOffsetI16 !== 0) {
      obj.addressFixedOffsetI16 = Math.round(message.addressFixedOffsetI16);
    }
    if (message.varSubscripts?.length) {
      obj.varSubscripts = message.varSubscripts.map((e) => DataReferenceUInt8.toJSON(e));
    }
    if (message.varSubscriptOffsetI16 !== 0) {
      obj.varSubscriptOffsetI16 = Math.round(message.varSubscriptOffsetI16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VariableReference_UserDefined>, I>>(base?: I): VariableReference_UserDefined {
    return VariableReference_UserDefined.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VariableReference_UserDefined>, I>>(
    object: I,
  ): VariableReference_UserDefined {
    const message = createBaseVariableReference_UserDefined();
    message.deviceIdU8 = object.deviceIdU8 ?? 0;
    message.varIdU16 = object.varIdU16 ?? 0;
    message.addressFixedOffsetI16 = object.addressFixedOffsetI16 ?? 0;
    message.varSubscripts = object.varSubscripts?.map((e) => DataReferenceUInt8.fromPartial(e)) || [];
    message.varSubscriptOffsetI16 = object.varSubscriptOffsetI16 ?? 0;
    return message;
  },
};

function createBaseVariableReference_ExtendParamsEntry(): VariableReference_ExtendParamsEntry {
  return { key: 0, value: undefined };
}

export const VariableReference_ExtendParamsEntry: MessageFns<VariableReference_ExtendParamsEntry> = {
  encode(message: VariableReference_ExtendParamsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      DataReferenceInt16.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VariableReference_ExtendParamsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVariableReference_ExtendParamsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = DataReferenceInt16.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VariableReference_ExtendParamsEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? DataReferenceInt16.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: VariableReference_ExtendParamsEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = DataReferenceInt16.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VariableReference_ExtendParamsEntry>, I>>(
    base?: I,
  ): VariableReference_ExtendParamsEntry {
    return VariableReference_ExtendParamsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VariableReference_ExtendParamsEntry>, I>>(
    object: I,
  ): VariableReference_ExtendParamsEntry {
    const message = createBaseVariableReference_ExtendParamsEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? DataReferenceInt16.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseDataReference(): DataReference {
  return { from: undefined };
}

export const DataReference: MessageFns<DataReference> = {
  encode(message: DataReference, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "constant":
        AllTypeValue.encode(message.from.value, writer.uint32(10).fork()).join();
        break;
      case "variable":
        VariableReference.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataReference {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataReference();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.from = { $case: "constant", value: AllTypeValue.decode(reader, reader.uint32()) };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "variable", value: VariableReference.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataReference {
    return {
      from: isSet(object.constant)
        ? { $case: "constant", value: AllTypeValue.fromJSON(object.constant) }
        : isSet(object.variable)
        ? { $case: "variable", value: VariableReference.fromJSON(object.variable) }
        : undefined,
    };
  },

  toJSON(message: DataReference): unknown {
    const obj: any = {};
    if (message.from?.$case === "constant") {
      obj.constant = AllTypeValue.toJSON(message.from.value);
    } else if (message.from?.$case === "variable") {
      obj.variable = VariableReference.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataReference>, I>>(base?: I): DataReference {
    return DataReference.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataReference>, I>>(object: I): DataReference {
    const message = createBaseDataReference();
    switch (object.from?.$case) {
      case "constant": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "constant", value: AllTypeValue.fromPartial(object.from.value) };
        }
        break;
      }
      case "variable": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "variable", value: VariableReference.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseDataReferenceNumber(): DataReferenceNumber {
  return { from: undefined };
}

export const DataReferenceNumber: MessageFns<DataReferenceNumber> = {
  encode(message: DataReferenceNumber, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "constantNumber":
        NumberValue.encode(message.from.value, writer.uint32(10).fork()).join();
        break;
      case "variable":
        VariableReference.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataReferenceNumber {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataReferenceNumber();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.from = { $case: "constantNumber", value: NumberValue.decode(reader, reader.uint32()) };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "variable", value: VariableReference.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataReferenceNumber {
    return {
      from: isSet(object.constantNumber)
        ? { $case: "constantNumber", value: NumberValue.fromJSON(object.constantNumber) }
        : isSet(object.variable)
        ? { $case: "variable", value: VariableReference.fromJSON(object.variable) }
        : undefined,
    };
  },

  toJSON(message: DataReferenceNumber): unknown {
    const obj: any = {};
    if (message.from?.$case === "constantNumber") {
      obj.constantNumber = NumberValue.toJSON(message.from.value);
    } else if (message.from?.$case === "variable") {
      obj.variable = VariableReference.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataReferenceNumber>, I>>(base?: I): DataReferenceNumber {
    return DataReferenceNumber.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataReferenceNumber>, I>>(object: I): DataReferenceNumber {
    const message = createBaseDataReferenceNumber();
    switch (object.from?.$case) {
      case "constantNumber": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "constantNumber", value: NumberValue.fromPartial(object.from.value) };
        }
        break;
      }
      case "variable": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "variable", value: VariableReference.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseDataReferenceUInt8(): DataReferenceUInt8 {
  return { from: undefined };
}

export const DataReferenceUInt8: MessageFns<DataReferenceUInt8> = {
  encode(message: DataReferenceUInt8, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "constantU8":
        writer.uint32(8).uint32(message.from.value);
        break;
      case "variable":
        VariableReference.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataReferenceUInt8 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataReferenceUInt8();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.from = { $case: "constantU8", value: reader.uint32() };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "variable", value: VariableReference.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataReferenceUInt8 {
    return {
      from: isSet(object.constantU8)
        ? { $case: "constantU8", value: globalThis.Number(object.constantU8) }
        : isSet(object.variable)
        ? { $case: "variable", value: VariableReference.fromJSON(object.variable) }
        : undefined,
    };
  },

  toJSON(message: DataReferenceUInt8): unknown {
    const obj: any = {};
    if (message.from?.$case === "constantU8") {
      obj.constantU8 = Math.round(message.from.value);
    } else if (message.from?.$case === "variable") {
      obj.variable = VariableReference.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataReferenceUInt8>, I>>(base?: I): DataReferenceUInt8 {
    return DataReferenceUInt8.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataReferenceUInt8>, I>>(object: I): DataReferenceUInt8 {
    const message = createBaseDataReferenceUInt8();
    switch (object.from?.$case) {
      case "constantU8": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "constantU8", value: object.from.value };
        }
        break;
      }
      case "variable": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "variable", value: VariableReference.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseDataReferenceInt8(): DataReferenceInt8 {
  return { from: undefined };
}

export const DataReferenceInt8: MessageFns<DataReferenceInt8> = {
  encode(message: DataReferenceInt8, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "constantI8":
        writer.uint32(8).int32(message.from.value);
        break;
      case "variable":
        VariableReference.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataReferenceInt8 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataReferenceInt8();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.from = { $case: "constantI8", value: reader.int32() };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "variable", value: VariableReference.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataReferenceInt8 {
    return {
      from: isSet(object.constantI8)
        ? { $case: "constantI8", value: globalThis.Number(object.constantI8) }
        : isSet(object.variable)
        ? { $case: "variable", value: VariableReference.fromJSON(object.variable) }
        : undefined,
    };
  },

  toJSON(message: DataReferenceInt8): unknown {
    const obj: any = {};
    if (message.from?.$case === "constantI8") {
      obj.constantI8 = Math.round(message.from.value);
    } else if (message.from?.$case === "variable") {
      obj.variable = VariableReference.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataReferenceInt8>, I>>(base?: I): DataReferenceInt8 {
    return DataReferenceInt8.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataReferenceInt8>, I>>(object: I): DataReferenceInt8 {
    const message = createBaseDataReferenceInt8();
    switch (object.from?.$case) {
      case "constantI8": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "constantI8", value: object.from.value };
        }
        break;
      }
      case "variable": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "variable", value: VariableReference.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseDataReferenceInt16(): DataReferenceInt16 {
  return { from: undefined };
}

export const DataReferenceInt16: MessageFns<DataReferenceInt16> = {
  encode(message: DataReferenceInt16, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "constantI16":
        writer.uint32(8).int32(message.from.value);
        break;
      case "variable":
        VariableReference.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataReferenceInt16 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataReferenceInt16();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.from = { $case: "constantI16", value: reader.int32() };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "variable", value: VariableReference.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataReferenceInt16 {
    return {
      from: isSet(object.constantI16)
        ? { $case: "constantI16", value: globalThis.Number(object.constantI16) }
        : isSet(object.variable)
        ? { $case: "variable", value: VariableReference.fromJSON(object.variable) }
        : undefined,
    };
  },

  toJSON(message: DataReferenceInt16): unknown {
    const obj: any = {};
    if (message.from?.$case === "constantI16") {
      obj.constantI16 = Math.round(message.from.value);
    } else if (message.from?.$case === "variable") {
      obj.variable = VariableReference.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataReferenceInt16>, I>>(base?: I): DataReferenceInt16 {
    return DataReferenceInt16.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataReferenceInt16>, I>>(object: I): DataReferenceInt16 {
    const message = createBaseDataReferenceInt16();
    switch (object.from?.$case) {
      case "constantI16": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "constantI16", value: object.from.value };
        }
        break;
      }
      case "variable": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "variable", value: VariableReference.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseDataReferenceUInt16(): DataReferenceUInt16 {
  return { from: undefined };
}

export const DataReferenceUInt16: MessageFns<DataReferenceUInt16> = {
  encode(message: DataReferenceUInt16, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "constantU16":
        writer.uint32(8).uint32(message.from.value);
        break;
      case "variable":
        VariableReference.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataReferenceUInt16 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataReferenceUInt16();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.from = { $case: "constantU16", value: reader.uint32() };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "variable", value: VariableReference.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataReferenceUInt16 {
    return {
      from: isSet(object.constantU16)
        ? { $case: "constantU16", value: globalThis.Number(object.constantU16) }
        : isSet(object.variable)
        ? { $case: "variable", value: VariableReference.fromJSON(object.variable) }
        : undefined,
    };
  },

  toJSON(message: DataReferenceUInt16): unknown {
    const obj: any = {};
    if (message.from?.$case === "constantU16") {
      obj.constantU16 = Math.round(message.from.value);
    } else if (message.from?.$case === "variable") {
      obj.variable = VariableReference.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataReferenceUInt16>, I>>(base?: I): DataReferenceUInt16 {
    return DataReferenceUInt16.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataReferenceUInt16>, I>>(object: I): DataReferenceUInt16 {
    const message = createBaseDataReferenceUInt16();
    switch (object.from?.$case) {
      case "constantU16": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "constantU16", value: object.from.value };
        }
        break;
      }
      case "variable": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "variable", value: VariableReference.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseDataReferenceInt32(): DataReferenceInt32 {
  return { from: undefined };
}

export const DataReferenceInt32: MessageFns<DataReferenceInt32> = {
  encode(message: DataReferenceInt32, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "constantI32":
        writer.uint32(8).int32(message.from.value);
        break;
      case "variable":
        VariableReference.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataReferenceInt32 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataReferenceInt32();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.from = { $case: "constantI32", value: reader.int32() };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "variable", value: VariableReference.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataReferenceInt32 {
    return {
      from: isSet(object.constantI32)
        ? { $case: "constantI32", value: globalThis.Number(object.constantI32) }
        : isSet(object.variable)
        ? { $case: "variable", value: VariableReference.fromJSON(object.variable) }
        : undefined,
    };
  },

  toJSON(message: DataReferenceInt32): unknown {
    const obj: any = {};
    if (message.from?.$case === "constantI32") {
      obj.constantI32 = Math.round(message.from.value);
    } else if (message.from?.$case === "variable") {
      obj.variable = VariableReference.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataReferenceInt32>, I>>(base?: I): DataReferenceInt32 {
    return DataReferenceInt32.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataReferenceInt32>, I>>(object: I): DataReferenceInt32 {
    const message = createBaseDataReferenceInt32();
    switch (object.from?.$case) {
      case "constantI32": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "constantI32", value: object.from.value };
        }
        break;
      }
      case "variable": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "variable", value: VariableReference.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseDataReferenceUInt32(): DataReferenceUInt32 {
  return { from: undefined };
}

export const DataReferenceUInt32: MessageFns<DataReferenceUInt32> = {
  encode(message: DataReferenceUInt32, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "constantU32":
        writer.uint32(8).uint32(message.from.value);
        break;
      case "variable":
        VariableReference.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataReferenceUInt32 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataReferenceUInt32();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.from = { $case: "constantU32", value: reader.uint32() };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "variable", value: VariableReference.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataReferenceUInt32 {
    return {
      from: isSet(object.constantU32)
        ? { $case: "constantU32", value: globalThis.Number(object.constantU32) }
        : isSet(object.variable)
        ? { $case: "variable", value: VariableReference.fromJSON(object.variable) }
        : undefined,
    };
  },

  toJSON(message: DataReferenceUInt32): unknown {
    const obj: any = {};
    if (message.from?.$case === "constantU32") {
      obj.constantU32 = Math.round(message.from.value);
    } else if (message.from?.$case === "variable") {
      obj.variable = VariableReference.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataReferenceUInt32>, I>>(base?: I): DataReferenceUInt32 {
    return DataReferenceUInt32.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataReferenceUInt32>, I>>(object: I): DataReferenceUInt32 {
    const message = createBaseDataReferenceUInt32();
    switch (object.from?.$case) {
      case "constantU32": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "constantU32", value: object.from.value };
        }
        break;
      }
      case "variable": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "variable", value: VariableReference.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseDataReferenceInt64(): DataReferenceInt64 {
  return { from: undefined };
}

export const DataReferenceInt64: MessageFns<DataReferenceInt64> = {
  encode(message: DataReferenceInt64, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "constantI64":
        writer.uint32(8).int64(message.from.value);
        break;
      case "variable":
        VariableReference.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataReferenceInt64 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataReferenceInt64();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.from = { $case: "constantI64", value: longToNumber(reader.int64()) };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "variable", value: VariableReference.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataReferenceInt64 {
    return {
      from: isSet(object.constantI64)
        ? { $case: "constantI64", value: globalThis.Number(object.constantI64) }
        : isSet(object.variable)
        ? { $case: "variable", value: VariableReference.fromJSON(object.variable) }
        : undefined,
    };
  },

  toJSON(message: DataReferenceInt64): unknown {
    const obj: any = {};
    if (message.from?.$case === "constantI64") {
      obj.constantI64 = Math.round(message.from.value);
    } else if (message.from?.$case === "variable") {
      obj.variable = VariableReference.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataReferenceInt64>, I>>(base?: I): DataReferenceInt64 {
    return DataReferenceInt64.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataReferenceInt64>, I>>(object: I): DataReferenceInt64 {
    const message = createBaseDataReferenceInt64();
    switch (object.from?.$case) {
      case "constantI64": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "constantI64", value: object.from.value };
        }
        break;
      }
      case "variable": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "variable", value: VariableReference.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseDataReferenceUInt64(): DataReferenceUInt64 {
  return { from: undefined };
}

export const DataReferenceUInt64: MessageFns<DataReferenceUInt64> = {
  encode(message: DataReferenceUInt64, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "constantU64":
        writer.uint32(8).uint64(message.from.value);
        break;
      case "variable":
        VariableReference.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataReferenceUInt64 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataReferenceUInt64();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.from = { $case: "constantU64", value: longToNumber(reader.uint64()) };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "variable", value: VariableReference.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataReferenceUInt64 {
    return {
      from: isSet(object.constantU64)
        ? { $case: "constantU64", value: globalThis.Number(object.constantU64) }
        : isSet(object.variable)
        ? { $case: "variable", value: VariableReference.fromJSON(object.variable) }
        : undefined,
    };
  },

  toJSON(message: DataReferenceUInt64): unknown {
    const obj: any = {};
    if (message.from?.$case === "constantU64") {
      obj.constantU64 = Math.round(message.from.value);
    } else if (message.from?.$case === "variable") {
      obj.variable = VariableReference.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataReferenceUInt64>, I>>(base?: I): DataReferenceUInt64 {
    return DataReferenceUInt64.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataReferenceUInt64>, I>>(object: I): DataReferenceUInt64 {
    const message = createBaseDataReferenceUInt64();
    switch (object.from?.$case) {
      case "constantU64": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "constantU64", value: object.from.value };
        }
        break;
      }
      case "variable": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "variable", value: VariableReference.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseDataReferenceFloat(): DataReferenceFloat {
  return { from: undefined };
}

export const DataReferenceFloat: MessageFns<DataReferenceFloat> = {
  encode(message: DataReferenceFloat, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "constantFloat":
        writer.uint32(13).float(message.from.value);
        break;
      case "variable":
        VariableReference.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataReferenceFloat {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataReferenceFloat();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 13) {
            break;
          }

          message.from = { $case: "constantFloat", value: reader.float() };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "variable", value: VariableReference.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataReferenceFloat {
    return {
      from: isSet(object.constantFloat)
        ? { $case: "constantFloat", value: globalThis.Number(object.constantFloat) }
        : isSet(object.variable)
        ? { $case: "variable", value: VariableReference.fromJSON(object.variable) }
        : undefined,
    };
  },

  toJSON(message: DataReferenceFloat): unknown {
    const obj: any = {};
    if (message.from?.$case === "constantFloat") {
      obj.constantFloat = message.from.value;
    } else if (message.from?.$case === "variable") {
      obj.variable = VariableReference.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataReferenceFloat>, I>>(base?: I): DataReferenceFloat {
    return DataReferenceFloat.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataReferenceFloat>, I>>(object: I): DataReferenceFloat {
    const message = createBaseDataReferenceFloat();
    switch (object.from?.$case) {
      case "constantFloat": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "constantFloat", value: object.from.value };
        }
        break;
      }
      case "variable": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "variable", value: VariableReference.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseDataReferenceDouble(): DataReferenceDouble {
  return { from: undefined };
}

export const DataReferenceDouble: MessageFns<DataReferenceDouble> = {
  encode(message: DataReferenceDouble, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "constantDouble":
        writer.uint32(9).double(message.from.value);
        break;
      case "variable":
        VariableReference.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataReferenceDouble {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataReferenceDouble();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.from = { $case: "constantDouble", value: reader.double() };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "variable", value: VariableReference.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataReferenceDouble {
    return {
      from: isSet(object.constantDouble)
        ? { $case: "constantDouble", value: globalThis.Number(object.constantDouble) }
        : isSet(object.variable)
        ? { $case: "variable", value: VariableReference.fromJSON(object.variable) }
        : undefined,
    };
  },

  toJSON(message: DataReferenceDouble): unknown {
    const obj: any = {};
    if (message.from?.$case === "constantDouble") {
      obj.constantDouble = message.from.value;
    } else if (message.from?.$case === "variable") {
      obj.variable = VariableReference.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataReferenceDouble>, I>>(base?: I): DataReferenceDouble {
    return DataReferenceDouble.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataReferenceDouble>, I>>(object: I): DataReferenceDouble {
    const message = createBaseDataReferenceDouble();
    switch (object.from?.$case) {
      case "constantDouble": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "constantDouble", value: object.from.value };
        }
        break;
      }
      case "variable": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "variable", value: VariableReference.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseDataReferenceBool(): DataReferenceBool {
  return { from: undefined };
}

export const DataReferenceBool: MessageFns<DataReferenceBool> = {
  encode(message: DataReferenceBool, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "constantBool":
        writer.uint32(8).bool(message.from.value);
        break;
      case "variable":
        VariableReference.encode(message.from.value, writer.uint32(18).fork()).join();
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataReferenceBool {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataReferenceBool();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.from = { $case: "constantBool", value: reader.bool() };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.from = { $case: "variable", value: VariableReference.decode(reader, reader.uint32()) };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataReferenceBool {
    return {
      from: isSet(object.constantBool)
        ? { $case: "constantBool", value: globalThis.Boolean(object.constantBool) }
        : isSet(object.variable)
        ? { $case: "variable", value: VariableReference.fromJSON(object.variable) }
        : undefined,
    };
  },

  toJSON(message: DataReferenceBool): unknown {
    const obj: any = {};
    if (message.from?.$case === "constantBool") {
      obj.constantBool = message.from.value;
    } else if (message.from?.$case === "variable") {
      obj.variable = VariableReference.toJSON(message.from.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataReferenceBool>, I>>(base?: I): DataReferenceBool {
    return DataReferenceBool.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataReferenceBool>, I>>(object: I): DataReferenceBool {
    const message = createBaseDataReferenceBool();
    switch (object.from?.$case) {
      case "constantBool": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "constantBool", value: object.from.value };
        }
        break;
      }
      case "variable": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "variable", value: VariableReference.fromPartial(object.from.value) };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseVariable(): Variable {
  return { name: "", memo: "", varClassifyIdsU16: [], varDeviceIdU8: 0, definition: undefined, dataFormat: 0 };
}

export const Variable: MessageFns<Variable> = {
  encode(message: Variable, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.memo !== "") {
      writer.uint32(18).string(message.memo);
    }
    writer.uint32(26).fork();
    for (const v of message.varClassifyIdsU16) {
      writer.uint32(v);
    }
    writer.join();
    if (message.varDeviceIdU8 !== 0) {
      writer.uint32(32).uint32(message.varDeviceIdU8);
    }
    switch (message.definition?.$case) {
      case "registerAddress":
        RegisterAddress.encode(message.definition.value, writer.uint32(42).fork()).join();
        break;
      case "dynamicVariable":
        DynamicVariable.encode(message.definition.value, writer.uint32(50).fork()).join();
        break;
      case "expression":
        Expression.encode(message.definition.value, writer.uint32(58).fork()).join();
        break;
    }
    if (message.dataFormat !== 0) {
      writer.uint32(64).int32(message.dataFormat);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Variable {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVariable();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.varClassifyIdsU16.push(reader.uint32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.varClassifyIdsU16.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.varDeviceIdU8 = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.definition = { $case: "registerAddress", value: RegisterAddress.decode(reader, reader.uint32()) };
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.definition = { $case: "dynamicVariable", value: DynamicVariable.decode(reader, reader.uint32()) };
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.definition = { $case: "expression", value: Expression.decode(reader, reader.uint32()) };
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.dataFormat = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Variable {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      memo: isSet(object.memo) ? globalThis.String(object.memo) : "",
      varClassifyIdsU16: globalThis.Array.isArray(object?.varClassifyIdsU16)
        ? object.varClassifyIdsU16.map((e: any) => globalThis.Number(e))
        : [],
      varDeviceIdU8: isSet(object.varDeviceIdU8) ? globalThis.Number(object.varDeviceIdU8) : 0,
      definition: isSet(object.registerAddress)
        ? { $case: "registerAddress", value: RegisterAddress.fromJSON(object.registerAddress) }
        : isSet(object.dynamicVariable)
        ? { $case: "dynamicVariable", value: DynamicVariable.fromJSON(object.dynamicVariable) }
        : isSet(object.expression)
        ? { $case: "expression", value: Expression.fromJSON(object.expression) }
        : undefined,
      dataFormat: isSet(object.dataFormat) ? dataFormatTypeFromJSON(object.dataFormat) : 0,
    };
  },

  toJSON(message: Variable): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.memo !== "") {
      obj.memo = message.memo;
    }
    if (message.varClassifyIdsU16?.length) {
      obj.varClassifyIdsU16 = message.varClassifyIdsU16.map((e) => Math.round(e));
    }
    if (message.varDeviceIdU8 !== 0) {
      obj.varDeviceIdU8 = Math.round(message.varDeviceIdU8);
    }
    if (message.definition?.$case === "registerAddress") {
      obj.registerAddress = RegisterAddress.toJSON(message.definition.value);
    } else if (message.definition?.$case === "dynamicVariable") {
      obj.dynamicVariable = DynamicVariable.toJSON(message.definition.value);
    } else if (message.definition?.$case === "expression") {
      obj.expression = Expression.toJSON(message.definition.value);
    }
    if (message.dataFormat !== 0) {
      obj.dataFormat = dataFormatTypeToJSON(message.dataFormat);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Variable>, I>>(base?: I): Variable {
    return Variable.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Variable>, I>>(object: I): Variable {
    const message = createBaseVariable();
    message.name = object.name ?? "";
    message.memo = object.memo ?? "";
    message.varClassifyIdsU16 = object.varClassifyIdsU16?.map((e) => e) || [];
    message.varDeviceIdU8 = object.varDeviceIdU8 ?? 0;
    switch (object.definition?.$case) {
      case "registerAddress": {
        if (object.definition?.value !== undefined && object.definition?.value !== null) {
          message.definition = {
            $case: "registerAddress",
            value: RegisterAddress.fromPartial(object.definition.value),
          };
        }
        break;
      }
      case "dynamicVariable": {
        if (object.definition?.value !== undefined && object.definition?.value !== null) {
          message.definition = {
            $case: "dynamicVariable",
            value: DynamicVariable.fromPartial(object.definition.value),
          };
        }
        break;
      }
      case "expression": {
        if (object.definition?.value !== undefined && object.definition?.value !== null) {
          message.definition = { $case: "expression", value: Expression.fromPartial(object.definition.value) };
        }
        break;
      }
    }
    message.dataFormat = object.dataFormat ?? 0;
    return message;
  },
};

function createBaseRegisterAddress(): RegisterAddress {
  return {
    isPresetAddressTag: false,
    registerDisplayType: "",
    registerDisplayAddressU32: 0,
    deviceIdU8: 0,
    registerTypeU8: 0,
    registerAddressU32: 0,
    registerBitLengthU8: 0,
    bitIndexU8: undefined,
    byteIndexU8: undefined,
    bitReverse: false,
    addressLengthU16: 0,
    array1dLengthU8: 0,
    array1dStartIndexU8: 0,
    array2dLengthU8: 0,
    array2dStartIndexU8: 0,
    dataTransforms: [],
    registerDataFormat: 0,
  };
}

export const RegisterAddress: MessageFns<RegisterAddress> = {
  encode(message: RegisterAddress, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isPresetAddressTag !== false) {
      writer.uint32(8).bool(message.isPresetAddressTag);
    }
    if (message.registerDisplayType !== "") {
      writer.uint32(18).string(message.registerDisplayType);
    }
    if (message.registerDisplayAddressU32 !== 0) {
      writer.uint32(24).uint32(message.registerDisplayAddressU32);
    }
    if (message.deviceIdU8 !== 0) {
      writer.uint32(32).uint32(message.deviceIdU8);
    }
    if (message.registerTypeU8 !== 0) {
      writer.uint32(40).uint32(message.registerTypeU8);
    }
    if (message.registerAddressU32 !== 0) {
      writer.uint32(48).uint32(message.registerAddressU32);
    }
    if (message.registerBitLengthU8 !== 0) {
      writer.uint32(56).uint32(message.registerBitLengthU8);
    }
    if (message.bitIndexU8 !== undefined) {
      writer.uint32(64).uint32(message.bitIndexU8);
    }
    if (message.byteIndexU8 !== undefined) {
      writer.uint32(72).uint32(message.byteIndexU8);
    }
    if (message.bitReverse !== false) {
      writer.uint32(88).bool(message.bitReverse);
    }
    if (message.addressLengthU16 !== 0) {
      writer.uint32(96).uint32(message.addressLengthU16);
    }
    if (message.array1dLengthU8 !== 0) {
      writer.uint32(136).uint32(message.array1dLengthU8);
    }
    if (message.array1dStartIndexU8 !== 0) {
      writer.uint32(144).uint32(message.array1dStartIndexU8);
    }
    if (message.array2dLengthU8 !== 0) {
      writer.uint32(152).uint32(message.array2dLengthU8);
    }
    if (message.array2dStartIndexU8 !== 0) {
      writer.uint32(160).uint32(message.array2dStartIndexU8);
    }
    for (const v of message.dataTransforms) {
      DataTransform.encode(v!, writer.uint32(170).fork()).join();
    }
    if (message.registerDataFormat !== 0) {
      writer.uint32(176).int32(message.registerDataFormat);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RegisterAddress {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRegisterAddress();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isPresetAddressTag = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.registerDisplayType = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.registerDisplayAddressU32 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.deviceIdU8 = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.registerTypeU8 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.registerAddressU32 = reader.uint32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.registerBitLengthU8 = reader.uint32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.bitIndexU8 = reader.uint32();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.byteIndexU8 = reader.uint32();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.bitReverse = reader.bool();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.addressLengthU16 = reader.uint32();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.array1dLengthU8 = reader.uint32();
          continue;
        }
        case 18: {
          if (tag !== 144) {
            break;
          }

          message.array1dStartIndexU8 = reader.uint32();
          continue;
        }
        case 19: {
          if (tag !== 152) {
            break;
          }

          message.array2dLengthU8 = reader.uint32();
          continue;
        }
        case 20: {
          if (tag !== 160) {
            break;
          }

          message.array2dStartIndexU8 = reader.uint32();
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.dataTransforms.push(DataTransform.decode(reader, reader.uint32()));
          continue;
        }
        case 22: {
          if (tag !== 176) {
            break;
          }

          message.registerDataFormat = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RegisterAddress {
    return {
      isPresetAddressTag: isSet(object.isPresetAddressTag) ? globalThis.Boolean(object.isPresetAddressTag) : false,
      registerDisplayType: isSet(object.registerDisplayType) ? globalThis.String(object.registerDisplayType) : "",
      registerDisplayAddressU32: isSet(object.registerDisplayAddressU32)
        ? globalThis.Number(object.registerDisplayAddressU32)
        : 0,
      deviceIdU8: isSet(object.deviceIdU8) ? globalThis.Number(object.deviceIdU8) : 0,
      registerTypeU8: isSet(object.registerTypeU8) ? globalThis.Number(object.registerTypeU8) : 0,
      registerAddressU32: isSet(object.registerAddressU32) ? globalThis.Number(object.registerAddressU32) : 0,
      registerBitLengthU8: isSet(object.registerBitLengthU8) ? globalThis.Number(object.registerBitLengthU8) : 0,
      bitIndexU8: isSet(object.bitIndexU8) ? globalThis.Number(object.bitIndexU8) : undefined,
      byteIndexU8: isSet(object.byteIndexU8) ? globalThis.Number(object.byteIndexU8) : undefined,
      bitReverse: isSet(object.bitReverse) ? globalThis.Boolean(object.bitReverse) : false,
      addressLengthU16: isSet(object.addressLengthU16) ? globalThis.Number(object.addressLengthU16) : 0,
      array1dLengthU8: isSet(object.array1dLengthU8) ? globalThis.Number(object.array1dLengthU8) : 0,
      array1dStartIndexU8: isSet(object.array1dStartIndexU8) ? globalThis.Number(object.array1dStartIndexU8) : 0,
      array2dLengthU8: isSet(object.array2dLengthU8) ? globalThis.Number(object.array2dLengthU8) : 0,
      array2dStartIndexU8: isSet(object.array2dStartIndexU8) ? globalThis.Number(object.array2dStartIndexU8) : 0,
      dataTransforms: globalThis.Array.isArray(object?.dataTransforms)
        ? object.dataTransforms.map((e: any) => DataTransform.fromJSON(e))
        : [],
      registerDataFormat: isSet(object.registerDataFormat) ? dataFormatTypeFromJSON(object.registerDataFormat) : 0,
    };
  },

  toJSON(message: RegisterAddress): unknown {
    const obj: any = {};
    if (message.isPresetAddressTag !== false) {
      obj.isPresetAddressTag = message.isPresetAddressTag;
    }
    if (message.registerDisplayType !== "") {
      obj.registerDisplayType = message.registerDisplayType;
    }
    if (message.registerDisplayAddressU32 !== 0) {
      obj.registerDisplayAddressU32 = Math.round(message.registerDisplayAddressU32);
    }
    if (message.deviceIdU8 !== 0) {
      obj.deviceIdU8 = Math.round(message.deviceIdU8);
    }
    if (message.registerTypeU8 !== 0) {
      obj.registerTypeU8 = Math.round(message.registerTypeU8);
    }
    if (message.registerAddressU32 !== 0) {
      obj.registerAddressU32 = Math.round(message.registerAddressU32);
    }
    if (message.registerBitLengthU8 !== 0) {
      obj.registerBitLengthU8 = Math.round(message.registerBitLengthU8);
    }
    if (message.bitIndexU8 !== undefined) {
      obj.bitIndexU8 = Math.round(message.bitIndexU8);
    }
    if (message.byteIndexU8 !== undefined) {
      obj.byteIndexU8 = Math.round(message.byteIndexU8);
    }
    if (message.bitReverse !== false) {
      obj.bitReverse = message.bitReverse;
    }
    if (message.addressLengthU16 !== 0) {
      obj.addressLengthU16 = Math.round(message.addressLengthU16);
    }
    if (message.array1dLengthU8 !== 0) {
      obj.array1dLengthU8 = Math.round(message.array1dLengthU8);
    }
    if (message.array1dStartIndexU8 !== 0) {
      obj.array1dStartIndexU8 = Math.round(message.array1dStartIndexU8);
    }
    if (message.array2dLengthU8 !== 0) {
      obj.array2dLengthU8 = Math.round(message.array2dLengthU8);
    }
    if (message.array2dStartIndexU8 !== 0) {
      obj.array2dStartIndexU8 = Math.round(message.array2dStartIndexU8);
    }
    if (message.dataTransforms?.length) {
      obj.dataTransforms = message.dataTransforms.map((e) => DataTransform.toJSON(e));
    }
    if (message.registerDataFormat !== 0) {
      obj.registerDataFormat = dataFormatTypeToJSON(message.registerDataFormat);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RegisterAddress>, I>>(base?: I): RegisterAddress {
    return RegisterAddress.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RegisterAddress>, I>>(object: I): RegisterAddress {
    const message = createBaseRegisterAddress();
    message.isPresetAddressTag = object.isPresetAddressTag ?? false;
    message.registerDisplayType = object.registerDisplayType ?? "";
    message.registerDisplayAddressU32 = object.registerDisplayAddressU32 ?? 0;
    message.deviceIdU8 = object.deviceIdU8 ?? 0;
    message.registerTypeU8 = object.registerTypeU8 ?? 0;
    message.registerAddressU32 = object.registerAddressU32 ?? 0;
    message.registerBitLengthU8 = object.registerBitLengthU8 ?? 0;
    message.bitIndexU8 = object.bitIndexU8 ?? undefined;
    message.byteIndexU8 = object.byteIndexU8 ?? undefined;
    message.bitReverse = object.bitReverse ?? false;
    message.addressLengthU16 = object.addressLengthU16 ?? 0;
    message.array1dLengthU8 = object.array1dLengthU8 ?? 0;
    message.array1dStartIndexU8 = object.array1dStartIndexU8 ?? 0;
    message.array2dLengthU8 = object.array2dLengthU8 ?? 0;
    message.array2dStartIndexU8 = object.array2dStartIndexU8 ?? 0;
    message.dataTransforms = object.dataTransforms?.map((e) => DataTransform.fromPartial(e)) || [];
    message.registerDataFormat = object.registerDataFormat ?? 0;
    return message;
  },
};

function createBaseDynamicVariable(): DynamicVariable {
  return { subVariableIndex: undefined, subVariables: [], subVariableIndexOffsetI16: 0 };
}

export const DynamicVariable: MessageFns<DynamicVariable> = {
  encode(message: DynamicVariable, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.subVariableIndex !== undefined) {
      VariableReference.encode(message.subVariableIndex, writer.uint32(10).fork()).join();
    }
    for (const v of message.subVariables) {
      VariableReference.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.subVariableIndexOffsetI16 !== 0) {
      writer.uint32(24).int32(message.subVariableIndexOffsetI16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DynamicVariable {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDynamicVariable();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.subVariableIndex = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subVariables.push(VariableReference.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.subVariableIndexOffsetI16 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DynamicVariable {
    return {
      subVariableIndex: isSet(object.subVariableIndex)
        ? VariableReference.fromJSON(object.subVariableIndex)
        : undefined,
      subVariables: globalThis.Array.isArray(object?.subVariables)
        ? object.subVariables.map((e: any) => VariableReference.fromJSON(e))
        : [],
      subVariableIndexOffsetI16: isSet(object.subVariableIndexOffsetI16)
        ? globalThis.Number(object.subVariableIndexOffsetI16)
        : 0,
    };
  },

  toJSON(message: DynamicVariable): unknown {
    const obj: any = {};
    if (message.subVariableIndex !== undefined) {
      obj.subVariableIndex = VariableReference.toJSON(message.subVariableIndex);
    }
    if (message.subVariables?.length) {
      obj.subVariables = message.subVariables.map((e) => VariableReference.toJSON(e));
    }
    if (message.subVariableIndexOffsetI16 !== 0) {
      obj.subVariableIndexOffsetI16 = Math.round(message.subVariableIndexOffsetI16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DynamicVariable>, I>>(base?: I): DynamicVariable {
    return DynamicVariable.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DynamicVariable>, I>>(object: I): DynamicVariable {
    const message = createBaseDynamicVariable();
    message.subVariableIndex = (object.subVariableIndex !== undefined && object.subVariableIndex !== null)
      ? VariableReference.fromPartial(object.subVariableIndex)
      : undefined;
    message.subVariables = object.subVariables?.map((e) => VariableReference.fromPartial(e)) || [];
    message.subVariableIndexOffsetI16 = object.subVariableIndexOffsetI16 ?? 0;
    return message;
  },
};

function createBaseExpression(): Expression {
  return { refVariables: {}, readExpression: "", writeExpressions: {} };
}

export const Expression: MessageFns<Expression> = {
  encode(message: Expression, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.refVariables).forEach(([key, value]) => {
      Expression_RefVariablesEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    if (message.readExpression !== "") {
      writer.uint32(18).string(message.readExpression);
    }
    Object.entries(message.writeExpressions).forEach(([key, value]) => {
      Expression_WriteExpressionsEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Expression {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseExpression();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = Expression_RefVariablesEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.refVariables[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.readExpression = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = Expression_WriteExpressionsEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.writeExpressions[entry3.key] = entry3.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Expression {
    return {
      refVariables: isObject(object.refVariables)
        ? Object.entries(object.refVariables).reduce<{ [key: string]: VariableReference }>((acc, [key, value]) => {
          acc[key] = VariableReference.fromJSON(value);
          return acc;
        }, {})
        : {},
      readExpression: isSet(object.readExpression) ? globalThis.String(object.readExpression) : "",
      writeExpressions: isObject(object.writeExpressions)
        ? Object.entries(object.writeExpressions).reduce<{ [key: string]: string }>((acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: Expression): unknown {
    const obj: any = {};
    if (message.refVariables) {
      const entries = Object.entries(message.refVariables);
      if (entries.length > 0) {
        obj.refVariables = {};
        entries.forEach(([k, v]) => {
          obj.refVariables[k] = VariableReference.toJSON(v);
        });
      }
    }
    if (message.readExpression !== "") {
      obj.readExpression = message.readExpression;
    }
    if (message.writeExpressions) {
      const entries = Object.entries(message.writeExpressions);
      if (entries.length > 0) {
        obj.writeExpressions = {};
        entries.forEach(([k, v]) => {
          obj.writeExpressions[k] = v;
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Expression>, I>>(base?: I): Expression {
    return Expression.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Expression>, I>>(object: I): Expression {
    const message = createBaseExpression();
    message.refVariables = Object.entries(object.refVariables ?? {}).reduce<{ [key: string]: VariableReference }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = VariableReference.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.readExpression = object.readExpression ?? "";
    message.writeExpressions = Object.entries(object.writeExpressions ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseExpression_RefVariablesEntry(): Expression_RefVariablesEntry {
  return { key: "", value: undefined };
}

export const Expression_RefVariablesEntry: MessageFns<Expression_RefVariablesEntry> = {
  encode(message: Expression_RefVariablesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      VariableReference.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Expression_RefVariablesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseExpression_RefVariablesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = VariableReference.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Expression_RefVariablesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? VariableReference.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: Expression_RefVariablesEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = VariableReference.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Expression_RefVariablesEntry>, I>>(base?: I): Expression_RefVariablesEntry {
    return Expression_RefVariablesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Expression_RefVariablesEntry>, I>>(object: I): Expression_RefVariablesEntry {
    const message = createBaseExpression_RefVariablesEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? VariableReference.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseExpression_WriteExpressionsEntry(): Expression_WriteExpressionsEntry {
  return { key: "", value: "" };
}

export const Expression_WriteExpressionsEntry: MessageFns<Expression_WriteExpressionsEntry> = {
  encode(message: Expression_WriteExpressionsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Expression_WriteExpressionsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseExpression_WriteExpressionsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Expression_WriteExpressionsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: Expression_WriteExpressionsEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Expression_WriteExpressionsEntry>, I>>(
    base?: I,
  ): Expression_WriteExpressionsEntry {
    return Expression_WriteExpressionsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Expression_WriteExpressionsEntry>, I>>(
    object: I,
  ): Expression_WriteExpressionsEntry {
    const message = createBaseExpression_WriteExpressionsEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseCalculateExpression(): CalculateExpression {
  return { refVariables: {}, expression: "" };
}

export const CalculateExpression: MessageFns<CalculateExpression> = {
  encode(message: CalculateExpression, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.refVariables).forEach(([key, value]) => {
      CalculateExpression_RefVariablesEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    if (message.expression !== "") {
      writer.uint32(18).string(message.expression);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CalculateExpression {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCalculateExpression();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = CalculateExpression_RefVariablesEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.refVariables[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.expression = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CalculateExpression {
    return {
      refVariables: isObject(object.refVariables)
        ? Object.entries(object.refVariables).reduce<{ [key: string]: VariableReference }>((acc, [key, value]) => {
          acc[key] = VariableReference.fromJSON(value);
          return acc;
        }, {})
        : {},
      expression: isSet(object.expression) ? globalThis.String(object.expression) : "",
    };
  },

  toJSON(message: CalculateExpression): unknown {
    const obj: any = {};
    if (message.refVariables) {
      const entries = Object.entries(message.refVariables);
      if (entries.length > 0) {
        obj.refVariables = {};
        entries.forEach(([k, v]) => {
          obj.refVariables[k] = VariableReference.toJSON(v);
        });
      }
    }
    if (message.expression !== "") {
      obj.expression = message.expression;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CalculateExpression>, I>>(base?: I): CalculateExpression {
    return CalculateExpression.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CalculateExpression>, I>>(object: I): CalculateExpression {
    const message = createBaseCalculateExpression();
    message.refVariables = Object.entries(object.refVariables ?? {}).reduce<{ [key: string]: VariableReference }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = VariableReference.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.expression = object.expression ?? "";
    return message;
  },
};

function createBaseCalculateExpression_RefVariablesEntry(): CalculateExpression_RefVariablesEntry {
  return { key: "", value: undefined };
}

export const CalculateExpression_RefVariablesEntry: MessageFns<CalculateExpression_RefVariablesEntry> = {
  encode(message: CalculateExpression_RefVariablesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      VariableReference.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CalculateExpression_RefVariablesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCalculateExpression_RefVariablesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = VariableReference.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CalculateExpression_RefVariablesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? VariableReference.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: CalculateExpression_RefVariablesEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = VariableReference.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CalculateExpression_RefVariablesEntry>, I>>(
    base?: I,
  ): CalculateExpression_RefVariablesEntry {
    return CalculateExpression_RefVariablesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CalculateExpression_RefVariablesEntry>, I>>(
    object: I,
  ): CalculateExpression_RefVariablesEntry {
    const message = createBaseCalculateExpression_RefVariablesEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? VariableReference.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseDataTransform(): DataTransform {
  return { transform: undefined, dataFormat: 0 };
}

export const DataTransform: MessageFns<DataTransform> = {
  encode(message: DataTransform, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.transform?.$case) {
      case "offset":
        DataReferenceNumber.encode(message.transform.value, writer.uint32(10).fork()).join();
        break;
      case "factor":
        DataReferenceNumber.encode(message.transform.value, writer.uint32(18).fork()).join();
        break;
      case "linear":
        LinearTransform.encode(message.transform.value, writer.uint32(26).fork()).join();
        break;
      case "expression":
        Expression.encode(message.transform.value, writer.uint32(34).fork()).join();
        break;
      case "diff":
        DiffTransform.encode(message.transform.value, writer.uint32(42).fork()).join();
        break;
    }
    if (message.dataFormat !== 0) {
      writer.uint32(48).int32(message.dataFormat);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataTransform {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataTransform();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transform = { $case: "offset", value: DataReferenceNumber.decode(reader, reader.uint32()) };
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.transform = { $case: "factor", value: DataReferenceNumber.decode(reader, reader.uint32()) };
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.transform = { $case: "linear", value: LinearTransform.decode(reader, reader.uint32()) };
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.transform = { $case: "expression", value: Expression.decode(reader, reader.uint32()) };
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.transform = { $case: "diff", value: DiffTransform.decode(reader, reader.uint32()) };
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.dataFormat = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataTransform {
    return {
      transform: isSet(object.offset)
        ? { $case: "offset", value: DataReferenceNumber.fromJSON(object.offset) }
        : isSet(object.factor)
        ? { $case: "factor", value: DataReferenceNumber.fromJSON(object.factor) }
        : isSet(object.linear)
        ? { $case: "linear", value: LinearTransform.fromJSON(object.linear) }
        : isSet(object.expression)
        ? { $case: "expression", value: Expression.fromJSON(object.expression) }
        : isSet(object.diff)
        ? { $case: "diff", value: DiffTransform.fromJSON(object.diff) }
        : undefined,
      dataFormat: isSet(object.dataFormat) ? dataFormatTypeFromJSON(object.dataFormat) : 0,
    };
  },

  toJSON(message: DataTransform): unknown {
    const obj: any = {};
    if (message.transform?.$case === "offset") {
      obj.offset = DataReferenceNumber.toJSON(message.transform.value);
    } else if (message.transform?.$case === "factor") {
      obj.factor = DataReferenceNumber.toJSON(message.transform.value);
    } else if (message.transform?.$case === "linear") {
      obj.linear = LinearTransform.toJSON(message.transform.value);
    } else if (message.transform?.$case === "expression") {
      obj.expression = Expression.toJSON(message.transform.value);
    } else if (message.transform?.$case === "diff") {
      obj.diff = DiffTransform.toJSON(message.transform.value);
    }
    if (message.dataFormat !== 0) {
      obj.dataFormat = dataFormatTypeToJSON(message.dataFormat);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataTransform>, I>>(base?: I): DataTransform {
    return DataTransform.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataTransform>, I>>(object: I): DataTransform {
    const message = createBaseDataTransform();
    switch (object.transform?.$case) {
      case "offset": {
        if (object.transform?.value !== undefined && object.transform?.value !== null) {
          message.transform = { $case: "offset", value: DataReferenceNumber.fromPartial(object.transform.value) };
        }
        break;
      }
      case "factor": {
        if (object.transform?.value !== undefined && object.transform?.value !== null) {
          message.transform = { $case: "factor", value: DataReferenceNumber.fromPartial(object.transform.value) };
        }
        break;
      }
      case "linear": {
        if (object.transform?.value !== undefined && object.transform?.value !== null) {
          message.transform = { $case: "linear", value: LinearTransform.fromPartial(object.transform.value) };
        }
        break;
      }
      case "expression": {
        if (object.transform?.value !== undefined && object.transform?.value !== null) {
          message.transform = { $case: "expression", value: Expression.fromPartial(object.transform.value) };
        }
        break;
      }
      case "diff": {
        if (object.transform?.value !== undefined && object.transform?.value !== null) {
          message.transform = { $case: "diff", value: DiffTransform.fromPartial(object.transform.value) };
        }
        break;
      }
    }
    message.dataFormat = object.dataFormat ?? 0;
    return message;
  },
};

function createBaseDiffTransform(): DiffTransform {
  return { timeIntervalU16: 0, isRange: false, minValue: undefined, maxValue: undefined };
}

export const DiffTransform: MessageFns<DiffTransform> = {
  encode(message: DiffTransform, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.timeIntervalU16 !== 0) {
      writer.uint32(8).uint32(message.timeIntervalU16);
    }
    if (message.isRange !== false) {
      writer.uint32(16).bool(message.isRange);
    }
    if (message.minValue !== undefined) {
      DataReferenceNumber.encode(message.minValue, writer.uint32(26).fork()).join();
    }
    if (message.maxValue !== undefined) {
      DataReferenceNumber.encode(message.maxValue, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DiffTransform {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDiffTransform();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.timeIntervalU16 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isRange = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.minValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.maxValue = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DiffTransform {
    return {
      timeIntervalU16: isSet(object.timeIntervalU16) ? globalThis.Number(object.timeIntervalU16) : 0,
      isRange: isSet(object.isRange) ? globalThis.Boolean(object.isRange) : false,
      minValue: isSet(object.minValue) ? DataReferenceNumber.fromJSON(object.minValue) : undefined,
      maxValue: isSet(object.maxValue) ? DataReferenceNumber.fromJSON(object.maxValue) : undefined,
    };
  },

  toJSON(message: DiffTransform): unknown {
    const obj: any = {};
    if (message.timeIntervalU16 !== 0) {
      obj.timeIntervalU16 = Math.round(message.timeIntervalU16);
    }
    if (message.isRange !== false) {
      obj.isRange = message.isRange;
    }
    if (message.minValue !== undefined) {
      obj.minValue = DataReferenceNumber.toJSON(message.minValue);
    }
    if (message.maxValue !== undefined) {
      obj.maxValue = DataReferenceNumber.toJSON(message.maxValue);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DiffTransform>, I>>(base?: I): DiffTransform {
    return DiffTransform.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DiffTransform>, I>>(object: I): DiffTransform {
    const message = createBaseDiffTransform();
    message.timeIntervalU16 = object.timeIntervalU16 ?? 0;
    message.isRange = object.isRange ?? false;
    message.minValue = (object.minValue !== undefined && object.minValue !== null)
      ? DataReferenceNumber.fromPartial(object.minValue)
      : undefined;
    message.maxValue = (object.maxValue !== undefined && object.maxValue !== null)
      ? DataReferenceNumber.fromPartial(object.maxValue)
      : undefined;
    return message;
  },
};

function createBaseLinearTransform(): LinearTransform {
  return { orgValueMin: undefined, orgValueMax: undefined, targetValueMin: undefined, targetValueMax: undefined };
}

export const LinearTransform: MessageFns<LinearTransform> = {
  encode(message: LinearTransform, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.orgValueMin !== undefined) {
      DataReferenceNumber.encode(message.orgValueMin, writer.uint32(10).fork()).join();
    }
    if (message.orgValueMax !== undefined) {
      DataReferenceNumber.encode(message.orgValueMax, writer.uint32(18).fork()).join();
    }
    if (message.targetValueMin !== undefined) {
      DataReferenceNumber.encode(message.targetValueMin, writer.uint32(26).fork()).join();
    }
    if (message.targetValueMax !== undefined) {
      DataReferenceNumber.encode(message.targetValueMax, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LinearTransform {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLinearTransform();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.orgValueMin = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.orgValueMax = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.targetValueMin = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.targetValueMax = DataReferenceNumber.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LinearTransform {
    return {
      orgValueMin: isSet(object.orgValueMin) ? DataReferenceNumber.fromJSON(object.orgValueMin) : undefined,
      orgValueMax: isSet(object.orgValueMax) ? DataReferenceNumber.fromJSON(object.orgValueMax) : undefined,
      targetValueMin: isSet(object.targetValueMin) ? DataReferenceNumber.fromJSON(object.targetValueMin) : undefined,
      targetValueMax: isSet(object.targetValueMax) ? DataReferenceNumber.fromJSON(object.targetValueMax) : undefined,
    };
  },

  toJSON(message: LinearTransform): unknown {
    const obj: any = {};
    if (message.orgValueMin !== undefined) {
      obj.orgValueMin = DataReferenceNumber.toJSON(message.orgValueMin);
    }
    if (message.orgValueMax !== undefined) {
      obj.orgValueMax = DataReferenceNumber.toJSON(message.orgValueMax);
    }
    if (message.targetValueMin !== undefined) {
      obj.targetValueMin = DataReferenceNumber.toJSON(message.targetValueMin);
    }
    if (message.targetValueMax !== undefined) {
      obj.targetValueMax = DataReferenceNumber.toJSON(message.targetValueMax);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LinearTransform>, I>>(base?: I): LinearTransform {
    return LinearTransform.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LinearTransform>, I>>(object: I): LinearTransform {
    const message = createBaseLinearTransform();
    message.orgValueMin = (object.orgValueMin !== undefined && object.orgValueMin !== null)
      ? DataReferenceNumber.fromPartial(object.orgValueMin)
      : undefined;
    message.orgValueMax = (object.orgValueMax !== undefined && object.orgValueMax !== null)
      ? DataReferenceNumber.fromPartial(object.orgValueMax)
      : undefined;
    message.targetValueMin = (object.targetValueMin !== undefined && object.targetValueMin !== null)
      ? DataReferenceNumber.fromPartial(object.targetValueMin)
      : undefined;
    message.targetValueMax = (object.targetValueMax !== undefined && object.targetValueMax !== null)
      ? DataReferenceNumber.fromPartial(object.targetValueMax)
      : undefined;
    return message;
  },
};

function createBaseNumberValue(): NumberValue {
  return { from: undefined };
}

export const NumberValue: MessageFns<NumberValue> = {
  encode(message: NumberValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "valueI8":
        writer.uint32(8).int32(message.from.value);
        break;
      case "valueU8":
        writer.uint32(16).uint32(message.from.value);
        break;
      case "valueI16":
        writer.uint32(24).int32(message.from.value);
        break;
      case "valueU16":
        writer.uint32(32).uint32(message.from.value);
        break;
      case "valueI32":
        writer.uint32(40).int32(message.from.value);
        break;
      case "valueU32":
        writer.uint32(48).uint32(message.from.value);
        break;
      case "valueI64":
        writer.uint32(56).int64(message.from.value);
        break;
      case "valueU64":
        writer.uint32(64).uint64(message.from.value);
        break;
      case "valueFloat":
        writer.uint32(77).float(message.from.value);
        break;
      case "valueDouble":
        writer.uint32(81).double(message.from.value);
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NumberValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNumberValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.from = { $case: "valueI8", value: reader.int32() };
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.from = { $case: "valueU8", value: reader.uint32() };
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.from = { $case: "valueI16", value: reader.int32() };
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.from = { $case: "valueU16", value: reader.uint32() };
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.from = { $case: "valueI32", value: reader.int32() };
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.from = { $case: "valueU32", value: reader.uint32() };
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.from = { $case: "valueI64", value: longToNumber(reader.int64()) };
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.from = { $case: "valueU64", value: longToNumber(reader.uint64()) };
          continue;
        }
        case 9: {
          if (tag !== 77) {
            break;
          }

          message.from = { $case: "valueFloat", value: reader.float() };
          continue;
        }
        case 10: {
          if (tag !== 81) {
            break;
          }

          message.from = { $case: "valueDouble", value: reader.double() };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NumberValue {
    return {
      from: isSet(object.valueI8)
        ? { $case: "valueI8", value: globalThis.Number(object.valueI8) }
        : isSet(object.valueU8)
        ? { $case: "valueU8", value: globalThis.Number(object.valueU8) }
        : isSet(object.valueI16)
        ? { $case: "valueI16", value: globalThis.Number(object.valueI16) }
        : isSet(object.valueU16)
        ? { $case: "valueU16", value: globalThis.Number(object.valueU16) }
        : isSet(object.valueI32)
        ? { $case: "valueI32", value: globalThis.Number(object.valueI32) }
        : isSet(object.valueU32)
        ? { $case: "valueU32", value: globalThis.Number(object.valueU32) }
        : isSet(object.valueI64)
        ? { $case: "valueI64", value: globalThis.Number(object.valueI64) }
        : isSet(object.valueU64)
        ? { $case: "valueU64", value: globalThis.Number(object.valueU64) }
        : isSet(object.valueFloat)
        ? { $case: "valueFloat", value: globalThis.Number(object.valueFloat) }
        : isSet(object.valueDouble)
        ? { $case: "valueDouble", value: globalThis.Number(object.valueDouble) }
        : undefined,
    };
  },

  toJSON(message: NumberValue): unknown {
    const obj: any = {};
    if (message.from?.$case === "valueI8") {
      obj.valueI8 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueU8") {
      obj.valueU8 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueI16") {
      obj.valueI16 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueU16") {
      obj.valueU16 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueI32") {
      obj.valueI32 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueU32") {
      obj.valueU32 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueI64") {
      obj.valueI64 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueU64") {
      obj.valueU64 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueFloat") {
      obj.valueFloat = message.from.value;
    } else if (message.from?.$case === "valueDouble") {
      obj.valueDouble = message.from.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NumberValue>, I>>(base?: I): NumberValue {
    return NumberValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NumberValue>, I>>(object: I): NumberValue {
    const message = createBaseNumberValue();
    switch (object.from?.$case) {
      case "valueI8": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueI8", value: object.from.value };
        }
        break;
      }
      case "valueU8": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueU8", value: object.from.value };
        }
        break;
      }
      case "valueI16": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueI16", value: object.from.value };
        }
        break;
      }
      case "valueU16": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueU16", value: object.from.value };
        }
        break;
      }
      case "valueI32": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueI32", value: object.from.value };
        }
        break;
      }
      case "valueU32": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueU32", value: object.from.value };
        }
        break;
      }
      case "valueI64": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueI64", value: object.from.value };
        }
        break;
      }
      case "valueU64": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueU64", value: object.from.value };
        }
        break;
      }
      case "valueFloat": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueFloat", value: object.from.value };
        }
        break;
      }
      case "valueDouble": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueDouble", value: object.from.value };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseAllTypeValue(): AllTypeValue {
  return { from: undefined, dataFormat: undefined };
}

export const AllTypeValue: MessageFns<AllTypeValue> = {
  encode(message: AllTypeValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.from?.$case) {
      case "valueI8":
        writer.uint32(8).int32(message.from.value);
        break;
      case "valueU8":
        writer.uint32(16).uint32(message.from.value);
        break;
      case "valueI16":
        writer.uint32(24).int32(message.from.value);
        break;
      case "valueU16":
        writer.uint32(32).uint32(message.from.value);
        break;
      case "valueI32":
        writer.uint32(40).int32(message.from.value);
        break;
      case "valueU32":
        writer.uint32(48).uint32(message.from.value);
        break;
      case "valueI64":
        writer.uint32(56).int64(message.from.value);
        break;
      case "valueU64":
        writer.uint32(64).uint64(message.from.value);
        break;
      case "valueFloat":
        writer.uint32(77).float(message.from.value);
        break;
      case "valueDouble":
        writer.uint32(81).double(message.from.value);
        break;
      case "valueBool":
        writer.uint32(88).bool(message.from.value);
        break;
      case "valueString":
        writer.uint32(98).string(message.from.value);
        break;
    }
    if (message.dataFormat !== undefined) {
      writer.uint32(104).int32(message.dataFormat);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AllTypeValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAllTypeValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.from = { $case: "valueI8", value: reader.int32() };
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.from = { $case: "valueU8", value: reader.uint32() };
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.from = { $case: "valueI16", value: reader.int32() };
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.from = { $case: "valueU16", value: reader.uint32() };
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.from = { $case: "valueI32", value: reader.int32() };
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.from = { $case: "valueU32", value: reader.uint32() };
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.from = { $case: "valueI64", value: longToNumber(reader.int64()) };
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.from = { $case: "valueU64", value: longToNumber(reader.uint64()) };
          continue;
        }
        case 9: {
          if (tag !== 77) {
            break;
          }

          message.from = { $case: "valueFloat", value: reader.float() };
          continue;
        }
        case 10: {
          if (tag !== 81) {
            break;
          }

          message.from = { $case: "valueDouble", value: reader.double() };
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.from = { $case: "valueBool", value: reader.bool() };
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.from = { $case: "valueString", value: reader.string() };
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.dataFormat = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AllTypeValue {
    return {
      from: isSet(object.valueI8)
        ? { $case: "valueI8", value: globalThis.Number(object.valueI8) }
        : isSet(object.valueU8)
        ? { $case: "valueU8", value: globalThis.Number(object.valueU8) }
        : isSet(object.valueI16)
        ? { $case: "valueI16", value: globalThis.Number(object.valueI16) }
        : isSet(object.valueU16)
        ? { $case: "valueU16", value: globalThis.Number(object.valueU16) }
        : isSet(object.valueI32)
        ? { $case: "valueI32", value: globalThis.Number(object.valueI32) }
        : isSet(object.valueU32)
        ? { $case: "valueU32", value: globalThis.Number(object.valueU32) }
        : isSet(object.valueI64)
        ? { $case: "valueI64", value: globalThis.Number(object.valueI64) }
        : isSet(object.valueU64)
        ? { $case: "valueU64", value: globalThis.Number(object.valueU64) }
        : isSet(object.valueFloat)
        ? { $case: "valueFloat", value: globalThis.Number(object.valueFloat) }
        : isSet(object.valueDouble)
        ? { $case: "valueDouble", value: globalThis.Number(object.valueDouble) }
        : isSet(object.valueBool)
        ? { $case: "valueBool", value: globalThis.Boolean(object.valueBool) }
        : isSet(object.valueString)
        ? { $case: "valueString", value: globalThis.String(object.valueString) }
        : undefined,
      dataFormat: isSet(object.dataFormat) ? dataFormatTypeFromJSON(object.dataFormat) : undefined,
    };
  },

  toJSON(message: AllTypeValue): unknown {
    const obj: any = {};
    if (message.from?.$case === "valueI8") {
      obj.valueI8 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueU8") {
      obj.valueU8 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueI16") {
      obj.valueI16 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueU16") {
      obj.valueU16 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueI32") {
      obj.valueI32 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueU32") {
      obj.valueU32 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueI64") {
      obj.valueI64 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueU64") {
      obj.valueU64 = Math.round(message.from.value);
    } else if (message.from?.$case === "valueFloat") {
      obj.valueFloat = message.from.value;
    } else if (message.from?.$case === "valueDouble") {
      obj.valueDouble = message.from.value;
    } else if (message.from?.$case === "valueBool") {
      obj.valueBool = message.from.value;
    } else if (message.from?.$case === "valueString") {
      obj.valueString = message.from.value;
    }
    if (message.dataFormat !== undefined) {
      obj.dataFormat = dataFormatTypeToJSON(message.dataFormat);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AllTypeValue>, I>>(base?: I): AllTypeValue {
    return AllTypeValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AllTypeValue>, I>>(object: I): AllTypeValue {
    const message = createBaseAllTypeValue();
    switch (object.from?.$case) {
      case "valueI8": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueI8", value: object.from.value };
        }
        break;
      }
      case "valueU8": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueU8", value: object.from.value };
        }
        break;
      }
      case "valueI16": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueI16", value: object.from.value };
        }
        break;
      }
      case "valueU16": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueU16", value: object.from.value };
        }
        break;
      }
      case "valueI32": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueI32", value: object.from.value };
        }
        break;
      }
      case "valueU32": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueU32", value: object.from.value };
        }
        break;
      }
      case "valueI64": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueI64", value: object.from.value };
        }
        break;
      }
      case "valueU64": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueU64", value: object.from.value };
        }
        break;
      }
      case "valueFloat": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueFloat", value: object.from.value };
        }
        break;
      }
      case "valueDouble": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueDouble", value: object.from.value };
        }
        break;
      }
      case "valueBool": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueBool", value: object.from.value };
        }
        break;
      }
      case "valueString": {
        if (object.from?.value !== undefined && object.from?.value !== null) {
          message.from = { $case: "valueString", value: object.from.value };
        }
        break;
      }
    }
    message.dataFormat = object.dataFormat ?? undefined;
    return message;
  },
};

function createBaseVariableTrigger(): VariableTrigger {
  return { variableBool: undefined, triggerType: 0, autoReset: false };
}

export const VariableTrigger: MessageFns<VariableTrigger> = {
  encode(message: VariableTrigger, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.variableBool !== undefined) {
      VariableReference.encode(message.variableBool, writer.uint32(10).fork()).join();
    }
    if (message.triggerType !== 0) {
      writer.uint32(16).int32(message.triggerType);
    }
    if (message.autoReset !== false) {
      writer.uint32(24).bool(message.autoReset);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VariableTrigger {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVariableTrigger();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.variableBool = VariableReference.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.triggerType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.autoReset = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VariableTrigger {
    return {
      variableBool: isSet(object.variableBool) ? VariableReference.fromJSON(object.variableBool) : undefined,
      triggerType: isSet(object.triggerType) ? variableTrigger_TypeFromJSON(object.triggerType) : 0,
      autoReset: isSet(object.autoReset) ? globalThis.Boolean(object.autoReset) : false,
    };
  },

  toJSON(message: VariableTrigger): unknown {
    const obj: any = {};
    if (message.variableBool !== undefined) {
      obj.variableBool = VariableReference.toJSON(message.variableBool);
    }
    if (message.triggerType !== 0) {
      obj.triggerType = variableTrigger_TypeToJSON(message.triggerType);
    }
    if (message.autoReset !== false) {
      obj.autoReset = message.autoReset;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VariableTrigger>, I>>(base?: I): VariableTrigger {
    return VariableTrigger.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VariableTrigger>, I>>(object: I): VariableTrigger {
    const message = createBaseVariableTrigger();
    message.variableBool = (object.variableBool !== undefined && object.variableBool !== null)
      ? VariableReference.fromPartial(object.variableBool)
      : undefined;
    message.triggerType = object.triggerType ?? 0;
    message.autoReset = object.autoReset ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
