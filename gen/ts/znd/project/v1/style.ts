// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/style.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  AlignType,
  alignTypeFromJSON,
  alignTypeToJSON,
  PositionOffset,
  ScrollDirection,
  scrollDirectionFromJSON,
  scrollDirectionToJSON,
} from "./common";

export const protobufPackage = "znd.project.v1";

/** 语言类型 */
export enum LanguageType {
  LANGUAGE_TYPE_UNSPECIFIED = 0,
  /** LANGUAGE_TYPE_ENGLISH - 英语 */
  LANGUAGE_TYPE_ENGLISH = 1,
  /** LANGUAGE_TYPE_ZH_CN - 简体中文 */
  LANGUAGE_TYPE_ZH_CN = 2,
  /** LANGUAGE_TYPE_ZH_TW - 繁体中文 */
  LANGUAGE_TYPE_ZH_TW = 3,
  /** LANGUAGE_TYPE_JAPANESE - 日语 */
  LANGUAGE_TYPE_JAPANESE = 4,
  /** LANGUAGE_TYPE_KOREAN - 韩语 */
  LANGUAGE_TYPE_KOREAN = 5,
  /** LANGUAGE_TYPE_FRENCH - 法语 */
  LANGUAGE_TYPE_FRENCH = 6,
  /** LANGUAGE_TYPE_GERMAN - 德语 */
  LANGUAGE_TYPE_GERMAN = 7,
  /** LANGUAGE_TYPE_ITALIAN - 意大利语 */
  LANGUAGE_TYPE_ITALIAN = 8,
  /** LANGUAGE_TYPE_SPANISH - 西班牙语 */
  LANGUAGE_TYPE_SPANISH = 9,
  /** LANGUAGE_TYPE_PORTUGUESE - 葡萄牙语 */
  LANGUAGE_TYPE_PORTUGUESE = 10,
  /** LANGUAGE_TYPE_ARABIC - 阿拉伯语 */
  LANGUAGE_TYPE_ARABIC = 11,
  /** LANGUAGE_TYPE_RUSSIAN - 俄语 */
  LANGUAGE_TYPE_RUSSIAN = 12,
  /** LANGUAGE_TYPE_VIETNAMESE - 越南语 */
  LANGUAGE_TYPE_VIETNAMESE = 13,
  /** LANGUAGE_TYPE_THAI - 泰语 */
  LANGUAGE_TYPE_THAI = 14,
  /** LANGUAGE_TYPE_POLISH - 波兰语 */
  LANGUAGE_TYPE_POLISH = 15,
  /** LANGUAGE_TYPE_DUTCH - 荷兰语 */
  LANGUAGE_TYPE_DUTCH = 16,
  /** LANGUAGE_TYPE_SWEDISH - 瑞典语 */
  LANGUAGE_TYPE_SWEDISH = 17,
  /** LANGUAGE_TYPE_TURKISH - 土耳其语 */
  LANGUAGE_TYPE_TURKISH = 18,
  /** LANGUAGE_TYPE_SLOVENIAN - 斯洛文尼亚语 */
  LANGUAGE_TYPE_SLOVENIAN = 19,
  /** LANGUAGE_TYPE_CZECH - 捷克语 */
  LANGUAGE_TYPE_CZECH = 20,
  /** LANGUAGE_TYPE_HUNGARIAN - 匈牙利语 */
  LANGUAGE_TYPE_HUNGARIAN = 21,
  /** LANGUAGE_TYPE_ROMANIAN - 罗马尼亚语 */
  LANGUAGE_TYPE_ROMANIAN = 22,
  /** LANGUAGE_TYPE_BULGARIAN - 保加利亚语 */
  LANGUAGE_TYPE_BULGARIAN = 23,
  /** LANGUAGE_TYPE_DANISH - 丹麦语 */
  LANGUAGE_TYPE_DANISH = 24,
  /** LANGUAGE_TYPE_ESTONIAN - 爱沙尼亚语 */
  LANGUAGE_TYPE_ESTONIAN = 25,
  /** LANGUAGE_TYPE_FINNISH - 芬兰语 */
  LANGUAGE_TYPE_FINNISH = 26,
  /** LANGUAGE_TYPE_GREEK - 希腊语 */
  LANGUAGE_TYPE_GREEK = 27,
  UNRECOGNIZED = -1,
}

export function languageTypeFromJSON(object: any): LanguageType {
  switch (object) {
    case 0:
    case "LANGUAGE_TYPE_UNSPECIFIED":
      return LanguageType.LANGUAGE_TYPE_UNSPECIFIED;
    case 1:
    case "LANGUAGE_TYPE_ENGLISH":
      return LanguageType.LANGUAGE_TYPE_ENGLISH;
    case 2:
    case "LANGUAGE_TYPE_ZH_CN":
      return LanguageType.LANGUAGE_TYPE_ZH_CN;
    case 3:
    case "LANGUAGE_TYPE_ZH_TW":
      return LanguageType.LANGUAGE_TYPE_ZH_TW;
    case 4:
    case "LANGUAGE_TYPE_JAPANESE":
      return LanguageType.LANGUAGE_TYPE_JAPANESE;
    case 5:
    case "LANGUAGE_TYPE_KOREAN":
      return LanguageType.LANGUAGE_TYPE_KOREAN;
    case 6:
    case "LANGUAGE_TYPE_FRENCH":
      return LanguageType.LANGUAGE_TYPE_FRENCH;
    case 7:
    case "LANGUAGE_TYPE_GERMAN":
      return LanguageType.LANGUAGE_TYPE_GERMAN;
    case 8:
    case "LANGUAGE_TYPE_ITALIAN":
      return LanguageType.LANGUAGE_TYPE_ITALIAN;
    case 9:
    case "LANGUAGE_TYPE_SPANISH":
      return LanguageType.LANGUAGE_TYPE_SPANISH;
    case 10:
    case "LANGUAGE_TYPE_PORTUGUESE":
      return LanguageType.LANGUAGE_TYPE_PORTUGUESE;
    case 11:
    case "LANGUAGE_TYPE_ARABIC":
      return LanguageType.LANGUAGE_TYPE_ARABIC;
    case 12:
    case "LANGUAGE_TYPE_RUSSIAN":
      return LanguageType.LANGUAGE_TYPE_RUSSIAN;
    case 13:
    case "LANGUAGE_TYPE_VIETNAMESE":
      return LanguageType.LANGUAGE_TYPE_VIETNAMESE;
    case 14:
    case "LANGUAGE_TYPE_THAI":
      return LanguageType.LANGUAGE_TYPE_THAI;
    case 15:
    case "LANGUAGE_TYPE_POLISH":
      return LanguageType.LANGUAGE_TYPE_POLISH;
    case 16:
    case "LANGUAGE_TYPE_DUTCH":
      return LanguageType.LANGUAGE_TYPE_DUTCH;
    case 17:
    case "LANGUAGE_TYPE_SWEDISH":
      return LanguageType.LANGUAGE_TYPE_SWEDISH;
    case 18:
    case "LANGUAGE_TYPE_TURKISH":
      return LanguageType.LANGUAGE_TYPE_TURKISH;
    case 19:
    case "LANGUAGE_TYPE_SLOVENIAN":
      return LanguageType.LANGUAGE_TYPE_SLOVENIAN;
    case 20:
    case "LANGUAGE_TYPE_CZECH":
      return LanguageType.LANGUAGE_TYPE_CZECH;
    case 21:
    case "LANGUAGE_TYPE_HUNGARIAN":
      return LanguageType.LANGUAGE_TYPE_HUNGARIAN;
    case 22:
    case "LANGUAGE_TYPE_ROMANIAN":
      return LanguageType.LANGUAGE_TYPE_ROMANIAN;
    case 23:
    case "LANGUAGE_TYPE_BULGARIAN":
      return LanguageType.LANGUAGE_TYPE_BULGARIAN;
    case 24:
    case "LANGUAGE_TYPE_DANISH":
      return LanguageType.LANGUAGE_TYPE_DANISH;
    case 25:
    case "LANGUAGE_TYPE_ESTONIAN":
      return LanguageType.LANGUAGE_TYPE_ESTONIAN;
    case 26:
    case "LANGUAGE_TYPE_FINNISH":
      return LanguageType.LANGUAGE_TYPE_FINNISH;
    case 27:
    case "LANGUAGE_TYPE_GREEK":
      return LanguageType.LANGUAGE_TYPE_GREEK;
    case -1:
    case "UNRECOGNIZED":
    default:
      return LanguageType.UNRECOGNIZED;
  }
}

export function languageTypeToJSON(object: LanguageType): string {
  switch (object) {
    case LanguageType.LANGUAGE_TYPE_UNSPECIFIED:
      return "LANGUAGE_TYPE_UNSPECIFIED";
    case LanguageType.LANGUAGE_TYPE_ENGLISH:
      return "LANGUAGE_TYPE_ENGLISH";
    case LanguageType.LANGUAGE_TYPE_ZH_CN:
      return "LANGUAGE_TYPE_ZH_CN";
    case LanguageType.LANGUAGE_TYPE_ZH_TW:
      return "LANGUAGE_TYPE_ZH_TW";
    case LanguageType.LANGUAGE_TYPE_JAPANESE:
      return "LANGUAGE_TYPE_JAPANESE";
    case LanguageType.LANGUAGE_TYPE_KOREAN:
      return "LANGUAGE_TYPE_KOREAN";
    case LanguageType.LANGUAGE_TYPE_FRENCH:
      return "LANGUAGE_TYPE_FRENCH";
    case LanguageType.LANGUAGE_TYPE_GERMAN:
      return "LANGUAGE_TYPE_GERMAN";
    case LanguageType.LANGUAGE_TYPE_ITALIAN:
      return "LANGUAGE_TYPE_ITALIAN";
    case LanguageType.LANGUAGE_TYPE_SPANISH:
      return "LANGUAGE_TYPE_SPANISH";
    case LanguageType.LANGUAGE_TYPE_PORTUGUESE:
      return "LANGUAGE_TYPE_PORTUGUESE";
    case LanguageType.LANGUAGE_TYPE_ARABIC:
      return "LANGUAGE_TYPE_ARABIC";
    case LanguageType.LANGUAGE_TYPE_RUSSIAN:
      return "LANGUAGE_TYPE_RUSSIAN";
    case LanguageType.LANGUAGE_TYPE_VIETNAMESE:
      return "LANGUAGE_TYPE_VIETNAMESE";
    case LanguageType.LANGUAGE_TYPE_THAI:
      return "LANGUAGE_TYPE_THAI";
    case LanguageType.LANGUAGE_TYPE_POLISH:
      return "LANGUAGE_TYPE_POLISH";
    case LanguageType.LANGUAGE_TYPE_DUTCH:
      return "LANGUAGE_TYPE_DUTCH";
    case LanguageType.LANGUAGE_TYPE_SWEDISH:
      return "LANGUAGE_TYPE_SWEDISH";
    case LanguageType.LANGUAGE_TYPE_TURKISH:
      return "LANGUAGE_TYPE_TURKISH";
    case LanguageType.LANGUAGE_TYPE_SLOVENIAN:
      return "LANGUAGE_TYPE_SLOVENIAN";
    case LanguageType.LANGUAGE_TYPE_CZECH:
      return "LANGUAGE_TYPE_CZECH";
    case LanguageType.LANGUAGE_TYPE_HUNGARIAN:
      return "LANGUAGE_TYPE_HUNGARIAN";
    case LanguageType.LANGUAGE_TYPE_ROMANIAN:
      return "LANGUAGE_TYPE_ROMANIAN";
    case LanguageType.LANGUAGE_TYPE_BULGARIAN:
      return "LANGUAGE_TYPE_BULGARIAN";
    case LanguageType.LANGUAGE_TYPE_DANISH:
      return "LANGUAGE_TYPE_DANISH";
    case LanguageType.LANGUAGE_TYPE_ESTONIAN:
      return "LANGUAGE_TYPE_ESTONIAN";
    case LanguageType.LANGUAGE_TYPE_FINNISH:
      return "LANGUAGE_TYPE_FINNISH";
    case LanguageType.LANGUAGE_TYPE_GREEK:
      return "LANGUAGE_TYPE_GREEK";
    case LanguageType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 字体文件类型 */
export enum FontFileType {
  FONT_FILE_TYPE_UNSPECIFIED = 0,
  FONT_FILE_TYPE_TTF = 1,
  FONT_FILE_TYPE_OTF = 2,
  FONT_FILE_TYPE_WOFF = 3,
  FONT_FILE_TYPE_WOFF2 = 4,
  UNRECOGNIZED = -1,
}

export function fontFileTypeFromJSON(object: any): FontFileType {
  switch (object) {
    case 0:
    case "FONT_FILE_TYPE_UNSPECIFIED":
      return FontFileType.FONT_FILE_TYPE_UNSPECIFIED;
    case 1:
    case "FONT_FILE_TYPE_TTF":
      return FontFileType.FONT_FILE_TYPE_TTF;
    case 2:
    case "FONT_FILE_TYPE_OTF":
      return FontFileType.FONT_FILE_TYPE_OTF;
    case 3:
    case "FONT_FILE_TYPE_WOFF":
      return FontFileType.FONT_FILE_TYPE_WOFF;
    case 4:
    case "FONT_FILE_TYPE_WOFF2":
      return FontFileType.FONT_FILE_TYPE_WOFF2;
    case -1:
    case "UNRECOGNIZED":
    default:
      return FontFileType.UNRECOGNIZED;
  }
}

export function fontFileTypeToJSON(object: FontFileType): string {
  switch (object) {
    case FontFileType.FONT_FILE_TYPE_UNSPECIFIED:
      return "FONT_FILE_TYPE_UNSPECIFIED";
    case FontFileType.FONT_FILE_TYPE_TTF:
      return "FONT_FILE_TYPE_TTF";
    case FontFileType.FONT_FILE_TYPE_OTF:
      return "FONT_FILE_TYPE_OTF";
    case FontFileType.FONT_FILE_TYPE_WOFF:
      return "FONT_FILE_TYPE_WOFF";
    case FontFileType.FONT_FILE_TYPE_WOFF2:
      return "FONT_FILE_TYPE_WOFF2";
    case FontFileType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/**
 * 字体样式(简化掉删除线)
 * 以下对应的数字和顺序不能变,因为代码中会用这些数字进行位运算以快速知道是否包含哪种样式
 */
export enum FontStyle {
  /** FONT_STYLE_UNSPECIFIED - 正常(0000) */
  FONT_STYLE_UNSPECIFIED = 0,
  /** FONT_STYLE_BOLD - 粗体(0001) */
  FONT_STYLE_BOLD = 1,
  /** FONT_STYLE_ITALIC - 斜体(0010) */
  FONT_STYLE_ITALIC = 2,
  /** FONT_STYLE_BOLD_ITALIC - 粗+斜体(0011) */
  FONT_STYLE_BOLD_ITALIC = 3,
  /** FONT_STYLE_UNDERLINE - 下划线(0100) */
  FONT_STYLE_UNDERLINE = 4,
  /** FONT_STYLE_BOLD_UNDERLINE - 粗+下划线(0101) */
  FONT_STYLE_BOLD_UNDERLINE = 5,
  /** FONT_STYLE_ITALIC_UNDERLINE - 斜+下划线(0110) */
  FONT_STYLE_ITALIC_UNDERLINE = 6,
  /** FONT_STYLE_BOLD_ITALIC_UNDERLINE - 粗+斜+下划线(0111) */
  FONT_STYLE_BOLD_ITALIC_UNDERLINE = 7,
  UNRECOGNIZED = -1,
}

export function fontStyleFromJSON(object: any): FontStyle {
  switch (object) {
    case 0:
    case "FONT_STYLE_UNSPECIFIED":
      return FontStyle.FONT_STYLE_UNSPECIFIED;
    case 1:
    case "FONT_STYLE_BOLD":
      return FontStyle.FONT_STYLE_BOLD;
    case 2:
    case "FONT_STYLE_ITALIC":
      return FontStyle.FONT_STYLE_ITALIC;
    case 3:
    case "FONT_STYLE_BOLD_ITALIC":
      return FontStyle.FONT_STYLE_BOLD_ITALIC;
    case 4:
    case "FONT_STYLE_UNDERLINE":
      return FontStyle.FONT_STYLE_UNDERLINE;
    case 5:
    case "FONT_STYLE_BOLD_UNDERLINE":
      return FontStyle.FONT_STYLE_BOLD_UNDERLINE;
    case 6:
    case "FONT_STYLE_ITALIC_UNDERLINE":
      return FontStyle.FONT_STYLE_ITALIC_UNDERLINE;
    case 7:
    case "FONT_STYLE_BOLD_ITALIC_UNDERLINE":
      return FontStyle.FONT_STYLE_BOLD_ITALIC_UNDERLINE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return FontStyle.UNRECOGNIZED;
  }
}

export function fontStyleToJSON(object: FontStyle): string {
  switch (object) {
    case FontStyle.FONT_STYLE_UNSPECIFIED:
      return "FONT_STYLE_UNSPECIFIED";
    case FontStyle.FONT_STYLE_BOLD:
      return "FONT_STYLE_BOLD";
    case FontStyle.FONT_STYLE_ITALIC:
      return "FONT_STYLE_ITALIC";
    case FontStyle.FONT_STYLE_BOLD_ITALIC:
      return "FONT_STYLE_BOLD_ITALIC";
    case FontStyle.FONT_STYLE_UNDERLINE:
      return "FONT_STYLE_UNDERLINE";
    case FontStyle.FONT_STYLE_BOLD_UNDERLINE:
      return "FONT_STYLE_BOLD_UNDERLINE";
    case FontStyle.FONT_STYLE_ITALIC_UNDERLINE:
      return "FONT_STYLE_ITALIC_UNDERLINE";
    case FontStyle.FONT_STYLE_BOLD_ITALIC_UNDERLINE:
      return "FONT_STYLE_BOLD_ITALIC_UNDERLINE";
    case FontStyle.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 渐变方向 */
export enum GradientDirection {
  GRADIENT_DIRECTION_UNSPECIFIED = 0,
  /** GRADIENT_DIRECTION_HORIZONTAL - 水平 */
  GRADIENT_DIRECTION_HORIZONTAL = 1,
  /** GRADIENT_DIRECTION_VERTICAL - 垂直 */
  GRADIENT_DIRECTION_VERTICAL = 2,
  UNRECOGNIZED = -1,
}

export function gradientDirectionFromJSON(object: any): GradientDirection {
  switch (object) {
    case 0:
    case "GRADIENT_DIRECTION_UNSPECIFIED":
      return GradientDirection.GRADIENT_DIRECTION_UNSPECIFIED;
    case 1:
    case "GRADIENT_DIRECTION_HORIZONTAL":
      return GradientDirection.GRADIENT_DIRECTION_HORIZONTAL;
    case 2:
    case "GRADIENT_DIRECTION_VERTICAL":
      return GradientDirection.GRADIENT_DIRECTION_VERTICAL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GradientDirection.UNRECOGNIZED;
  }
}

export function gradientDirectionToJSON(object: GradientDirection): string {
  switch (object) {
    case GradientDirection.GRADIENT_DIRECTION_UNSPECIFIED:
      return "GRADIENT_DIRECTION_UNSPECIFIED";
    case GradientDirection.GRADIENT_DIRECTION_HORIZONTAL:
      return "GRADIENT_DIRECTION_HORIZONTAL";
    case GradientDirection.GRADIENT_DIRECTION_VERTICAL:
      return "GRADIENT_DIRECTION_VERTICAL";
    case GradientDirection.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum GraphicType {
  GRAPHIC_TYPE_UNSPECIFIED = 0,
  /** GRAPHIC_TYPE_IMAGE - 图片 */
  GRAPHIC_TYPE_IMAGE = 1,
  /** GRAPHIC_TYPE_ICON_FONT_AWESOME - font-awesome图标 */
  GRAPHIC_TYPE_ICON_FONT_AWESOME = 2,
  UNRECOGNIZED = -1,
}

export function graphicTypeFromJSON(object: any): GraphicType {
  switch (object) {
    case 0:
    case "GRAPHIC_TYPE_UNSPECIFIED":
      return GraphicType.GRAPHIC_TYPE_UNSPECIFIED;
    case 1:
    case "GRAPHIC_TYPE_IMAGE":
      return GraphicType.GRAPHIC_TYPE_IMAGE;
    case 2:
    case "GRAPHIC_TYPE_ICON_FONT_AWESOME":
      return GraphicType.GRAPHIC_TYPE_ICON_FONT_AWESOME;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GraphicType.UNRECOGNIZED;
  }
}

export function graphicTypeToJSON(object: GraphicType): string {
  switch (object) {
    case GraphicType.GRAPHIC_TYPE_UNSPECIFIED:
      return "GRAPHIC_TYPE_UNSPECIFIED";
    case GraphicType.GRAPHIC_TYPE_IMAGE:
      return "GRAPHIC_TYPE_IMAGE";
    case GraphicType.GRAPHIC_TYPE_ICON_FONT_AWESOME:
      return "GRAPHIC_TYPE_ICON_FONT_AWESOME";
    case GraphicType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum GraphicTextLayout {
  GRAPHIC_TEXT_LAYOUT_UNSPECIFIED = 0,
  /** GRAPHIC_TEXT_LAYOUT_DEFAULT - 文字在上层,图片在下层 */
  GRAPHIC_TEXT_LAYOUT_DEFAULT = 1,
  /** GRAPHIC_TEXT_LAYOUT_HORIZONTAL - 文字在右边,图片在左边 */
  GRAPHIC_TEXT_LAYOUT_HORIZONTAL = 2,
  /** GRAPHIC_TEXT_LAYOUT_VERTICAL - 文字在下边,图片在上边 */
  GRAPHIC_TEXT_LAYOUT_VERTICAL = 3,
  /** GRAPHIC_TEXT_LAYOUT_ONLY_TEXT - 仅文字 */
  GRAPHIC_TEXT_LAYOUT_ONLY_TEXT = 4,
  /** GRAPHIC_TEXT_LAYOUT_ONLY_GRAPHIC - 仅图片 */
  GRAPHIC_TEXT_LAYOUT_ONLY_GRAPHIC = 5,
  UNRECOGNIZED = -1,
}

export function graphicTextLayoutFromJSON(object: any): GraphicTextLayout {
  switch (object) {
    case 0:
    case "GRAPHIC_TEXT_LAYOUT_UNSPECIFIED":
      return GraphicTextLayout.GRAPHIC_TEXT_LAYOUT_UNSPECIFIED;
    case 1:
    case "GRAPHIC_TEXT_LAYOUT_DEFAULT":
      return GraphicTextLayout.GRAPHIC_TEXT_LAYOUT_DEFAULT;
    case 2:
    case "GRAPHIC_TEXT_LAYOUT_HORIZONTAL":
      return GraphicTextLayout.GRAPHIC_TEXT_LAYOUT_HORIZONTAL;
    case 3:
    case "GRAPHIC_TEXT_LAYOUT_VERTICAL":
      return GraphicTextLayout.GRAPHIC_TEXT_LAYOUT_VERTICAL;
    case 4:
    case "GRAPHIC_TEXT_LAYOUT_ONLY_TEXT":
      return GraphicTextLayout.GRAPHIC_TEXT_LAYOUT_ONLY_TEXT;
    case 5:
    case "GRAPHIC_TEXT_LAYOUT_ONLY_GRAPHIC":
      return GraphicTextLayout.GRAPHIC_TEXT_LAYOUT_ONLY_GRAPHIC;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GraphicTextLayout.UNRECOGNIZED;
  }
}

export function graphicTextLayoutToJSON(object: GraphicTextLayout): string {
  switch (object) {
    case GraphicTextLayout.GRAPHIC_TEXT_LAYOUT_UNSPECIFIED:
      return "GRAPHIC_TEXT_LAYOUT_UNSPECIFIED";
    case GraphicTextLayout.GRAPHIC_TEXT_LAYOUT_DEFAULT:
      return "GRAPHIC_TEXT_LAYOUT_DEFAULT";
    case GraphicTextLayout.GRAPHIC_TEXT_LAYOUT_HORIZONTAL:
      return "GRAPHIC_TEXT_LAYOUT_HORIZONTAL";
    case GraphicTextLayout.GRAPHIC_TEXT_LAYOUT_VERTICAL:
      return "GRAPHIC_TEXT_LAYOUT_VERTICAL";
    case GraphicTextLayout.GRAPHIC_TEXT_LAYOUT_ONLY_TEXT:
      return "GRAPHIC_TEXT_LAYOUT_ONLY_TEXT";
    case GraphicTextLayout.GRAPHIC_TEXT_LAYOUT_ONLY_GRAPHIC:
      return "GRAPHIC_TEXT_LAYOUT_ONLY_GRAPHIC";
    case GraphicTextLayout.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 工程的语言和字体配置 */
export interface ProjectStyleConfig {
  /** 语言列表 */
  languages: Language[];
  /** 默认语言(语言列表数组下标,从0开始) */
  defaultLanguageNoU8: number;
  /** 主题数组,数组下标表示主题号(不超过255) */
  themes: Theme[];
  /** 默认主题,指的是themes数组的下标,默认为0 */
  defaultThemeNoU8: number;
  /** 颜色变量的存储(id不超过255个,用u8) */
  colorVariables: { [key: number]: ColorVariable };
  /** 主题和颜色变量值的对应(key用u16,key是用主题号+颜色变量id,高位主题号,低位颜色变量id) */
  themeColorValues: { [key: number]: number };
  /** key用u8,样式ID */
  styleTemplates: { [key: number]: StyleTemplate };
  /** 元件默认样式(设计端使用) */
  defaultStyleIdU8: number;
  /** 字体定义,key是字体id, uint8 */
  fontDefinitions: { [key: number]: FontDefinition };
  /** 默认字体名称(设计端使用) */
  defaultFontName: string;
  /** 默认样式属性 */
  defaultStyleProperties:
    | StyleProperties
    | undefined;
  /** 字体文件,key是字体文件ID(Uint8)，设计端用 */
  fontFiles: { [key: number]: Font };
  /** 给运行端用,字体对应的字体文件ID,key是uint16,用(FontDefinition的id+LanguageType)拼接,value是字体文件ID(Uint8) */
  fontFileMap: { [key: number]: number };
}

export interface ProjectStyleConfig_ColorVariablesEntry {
  key: number;
  value: ColorVariable | undefined;
}

export interface ProjectStyleConfig_ThemeColorValuesEntry {
  key: number;
  value: number;
}

export interface ProjectStyleConfig_StyleTemplatesEntry {
  key: number;
  value: StyleTemplate | undefined;
}

export interface ProjectStyleConfig_FontDefinitionsEntry {
  key: number;
  value: FontDefinition | undefined;
}

export interface ProjectStyleConfig_FontFilesEntry {
  key: number;
  value: Font | undefined;
}

export interface ProjectStyleConfig_FontFileMapEntry {
  key: number;
  value: number;
}

/** 语言 */
export interface Language {
  languageType: LanguageType;
  /** 默认字体名称 */
  defaultFontName: string;
}

/** 主题(主题号为ProjectStyleConfig中的themes数组下标) */
export interface Theme {
  name: string;
  memo: string;
}

/** 颜色变量 */
export interface ColorVariable {
  name: string;
  defaultColorU32: number;
  memo: string;
}

/**
 * 改为保存工程使用的字体名称,并给个id,工程中其他地方使用id
 * 考虑过用enum,但一是后续增加字体工作量较大,二是使用字体名称id也可以节省空间
 * 至于存在云端,改为用户使用时,自动从本地上传到云端(压缩,且先跟云端对比有没有),换电脑时可从云端下载
 */
export interface FontDefinition {
  /** 名称 */
  name: string;
  /**
   * oneof font {
   *     //所有语言一种字体
   *     Font single_lang = 2;
   *     //不同语言不同字体
   *     MultiLangFont multi_lang = 3;
   * }
   * 一定分语言，如果想不分语言，用同步字体
   */
  fonts: { [key: number]: Font };
  /** 备注 */
  memo: string;
  /** 相同字体名称 */
  sameFontName: boolean;
  /** 相同字号 */
  sameFontSize: boolean;
  /** 相同字型 */
  sameFontStyle: boolean;
  /** 禁用(设计端使用) */
  disabled: boolean;
}

export interface FontDefinition_FontsEntry {
  key: number;
  value: Font | undefined;
}

/** 字体 */
export interface Font {
  /** 字体名称,从字体文件中读取并显示给用户的 */
  fontName: string;
  /** 字号 */
  fontSize: number;
  /** 字型 */
  fontStyle: FontStyle;
}

/** 主题项图片 */
export interface GraphicReference {
  /** 图形类型,默认为IMAGE */
  graphicType: GraphicType;
  /** 图片id/图标编号 */
  src: number;
  /** 图片宽度/图标尺寸 */
  widthU16: number;
  /** 图片高度/图标尺寸 */
  heightU16: number;
  /** 透明度,无则表示未设置透明度,否则按0-255设置 */
  opacityU8?:
    | number
    | undefined;
  /** 重新着色(无值表示不会重新着色) */
  recolor?: ColorReference | undefined;
}

/** 颜色引用 */
export interface ColorReference {
  color?:
    | //
    /** 颜色变量id(不超过255,只用一个byte,0表示未设置) */
    { $case: "colorVarIdU8"; value: number }
    | //
    /** 颜色值RGBA */
    { $case: "colorValueU32"; value: number }
    | undefined;
}

/** 边框 */
export interface StyleBorder {
  /** 边框颜色 */
  color:
    | ColorReference
    | undefined;
  /** 边框宽度 */
  widthU8: number;
  /** 是否启用左侧 */
  left: boolean;
  /** 是否启用顶部 */
  top: boolean;
  /** 是否启用右侧 */
  right: boolean;
  /** 是否启用底部 */
  bottom: boolean;
  /** 是否启用内部线 */
  internal: boolean;
}

/** 阴影 */
export interface StyleShadow {
  /** 是否启用 */
  enable: boolean;
  /** 阴影颜色 */
  color:
    | ColorReference
    | undefined;
  /** 阴影x偏移量 */
  offsetXI16: number;
  /** 阴影y偏移量 */
  offsetYI16: number;
  /** 阴影扩散程度 */
  spreadI16: number;
  /** 阴影模糊程度 */
  widthI16: number;
}

/** 样式模板定义 */
export interface StyleTemplate {
  /** int32 id = 1; */
  name: string;
  properties: StyleProperties | undefined;
}

/** 样式(图片移到图元上) */
export interface StyleProperties {
  /** 样式id(空表示无样式继承) */
  styleIdU8?:
    | number
    | undefined;
  /** 背景色 */
  backgroundColor?:
    | ColorReference
    | undefined;
  /** 边框 */
  borderProps?:
    | StyleBorder
    | undefined;
  /** 阴影 */
  shadowProps?:
    | StyleShadow
    | undefined;
  /** 字体(把字体加回来，是从文本那边移除了，这样逻辑就可以统一了) */
  fontIdU8?:
    | number
    | undefined;
  /**
   * 文本位置(数组下标表示语言LanguageType,uint8)
   * 所有语言一样时,也会在不同语言中拷贝一样的设置,如果不存在,可能是新增的语言,则取默认语言的值,取不到默认语言则取遍历到的第1个
   */
  textLocation?:
    | MultiLangTextLocation
    | undefined;
  /** 文本颜色 */
  textColor?:
    | ColorReference
    | undefined;
  /** 闪烁间隔(100ms),0表示不闪烁 */
  blinkIntervalU8?:
    | number
    | undefined;
  /** 跑马灯效果 */
  marquee?: MarqueeDefine | undefined;
}

/** 文本位置属性 */
export interface TextLocation {
  /** 文本对齐方式(0也表示上下左右都居中) */
  textAlign: AlignType;
  /** 文本边距(没值表示全是0) */
  textPadding?: PositionOffset | undefined;
}

/** 多语言文本位置属性 */
export interface MultiLangTextLocation {
  /** key是LanguageType枚举值, uint8 */
  multiLang: { [key: number]: TextLocation };
}

export interface MultiLangTextLocation_MultiLangEntry {
  key: number;
  value: TextLocation | undefined;
}

/** 跑马灯定义 */
export interface MarqueeDefine {
  /** 是否启用 */
  enable: boolean;
  /** 每次滚动时间间隔(几个100ms) */
  intervalTimeU8: number;
  /** 每次滚动距离(px) */
  scrollDistanceU16: number;
  /** 滚动方向 */
  scrollDirection: ScrollDirection;
  /** 是否持续循环滚动 */
  loop: boolean;
}

function createBaseProjectStyleConfig(): ProjectStyleConfig {
  return {
    languages: [],
    defaultLanguageNoU8: 0,
    themes: [],
    defaultThemeNoU8: 0,
    colorVariables: {},
    themeColorValues: {},
    styleTemplates: {},
    defaultStyleIdU8: 0,
    fontDefinitions: {},
    defaultFontName: "",
    defaultStyleProperties: undefined,
    fontFiles: {},
    fontFileMap: {},
  };
}

export const ProjectStyleConfig: MessageFns<ProjectStyleConfig> = {
  encode(message: ProjectStyleConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.languages) {
      Language.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.defaultLanguageNoU8 !== 0) {
      writer.uint32(16).uint32(message.defaultLanguageNoU8);
    }
    for (const v of message.themes) {
      Theme.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.defaultThemeNoU8 !== 0) {
      writer.uint32(32).uint32(message.defaultThemeNoU8);
    }
    Object.entries(message.colorVariables).forEach(([key, value]) => {
      ProjectStyleConfig_ColorVariablesEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    Object.entries(message.themeColorValues).forEach(([key, value]) => {
      ProjectStyleConfig_ThemeColorValuesEntry.encode({ key: key as any, value }, writer.uint32(50).fork()).join();
    });
    Object.entries(message.styleTemplates).forEach(([key, value]) => {
      ProjectStyleConfig_StyleTemplatesEntry.encode({ key: key as any, value }, writer.uint32(58).fork()).join();
    });
    if (message.defaultStyleIdU8 !== 0) {
      writer.uint32(64).uint32(message.defaultStyleIdU8);
    }
    Object.entries(message.fontDefinitions).forEach(([key, value]) => {
      ProjectStyleConfig_FontDefinitionsEntry.encode({ key: key as any, value }, writer.uint32(74).fork()).join();
    });
    if (message.defaultFontName !== "") {
      writer.uint32(82).string(message.defaultFontName);
    }
    if (message.defaultStyleProperties !== undefined) {
      StyleProperties.encode(message.defaultStyleProperties, writer.uint32(90).fork()).join();
    }
    Object.entries(message.fontFiles).forEach(([key, value]) => {
      ProjectStyleConfig_FontFilesEntry.encode({ key: key as any, value }, writer.uint32(98).fork()).join();
    });
    Object.entries(message.fontFileMap).forEach(([key, value]) => {
      ProjectStyleConfig_FontFileMapEntry.encode({ key: key as any, value }, writer.uint32(106).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProjectStyleConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProjectStyleConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.languages.push(Language.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.defaultLanguageNoU8 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.themes.push(Theme.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.defaultThemeNoU8 = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = ProjectStyleConfig_ColorVariablesEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.colorVariables[entry5.key] = entry5.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          const entry6 = ProjectStyleConfig_ThemeColorValuesEntry.decode(reader, reader.uint32());
          if (entry6.value !== undefined) {
            message.themeColorValues[entry6.key] = entry6.value;
          }
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          const entry7 = ProjectStyleConfig_StyleTemplatesEntry.decode(reader, reader.uint32());
          if (entry7.value !== undefined) {
            message.styleTemplates[entry7.key] = entry7.value;
          }
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.defaultStyleIdU8 = reader.uint32();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          const entry9 = ProjectStyleConfig_FontDefinitionsEntry.decode(reader, reader.uint32());
          if (entry9.value !== undefined) {
            message.fontDefinitions[entry9.key] = entry9.value;
          }
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.defaultFontName = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.defaultStyleProperties = StyleProperties.decode(reader, reader.uint32());
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          const entry12 = ProjectStyleConfig_FontFilesEntry.decode(reader, reader.uint32());
          if (entry12.value !== undefined) {
            message.fontFiles[entry12.key] = entry12.value;
          }
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          const entry13 = ProjectStyleConfig_FontFileMapEntry.decode(reader, reader.uint32());
          if (entry13.value !== undefined) {
            message.fontFileMap[entry13.key] = entry13.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProjectStyleConfig {
    return {
      languages: globalThis.Array.isArray(object?.languages)
        ? object.languages.map((e: any) => Language.fromJSON(e))
        : [],
      defaultLanguageNoU8: isSet(object.defaultLanguageNoU8) ? globalThis.Number(object.defaultLanguageNoU8) : 0,
      themes: globalThis.Array.isArray(object?.themes) ? object.themes.map((e: any) => Theme.fromJSON(e)) : [],
      defaultThemeNoU8: isSet(object.defaultThemeNoU8) ? globalThis.Number(object.defaultThemeNoU8) : 0,
      colorVariables: isObject(object.colorVariables)
        ? Object.entries(object.colorVariables).reduce<{ [key: number]: ColorVariable }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = ColorVariable.fromJSON(value);
          return acc;
        }, {})
        : {},
      themeColorValues: isObject(object.themeColorValues)
        ? Object.entries(object.themeColorValues).reduce<{ [key: number]: number }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = Number(value);
          return acc;
        }, {})
        : {},
      styleTemplates: isObject(object.styleTemplates)
        ? Object.entries(object.styleTemplates).reduce<{ [key: number]: StyleTemplate }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = StyleTemplate.fromJSON(value);
          return acc;
        }, {})
        : {},
      defaultStyleIdU8: isSet(object.defaultStyleIdU8) ? globalThis.Number(object.defaultStyleIdU8) : 0,
      fontDefinitions: isObject(object.fontDefinitions)
        ? Object.entries(object.fontDefinitions).reduce<{ [key: number]: FontDefinition }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = FontDefinition.fromJSON(value);
          return acc;
        }, {})
        : {},
      defaultFontName: isSet(object.defaultFontName) ? globalThis.String(object.defaultFontName) : "",
      defaultStyleProperties: isSet(object.defaultStyleProperties)
        ? StyleProperties.fromJSON(object.defaultStyleProperties)
        : undefined,
      fontFiles: isObject(object.fontFiles)
        ? Object.entries(object.fontFiles).reduce<{ [key: number]: Font }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = Font.fromJSON(value);
          return acc;
        }, {})
        : {},
      fontFileMap: isObject(object.fontFileMap)
        ? Object.entries(object.fontFileMap).reduce<{ [key: number]: number }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = Number(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: ProjectStyleConfig): unknown {
    const obj: any = {};
    if (message.languages?.length) {
      obj.languages = message.languages.map((e) => Language.toJSON(e));
    }
    if (message.defaultLanguageNoU8 !== 0) {
      obj.defaultLanguageNoU8 = Math.round(message.defaultLanguageNoU8);
    }
    if (message.themes?.length) {
      obj.themes = message.themes.map((e) => Theme.toJSON(e));
    }
    if (message.defaultThemeNoU8 !== 0) {
      obj.defaultThemeNoU8 = Math.round(message.defaultThemeNoU8);
    }
    if (message.colorVariables) {
      const entries = Object.entries(message.colorVariables);
      if (entries.length > 0) {
        obj.colorVariables = {};
        entries.forEach(([k, v]) => {
          obj.colorVariables[k] = ColorVariable.toJSON(v);
        });
      }
    }
    if (message.themeColorValues) {
      const entries = Object.entries(message.themeColorValues);
      if (entries.length > 0) {
        obj.themeColorValues = {};
        entries.forEach(([k, v]) => {
          obj.themeColorValues[k] = Math.round(v);
        });
      }
    }
    if (message.styleTemplates) {
      const entries = Object.entries(message.styleTemplates);
      if (entries.length > 0) {
        obj.styleTemplates = {};
        entries.forEach(([k, v]) => {
          obj.styleTemplates[k] = StyleTemplate.toJSON(v);
        });
      }
    }
    if (message.defaultStyleIdU8 !== 0) {
      obj.defaultStyleIdU8 = Math.round(message.defaultStyleIdU8);
    }
    if (message.fontDefinitions) {
      const entries = Object.entries(message.fontDefinitions);
      if (entries.length > 0) {
        obj.fontDefinitions = {};
        entries.forEach(([k, v]) => {
          obj.fontDefinitions[k] = FontDefinition.toJSON(v);
        });
      }
    }
    if (message.defaultFontName !== "") {
      obj.defaultFontName = message.defaultFontName;
    }
    if (message.defaultStyleProperties !== undefined) {
      obj.defaultStyleProperties = StyleProperties.toJSON(message.defaultStyleProperties);
    }
    if (message.fontFiles) {
      const entries = Object.entries(message.fontFiles);
      if (entries.length > 0) {
        obj.fontFiles = {};
        entries.forEach(([k, v]) => {
          obj.fontFiles[k] = Font.toJSON(v);
        });
      }
    }
    if (message.fontFileMap) {
      const entries = Object.entries(message.fontFileMap);
      if (entries.length > 0) {
        obj.fontFileMap = {};
        entries.forEach(([k, v]) => {
          obj.fontFileMap[k] = Math.round(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProjectStyleConfig>, I>>(base?: I): ProjectStyleConfig {
    return ProjectStyleConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProjectStyleConfig>, I>>(object: I): ProjectStyleConfig {
    const message = createBaseProjectStyleConfig();
    message.languages = object.languages?.map((e) => Language.fromPartial(e)) || [];
    message.defaultLanguageNoU8 = object.defaultLanguageNoU8 ?? 0;
    message.themes = object.themes?.map((e) => Theme.fromPartial(e)) || [];
    message.defaultThemeNoU8 = object.defaultThemeNoU8 ?? 0;
    message.colorVariables = Object.entries(object.colorVariables ?? {}).reduce<{ [key: number]: ColorVariable }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = ColorVariable.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.themeColorValues = Object.entries(object.themeColorValues ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.styleTemplates = Object.entries(object.styleTemplates ?? {}).reduce<{ [key: number]: StyleTemplate }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = StyleTemplate.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.defaultStyleIdU8 = object.defaultStyleIdU8 ?? 0;
    message.fontDefinitions = Object.entries(object.fontDefinitions ?? {}).reduce<{ [key: number]: FontDefinition }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = FontDefinition.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.defaultFontName = object.defaultFontName ?? "";
    message.defaultStyleProperties =
      (object.defaultStyleProperties !== undefined && object.defaultStyleProperties !== null)
        ? StyleProperties.fromPartial(object.defaultStyleProperties)
        : undefined;
    message.fontFiles = Object.entries(object.fontFiles ?? {}).reduce<{ [key: number]: Font }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = Font.fromPartial(value);
      }
      return acc;
    }, {});
    message.fontFileMap = Object.entries(object.fontFileMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseProjectStyleConfig_ColorVariablesEntry(): ProjectStyleConfig_ColorVariablesEntry {
  return { key: 0, value: undefined };
}

export const ProjectStyleConfig_ColorVariablesEntry: MessageFns<ProjectStyleConfig_ColorVariablesEntry> = {
  encode(message: ProjectStyleConfig_ColorVariablesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      ColorVariable.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProjectStyleConfig_ColorVariablesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProjectStyleConfig_ColorVariablesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = ColorVariable.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProjectStyleConfig_ColorVariablesEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? ColorVariable.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: ProjectStyleConfig_ColorVariablesEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = ColorVariable.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProjectStyleConfig_ColorVariablesEntry>, I>>(
    base?: I,
  ): ProjectStyleConfig_ColorVariablesEntry {
    return ProjectStyleConfig_ColorVariablesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProjectStyleConfig_ColorVariablesEntry>, I>>(
    object: I,
  ): ProjectStyleConfig_ColorVariablesEntry {
    const message = createBaseProjectStyleConfig_ColorVariablesEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? ColorVariable.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseProjectStyleConfig_ThemeColorValuesEntry(): ProjectStyleConfig_ThemeColorValuesEntry {
  return { key: 0, value: 0 };
}

export const ProjectStyleConfig_ThemeColorValuesEntry: MessageFns<ProjectStyleConfig_ThemeColorValuesEntry> = {
  encode(message: ProjectStyleConfig_ThemeColorValuesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).uint32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProjectStyleConfig_ThemeColorValuesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProjectStyleConfig_ThemeColorValuesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProjectStyleConfig_ThemeColorValuesEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
    };
  },

  toJSON(message: ProjectStyleConfig_ThemeColorValuesEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== 0) {
      obj.value = Math.round(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProjectStyleConfig_ThemeColorValuesEntry>, I>>(
    base?: I,
  ): ProjectStyleConfig_ThemeColorValuesEntry {
    return ProjectStyleConfig_ThemeColorValuesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProjectStyleConfig_ThemeColorValuesEntry>, I>>(
    object: I,
  ): ProjectStyleConfig_ThemeColorValuesEntry {
    const message = createBaseProjectStyleConfig_ThemeColorValuesEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseProjectStyleConfig_StyleTemplatesEntry(): ProjectStyleConfig_StyleTemplatesEntry {
  return { key: 0, value: undefined };
}

export const ProjectStyleConfig_StyleTemplatesEntry: MessageFns<ProjectStyleConfig_StyleTemplatesEntry> = {
  encode(message: ProjectStyleConfig_StyleTemplatesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      StyleTemplate.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProjectStyleConfig_StyleTemplatesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProjectStyleConfig_StyleTemplatesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = StyleTemplate.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProjectStyleConfig_StyleTemplatesEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? StyleTemplate.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: ProjectStyleConfig_StyleTemplatesEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = StyleTemplate.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProjectStyleConfig_StyleTemplatesEntry>, I>>(
    base?: I,
  ): ProjectStyleConfig_StyleTemplatesEntry {
    return ProjectStyleConfig_StyleTemplatesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProjectStyleConfig_StyleTemplatesEntry>, I>>(
    object: I,
  ): ProjectStyleConfig_StyleTemplatesEntry {
    const message = createBaseProjectStyleConfig_StyleTemplatesEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? StyleTemplate.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseProjectStyleConfig_FontDefinitionsEntry(): ProjectStyleConfig_FontDefinitionsEntry {
  return { key: 0, value: undefined };
}

export const ProjectStyleConfig_FontDefinitionsEntry: MessageFns<ProjectStyleConfig_FontDefinitionsEntry> = {
  encode(message: ProjectStyleConfig_FontDefinitionsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      FontDefinition.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProjectStyleConfig_FontDefinitionsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProjectStyleConfig_FontDefinitionsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = FontDefinition.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProjectStyleConfig_FontDefinitionsEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? FontDefinition.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: ProjectStyleConfig_FontDefinitionsEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = FontDefinition.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProjectStyleConfig_FontDefinitionsEntry>, I>>(
    base?: I,
  ): ProjectStyleConfig_FontDefinitionsEntry {
    return ProjectStyleConfig_FontDefinitionsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProjectStyleConfig_FontDefinitionsEntry>, I>>(
    object: I,
  ): ProjectStyleConfig_FontDefinitionsEntry {
    const message = createBaseProjectStyleConfig_FontDefinitionsEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? FontDefinition.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseProjectStyleConfig_FontFilesEntry(): ProjectStyleConfig_FontFilesEntry {
  return { key: 0, value: undefined };
}

export const ProjectStyleConfig_FontFilesEntry: MessageFns<ProjectStyleConfig_FontFilesEntry> = {
  encode(message: ProjectStyleConfig_FontFilesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      Font.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProjectStyleConfig_FontFilesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProjectStyleConfig_FontFilesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = Font.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProjectStyleConfig_FontFilesEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? Font.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: ProjectStyleConfig_FontFilesEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = Font.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProjectStyleConfig_FontFilesEntry>, I>>(
    base?: I,
  ): ProjectStyleConfig_FontFilesEntry {
    return ProjectStyleConfig_FontFilesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProjectStyleConfig_FontFilesEntry>, I>>(
    object: I,
  ): ProjectStyleConfig_FontFilesEntry {
    const message = createBaseProjectStyleConfig_FontFilesEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null) ? Font.fromPartial(object.value) : undefined;
    return message;
  },
};

function createBaseProjectStyleConfig_FontFileMapEntry(): ProjectStyleConfig_FontFileMapEntry {
  return { key: 0, value: 0 };
}

export const ProjectStyleConfig_FontFileMapEntry: MessageFns<ProjectStyleConfig_FontFileMapEntry> = {
  encode(message: ProjectStyleConfig_FontFileMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).uint32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProjectStyleConfig_FontFileMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProjectStyleConfig_FontFileMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProjectStyleConfig_FontFileMapEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
    };
  },

  toJSON(message: ProjectStyleConfig_FontFileMapEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== 0) {
      obj.value = Math.round(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProjectStyleConfig_FontFileMapEntry>, I>>(
    base?: I,
  ): ProjectStyleConfig_FontFileMapEntry {
    return ProjectStyleConfig_FontFileMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProjectStyleConfig_FontFileMapEntry>, I>>(
    object: I,
  ): ProjectStyleConfig_FontFileMapEntry {
    const message = createBaseProjectStyleConfig_FontFileMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseLanguage(): Language {
  return { languageType: 0, defaultFontName: "" };
}

export const Language: MessageFns<Language> = {
  encode(message: Language, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.languageType !== 0) {
      writer.uint32(8).int32(message.languageType);
    }
    if (message.defaultFontName !== "") {
      writer.uint32(18).string(message.defaultFontName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Language {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLanguage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.languageType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.defaultFontName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Language {
    return {
      languageType: isSet(object.languageType) ? languageTypeFromJSON(object.languageType) : 0,
      defaultFontName: isSet(object.defaultFontName) ? globalThis.String(object.defaultFontName) : "",
    };
  },

  toJSON(message: Language): unknown {
    const obj: any = {};
    if (message.languageType !== 0) {
      obj.languageType = languageTypeToJSON(message.languageType);
    }
    if (message.defaultFontName !== "") {
      obj.defaultFontName = message.defaultFontName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Language>, I>>(base?: I): Language {
    return Language.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Language>, I>>(object: I): Language {
    const message = createBaseLanguage();
    message.languageType = object.languageType ?? 0;
    message.defaultFontName = object.defaultFontName ?? "";
    return message;
  },
};

function createBaseTheme(): Theme {
  return { name: "", memo: "" };
}

export const Theme: MessageFns<Theme> = {
  encode(message: Theme, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.memo !== "") {
      writer.uint32(18).string(message.memo);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Theme {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTheme();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Theme {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      memo: isSet(object.memo) ? globalThis.String(object.memo) : "",
    };
  },

  toJSON(message: Theme): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.memo !== "") {
      obj.memo = message.memo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Theme>, I>>(base?: I): Theme {
    return Theme.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Theme>, I>>(object: I): Theme {
    const message = createBaseTheme();
    message.name = object.name ?? "";
    message.memo = object.memo ?? "";
    return message;
  },
};

function createBaseColorVariable(): ColorVariable {
  return { name: "", defaultColorU32: 0, memo: "" };
}

export const ColorVariable: MessageFns<ColorVariable> = {
  encode(message: ColorVariable, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.defaultColorU32 !== 0) {
      writer.uint32(16).uint32(message.defaultColorU32);
    }
    if (message.memo !== "") {
      writer.uint32(26).string(message.memo);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ColorVariable {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseColorVariable();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.defaultColorU32 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ColorVariable {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      defaultColorU32: isSet(object.defaultColorU32) ? globalThis.Number(object.defaultColorU32) : 0,
      memo: isSet(object.memo) ? globalThis.String(object.memo) : "",
    };
  },

  toJSON(message: ColorVariable): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.defaultColorU32 !== 0) {
      obj.defaultColorU32 = Math.round(message.defaultColorU32);
    }
    if (message.memo !== "") {
      obj.memo = message.memo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ColorVariable>, I>>(base?: I): ColorVariable {
    return ColorVariable.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ColorVariable>, I>>(object: I): ColorVariable {
    const message = createBaseColorVariable();
    message.name = object.name ?? "";
    message.defaultColorU32 = object.defaultColorU32 ?? 0;
    message.memo = object.memo ?? "";
    return message;
  },
};

function createBaseFontDefinition(): FontDefinition {
  return {
    name: "",
    fonts: {},
    memo: "",
    sameFontName: false,
    sameFontSize: false,
    sameFontStyle: false,
    disabled: false,
  };
}

export const FontDefinition: MessageFns<FontDefinition> = {
  encode(message: FontDefinition, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    Object.entries(message.fonts).forEach(([key, value]) => {
      FontDefinition_FontsEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    if (message.memo !== "") {
      writer.uint32(26).string(message.memo);
    }
    if (message.sameFontName !== false) {
      writer.uint32(32).bool(message.sameFontName);
    }
    if (message.sameFontSize !== false) {
      writer.uint32(40).bool(message.sameFontSize);
    }
    if (message.sameFontStyle !== false) {
      writer.uint32(48).bool(message.sameFontStyle);
    }
    if (message.disabled !== false) {
      writer.uint32(56).bool(message.disabled);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FontDefinition {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFontDefinition();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = FontDefinition_FontsEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.fonts[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.memo = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.sameFontName = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.sameFontSize = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.sameFontStyle = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.disabled = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FontDefinition {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      fonts: isObject(object.fonts)
        ? Object.entries(object.fonts).reduce<{ [key: number]: Font }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = Font.fromJSON(value);
          return acc;
        }, {})
        : {},
      memo: isSet(object.memo) ? globalThis.String(object.memo) : "",
      sameFontName: isSet(object.sameFontName) ? globalThis.Boolean(object.sameFontName) : false,
      sameFontSize: isSet(object.sameFontSize) ? globalThis.Boolean(object.sameFontSize) : false,
      sameFontStyle: isSet(object.sameFontStyle) ? globalThis.Boolean(object.sameFontStyle) : false,
      disabled: isSet(object.disabled) ? globalThis.Boolean(object.disabled) : false,
    };
  },

  toJSON(message: FontDefinition): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.fonts) {
      const entries = Object.entries(message.fonts);
      if (entries.length > 0) {
        obj.fonts = {};
        entries.forEach(([k, v]) => {
          obj.fonts[k] = Font.toJSON(v);
        });
      }
    }
    if (message.memo !== "") {
      obj.memo = message.memo;
    }
    if (message.sameFontName !== false) {
      obj.sameFontName = message.sameFontName;
    }
    if (message.sameFontSize !== false) {
      obj.sameFontSize = message.sameFontSize;
    }
    if (message.sameFontStyle !== false) {
      obj.sameFontStyle = message.sameFontStyle;
    }
    if (message.disabled !== false) {
      obj.disabled = message.disabled;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FontDefinition>, I>>(base?: I): FontDefinition {
    return FontDefinition.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FontDefinition>, I>>(object: I): FontDefinition {
    const message = createBaseFontDefinition();
    message.name = object.name ?? "";
    message.fonts = Object.entries(object.fonts ?? {}).reduce<{ [key: number]: Font }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = Font.fromPartial(value);
      }
      return acc;
    }, {});
    message.memo = object.memo ?? "";
    message.sameFontName = object.sameFontName ?? false;
    message.sameFontSize = object.sameFontSize ?? false;
    message.sameFontStyle = object.sameFontStyle ?? false;
    message.disabled = object.disabled ?? false;
    return message;
  },
};

function createBaseFontDefinition_FontsEntry(): FontDefinition_FontsEntry {
  return { key: 0, value: undefined };
}

export const FontDefinition_FontsEntry: MessageFns<FontDefinition_FontsEntry> = {
  encode(message: FontDefinition_FontsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      Font.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FontDefinition_FontsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFontDefinition_FontsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = Font.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FontDefinition_FontsEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? Font.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: FontDefinition_FontsEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = Font.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FontDefinition_FontsEntry>, I>>(base?: I): FontDefinition_FontsEntry {
    return FontDefinition_FontsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FontDefinition_FontsEntry>, I>>(object: I): FontDefinition_FontsEntry {
    const message = createBaseFontDefinition_FontsEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null) ? Font.fromPartial(object.value) : undefined;
    return message;
  },
};

function createBaseFont(): Font {
  return { fontName: "", fontSize: 0, fontStyle: 0 };
}

export const Font: MessageFns<Font> = {
  encode(message: Font, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fontName !== "") {
      writer.uint32(10).string(message.fontName);
    }
    if (message.fontSize !== 0) {
      writer.uint32(16).int32(message.fontSize);
    }
    if (message.fontStyle !== 0) {
      writer.uint32(24).int32(message.fontStyle);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Font {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFont();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fontName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.fontSize = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.fontStyle = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Font {
    return {
      fontName: isSet(object.fontName) ? globalThis.String(object.fontName) : "",
      fontSize: isSet(object.fontSize) ? globalThis.Number(object.fontSize) : 0,
      fontStyle: isSet(object.fontStyle) ? fontStyleFromJSON(object.fontStyle) : 0,
    };
  },

  toJSON(message: Font): unknown {
    const obj: any = {};
    if (message.fontName !== "") {
      obj.fontName = message.fontName;
    }
    if (message.fontSize !== 0) {
      obj.fontSize = Math.round(message.fontSize);
    }
    if (message.fontStyle !== 0) {
      obj.fontStyle = fontStyleToJSON(message.fontStyle);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Font>, I>>(base?: I): Font {
    return Font.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Font>, I>>(object: I): Font {
    const message = createBaseFont();
    message.fontName = object.fontName ?? "";
    message.fontSize = object.fontSize ?? 0;
    message.fontStyle = object.fontStyle ?? 0;
    return message;
  },
};

function createBaseGraphicReference(): GraphicReference {
  return { graphicType: 0, src: 0, widthU16: 0, heightU16: 0, opacityU8: undefined, recolor: undefined };
}

export const GraphicReference: MessageFns<GraphicReference> = {
  encode(message: GraphicReference, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.graphicType !== 0) {
      writer.uint32(8).int32(message.graphicType);
    }
    if (message.src !== 0) {
      writer.uint32(16).uint32(message.src);
    }
    if (message.widthU16 !== 0) {
      writer.uint32(24).uint32(message.widthU16);
    }
    if (message.heightU16 !== 0) {
      writer.uint32(32).uint32(message.heightU16);
    }
    if (message.opacityU8 !== undefined) {
      writer.uint32(40).uint32(message.opacityU8);
    }
    if (message.recolor !== undefined) {
      ColorReference.encode(message.recolor, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GraphicReference {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGraphicReference();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.graphicType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.src = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.widthU16 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.heightU16 = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.opacityU8 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.recolor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GraphicReference {
    return {
      graphicType: isSet(object.graphicType) ? graphicTypeFromJSON(object.graphicType) : 0,
      src: isSet(object.src) ? globalThis.Number(object.src) : 0,
      widthU16: isSet(object.widthU16) ? globalThis.Number(object.widthU16) : 0,
      heightU16: isSet(object.heightU16) ? globalThis.Number(object.heightU16) : 0,
      opacityU8: isSet(object.opacityU8) ? globalThis.Number(object.opacityU8) : undefined,
      recolor: isSet(object.recolor) ? ColorReference.fromJSON(object.recolor) : undefined,
    };
  },

  toJSON(message: GraphicReference): unknown {
    const obj: any = {};
    if (message.graphicType !== 0) {
      obj.graphicType = graphicTypeToJSON(message.graphicType);
    }
    if (message.src !== 0) {
      obj.src = Math.round(message.src);
    }
    if (message.widthU16 !== 0) {
      obj.widthU16 = Math.round(message.widthU16);
    }
    if (message.heightU16 !== 0) {
      obj.heightU16 = Math.round(message.heightU16);
    }
    if (message.opacityU8 !== undefined) {
      obj.opacityU8 = Math.round(message.opacityU8);
    }
    if (message.recolor !== undefined) {
      obj.recolor = ColorReference.toJSON(message.recolor);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GraphicReference>, I>>(base?: I): GraphicReference {
    return GraphicReference.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GraphicReference>, I>>(object: I): GraphicReference {
    const message = createBaseGraphicReference();
    message.graphicType = object.graphicType ?? 0;
    message.src = object.src ?? 0;
    message.widthU16 = object.widthU16 ?? 0;
    message.heightU16 = object.heightU16 ?? 0;
    message.opacityU8 = object.opacityU8 ?? undefined;
    message.recolor = (object.recolor !== undefined && object.recolor !== null)
      ? ColorReference.fromPartial(object.recolor)
      : undefined;
    return message;
  },
};

function createBaseColorReference(): ColorReference {
  return { color: undefined };
}

export const ColorReference: MessageFns<ColorReference> = {
  encode(message: ColorReference, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    switch (message.color?.$case) {
      case "colorVarIdU8":
        writer.uint32(8).uint32(message.color.value);
        break;
      case "colorValueU32":
        writer.uint32(16).uint32(message.color.value);
        break;
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ColorReference {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseColorReference();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.color = { $case: "colorVarIdU8", value: reader.uint32() };
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.color = { $case: "colorValueU32", value: reader.uint32() };
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ColorReference {
    return {
      color: isSet(object.colorVarIdU8)
        ? { $case: "colorVarIdU8", value: globalThis.Number(object.colorVarIdU8) }
        : isSet(object.colorValueU32)
        ? { $case: "colorValueU32", value: globalThis.Number(object.colorValueU32) }
        : undefined,
    };
  },

  toJSON(message: ColorReference): unknown {
    const obj: any = {};
    if (message.color?.$case === "colorVarIdU8") {
      obj.colorVarIdU8 = Math.round(message.color.value);
    } else if (message.color?.$case === "colorValueU32") {
      obj.colorValueU32 = Math.round(message.color.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ColorReference>, I>>(base?: I): ColorReference {
    return ColorReference.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ColorReference>, I>>(object: I): ColorReference {
    const message = createBaseColorReference();
    switch (object.color?.$case) {
      case "colorVarIdU8": {
        if (object.color?.value !== undefined && object.color?.value !== null) {
          message.color = { $case: "colorVarIdU8", value: object.color.value };
        }
        break;
      }
      case "colorValueU32": {
        if (object.color?.value !== undefined && object.color?.value !== null) {
          message.color = { $case: "colorValueU32", value: object.color.value };
        }
        break;
      }
    }
    return message;
  },
};

function createBaseStyleBorder(): StyleBorder {
  return { color: undefined, widthU8: 0, left: false, top: false, right: false, bottom: false, internal: false };
}

export const StyleBorder: MessageFns<StyleBorder> = {
  encode(message: StyleBorder, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.color !== undefined) {
      ColorReference.encode(message.color, writer.uint32(10).fork()).join();
    }
    if (message.widthU8 !== 0) {
      writer.uint32(16).uint32(message.widthU8);
    }
    if (message.left !== false) {
      writer.uint32(24).bool(message.left);
    }
    if (message.top !== false) {
      writer.uint32(32).bool(message.top);
    }
    if (message.right !== false) {
      writer.uint32(40).bool(message.right);
    }
    if (message.bottom !== false) {
      writer.uint32(48).bool(message.bottom);
    }
    if (message.internal !== false) {
      writer.uint32(56).bool(message.internal);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StyleBorder {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStyleBorder();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.color = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.widthU8 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.left = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.top = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.right = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.bottom = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.internal = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StyleBorder {
    return {
      color: isSet(object.color) ? ColorReference.fromJSON(object.color) : undefined,
      widthU8: isSet(object.widthU8) ? globalThis.Number(object.widthU8) : 0,
      left: isSet(object.left) ? globalThis.Boolean(object.left) : false,
      top: isSet(object.top) ? globalThis.Boolean(object.top) : false,
      right: isSet(object.right) ? globalThis.Boolean(object.right) : false,
      bottom: isSet(object.bottom) ? globalThis.Boolean(object.bottom) : false,
      internal: isSet(object.internal) ? globalThis.Boolean(object.internal) : false,
    };
  },

  toJSON(message: StyleBorder): unknown {
    const obj: any = {};
    if (message.color !== undefined) {
      obj.color = ColorReference.toJSON(message.color);
    }
    if (message.widthU8 !== 0) {
      obj.widthU8 = Math.round(message.widthU8);
    }
    if (message.left !== false) {
      obj.left = message.left;
    }
    if (message.top !== false) {
      obj.top = message.top;
    }
    if (message.right !== false) {
      obj.right = message.right;
    }
    if (message.bottom !== false) {
      obj.bottom = message.bottom;
    }
    if (message.internal !== false) {
      obj.internal = message.internal;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StyleBorder>, I>>(base?: I): StyleBorder {
    return StyleBorder.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StyleBorder>, I>>(object: I): StyleBorder {
    const message = createBaseStyleBorder();
    message.color = (object.color !== undefined && object.color !== null)
      ? ColorReference.fromPartial(object.color)
      : undefined;
    message.widthU8 = object.widthU8 ?? 0;
    message.left = object.left ?? false;
    message.top = object.top ?? false;
    message.right = object.right ?? false;
    message.bottom = object.bottom ?? false;
    message.internal = object.internal ?? false;
    return message;
  },
};

function createBaseStyleShadow(): StyleShadow {
  return { enable: false, color: undefined, offsetXI16: 0, offsetYI16: 0, spreadI16: 0, widthI16: 0 };
}

export const StyleShadow: MessageFns<StyleShadow> = {
  encode(message: StyleShadow, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.enable !== false) {
      writer.uint32(8).bool(message.enable);
    }
    if (message.color !== undefined) {
      ColorReference.encode(message.color, writer.uint32(18).fork()).join();
    }
    if (message.offsetXI16 !== 0) {
      writer.uint32(24).int32(message.offsetXI16);
    }
    if (message.offsetYI16 !== 0) {
      writer.uint32(32).int32(message.offsetYI16);
    }
    if (message.spreadI16 !== 0) {
      writer.uint32(40).int32(message.spreadI16);
    }
    if (message.widthI16 !== 0) {
      writer.uint32(48).int32(message.widthI16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StyleShadow {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStyleShadow();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.enable = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.color = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.offsetXI16 = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.offsetYI16 = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.spreadI16 = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.widthI16 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StyleShadow {
    return {
      enable: isSet(object.enable) ? globalThis.Boolean(object.enable) : false,
      color: isSet(object.color) ? ColorReference.fromJSON(object.color) : undefined,
      offsetXI16: isSet(object.offsetXI16) ? globalThis.Number(object.offsetXI16) : 0,
      offsetYI16: isSet(object.offsetYI16) ? globalThis.Number(object.offsetYI16) : 0,
      spreadI16: isSet(object.spreadI16) ? globalThis.Number(object.spreadI16) : 0,
      widthI16: isSet(object.widthI16) ? globalThis.Number(object.widthI16) : 0,
    };
  },

  toJSON(message: StyleShadow): unknown {
    const obj: any = {};
    if (message.enable !== false) {
      obj.enable = message.enable;
    }
    if (message.color !== undefined) {
      obj.color = ColorReference.toJSON(message.color);
    }
    if (message.offsetXI16 !== 0) {
      obj.offsetXI16 = Math.round(message.offsetXI16);
    }
    if (message.offsetYI16 !== 0) {
      obj.offsetYI16 = Math.round(message.offsetYI16);
    }
    if (message.spreadI16 !== 0) {
      obj.spreadI16 = Math.round(message.spreadI16);
    }
    if (message.widthI16 !== 0) {
      obj.widthI16 = Math.round(message.widthI16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StyleShadow>, I>>(base?: I): StyleShadow {
    return StyleShadow.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StyleShadow>, I>>(object: I): StyleShadow {
    const message = createBaseStyleShadow();
    message.enable = object.enable ?? false;
    message.color = (object.color !== undefined && object.color !== null)
      ? ColorReference.fromPartial(object.color)
      : undefined;
    message.offsetXI16 = object.offsetXI16 ?? 0;
    message.offsetYI16 = object.offsetYI16 ?? 0;
    message.spreadI16 = object.spreadI16 ?? 0;
    message.widthI16 = object.widthI16 ?? 0;
    return message;
  },
};

function createBaseStyleTemplate(): StyleTemplate {
  return { name: "", properties: undefined };
}

export const StyleTemplate: MessageFns<StyleTemplate> = {
  encode(message: StyleTemplate, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.properties !== undefined) {
      StyleProperties.encode(message.properties, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StyleTemplate {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStyleTemplate();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.properties = StyleProperties.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StyleTemplate {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      properties: isSet(object.properties) ? StyleProperties.fromJSON(object.properties) : undefined,
    };
  },

  toJSON(message: StyleTemplate): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.properties !== undefined) {
      obj.properties = StyleProperties.toJSON(message.properties);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StyleTemplate>, I>>(base?: I): StyleTemplate {
    return StyleTemplate.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StyleTemplate>, I>>(object: I): StyleTemplate {
    const message = createBaseStyleTemplate();
    message.name = object.name ?? "";
    message.properties = (object.properties !== undefined && object.properties !== null)
      ? StyleProperties.fromPartial(object.properties)
      : undefined;
    return message;
  },
};

function createBaseStyleProperties(): StyleProperties {
  return {
    styleIdU8: undefined,
    backgroundColor: undefined,
    borderProps: undefined,
    shadowProps: undefined,
    fontIdU8: undefined,
    textLocation: undefined,
    textColor: undefined,
    blinkIntervalU8: undefined,
    marquee: undefined,
  };
}

export const StyleProperties: MessageFns<StyleProperties> = {
  encode(message: StyleProperties, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.styleIdU8 !== undefined) {
      writer.uint32(8).uint32(message.styleIdU8);
    }
    if (message.backgroundColor !== undefined) {
      ColorReference.encode(message.backgroundColor, writer.uint32(18).fork()).join();
    }
    if (message.borderProps !== undefined) {
      StyleBorder.encode(message.borderProps, writer.uint32(26).fork()).join();
    }
    if (message.shadowProps !== undefined) {
      StyleShadow.encode(message.shadowProps, writer.uint32(34).fork()).join();
    }
    if (message.fontIdU8 !== undefined) {
      writer.uint32(40).uint32(message.fontIdU8);
    }
    if (message.textLocation !== undefined) {
      MultiLangTextLocation.encode(message.textLocation, writer.uint32(50).fork()).join();
    }
    if (message.textColor !== undefined) {
      ColorReference.encode(message.textColor, writer.uint32(58).fork()).join();
    }
    if (message.blinkIntervalU8 !== undefined) {
      writer.uint32(64).uint32(message.blinkIntervalU8);
    }
    if (message.marquee !== undefined) {
      MarqueeDefine.encode(message.marquee, writer.uint32(74).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StyleProperties {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStyleProperties();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.styleIdU8 = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.backgroundColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.borderProps = StyleBorder.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.shadowProps = StyleShadow.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.fontIdU8 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.textLocation = MultiLangTextLocation.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.textColor = ColorReference.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.blinkIntervalU8 = reader.uint32();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.marquee = MarqueeDefine.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StyleProperties {
    return {
      styleIdU8: isSet(object.styleIdU8) ? globalThis.Number(object.styleIdU8) : undefined,
      backgroundColor: isSet(object.backgroundColor) ? ColorReference.fromJSON(object.backgroundColor) : undefined,
      borderProps: isSet(object.borderProps) ? StyleBorder.fromJSON(object.borderProps) : undefined,
      shadowProps: isSet(object.shadowProps) ? StyleShadow.fromJSON(object.shadowProps) : undefined,
      fontIdU8: isSet(object.fontIdU8) ? globalThis.Number(object.fontIdU8) : undefined,
      textLocation: isSet(object.textLocation) ? MultiLangTextLocation.fromJSON(object.textLocation) : undefined,
      textColor: isSet(object.textColor) ? ColorReference.fromJSON(object.textColor) : undefined,
      blinkIntervalU8: isSet(object.blinkIntervalU8) ? globalThis.Number(object.blinkIntervalU8) : undefined,
      marquee: isSet(object.marquee) ? MarqueeDefine.fromJSON(object.marquee) : undefined,
    };
  },

  toJSON(message: StyleProperties): unknown {
    const obj: any = {};
    if (message.styleIdU8 !== undefined) {
      obj.styleIdU8 = Math.round(message.styleIdU8);
    }
    if (message.backgroundColor !== undefined) {
      obj.backgroundColor = ColorReference.toJSON(message.backgroundColor);
    }
    if (message.borderProps !== undefined) {
      obj.borderProps = StyleBorder.toJSON(message.borderProps);
    }
    if (message.shadowProps !== undefined) {
      obj.shadowProps = StyleShadow.toJSON(message.shadowProps);
    }
    if (message.fontIdU8 !== undefined) {
      obj.fontIdU8 = Math.round(message.fontIdU8);
    }
    if (message.textLocation !== undefined) {
      obj.textLocation = MultiLangTextLocation.toJSON(message.textLocation);
    }
    if (message.textColor !== undefined) {
      obj.textColor = ColorReference.toJSON(message.textColor);
    }
    if (message.blinkIntervalU8 !== undefined) {
      obj.blinkIntervalU8 = Math.round(message.blinkIntervalU8);
    }
    if (message.marquee !== undefined) {
      obj.marquee = MarqueeDefine.toJSON(message.marquee);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StyleProperties>, I>>(base?: I): StyleProperties {
    return StyleProperties.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StyleProperties>, I>>(object: I): StyleProperties {
    const message = createBaseStyleProperties();
    message.styleIdU8 = object.styleIdU8 ?? undefined;
    message.backgroundColor = (object.backgroundColor !== undefined && object.backgroundColor !== null)
      ? ColorReference.fromPartial(object.backgroundColor)
      : undefined;
    message.borderProps = (object.borderProps !== undefined && object.borderProps !== null)
      ? StyleBorder.fromPartial(object.borderProps)
      : undefined;
    message.shadowProps = (object.shadowProps !== undefined && object.shadowProps !== null)
      ? StyleShadow.fromPartial(object.shadowProps)
      : undefined;
    message.fontIdU8 = object.fontIdU8 ?? undefined;
    message.textLocation = (object.textLocation !== undefined && object.textLocation !== null)
      ? MultiLangTextLocation.fromPartial(object.textLocation)
      : undefined;
    message.textColor = (object.textColor !== undefined && object.textColor !== null)
      ? ColorReference.fromPartial(object.textColor)
      : undefined;
    message.blinkIntervalU8 = object.blinkIntervalU8 ?? undefined;
    message.marquee = (object.marquee !== undefined && object.marquee !== null)
      ? MarqueeDefine.fromPartial(object.marquee)
      : undefined;
    return message;
  },
};

function createBaseTextLocation(): TextLocation {
  return { textAlign: 0, textPadding: undefined };
}

export const TextLocation: MessageFns<TextLocation> = {
  encode(message: TextLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.textAlign !== 0) {
      writer.uint32(8).int32(message.textAlign);
    }
    if (message.textPadding !== undefined) {
      PositionOffset.encode(message.textPadding, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TextLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTextLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.textAlign = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.textPadding = PositionOffset.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TextLocation {
    return {
      textAlign: isSet(object.textAlign) ? alignTypeFromJSON(object.textAlign) : 0,
      textPadding: isSet(object.textPadding) ? PositionOffset.fromJSON(object.textPadding) : undefined,
    };
  },

  toJSON(message: TextLocation): unknown {
    const obj: any = {};
    if (message.textAlign !== 0) {
      obj.textAlign = alignTypeToJSON(message.textAlign);
    }
    if (message.textPadding !== undefined) {
      obj.textPadding = PositionOffset.toJSON(message.textPadding);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TextLocation>, I>>(base?: I): TextLocation {
    return TextLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TextLocation>, I>>(object: I): TextLocation {
    const message = createBaseTextLocation();
    message.textAlign = object.textAlign ?? 0;
    message.textPadding = (object.textPadding !== undefined && object.textPadding !== null)
      ? PositionOffset.fromPartial(object.textPadding)
      : undefined;
    return message;
  },
};

function createBaseMultiLangTextLocation(): MultiLangTextLocation {
  return { multiLang: {} };
}

export const MultiLangTextLocation: MessageFns<MultiLangTextLocation> = {
  encode(message: MultiLangTextLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.multiLang).forEach(([key, value]) => {
      MultiLangTextLocation_MultiLangEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MultiLangTextLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMultiLangTextLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = MultiLangTextLocation_MultiLangEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.multiLang[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MultiLangTextLocation {
    return {
      multiLang: isObject(object.multiLang)
        ? Object.entries(object.multiLang).reduce<{ [key: number]: TextLocation }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = TextLocation.fromJSON(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: MultiLangTextLocation): unknown {
    const obj: any = {};
    if (message.multiLang) {
      const entries = Object.entries(message.multiLang);
      if (entries.length > 0) {
        obj.multiLang = {};
        entries.forEach(([k, v]) => {
          obj.multiLang[k] = TextLocation.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MultiLangTextLocation>, I>>(base?: I): MultiLangTextLocation {
    return MultiLangTextLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MultiLangTextLocation>, I>>(object: I): MultiLangTextLocation {
    const message = createBaseMultiLangTextLocation();
    message.multiLang = Object.entries(object.multiLang ?? {}).reduce<{ [key: number]: TextLocation }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = TextLocation.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseMultiLangTextLocation_MultiLangEntry(): MultiLangTextLocation_MultiLangEntry {
  return { key: 0, value: undefined };
}

export const MultiLangTextLocation_MultiLangEntry: MessageFns<MultiLangTextLocation_MultiLangEntry> = {
  encode(message: MultiLangTextLocation_MultiLangEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      TextLocation.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MultiLangTextLocation_MultiLangEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMultiLangTextLocation_MultiLangEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = TextLocation.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MultiLangTextLocation_MultiLangEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? TextLocation.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: MultiLangTextLocation_MultiLangEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = TextLocation.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MultiLangTextLocation_MultiLangEntry>, I>>(
    base?: I,
  ): MultiLangTextLocation_MultiLangEntry {
    return MultiLangTextLocation_MultiLangEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MultiLangTextLocation_MultiLangEntry>, I>>(
    object: I,
  ): MultiLangTextLocation_MultiLangEntry {
    const message = createBaseMultiLangTextLocation_MultiLangEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? TextLocation.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseMarqueeDefine(): MarqueeDefine {
  return { enable: false, intervalTimeU8: 0, scrollDistanceU16: 0, scrollDirection: 0, loop: false };
}

export const MarqueeDefine: MessageFns<MarqueeDefine> = {
  encode(message: MarqueeDefine, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.enable !== false) {
      writer.uint32(8).bool(message.enable);
    }
    if (message.intervalTimeU8 !== 0) {
      writer.uint32(16).uint32(message.intervalTimeU8);
    }
    if (message.scrollDistanceU16 !== 0) {
      writer.uint32(24).uint32(message.scrollDistanceU16);
    }
    if (message.scrollDirection !== 0) {
      writer.uint32(32).int32(message.scrollDirection);
    }
    if (message.loop !== false) {
      writer.uint32(40).bool(message.loop);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MarqueeDefine {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMarqueeDefine();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.enable = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.intervalTimeU8 = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.scrollDistanceU16 = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.scrollDirection = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.loop = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MarqueeDefine {
    return {
      enable: isSet(object.enable) ? globalThis.Boolean(object.enable) : false,
      intervalTimeU8: isSet(object.intervalTimeU8) ? globalThis.Number(object.intervalTimeU8) : 0,
      scrollDistanceU16: isSet(object.scrollDistanceU16) ? globalThis.Number(object.scrollDistanceU16) : 0,
      scrollDirection: isSet(object.scrollDirection) ? scrollDirectionFromJSON(object.scrollDirection) : 0,
      loop: isSet(object.loop) ? globalThis.Boolean(object.loop) : false,
    };
  },

  toJSON(message: MarqueeDefine): unknown {
    const obj: any = {};
    if (message.enable !== false) {
      obj.enable = message.enable;
    }
    if (message.intervalTimeU8 !== 0) {
      obj.intervalTimeU8 = Math.round(message.intervalTimeU8);
    }
    if (message.scrollDistanceU16 !== 0) {
      obj.scrollDistanceU16 = Math.round(message.scrollDistanceU16);
    }
    if (message.scrollDirection !== 0) {
      obj.scrollDirection = scrollDirectionToJSON(message.scrollDirection);
    }
    if (message.loop !== false) {
      obj.loop = message.loop;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MarqueeDefine>, I>>(base?: I): MarqueeDefine {
    return MarqueeDefine.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MarqueeDefine>, I>>(object: I): MarqueeDefine {
    const message = createBaseMarqueeDefine();
    message.enable = object.enable ?? false;
    message.intervalTimeU8 = object.intervalTimeU8 ?? 0;
    message.scrollDistanceU16 = object.scrollDistanceU16 ?? 0;
    message.scrollDirection = object.scrollDirection ?? 0;
    message.loop = object.loop ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
