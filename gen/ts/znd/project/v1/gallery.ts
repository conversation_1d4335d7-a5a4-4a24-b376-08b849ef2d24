// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               unknown
// source: znd/project/v1/gallery.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "znd.project.v1";

/** 图片类型 */
export enum GalleryFileType {
  GALLERY_FILE_TYPE_UNSPECIFIED = 0,
  GALLERY_FILE_TYPE_PNG = 1,
  GALLERY_FILE_TYPE_JPEG = 2,
  GALLERY_FILE_TYPE_BMP = 3,
  GALLERY_FILE_TYPE_GIF = 4,
  GALLERY_FILE_TYPE_TIFF = 5,
  GALLERY_FILE_TYPE_ICO = 6,
  GALLERY_FILE_TYPE_WEBP = 7,
  UNRECOGNIZED = -1,
}

export function galleryFileTypeFromJSON(object: any): GalleryFileType {
  switch (object) {
    case 0:
    case "GALLERY_FILE_TYPE_UNSPECIFIED":
      return GalleryFileType.GALLERY_FILE_TYPE_UNSPECIFIED;
    case 1:
    case "GALLERY_FILE_TYPE_PNG":
      return GalleryFileType.GALLERY_FILE_TYPE_PNG;
    case 2:
    case "GALLERY_FILE_TYPE_JPEG":
      return GalleryFileType.GALLERY_FILE_TYPE_JPEG;
    case 3:
    case "GALLERY_FILE_TYPE_BMP":
      return GalleryFileType.GALLERY_FILE_TYPE_BMP;
    case 4:
    case "GALLERY_FILE_TYPE_GIF":
      return GalleryFileType.GALLERY_FILE_TYPE_GIF;
    case 5:
    case "GALLERY_FILE_TYPE_TIFF":
      return GalleryFileType.GALLERY_FILE_TYPE_TIFF;
    case 6:
    case "GALLERY_FILE_TYPE_ICO":
      return GalleryFileType.GALLERY_FILE_TYPE_ICO;
    case 7:
    case "GALLERY_FILE_TYPE_WEBP":
      return GalleryFileType.GALLERY_FILE_TYPE_WEBP;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GalleryFileType.UNRECOGNIZED;
  }
}

export function galleryFileTypeToJSON(object: GalleryFileType): string {
  switch (object) {
    case GalleryFileType.GALLERY_FILE_TYPE_UNSPECIFIED:
      return "GALLERY_FILE_TYPE_UNSPECIFIED";
    case GalleryFileType.GALLERY_FILE_TYPE_PNG:
      return "GALLERY_FILE_TYPE_PNG";
    case GalleryFileType.GALLERY_FILE_TYPE_JPEG:
      return "GALLERY_FILE_TYPE_JPEG";
    case GalleryFileType.GALLERY_FILE_TYPE_BMP:
      return "GALLERY_FILE_TYPE_BMP";
    case GalleryFileType.GALLERY_FILE_TYPE_GIF:
      return "GALLERY_FILE_TYPE_GIF";
    case GalleryFileType.GALLERY_FILE_TYPE_TIFF:
      return "GALLERY_FILE_TYPE_TIFF";
    case GalleryFileType.GALLERY_FILE_TYPE_ICO:
      return "GALLERY_FILE_TYPE_ICO";
    case GalleryFileType.GALLERY_FILE_TYPE_WEBP:
      return "GALLERY_FILE_TYPE_WEBP";
    case GalleryFileType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 工程图库列表 */
export interface ProjectGalleryList {
  /** 工程图库源文件列表 */
  galleryList: { [key: number]: ProjectGallery };
  /** 工程图库生成后图片文件列表（供界面端使用） */
  generatedImageList: { [key: number]: GeneratedImage };
}

export interface ProjectGalleryList_GalleryListEntry {
  key: number;
  value: ProjectGallery | undefined;
}

export interface ProjectGalleryList_GeneratedImageListEntry {
  key: number;
  value: GeneratedImage | undefined;
}

/** 工程图库源文件 */
export interface ProjectGallery {
  /** 用md5作为识别图片文件的key */
  md5: string;
  /** 图片宽度 */
  widthI16: number;
  /** 图片高度 */
  heightI16: number;
  /** 图片文件大小 */
  fileSizeU32: number;
  /** 图片类型 */
  fileType: GalleryFileType;
  /** 引用计数 */
  refCountI16: number;
}

/** 生成后图片文件 */
export interface GeneratedImage {
  /**
   * 图片id
   * uint32 id = 1;
   * 工程图库源文件id
   */
  projectGalleryId: number;
  /** 图片宽度 */
  widthI16: number;
  /** 图片高度 */
  heightI16: number;
  /** 引用计数 */
  refCountI16: number;
}

function createBaseProjectGalleryList(): ProjectGalleryList {
  return { galleryList: {}, generatedImageList: {} };
}

export const ProjectGalleryList: MessageFns<ProjectGalleryList> = {
  encode(message: ProjectGalleryList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.galleryList).forEach(([key, value]) => {
      ProjectGalleryList_GalleryListEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    Object.entries(message.generatedImageList).forEach(([key, value]) => {
      ProjectGalleryList_GeneratedImageListEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProjectGalleryList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProjectGalleryList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = ProjectGalleryList_GalleryListEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.galleryList[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = ProjectGalleryList_GeneratedImageListEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.generatedImageList[entry2.key] = entry2.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProjectGalleryList {
    return {
      galleryList: isObject(object.galleryList)
        ? Object.entries(object.galleryList).reduce<{ [key: number]: ProjectGallery }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = ProjectGallery.fromJSON(value);
          return acc;
        }, {})
        : {},
      generatedImageList: isObject(object.generatedImageList)
        ? Object.entries(object.generatedImageList).reduce<{ [key: number]: GeneratedImage }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = GeneratedImage.fromJSON(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: ProjectGalleryList): unknown {
    const obj: any = {};
    if (message.galleryList) {
      const entries = Object.entries(message.galleryList);
      if (entries.length > 0) {
        obj.galleryList = {};
        entries.forEach(([k, v]) => {
          obj.galleryList[k] = ProjectGallery.toJSON(v);
        });
      }
    }
    if (message.generatedImageList) {
      const entries = Object.entries(message.generatedImageList);
      if (entries.length > 0) {
        obj.generatedImageList = {};
        entries.forEach(([k, v]) => {
          obj.generatedImageList[k] = GeneratedImage.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProjectGalleryList>, I>>(base?: I): ProjectGalleryList {
    return ProjectGalleryList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProjectGalleryList>, I>>(object: I): ProjectGalleryList {
    const message = createBaseProjectGalleryList();
    message.galleryList = Object.entries(object.galleryList ?? {}).reduce<{ [key: number]: ProjectGallery }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = ProjectGallery.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.generatedImageList = Object.entries(object.generatedImageList ?? {}).reduce<
      { [key: number]: GeneratedImage }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = GeneratedImage.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseProjectGalleryList_GalleryListEntry(): ProjectGalleryList_GalleryListEntry {
  return { key: 0, value: undefined };
}

export const ProjectGalleryList_GalleryListEntry: MessageFns<ProjectGalleryList_GalleryListEntry> = {
  encode(message: ProjectGalleryList_GalleryListEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      ProjectGallery.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProjectGalleryList_GalleryListEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProjectGalleryList_GalleryListEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = ProjectGallery.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProjectGalleryList_GalleryListEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? ProjectGallery.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: ProjectGalleryList_GalleryListEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = ProjectGallery.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProjectGalleryList_GalleryListEntry>, I>>(
    base?: I,
  ): ProjectGalleryList_GalleryListEntry {
    return ProjectGalleryList_GalleryListEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProjectGalleryList_GalleryListEntry>, I>>(
    object: I,
  ): ProjectGalleryList_GalleryListEntry {
    const message = createBaseProjectGalleryList_GalleryListEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? ProjectGallery.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseProjectGalleryList_GeneratedImageListEntry(): ProjectGalleryList_GeneratedImageListEntry {
  return { key: 0, value: undefined };
}

export const ProjectGalleryList_GeneratedImageListEntry: MessageFns<ProjectGalleryList_GeneratedImageListEntry> = {
  encode(message: ProjectGalleryList_GeneratedImageListEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).uint32(message.key);
    }
    if (message.value !== undefined) {
      GeneratedImage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProjectGalleryList_GeneratedImageListEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProjectGalleryList_GeneratedImageListEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = GeneratedImage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProjectGalleryList_GeneratedImageListEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? GeneratedImage.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: ProjectGalleryList_GeneratedImageListEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = GeneratedImage.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProjectGalleryList_GeneratedImageListEntry>, I>>(
    base?: I,
  ): ProjectGalleryList_GeneratedImageListEntry {
    return ProjectGalleryList_GeneratedImageListEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProjectGalleryList_GeneratedImageListEntry>, I>>(
    object: I,
  ): ProjectGalleryList_GeneratedImageListEntry {
    const message = createBaseProjectGalleryList_GeneratedImageListEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? GeneratedImage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseProjectGallery(): ProjectGallery {
  return { md5: "", widthI16: 0, heightI16: 0, fileSizeU32: 0, fileType: 0, refCountI16: 0 };
}

export const ProjectGallery: MessageFns<ProjectGallery> = {
  encode(message: ProjectGallery, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.md5 !== "") {
      writer.uint32(18).string(message.md5);
    }
    if (message.widthI16 !== 0) {
      writer.uint32(24).int32(message.widthI16);
    }
    if (message.heightI16 !== 0) {
      writer.uint32(32).int32(message.heightI16);
    }
    if (message.fileSizeU32 !== 0) {
      writer.uint32(40).uint32(message.fileSizeU32);
    }
    if (message.fileType !== 0) {
      writer.uint32(48).int32(message.fileType);
    }
    if (message.refCountI16 !== 0) {
      writer.uint32(56).int32(message.refCountI16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProjectGallery {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProjectGallery();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.md5 = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.widthI16 = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.heightI16 = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.fileSizeU32 = reader.uint32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.fileType = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.refCountI16 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProjectGallery {
    return {
      md5: isSet(object.md5) ? globalThis.String(object.md5) : "",
      widthI16: isSet(object.widthI16) ? globalThis.Number(object.widthI16) : 0,
      heightI16: isSet(object.heightI16) ? globalThis.Number(object.heightI16) : 0,
      fileSizeU32: isSet(object.fileSizeU32) ? globalThis.Number(object.fileSizeU32) : 0,
      fileType: isSet(object.fileType) ? galleryFileTypeFromJSON(object.fileType) : 0,
      refCountI16: isSet(object.refCountI16) ? globalThis.Number(object.refCountI16) : 0,
    };
  },

  toJSON(message: ProjectGallery): unknown {
    const obj: any = {};
    if (message.md5 !== "") {
      obj.md5 = message.md5;
    }
    if (message.widthI16 !== 0) {
      obj.widthI16 = Math.round(message.widthI16);
    }
    if (message.heightI16 !== 0) {
      obj.heightI16 = Math.round(message.heightI16);
    }
    if (message.fileSizeU32 !== 0) {
      obj.fileSizeU32 = Math.round(message.fileSizeU32);
    }
    if (message.fileType !== 0) {
      obj.fileType = galleryFileTypeToJSON(message.fileType);
    }
    if (message.refCountI16 !== 0) {
      obj.refCountI16 = Math.round(message.refCountI16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProjectGallery>, I>>(base?: I): ProjectGallery {
    return ProjectGallery.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProjectGallery>, I>>(object: I): ProjectGallery {
    const message = createBaseProjectGallery();
    message.md5 = object.md5 ?? "";
    message.widthI16 = object.widthI16 ?? 0;
    message.heightI16 = object.heightI16 ?? 0;
    message.fileSizeU32 = object.fileSizeU32 ?? 0;
    message.fileType = object.fileType ?? 0;
    message.refCountI16 = object.refCountI16 ?? 0;
    return message;
  },
};

function createBaseGeneratedImage(): GeneratedImage {
  return { projectGalleryId: 0, widthI16: 0, heightI16: 0, refCountI16: 0 };
}

export const GeneratedImage: MessageFns<GeneratedImage> = {
  encode(message: GeneratedImage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.projectGalleryId !== 0) {
      writer.uint32(16).uint32(message.projectGalleryId);
    }
    if (message.widthI16 !== 0) {
      writer.uint32(24).int32(message.widthI16);
    }
    if (message.heightI16 !== 0) {
      writer.uint32(32).int32(message.heightI16);
    }
    if (message.refCountI16 !== 0) {
      writer.uint32(40).int32(message.refCountI16);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GeneratedImage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGeneratedImage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.projectGalleryId = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.widthI16 = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.heightI16 = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.refCountI16 = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GeneratedImage {
    return {
      projectGalleryId: isSet(object.projectGalleryId) ? globalThis.Number(object.projectGalleryId) : 0,
      widthI16: isSet(object.widthI16) ? globalThis.Number(object.widthI16) : 0,
      heightI16: isSet(object.heightI16) ? globalThis.Number(object.heightI16) : 0,
      refCountI16: isSet(object.refCountI16) ? globalThis.Number(object.refCountI16) : 0,
    };
  },

  toJSON(message: GeneratedImage): unknown {
    const obj: any = {};
    if (message.projectGalleryId !== 0) {
      obj.projectGalleryId = Math.round(message.projectGalleryId);
    }
    if (message.widthI16 !== 0) {
      obj.widthI16 = Math.round(message.widthI16);
    }
    if (message.heightI16 !== 0) {
      obj.heightI16 = Math.round(message.heightI16);
    }
    if (message.refCountI16 !== 0) {
      obj.refCountI16 = Math.round(message.refCountI16);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GeneratedImage>, I>>(base?: I): GeneratedImage {
    return GeneratedImage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GeneratedImage>, I>>(object: I): GeneratedImage {
    const message = createBaseGeneratedImage();
    message.projectGalleryId = object.projectGalleryId ?? 0;
    message.widthI16 = object.widthI16 ?? 0;
    message.heightI16 = object.heightI16 ?? 0;
    message.refCountI16 = object.refCountI16 ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends { $case: string; value: unknown } ? { $case: T["$case"]; value?: DeepPartial<T["value"]> }
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
