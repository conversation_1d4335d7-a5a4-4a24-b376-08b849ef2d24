# Copilot 使用规范

## 一、 回答使用中文

## 二、 依赖版本

- 项目中使用的 lvgl 版本为 8.3.9，如果我的问题和 lvgl 有关，请根据 lvgl 官方文档回答我的问题。如果在官方文档中找不到相应的内容，回答时需要明确说明未找到相关 lvgl 文档。

## 三、 注释规范

注释的示例：

```c
/// @brief 函数功能简介
///
/// @param param1 参数1的说明
/// @param param2 参数2的说明
/// @return 返回值的说明
/// @throw 异常的说明
void urq_exm(int param1, void* param2);
```

规范说明：

- 多个 param 时，按照参数的顺序依次写入，参数的说明需要对齐。
- 如果没有 return 值，也需要写明 `@return void`

## attribute 补全规则

attribute 示例

```c
void fn()
__attribute__((__malloc__()));
```

规范说明：

- __attribute__ 还是 __malloc__ 都需要使用双下划线。
- 可能的情况下为括号内的 __malloc__ 添加括号成 __malloc__()。

```c

