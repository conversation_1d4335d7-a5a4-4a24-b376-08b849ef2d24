#!/bin/bash

root_dir=$(dirname $(realpath $0))
protoc="${root_dir}/toolchain/protoc"

files=$(find "${root_dir}/proto/znd" -name "*.proto")
echo "files: ${files}"
echo "protoc --cpp_out=${root_dir}/--proto_path=${root_dir}/proto (files)..."

if [ -d "${root_dir}/gen/cxx11" ]; then
    rm -r "${root_dir}/gen/cxx11"
fi

mkdir -p "${root_dir}/gen/cxx11"
$protoc --cpp_out="${root_dir}" --proto_path=${root_dir}/proto $files

ZND_EMSDK_ENV="${HOME}/toolchain/emsdk/emsdk_env.sh"

function build_protocxx(){
    local build_platform=$1
    local build_type=$2

    local install_path="${root_dir}/lib/${build_platform}/${build_type}"
    local dist_dir="${root_dir}/../lib/${build_platform}/${build_type}/znd_proto"
    local build_dir="${root_dir}/build/${build_platform}/${build_type}"

    echo "install_path: ${install_path}"
    echo "dist_dir: ${dist_dir}"

    local args="-S $root_dir -B ${build_dir} -DPLATFORM=${build_platform} -DCMAKE_BUILD_TYPE=${build_type}"

    if [ "$build_platform" == "wasm" ] ; then
        exec 3>&1 4>&2 > /dev/null 2>&1
        source $ZND_EMSDK_ENV
        exec 1>&3 2>&4
        emcmake cmake $args
    elif [ "$build_platform" == "arm_v7hf" ] ; then
        export ZND_ARM_V7_CC="${HOME}/toolchain/PurPle-Pi-R1/toolchain/gcc-arm-8.2-2018.08-x86_64-arm-linux-gnueabihf/bin/arm-linux-gnueabihf-gcc"
        export ZND_ARM_V7_CXX="${HOME}/toolchain/PurPle-Pi-R1/toolchain/gcc-arm-8.2-2018.08-x86_64-arm-linux-gnueabihf/bin/arm-linux-gnueabihf-g++"
        
        cmake $args
    else
        export ZND_LINUX_64_CC="gcc"
        export ZND_LINUX_64_CXX="g++"
        
        cmake $args
    fi

    cmake --build ${build_dir} -j

    if [ -d ${dist_dir} ]; then
        echo "rm -r ${dist_dir}"
        rm -r ${dist_dir}
    fi

    echo "mkdir -p ${dist_dir}"
    mkdir -p ${dist_dir}/include

    cp -r ${root_dir}/znd ${dist_dir}/include/
    cp ${build_dir}/libznd_proto.a    ${dist_dir}/libznd_proto.a
    cp ${root_dir}/znd_proto-config.cmake          ${dist_dir}/znd_proto-config.cmake
    cp ${root_dir}/znd_proto-config-version.cmake  ${dist_dir}/znd_proto-config-version.cmake
}

build_protocxx linux_64 debug
build_protocxx linux_64 release

build_protocxx wasm debug
build_protocxx wasm release

build_protocxx arm_v7hf debug
build_protocxx arm_v7hf release
