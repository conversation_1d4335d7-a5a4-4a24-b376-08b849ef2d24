#!/usr/bin/env python3

from io import TextIOWrapper
import os
import sys
import re

def _get_new_guard(urq:str,file:str) ->str:
    """
    从文件名推断出新的 #define 宏定义
    """
    name = file[len(urq)+1:]
    name = name.replace("/", "__")
    name = name.replace(".", "_")
    name = name.upper()
    return name

def _find_old_define(lines:list[str]) ->str:
    """
    从文件中找到旧的 #define 宏定义
    """
    for line in lines:
        # 如果行以 #ifndef 开头
        if line.startswith("#ifndef"):
            if line.endswith("_H\n") or line.endswith("_HPP\n"):
                return line.removeprefix("#ifndef").strip()
    return None

def _replace_confirm(old:str, new:str) ->bool:
    """
    确认是否替换
    """
    print("将 \"{}\" 替换为 \"{}\"".format(old, new))
    return input("确认替换？(y/n)") == "y"

def _replace_define(f:TextIOWrapper,lines: list[str], old:str, new:str):
    """
    替换文件中的宏定义
    """
    f.seek(0)
    f.truncate()
    for line in lines:
        if line.__contains__(old):
            new_line = line.replace(old, new)
            f.writelines(new_line)
        else:
            f.writelines(line)

def _update_file(urq:str, file:str):
    """
    更新文件

    Args:
        file (str): 文件绝对路径 
    """
    # 需要更新的文件名
    new_guard = _get_new_guard(urq=urq, file=file)
    with open(file, mode= "r+", encoding="utf-8") as f:
        lines = f.readlines()
        old_guard = _find_old_define(lines)

        if old_guard == None or old_guard == "":
            if file.endswith(".h"):
                dir = file[:-2]
            if file.endswith(".hpp"):
                dir = file[:-4]
            
            if os.path.exists(dir): 
                # 忽略目录
                return
            print("没有找到宏定义: {}".format(file))
            return

        if old_guard == new_guard:
            print("{}: {}".format(new_guard, file[len(urq)+1:]))
            return

        if _replace_confirm(old_guard, new_guard):
            _replace_define(f,lines, old_guard, new_guard)
        else:
            print("取消替换, 文件: {}".format(file))
            return

def _help():
    print("更新 #define 宏定义")
    print("usage: {} 需要更新的文件名".format(sys.argv[0]))
    sys.exit(1)

def _all_project():
    ui_folder = os.path.join(__file__ , "../..")
    ui_folder = os.path.abspath(ui_folder)
    yield os.path.join(ui_folder,"core")
    yield os.path.join(ui_folder,"cxx")
    yield os.path.join(ui_folder,"lib")
    yield os.path.join(ui_folder,"parse")
    yield os.path.join(ui_folder,"ui")

def _read_header_files(folder:str): 
    dirs = [folder]

    while(len(dirs) > 0):
        dir = dirs.pop()
        names = os.listdir(dir)

        for n in names:
            n = os.path.join(dir,n)
            if os.path.isdir(n):
                dirs.append(n)
                continue

            if n.endswith(".h") or n.endswith(".hpp"):
                yield n

def run():
    """
    更新所有的 .h 和 .hpp 文件宏定义
    """

    for folder in _all_project():
        for f in _read_header_files(folder=folder):
            _update_file(folder,f)

run()