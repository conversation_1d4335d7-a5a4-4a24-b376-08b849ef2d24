#!/usr/bin/env python3

import os
import sys

def _update_file(urq:str, file:str):
    """
    更新文件，如果文件不以 \n 结尾，会自动添加

    Args:
        file (str): 文件绝对路径 
    """
    with open(file, mode= "r+", encoding="utf-8") as f:
        content = f.read()
        if not content.endswith("\n"):
            f.write("\n")
            print("添加了换行符到文件 {}".format(file))


def _all_project():
    ui_folder = os.path.join(__file__ , "../..")
    ui_folder = os.path.abspath(ui_folder)
    yield os.path.join(ui_folder,"core")
    yield os.path.join(ui_folder,"cxx")
    yield os.path.join(ui_folder,"lib")
    yield os.path.join(ui_folder,"ui")

def _read_header_files(folder:str): 
    dirs = [folder]

    while(len(dirs) > 0):
        dir = dirs.pop()
        names = os.listdir(dir)

        for n in names:
            n = os.path.join(dir,n)
            if os.path.isdir(n):
                dirs.append(n)
                continue

            if n.endswith(".h") or n.endswith(".hpp"):
                yield n

def run():
    """
    更新所有的 .h 和 .hpp 文件宏定义
    """

    for folder in _all_project():
        for f in _read_header_files(folder=folder):
            _update_file(folder,f)

run()