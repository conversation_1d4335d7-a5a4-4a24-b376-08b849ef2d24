#!/usr/bin/env python3

import argparse
import os
import re


class CommandLineArgs:
    """
    # 命令行参数
    """

    folder: str
    """
    # 创建 widget 的目录
    """

    name: str
    """
    # 创建 widget 的文件名
    """

    def __init__(self):
        pass

    def __str__(self):
        return f"""folder: {self.folder}
name: {self.name}"""


def _get_guard(name:str) ->str:
    guard = re.sub(r".+\/urq\/ui\/[^\/]+\/", "", name)
    guard = guard.replace("/", "__")
    guard = guard.replace(".", "_")
    guard = guard.upper()
    return guard

def _get_widget_name(name:str) ->str:
    n = re.sub(r".+\/urq\/ui\/[^\/]+\/urq\/", "", name)
    n = n.replace("/", "_")
    n = n[:-2]
    return n

def _create_header_file(folder:str, name:str):
    """
    # 创建头文件

    """
    filepath = os.path.join(folder, name + ".h")
    guard = _get_guard(filepath)
    widget_name = _get_widget_name(filepath)

    template_file = os.path.join(os.path.dirname(__file__), "widget.h")

    with open(template_file, "r") as f:
        template = f.read()

    template = template.replace("template_widget", widget_name)
    template = template.replace("// guard", f"""#ifndef {guard}
#define {guard}
#ifdef __cplusplus
extern "C" {{
#endif""")

    template += f"""
#ifdef __cplusplus
}}
#endif
#endif // {guard}
"""

    with open(filepath, "w") as f:
        f.write(template)

def _create_source_file(folder:str, name:str):  
    """
    # 创建源文件

    """
    filepath = os.path.join(folder, name + ".c")
    widget_name = _get_widget_name(filepath)

    template_file = os.path.join(os.path.dirname(__file__), "widget.c")
    with open(template_file, "r") as f:
        template = f.read()
    
    template = template.replace('"widget.h"', f'"urq/{widget_name.replace("_","/")}.h"')
    template = template.replace("template_widget", widget_name)

    with open(filepath, "w") as f:
        f.write(template)

def gen():
    """
    # 生成 widget

    """

    parser = argparse.ArgumentParser(description="gen widget")
    parser.add_argument("--folder", type=str, required=True, help="destination folder")
    parser.add_argument("--name",   type=str, required=True, help="file name")
    args = parser.parse_args(namespace=CommandLineArgs())
    args.folder = os.path.abspath(args.folder)

    _create_header_file(args.folder, args.name)
    _create_source_file(args.folder, args.name)



if __name__ == "__main__":
    gen()