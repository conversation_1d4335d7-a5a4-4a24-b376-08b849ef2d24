#!/usr/bin/env python3

# version: 0.0.1

import argparse
import http.server
import os
import socketserver

_PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

class CommandLineArgs:
    """
    # 命令行参数
    """

    __port: int
    """ 指定端口号, 整数类型，默认值为 8080 """

    def __init__(self):
        self.__port = 8080


    @property
    def port(self) -> int:
        return self.__port
    @port.setter
    def port(self, value: int):
        if value < 0 or value > 65535:
            raise ValueError("Invalid port")
        self.__port = value

class CustomHandler(http.server.SimpleHTTPRequestHandler):

    __root_list: list[str]
    """ 根目录列表 """

    def __init__(self, *args, directory=None, **kwargs):
        self.__root_list = [
            os.path.join(_PROJECT_DIR, "asset"),
            os.path.join(_PROJECT_DIR, "build/wasm/debug/ui"),
            # os.path.join( os.environ.get("HOME"), "code/cpm/asset")
        ]
        super().__init__(*args, directory=None, **kwargs)

    def send_head(self):
        if "If-Modified-Since" in self.headers:
            del self.headers["If-Modified-Since"]

        return super().send_head()

class ThreadedTCPServer(socketserver.ThreadingMixIn, socketserver.TCPServer):
    allow_reuse_address = True  # 允许地址重用

def run():
    parser = argparse.ArgumentParser(description="Run the test")
    parser.add_argument("--port", type=int, default=8080, help="The port to run the server on")
    args = parser.parse_args(namespace=CommandLineArgs())

    with ThreadedTCPServer(("127.0.0.1", args.port), CustomHandler) as httpd:
        # 获取本机所有的IP地址
        print(f"serving addr: http://127.0.0.1:{args.port}")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("server closed")
            httpd.server_close()
            pass


if __name__ == "__main__":
    run()