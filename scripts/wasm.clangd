CompileFlags:
    CompilationDatabase: build/wasm/debug
    Remove:
        - -gsource-map
        - -sNO_EXIT_RUNTIME=1
    Add:
        - -I{HOME}/toolchain/emsdk/upstream/emscripten/cache/sysroot/include
        - -I{HOME}/toolchain/emsdk/upstream/emscripten/cache/sysroot/include/c++/v1
---
If:
    PathMatch: [.*\.cpp, .*\.hpp]
CompileFlags:
    Add:
        - -xc++
        - --std=c++17
# Diagnostics:
#     ClangTidy:
---
If:
    PathMatch: [.*\.c, .*\.h]
CompileFlags:
    Add:
        - -xc
        - --std=c11
        - -D_POSIX_C_SOURCE=199309L
# Diagnostics:
#     ClangTidy:
