#pragma once

#include "lvgl.h"
#include "urq/user_data.h"

// guard

typedef struct {
    /// @brief 父组件
    lv_obj_t super;
    /// @brief user data 字段
    urq_user_data_t user_data;
} urq_template_widget_t;

/// @brief 创建 template_widget
/// @param parent 当前组件所属的页面
/// @returns 组件
lv_obj_t *urq_template_widget_create(lv_obj_t *parent)
    __attribute__((__nonnull__(1)));

/// @brief 配置 template_widget
/// @param self     组件自身
/// @param mut_conf 配置的内容
/// @returns 组件
void urq_template_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
    __attribute__((__nonnull__(1, 2)));
