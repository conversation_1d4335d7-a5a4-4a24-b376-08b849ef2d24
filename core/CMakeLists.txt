cmake_minimum_required(VERSION 3.22)

# 如果 URQ_ROOT_PROJECT 未设置，则添加 project
if(NOT URQ_ROOT_PROJECT)
    message(STATUS "urq_core build as top level project")
    project("urq_core" VERSION 0.0.1)
else()
    message(STATUS "urq_core build as sub project")
endif()

include("${CMAKE_CURRENT_LIST_DIR}/../cmake/configure-file.cmake")

file(GLOB_RECURSE SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.c"
)

# 忽略 linux 平台特定的 .c 文件
list(FILTER SOURCES EXCLUDE REGEX ".+\\.[a-z]+\\.c$")

set(URQ_CURRENT_BUILD urq_core)
add_library(${URQ_CURRENT_BUILD} STATIC ${SOURCES})

# target_compile_features(${URQ_CURRENT_BUILD} PUBLIC c_std_11)
target_include_directories(${URQ_CURRENT_BUILD} PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})


set(klib_DIR "${CMAKE_PREFIX_PATH}/klib")
find_package(klib REQUIRED)
target_include_directories(${URQ_CURRENT_BUILD} SYSTEM PUBLIC "${klib_INCLUDE_DIRS}")
target_link_libraries(${URQ_CURRENT_BUILD} PUBLIC ${klib_LIBRARIES})

configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/urq/compile.h.in" 
    "${CMAKE_CURRENT_SOURCE_DIR}/urq/compile.h" 
)