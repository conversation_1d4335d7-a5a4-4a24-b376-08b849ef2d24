#pragma once

#include <stdbool.h>
#include <stddef.h>

#ifndef URQ__PATH_H
#define URQ__PATH_H
#ifdef __cplusplus
extern "C" {
#endif

// static const char *URQ_PATH_CONF_STR[] = {
//     "0", "1", "2", "3", "4", "5", "6", "7", "8", "10", "11", "12", "13",
// };

/// @brief 路径的最大长度
#define URQ_PATH_MAX 256

/// @brief 路径分隔符
#define URQ_PATH_SEP '/'

/// @brief 工程的文件夹
#define URQ_PATH_ROOT_PROJECT_FOLDER "S:p/%s"

/// @brief 默认 proto 文件名，如果只有单个文件的话
#define URQ_PATH_CONF_DEFAULT_FILE 0

/// @brief project 配置目录名
#define URQ_PATH_CONF_FOLDER_PROJECT 1

/// @brief display 配置目录名
#define URQ_PATH_CONF_FOLDER_DISPLAY 2

/// @brief widgets 配置目录名
#define URQ_PATH_CONF_FOLDER_WIDGETS 3

/// @brief device 配置目录名
#define URQ_PATH_CONF_FOLDER_DEVICE 4

/// @brief address tag 变量标签配置
#define URQ_PATH_CONF_FOLDER_ATAG 5

/// @brief style 样式配置
#define URQ_PATH_CONF_FOLDER_STYLE 6

/// @brief language 配置目录名
#define URQ_PATH_CONF_FOLDER_LANGUAGE 7

/// @brief font 配置目录名
#define URQ_PATH_CONF_FOLDER_FONT 8

/// @brief image 配置目录名
#define URQ_PATH_CONF_FOLDER_IMAGE 10

// 工程图库列表(这个还是会下载到设备端，后续上载时可以从云端根据md5读源文件)
#define URQ_PATH_CONF_FOLDER_PROJECT_GALLERY 11

// 图片源文件（工程图库，一个图片一个文件，这个不会下载到设备端）
#define URQ_PATH_CONF_FOLDER_PROJECT_GALLERY_FILE 12

// 页面元件属性配置地址（一个页面一个文件）
#define URQ_PATH_CONF_FOLDER_PAGE_WIDGET_PROPERTY_ADDRESS 13

/// @brief 获取路径的目录名，这个方法会在输入目录的特定位置添加 '\0'
/// 截断字符串，返回新的字符串
/// @param mv_path 可变路径
/// @return 目录名
char *urq_path_dirname(char *mv_path) __attribute__((__nonnull__(1)))
__attribute__((__warn_unused_result__()));

#ifdef __cplusplus
}
#endif
#endif // URQ__PATH_H
