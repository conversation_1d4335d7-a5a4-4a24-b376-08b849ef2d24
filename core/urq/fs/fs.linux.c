#include "urq/compile.h"

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64 ||                   \
    URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_ARM_V7HF

#include "urq/errno.h"
#include "urq/fs/file.h"
#include "urq/fs/fs.h"
#include "urq/log/debug.h"
#include "urq/path.h"
#include "urq/preload.h"
#include <errno.h>
#include <fcntl.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <unistd.h>

char URQ_WORKING_FOLDER[URQ_PATH_MAX];

int urq_fs_mod_init(const int argc, char *argv[])
{
    char *project_root;
    project_root = NULL;
    for (int i = 1; i < argc - 1; i++) {
        if (strcmp(argv[i], "--project") == 0) {
            project_root = argv[i + 1];
            break;
        }
    }

    if (project_root == NULL || project_root[0] != '/') {
        log_e("project root is not set or not absolute path");
        return -1;
    }

    strncpy(URQ_WORKING_FOLDER, project_root, URQ_PATH_MAX);
    return 0;
}

static inline int _check_path(const char *const path)
{
    if (path[0] != 'S' || path[1] != ':') {
        log_w("invalid path, path: %s\n", path);
        errno = EINVAL;
        return -1;
    }
    return 0;
}

static inline int _to_full_path(const char *const path, char *const out_path)
{
    if (snprintf(
            out_path, URQ_PATH_MAX, "%s/%s", URQ_WORKING_FOLDER, path + 2) ==
        -1) {
        errno = EINVAL;
        return -1;
    }
    return 0;
}

int urq_fs_exists(urq_fs_t *self, const char *const path, size_t *out_size)
{
    urq_used(self);

    if (_check_path(path)) {
        errno = EINVAL;
        return -1;
    }
    char full_path[URQ_PATH_MAX];

    if (_to_full_path(path, full_path)) {
        errno = EINVAL;
        return -1;
    }

    struct stat st;
    if (stat(full_path, &st) != 0) {
        errno = ENOENT;
        return -1;
    }
    if (out_size != NULL) {
        *out_size = (size_t)st.st_size;
    }
    return 0;
}

int urq_fs_load_file(urq_fs_t *self, const char *const path)
{
    urq_used(self);
    urq_used(path);
    return 0;

    // TODO: 测试代码
    static int count = 0;
    count += 1;
    if (count == 8) {
        count = 0;
        return 0;
    } else {
        errno = URQ_EPENDING;
        return -1;
    }

    return 0;
}

int urq_fs_read_file(
    urq_fs_t *fs, const char *const path, urq_fs_file_t **out_file)
{
    int fd;
    char full_path[URQ_PATH_MAX];
    ssize_t read_len;
    struct stat st;
    urq_fs_file_t *file;

    urq_used(fs);

    if (_check_path(path)) {
        return -1;
    }

    if (_to_full_path(path, full_path)) {
        return -1;
    }

    if (stat(full_path, &st) != 0) {
        errno = URQ_ENOTFOUND;
        log_i("file not found: %s\n", full_path);
        return -1;
    }
    log_v("file size: %zu bytes, path: %s\n", st.st_size, path);

    file = urq_fs_file_new((uint32_t)st.st_size);
    if (file == NULL) {
        log_i("read file no mem, size: %zu, file: %s\n", st.st_size, full_path);
        errno = ENOMEM;
        return -1;
    }

    fd = open(full_path, O_RDONLY);
    if (fd == -1) {
        log_i("open file error\n");
        urq_free(file);
        return -1;
    }

    file->size = 0;
    while (true) {
        read_len = read(
            fd, urq_fs_file_data_mut(file) + file->size,
            (uint32_t)(st.st_size - file->size));
        file->size += (uint32_t)read_len;
        if (read_len == -1 || file->size > st.st_size) {
            log_w(
                "read file error, error: %s, file: %s\n", strerror(errno),
                full_path);
            urq_free(file);
            close(fd);
            return -1;
        }
        if (file->size == st.st_size) {
            break;
        }
    }
    close(fd);
    *out_file = file;

    log_v("read file success, file: %s\n", path);
    return 0;
}

#endif // linux_64 || arm_v7hf
