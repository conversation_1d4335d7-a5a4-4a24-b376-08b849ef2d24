#pragma once

#include "urq/compile.h"
#include "urq/fs/file.h"
#include "urq/preload.h"
#include <stdbool.h>
#include <stdio.h>
#include <string.h>

#ifndef URQ__FS__FS_H
#define URQ__FS__FS_H
#ifdef __cplusplus
extern "C" {
#endif

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64 ||                   \
    URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_ARM_V7HF

extern char URQ_WORKING_FOLDER[];

/// @brief 初始化文件系统
/// @param fs 文件系统
/// @returns 是否有错误
int urq_fs_mod_init(const int argc, char *argv[])
    __attribute__((__nonnull__(2), __warn_unused_result__()));

#endif

/// @brief 文件系统
///
/// 区别于常规的文件系统，这个文件系统还负责从远程主机读取文件
typedef struct {
    int id;                        // wasm 实例 id
    uint32_t ip;                   // 主机 ip
    uint16_t port;                 // 主机端口
    const char *project_root_path; // 工程根路径
    size_t project_root_len;       // 工程根路径长度
} urq_fs_t;

/// @brief 原地初始化
/// @param fs 文件系统
/// @return void
static inline void urq_fs_init_inplace(urq_fs_t *fs)
    __attribute__((__nonnull__(1)));

/// @brief 原地释放文件系统
/// @param fs 文件系统
/// @returns void
static inline void urq_fs_free_inplace(urq_fs_t *fs)
    __attribute__((__nonnull__(1)));

/// @brief 设置远程主机,设置这个方法前需要保证 host 的内存被释放了
/// @param mut_fs 文件系统
/// @param iid  全局唯一 id
/// @param ip  主机 ip
/// @param port 主机端口
/// @returns 0 成功，-1 失败
void urq_fs_set_host(urq_fs_t *mut_fs, int iid, uint32_t ip, uint16_t port)
    __attribute__((__nonnull__(1)));

/// @brief 设置工程唯一 id
/// @param mut_fs 文件系统
/// @param ref_id 工程唯一 id
/// @returns 0 成功，-1 失败
int urq_fs_set_project_id(urq_fs_t *mut_fs, const char *const ref_id)
    __attribute__((__nonnull__(1, 2)))
    __attribute__((__warn_unused_result__()));

/// @brief 加载文件
///
/// 这个方法的返回值只能用于识别文件是否处理 pending 状态
///
/// 只有 pending 状态才会返回 -1，并将 errno 设置为 URQ_EPENDING
/// @param self 文件系统
/// @param path 文件路径
/// @returns is pending
int urq_fs_load_file(urq_fs_t *self, const char *const path)
    __attribute__((__nonnull__(1, 2)))
    __attribute__((__warn_unused_result__()));

/// @brief 检查文件是否存在，
///
/// 如果文件还没加载完成，也返回 -1
///
/// @param self     文件系统
/// @param path     文件路径
/// @param out_size 文件大小，如果文件不存在，这个值是无效的，需要先判断
/// return，这个参数可以为 NULL
/// @returns
/// * 0  存在
/// * -1 不存在
int urq_fs_exists(urq_fs_t *self, const char *const path, size_t *out_size)
    __attribute__((__nonnull__(1, 2)))
    __attribute__((__warn_unused_result__()));

/// @brief 读取文件
///
/// @param self     文件系统
/// @param path     需要读取的文件，相对于工程根目录
/// @param out_file 读取到的内容，如果读取失败，这个值不一定是 NULL
/// @returns 错误码，可能的错误有
/// - ENOMEM: 内存不足
/// - ENOTFOUND: 文件不存在
/// - EIO: 读取文件失败
int urq_fs_read_file(
    urq_fs_t *self, const char *const path, urq_fs_file_t **out_file)
    __attribute__((__nonnull__(2, 3)))
    __attribute__((__warn_unused_result__()));

// impl

static inline void urq_fs_init_inplace(urq_fs_t *fs)
{
    fs->project_root_path = NULL;
}

static inline void urq_fs_free_inplace(urq_fs_t *fs)
{
    urq_free_if_not_null((void *)fs->project_root_path);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__FS__FS_H
