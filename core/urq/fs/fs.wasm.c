#include "urq/compile.h"
#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM

#include "urq/errno.h"
#include "urq/fs/file.h"
#include "urq/fs/fs.h"
#include "urq/log/verbose.h"
#include "urq/web.h"
#include <fcntl.h>
#include <stdlib.h>
#include <sys/stat.h>
#include <unistd.h>

static inline int _check_path(urq_fs_t *fs, const char *const path)
{
    if (path[0] != 'S' || path[1] != ':') {
        log_w("invalid path, path: %s\n", path);
        errno = EINVAL;
        return -1;
    }
    return 0;
}

int urq_fs_load_file(urq_fs_t *self, const char *const path)
{
    if (urq_web_fs_load_file(self->id, path + 2)) {
        errno = URQ_RELOAD;
        return -1;
    }

    return 0;
}

int urq_fs_exists(urq_fs_t *self, const char *const path, size_t *out_size)
{
    uint32_t size;
    if (urq_web_fs_exists(self->id, path + 2, &size)) {
        errno = ENOENT;
        return -1;
    }
    if (out_size != NULL) {
        *out_size = size;
    }
    return 0;
}

/// @brief 读取文件, 读取之前，需要先保证 out_file
/// 里面的内容是空的，否则会内存泄漏
/// @param self 文件系统
/// @param path 需要读取的文件
/// @param out_file 读取到的内容
/// @return 错误码
int urq_fs_read_file(
    urq_fs_t *self, const char *const path, urq_fs_file_t **out_file)
{
    if (_check_path(self, path)) {
        return -1;
    }
    if (urq_web_fs_read_file(self->id, path + 2, out_file)) {
        if (errno == URQ_EPENDING) {
            log_v("file pending: file: %s\n", path);
        } else {
            log_i("read file error, errno: %d, file: %s\n", errno, path);
        }
        return -1;
    }
    log_v("read file success, path: %s, length: %d\n", path, (*out_file)->size);
    return 0;
}

#endif // wasm
