#pragma once

#include "urq/preload.h"
#include <stddef.h>
#include <stdint.h>
#include <string.h>

#ifndef URQ__FS__FILE_H
#define URQ__FS__FILE_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 文件
typedef struct {
    /// @brief 文件内容的大小
    uint32_t size;
} urq_fs_file_t;

/// @brief 创建文件
static inline urq_fs_file_t *urq_fs_file_new(uint32_t size)
    __attribute__((__warn_unused_result__(), __malloc__()));

/// @brief 释放文件
/// @param file 文件
/// @returns void
static inline void urq_fs_file_free(urq_fs_file_t *file)
    __attribute__((__nonnull__(1)));

/// @brief 获取文件的内容
///
/// @param file 文件
/// @return 文件内容部分
static inline const uint8_t *urq_fs_file_data(const urq_fs_file_t *file)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 获取文件的内容
///
/// @param file 文件
/// @return 文件内容部分
static inline uint8_t *urq_fs_file_data_mut(urq_fs_file_t *file)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

// impl

static inline urq_fs_file_t *urq_fs_file_new(uint32_t size)
{
    urq_fs_file_t *file =
        (urq_fs_file_t *)urq_malloc(sizeof(urq_fs_file_t) + size);
    if (file == NULL) {
        return NULL;
    }
    file->size = size;
    return file;
}

static inline void urq_fs_file_free(urq_fs_file_t *file) { urq_free(file); }

static inline const uint8_t *urq_fs_file_data(const urq_fs_file_t *file)
{
    return (uint8_t *)(file + 1);
}

static inline uint8_t *urq_fs_file_data_mut(urq_fs_file_t *file)
{
    return (uint8_t *)(file + 1);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__FS__FILE_H
