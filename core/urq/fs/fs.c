#include "urq/fs/fs.h"
#include "urq/path.h"
#include <errno.h>
#include <string.h>

void urq_fs_set_host(urq_fs_t *mut_fs, int iid, uint32_t ip, uint16_t port)
{
    mut_fs->id = iid;
    mut_fs->ip = ip;
    mut_fs->port = port;
}

int urq_fs_set_project_id(urq_fs_t *mut_fs, const char *const ref_id)
{
    size_t name_len = strlen(ref_id);
    char *path;

    path =
        (char *)urq_malloc(name_len + sizeof(URQ_PATH_ROOT_PROJECT_FOLDER) + 1);
    if (path == NULL) {
        errno = ENOMEM;
        return -1;
    }
    snprintf(
        path, name_len + sizeof(URQ_PATH_ROOT_PROJECT_FOLDER) + 1,
        URQ_PATH_ROOT_PROJECT_FOLDER, ref_id);
    mut_fs->project_root_path = path;
    mut_fs->project_root_len = strlen(mut_fs->project_root_path);
    return 0;
}

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
#include "urq/fs/fs.wasm.c"
#else
#include "urq/fs/fs.linux.c"
#endif
