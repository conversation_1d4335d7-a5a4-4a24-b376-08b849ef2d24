#pragma once
#include "urq/compile.h"
#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
#ifdef __STRICT_ANSI__
#pragma message "__STRICT_ANSI__"
#undef __STRICT_ANSI__
#endif

#include "urq/fs/file.h"
#include <emscripten.h> // IWYU pragma: export
#include <stdint.h>

#ifndef URQ__WEB_H
#define URQ__WEB_H
#ifdef __cplusplus
extern "C" {
#endif

/// 事件结构体
typedef struct {
    /// 鼠标事件的 x 坐标
    int32_t x;
    /// 鼠标事件的 y 坐标
    int32_t y;
    /// 是否按下 0: released, 1: pressed
    int32_t pressure;
    /// 是否有下一个事件， 0: 没有，1: 有
    int32_t next;
} urq_web_mouse_event_t;

/// 事件结构体
typedef struct {
    /// 键盘事件的键值
    int32_t key;
    /// 是否按下 0: released, 1: pressed
    int32_t pressure;
    /// 是否有下一个事件， 0: 没有，1: 有
    int32_t next;
} urq_web_keyboard_event_t;

/// 刷新 web 显示设备
///
/// @param area 刷新区域
/// @param px_map 像素映射
/// @return void
__attribute__((__nonnull__(6))) static inline void urq_web_display_flush(
    int id, int32_t x1, int32_t y1, int32_t x2, int32_t y2, uint8_t *px_map)
{
    EM_ASM_(
        { urq_web_display_flush($0, $1, $2, $3, $4, $5); }, id, x1, y1, x2, y2,
        px_map);
}

/// @brief 加载一个文件
/// @param id       wasm id
/// @param path     文件路径
/// @return 文件是否已经加载，加载失败了也返回 true
static inline bool urq_web_fs_load_file(int id, const char *const path)
{
    return EM_ASM_({ return urq_web_fs_load_file($0, $1); }, id, path);
}

/// @brief 判断文件是否存在
///
/// 如果文件还没加载完成，也返回 -1
///
/// @param id   wasm id
/// @param path 文件路径
/// @param out_size 文件大小，如果文件不存在，这个值是无效的，需要先判断 return
/// @return 文件是否存在
__attribute__((__nonnull__(2, 3)))
__attribute__((__warn_unused_result__())) static inline bool
urq_web_fs_exists(int id, const char *const path, uint32_t *out_size)
{
    return EM_ASM_(
        { return urq_web_fs_exists($0, $1, $2); }, id, path, out_size);
}

/// @brief 读取一个文件
/// @param id       wasm id
/// @param path     文件路径
/// @param out_file 输出数据
/// @return 错误码
static inline int urq_web_fs_read_file(
    int id, const char *const path, urq_fs_file_t **out_file)
{
    return EM_ASM_(
        { return urq_web_fs_read_file($0, $1, $2); }, id, path, out_file);
}

/// @brief 打开一个文件
/// @param path   文件路径
/// @return 错误码
static inline int urq_web_fs_open(
    int32_t iid, const char *const path, void **out_file)
{
    return EM_ASM_(
        { return urq_web_fs_open($0, $1, $2); }, iid, path, out_file);
}

/// @brief seek 文件
/// @param id     wasm id
/// @param fd     文件描述符
/// @param pos    偏移量
/// @param whence 偏移方式
/// @return 错误码
static inline int urq_web_fs_seek(
    int id, int32_t fd, int32_t pos, int32_t whence)
{
    return EM_ASM_(
        { return urq_web_fs_seek($0, $1, $2, $3); }, id, fd, pos, whence);
}

/// 创建一个显示设备
///
/// @param width 显示设备的宽度
/// @param height 显示设备的高度
/// @return void
static inline void urq_web_create_display(
    int id, uint32_t width, uint32_t height)
{
    EM_ASM({ urq_web_create_display($0, $1, $2); }, id, width, height);
}

/// 读取 web 事件
///
/// @param id    js 那边的上下文 id
/// @param event 事件结构体
/// @return void
__attribute__((__nonnull__(2))) static inline void urq_web_read_mouse(
    int32_t id, urq_web_mouse_event_t *event)
{
    EM_ASM({return urq_web_read_mouse($0, $1)}, id, event);
}

/// 读取 web 事件
///
/// @param id    js 那边的上下文 id
/// @param event 事件结构体
/// @return void
__attribute__((__nonnull__(2))) static inline void urq_web_read_keyboard(
    int32_t id, urq_web_keyboard_event_t *event)
{
    EM_ASM({return urq_web_read_keyboard($0, $1)}, id, event);
}

// ---------------------------- socket ----------------------------

/// @brief 创建 socket 连接
/// @param wasm_id wasm 实例的 id
/// @return socket 描述符，如果创建失败，返回 -1
__attribute__((__warn_unused_result__())) static inline int
urq_web_socket_create(int32_t iid)
{
    return EM_ASM_({return urq_web_socket_create($0)}, iid);
}

/// @brief 删除 socket 连接
/// @param fd 需要删除的 socket 描述符
/// @return void
static inline void urq_web_socket_delete(int fd)
{
    EM_ASM_({ urq_web_socket_delete($0); }, fd);
}

/// @brief 连接到服务器
/// @param fd socket 描述符
/// @returns 是否连接成功
/// -  0: 连接成功
/// - -1: 连接失败
/// -  1: 连接中
__attribute__((__warn_unused_result__())) static inline int
urq_web_socket_connect(int fd)
{
    return EM_ASM_({ return urq_web_socket_connect($0); }, fd);
}

/// @brief 是否可读
/// @param fd socket 描述符
/// @return 是否可读
/// -  1: 可读
/// -  0: 超时
/// - -1: 出错
__attribute__((__warn_unused_result__)) static inline bool
urq_web_socket_readable(int fd)
{
    return EM_ASM_({ return urq_web_socket_readable($0); }, fd);
}

/// @brief 读取数据
/// @param fd       socket
/// @param size     需要读取的长度
/// @param out_data 读进这里
/// @param out_size 读取到的长度
/// @return 读取到的长度
static inline int urq_web_socket_read(
    int fd, uint32_t size, uint8_t *out_data, uint32_t *out_size)
{
    return EM_ASM_(
        { return urq_web_socket_read($0, $1, $2, $3); }, fd, size, out_data,
        out_size);
}

/// @brief 设置 socket 连接的主机
/// @param fd   socket 描述符
/// @param ip   ip 地址
/// @param port 端口
/// @return void
static inline void urq_web_socket_set_host(int fd, uint32_t ip, uint16_t port)
{
    EM_ASM_({ urq_web_socket_set_host($0, $1, $2); }, fd, ip, port);
}

/// @brief 写入数据
/// @param fd   socket 描述符
/// @param data 数据
/// @param len  数据长度
/// @return 是否写入成功
__attribute__((__warn_unused_result__)) static inline int urq_web_socket_write(
    int fd, const void *data, size_t len)
{
    return EM_ASM_({ return urq_web_socket_write($0, $1, $2); }, fd, data, len);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__WEB_H

#endif
