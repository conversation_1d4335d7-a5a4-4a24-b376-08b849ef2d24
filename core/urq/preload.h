#pragma once

#include "urq/compile.h" // IWYU pragma: export
#include <stddef.h>
#include <stdint.h>
#include <string.h>

// 只有 release 才让 malloc 可见，debug 时 malloc 由 .c 文件自行包含
#if URQ_COMPILE_MODE == URQ_COMPILE_MODE_RELEASE
#include <stdlib.h> // IWYU pragma: export
#endif

#ifndef URQ__PRELOAD_H
#define URQ__PRELOAD_H

/// @brief 数组引用类
#define urq_arr_t(t) t *

// EXTERN_C
#ifdef __cplusplus
#define EXTERN_C extern "C"
#else
#define EXTERN_C
#endif

#if URQ_COMPILE_MODE == URQ_COMPILE_MODE_DEBUG     // malloc begin
EXTERN_C void *urq_calloc(size_t num, size_t size) // malloc debug
    __attribute__((__malloc__())) __attribute__((__warn_unused_result__()));
#else
#ifdef __cplusplus
#define urq_calloc(num, size) ::calloc(num, size) // malloc release cxx
#else
#define urq_calloc(num, size) calloc(num, size) // malloc release c
#endif
#endif // malloc end

#if URQ_COMPILE_MODE == URQ_COMPILE_MODE_DEBUG // malloc begin
EXTERN_C void *urq_malloc(size_t size)         // malloc debug
    __attribute__((__malloc__())) __attribute__((__warn_unused_result__()));
#else
#ifdef __cplusplus
#define urq_malloc(size) ::malloc(size) // malloc release cxx
#else
#define urq_malloc(size) malloc(size) // malloc release c
#endif
#endif // malloc end

#if URQ_COMPILE_MODE == URQ_COMPILE_MODE_DEBUG // free begin
EXTERN_C void urq_free(void *ptr);             // free debug
#else
#ifdef __cplusplus
#define urq_free(ptr) ::free(ptr) // free release cxx
#else
#define urq_free(ptr) free(ptr) // free release c
#endif
#endif // free end

#if URQ_COMPILE_MODE == URQ_COMPILE_MODE_DEBUG     // realloc begin
EXTERN_C void *urq_realloc(void *ptr, size_t size) // realloc debug
    __attribute__((__warn_unused_result__()));
#else
#ifdef __cplusplus
#define urq_realloc(ptr, size) ::realloc(ptr, size) // realloc release cxx
#else
#define urq_realloc(ptr, size) realloc(ptr, size) // realloc release c
#endif
#endif // realloc end

// ================ c/c++ 兼容 ================
#ifdef __cplusplus
extern "C" {
#endif

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
extern int URQ_PROJECT_WIDTH;
extern int URQ_PROJECT_HEIGHT;
#else
#define URQ_PROJECT_WIDTH  800
#define URQ_PROJECT_HEIGHT 600
#endif

/// @brief 标记变量未使用
#define urq_used(x) ((void)x)

/// @brief 画面 id
typedef uint16_t urq_page_id_t;

/// @brief 画面数量
typedef int16_t urq_page_size_t;

/// @brief 无效的画面 id
#define URQ_PAGE_ID_INVALID -1

/// @brief 释放指针，如果指针不为空
static inline void urq_free_if_not_null(void *ptr);

static inline void urq_noop0(void) {}
static inline void urq_noop1(void *a1) { urq_used(a1); }
static inline void urq_noop2(void *a1, void *a2)
{
    urq_used(a1);
    urq_used(a2);
}
static inline void urq_noop3(void *a1, void *a2, void *a3)
{
    urq_used(a1);
    urq_used(a2);
    urq_used(a3);
}
static inline void urq_noop4(void *a1, void *a2, void *a3, void *a4)
{
    urq_used(a1);
    urq_used(a2);
    urq_used(a3);
    urq_used(a4);
}
static inline void urq_noop5(void *a1, void *a2, void *a3, void *a4, void *a5)
{
    urq_used(a1);
    urq_used(a2);
    urq_used(a3);
    urq_used(a4);
    urq_used(a5);
}
static inline void urq_noop6(
    void *a1, void *a2, void *a3, void *a4, void *a5, void *a6)
{
    urq_used(a1);
    urq_used(a2);
    urq_used(a3);
    urq_used(a4);
    urq_used(a5);
    urq_used(a6);
}
static inline void urq_noop7(
    void *a1, void *a2, void *a3, void *a4, void *a5, void *a6, void *a7)
{
    urq_used(a1);
    urq_used(a2);
    urq_used(a3);
    urq_used(a4);
    urq_used(a5);
    urq_used(a6);
    urq_used(a7);
}
static inline void urq_noop8(
    void *a1, void *a2, void *a3, void *a4, void *a5, void *a6, void *a7,
    void *a8)
{
    urq_used(a1);
    urq_used(a2);
    urq_used(a3);
    urq_used(a4);
    urq_used(a5);
    urq_used(a6);
    urq_used(a7);
    urq_used(a8);
}
static inline void urq_noop9(
    void *a1, void *a2, void *a3, void *a4, void *a5, void *a6, void *a7,
    void *a8, void *a9)
{
    urq_used(a1);
    urq_used(a2);
    urq_used(a3);
    urq_used(a4);
    urq_used(a5);
    urq_used(a6);
    urq_used(a7);
    urq_used(a8);
    urq_used(a9);
}

// ------------------- impl -------------------

static inline void urq_free_if_not_null(void *ptr)
{
    if (ptr != NULL) {
        urq_free(ptr);
    }
}

char *urq_strdup(const char *src);

#ifdef __cplusplus
}
#endif
#endif // URQ__PRELOAD_H
