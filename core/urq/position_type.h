#pragma once

#ifndef URQ__POSITION_TYPE_H
#define URQ__POSITION_TYPE_H
#ifdef __cplusplus
extern "C" {
#endif

/*
    POSITION_TYPE_UNSPECIFIED = 0;
    POSITION_TYPE_LEFT_TOP = 1;
    POSITION_TYPE_CENTER_TOP = 2;
    POSITION_TYPE_RIGHT_TOP = 3;
    POSITION_TYPE_LEFT_CENTER = 4;
    POSITION_TYPE_CENTER_CENTER = 5;
    POSITION_TYPE_RIGHT_CENTER = 6;
    POSITION_TYPE_LEFT_BOTTOM = 7;
    POSITION_TYPE_CENTER_BOTTOM = 8;
    POSITION_TYPE_RIGHT_BOTTOM = 9;
    */

typedef enum {
    /// @brief 未指定
    URQ_POSITION_TYPE_UNSPECIFIED = 0,
    /// @brief left top
    URQ_POSITION_TYPE_LEFT_TOP = 1,
    /// @brief center top
    URQ_POSITION_TYPE_CENTER_TOP = 2,
    /// @brief right top
    URQ_POSITION_TYPE_RIGHT_TOP = 3,
    /// @brief left center
    URQ_POSITION_TYPE_LEFT_CENTER = 4,
    /// @brief center center
    URQ_POSITION_TYPE_CENTER_CENTER = 5,
    /// @brief right center
    URQ_POSITION_TYPE_RIGHT_CENTER = 6,
    /// @brief left bottom
    URQ_POSITION_TYPE_LEFT_BOTTOM = 7,
    /// @brief center bottom
    URQ_POSITION_TYPE_CENTER_BOTTOM = 8,
    /// @brief right bottom
    URQ_POSITION_TYPE_RIGHT_BOTTOM = 9,
} urq_position_type_t;

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__POSITION_TYPE_H
