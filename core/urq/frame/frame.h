#pragma once

#include <stdint.h>

#ifndef URQ__FRAME__FRAME_H
#define URQ__FRAME__FRAME_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 帧数， 30 * 3600 * 24 * 365 * 4 = e1902400
typedef uint32_t urq_frame_t;

/// @brief 帧率
#define URQ_FRAME_RATE 30

/// @brief 帧时间(ms)
#define URQ_FRAME_TIME (1000 / URQ_FRAME_RATE)

/// @brief 系统启动之后到现在的总帧数
extern urq_frame_t urq_frame_total;

#ifdef __cplusplus
}
#endif
#endif // URQ__FRAME__FRAME_H
