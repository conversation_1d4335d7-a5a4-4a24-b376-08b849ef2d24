#include "urq/preload.h"
#include "urq/log/debug.h"
#include <stdlib.h>

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
int URQ_PROJECT_WIDTH = 800;
int URQ_PROJECT_HEIGHT = 600;
#endif

#if URQ_COMPILE_MODE == URQ_COMPILE_MODE_DEBUG

void *urq_calloc(size_t num, size_t size)
{
    void *ptr = calloc(num, size);
    log_v("malloc: %p\n", ptr);
    return ptr;
}

void *urq_malloc(size_t size)
{
    void *ptr = malloc(size);
    log_v("malloc: %p\n", ptr);
    return ptr;
}

void urq_free(void *ptr)
{
    log_v("free: %p\n", ptr);
    free(ptr);
}

void *urq_realloc(void *ptr, size_t size)
{
    void *new_ptr = realloc(ptr, size);
    log_v("realloc: %p -> %p\n", ptr, new_ptr);
    return new_ptr;
}

char *urq_strdup(const char *src)
{
    size_t len = strlen(src) + 1;
    char *dst = urq_malloc(len);
    if (dst) {
        memcpy(dst, src, len);
    } else {
        return NULL;
    }
    return dst;
}
#endif
