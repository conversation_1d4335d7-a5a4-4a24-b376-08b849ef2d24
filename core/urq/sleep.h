#pragma once

#include "urq/compile.h"
#include "urq/time/time.h"

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
#include <emscripten.h>
#endif

#ifndef URQ__SLEEP_H
#define URQ__SLEEP_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief sleep(ms)
/// @param ms 毫秒
static inline void urq_sleep(urq_time_t ms)
{
#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
    emscripten_sleep(ms);
#else
    struct timespec ts;
    urq_time_ms2ts(ms, &ts);
    nanosleep(&ts, NULL);
#endif
}

#ifdef __cplusplus
}
#endif
#endif // URQ__SLEEP_H
