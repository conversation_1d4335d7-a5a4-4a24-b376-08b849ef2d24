#pragma once

// #include <asm-generic/errno-base.h>
// #include <asm-generic/errno.h>
#include <errno.h> // IWYU pragma: export

#ifndef URQ__ERRNO_H
#define URQ__ERRNO_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    URQ_EOK = 0, // 大丈夫
    URQ_EEBUSY = EBUSY,
    URQ_EINVAL = EINVAL,
    URQ_ENOENT = ENOENT,
    URQ_ENOMEM = ENOMEM,

    // urq
    URQ_EMIN = 10000, // min
    // 常用
    URQ_EUNKNOWN,  // 未知
    URQ_ENOT_IMPL, // 未实现
    URQ_EPENDING,  // 等待中
    URQ_ENOTFOUND, // 找不到
    URQ_ECLOSED,   // 连接已关闭
    URQ_EEXISTS,   // 已存在
    URQ_RELOAD,    // 需要重新加载
    URQ_ERRNO_MAX, // max
} urq_errno_t;

/// @brief 获取错误的名称
/// @param errno 错误码
/// @returns 错误名称
const char *urq_errno_to_string(int no);

#define OK URQ_ERRNO_OK

#ifdef __cplusplus
}
#endif
#endif // URQ__ERRNO_H
