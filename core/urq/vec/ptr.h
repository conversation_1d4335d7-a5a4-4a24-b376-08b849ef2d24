#pragma once

#include "urq/preload.h"
#include "urq/vec/helper.h"
#include <errno.h>
#include <stddef.h>
#include <string.h>

#ifndef URQ__VEC__PTR_H
#define URQ__VEC__PTR_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    size_t capacity;            // 容量
    size_t size;                // 大小
    void (*destructor)(void *); // 析构函数，用于释放数据，可以为 NULL
    urq_arr_t(void *) data; // 数据
} urq_vec_ptr_t;

/// @brief 原地初始化 ptr 向量
/// @param self 向量
/// @param destructor 析构函数
/// @returns errno
static inline void urq_vec_ptr_init_inplace(
    urq_vec_ptr_t *self, void (*destructor)(void *))
    __attribute__((__nonnull__(1)));

/// @brief 原地释放 ptr 向量
/// @param self 向量
/// @returns void
static inline void urq_vec_ptr_free_inplace(urq_vec_ptr_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 克隆列表
/// @param dst 目标列表
/// @param src 源列表
/// @returns 是否克隆成功
static inline int urq_vec_ptr_clone(
    urq_vec_ptr_t *const dst, const urq_vec_ptr_t *const src)
    __attribute__((__nonnull__(1, 2)));

/// @brief 将列表 src 的数据移动到 dst
/// @param dst 目标
/// @param src 源
/// @returns void
static inline void urq_vec_ptr_move(
    urq_vec_ptr_t *const dst, urq_vec_ptr_t *const src)
    __attribute__((__nonnull__(1, 2)));

/// @brief 添加 ptr 向量
/// @param self 向量
/// @param value 值
/// @returns errno
static inline int urq_vec_ptr_push(urq_vec_ptr_t *self, void *value)
    __attribute__((__nonnull__(1)));

/// @brief 弹出 ptr 向量
/// @param self 向量
/// @returns 值
static inline void *urq_vec_ptr_pop(urq_vec_ptr_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 扩容 ptr 向量
/// @param self 向量
/// @param size 大小
/// @returns errno
static inline int urq_vec_ptr_reserve(urq_vec_ptr_t *self, size_t size)
    __attribute__((__nonnull__(1)));

// impl

static inline void urq_vec_ptr_init_inplace(
    urq_vec_ptr_t *self, void (*destructor)(void *))
{
    self->size = 0;
    self->capacity = 0;
    self->data = NULL;
    self->destructor = destructor;
}

static inline void urq_vec_ptr_free_inplace(urq_vec_ptr_t *self)
{
    if (self->destructor != NULL) {
        for (size_t i = 0; i < self->size; i++) {
            self->destructor(self->data[i]);
        }
    }
    urq_free_if_not_null(self->data);
}

static inline int urq_vec_ptr_clone(
    urq_vec_ptr_t *const dst, const urq_vec_ptr_t *const src)
{
    urq_vec_ptr_free_inplace(dst);
    if (urq_vec_ptr_reserve(dst, src->size)) {
        errno = ENOMEM;
        return -1;
    }
    dst->size = src->size;
    memcpy(dst->data, src->data, src->size * sizeof(void *));
    return 0;
}

static inline int urq_vec_ptr_reserve(urq_vec_ptr_t *self, size_t size)
{
    if (self->capacity < size) {
        void *new_data = urq_realloc(self->data, size * sizeof(void *));
        if (new_data == NULL) {
            return -1;
        }
        self->capacity = size;
        self->data = new_data;
    }
    return 0;
}

static inline int urq_vec_ptr_push(urq_vec_ptr_t *self, void *value)
{
    if (self->size >= self->capacity) {
        size_t cap = urq_vec_helper_grow_size(self->capacity, self->size + 1);
        if (urq_vec_ptr_reserve(self, cap)) {
            return -1;
        }
    }
    self->data[self->size] = value;
    self->size++;
    return 0;
}

static inline void *urq_vec_ptr_pop(urq_vec_ptr_t *self)
{
    return self->data[--self->size];
}

static inline void urq_vec_ptr_move(
    urq_vec_ptr_t *const dst, urq_vec_ptr_t *const src)
{
    urq_vec_ptr_free_inplace(dst);
    dst->size = src->size;
    dst->capacity = src->capacity;
    dst->data = src->data;
    src->size = 0;
    src->capacity = 0;
    src->data = NULL;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__VEC__PTR_H
