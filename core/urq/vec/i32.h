#pragma once

#include "urq/preload.h"
#include "urq/vec/helper.h"
#include <errno.h>
#include <stddef.h>
#include <stdint.h>
#include <string.h>

#ifndef URQ__VEC__I32_H
#define URQ__VEC__I32_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    size_t capacity; // 容量
    size_t size;     // 大小
    int32_t *data;   // 数据
} urq_vec_i32_t;

/// @brief 原地初始化 i32 向量
/// @param self 向量
/// @returns errno
static inline void urq_vec_i32_init_inplace(urq_vec_i32_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 原地释放 i32 向量
/// @param self 向量
/// @returns void
static inline void urq_vec_i32_free_inplace(urq_vec_i32_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 克隆列表
/// @param dst 目标列表
/// @param src 源列表
/// @returns 是否克隆成功
static inline int urq_vec_i32_clone(
    urq_vec_i32_t *const dst, const urq_vec_i32_t *const src)
    __attribute__((__nonnull__(1, 2)));

/// @brief 将列表 src 的数据移动到 dst
/// @param dst 目标
/// @param src 源
/// @returns void
static inline void urq_vec_i32_move(
    urq_vec_i32_t *const dst, urq_vec_i32_t *const src)
    __attribute__((__nonnull__(1, 2)));

/// @brief 添加 i32 向量
/// @param self 向量
/// @param value 值
/// @returns errno
static inline int urq_vec_i32_push(urq_vec_i32_t *self, int32_t value)
    __attribute__((__nonnull__(1)));

/// @brief 弹出 i32 向量
/// @param self 向量
/// @returns 值
static inline int32_t urq_vec_i32_pop(urq_vec_i32_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 扩容 i32 向量
/// @param self 向量
/// @param size 大小
/// @returns errno
static inline int urq_vec_i32_reserve(urq_vec_i32_t *self, size_t size)
    __attribute__((__nonnull__(1)));

// impl

static inline void urq_vec_i32_init_inplace(urq_vec_i32_t *self)
{
    self->size = 0;
    self->capacity = 0;
    self->data = NULL;
}

static inline void urq_vec_i32_free_inplace(urq_vec_i32_t *self)
{
    urq_free_if_not_null(self->data);
}

static inline int urq_vec_i32_clone(
    urq_vec_i32_t *const dst, const urq_vec_i32_t *const src)
{
    urq_vec_i32_free_inplace(dst);
    if (urq_vec_i32_reserve(dst, src->size)) {
        errno = ENOMEM;
        return -1;
    }
    dst->size = src->size;
    memcpy(dst->data, src->data, src->size * sizeof(int32_t));
    return 0;
}

static inline int urq_vec_i32_reserve(urq_vec_i32_t *self, size_t size)
{
    if (self->capacity < size) {
        int32_t *new_data =
            (int32_t *)urq_realloc(self->data, size * sizeof(int32_t));
        if (new_data == NULL) {
            return -1;
        }
        self->capacity = size;
        self->data = new_data;
    }
    return 0;
}

static inline int urq_vec_i32_push(urq_vec_i32_t *self, int32_t value)
{
    if (self->size >= self->capacity) {
        size_t cap = urq_vec_helper_grow_size(self->capacity, self->size + 1);
        if (urq_vec_i32_reserve(self, cap)) {
            return -1;
        }
    }
    self->data[self->size] = value;
    self->size++;
    return 0;
}

static inline int32_t urq_vec_i32_pop(urq_vec_i32_t *self)
{
    return self->data[--self->size];
}

static inline void urq_vec_i32_move(
    urq_vec_i32_t *const dst, urq_vec_i32_t *const src)
{
    urq_vec_i32_free_inplace(dst);
    dst->size = src->size;
    dst->capacity = src->capacity;
    dst->data = src->data;
    src->size = 0;
    src->capacity = 0;
    src->data = NULL;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__VEC__I32_H
