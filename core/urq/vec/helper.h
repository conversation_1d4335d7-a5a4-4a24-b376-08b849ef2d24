#pragma once

#include <stdlib.h>

#ifndef URQ__VEC__HELPER_H
#define URQ__VEC__HELPER_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 计算扩容大小
/// @param capacity 当前容量
/// @param size     当前大小
/// @returns 扩容大小
static inline size_t urq_vec_helper_grow_size(size_t capacity, size_t size)
{
    if (capacity >= size) {
        return capacity;
    }

    size_t new_capacity = capacity;
    if (new_capacity < 16) {
        new_capacity = 16;
    }
    if (new_capacity > 64) {
        new_capacity = 64;
    }
    return new_capacity + capacity;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__VEC__HELPER_H
