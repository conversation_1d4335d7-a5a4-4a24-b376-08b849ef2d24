#pragma once

#include "urq/preload.h"
#include "urq/vec/helper.h"
#include <stddef.h>
#include <stdint.h>
#include <string.h>

#ifndef URQ__VEC__STRUCT_H
#define URQ__VEC__STRUCT_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    size_t capacity; // 容量
    size_t size;     // 大小
    size_t stride;   // 结构体的大小
    uint8_t *data;   // 数据
} urq_vec_struct_t;

/// @brief 原地初始化 struct 向量
/// @param self 向量
/// @param stride 结构体的大小
/// @returns errno
static inline void urq_vec_struct_init_inplace(
    urq_vec_struct_t *self, size_t stride) __attribute__((__nonnull__(1)));

/// @brief 原地释放 struct 向量
/// @param self 向量
/// @param free_inplace 释放函数
/// @returns void
static inline void urq_vec_struct_free_inplace(
    urq_vec_struct_t *self, void (*free_inplace)(void *data))
    __attribute__((__nonnull__(1)));

/// @brief 将列表 src 的数据移动到 dst
/// @param dst 目标
/// @param src 源
/// @param free_inplace 释放函数
/// @returns void
static inline void urq_vec_struct_move(
    urq_vec_struct_t *const dst, urq_vec_struct_t *const src,
    void (*free_inplace)(void *data)) __attribute__((__nonnull__(1, 2)));

/// @brief 添加 ptr 向量
/// @param self 向量
/// @param value 值
/// @param move 移动函数
/// @returns errno
static inline int urq_vec_struct_push(
    urq_vec_struct_t *self, void *value, void (*move)(void *dst, void *src))
    __attribute__((__nonnull__(1, 2, 3)));

/// @brief 弹出 ptr 向量
/// @param self 向量
/// @param dst 目标
/// @param move 移动函数
/// @returns 值
static inline void *urq_vec_struct_pop(
    urq_vec_struct_t *self, void *dst, void (*move)(void *dst, void *src))
    __attribute__((__nonnull__(1, 2, 3)));

/// @brief 扩容 ptr 向量
/// @param self 向量
/// @param size 大小
/// @returns errno
static inline int urq_vec_struct_reserve(urq_vec_struct_t *self, size_t size)
    __attribute__((__nonnull__(1)));

// impl

static inline void urq_vec_struct_init_inplace(
    urq_vec_struct_t *self, size_t stride)
{
    self->size = 0;
    self->capacity = 0;
    self->data = NULL;
    self->stride = stride;
}

static inline void urq_vec_struct_free_inplace(
    urq_vec_struct_t *self, void (*free_inplace)(void *data))
{
    if (free_inplace != NULL) {
        for (size_t i = 0; i < self->size; i++) {
            free_inplace((void *)&self->data[i * self->stride]);
        }
    }
    urq_free_if_not_null(self->data);
}

static inline void urq_vec_struct_move(
    urq_vec_struct_t *const dst, urq_vec_struct_t *const src,
    void (*free_inplace)(void *data))
{
    urq_vec_struct_free_inplace(dst, free_inplace);
    dst->capacity = src->capacity;
    dst->size = src->size;
    dst->data = src->data;
    dst->stride = src->stride;

    src->capacity = 0;
    src->size = 0;
    src->data = NULL;
}

static inline int urq_vec_struct_push(
    urq_vec_struct_t *self, void *value, void (*move)(void *dst, void *src))
{
    if (self->size >= self->capacity) {
        size_t cap = urq_vec_helper_grow_size(self->capacity, self->size + 1);
        if (urq_vec_struct_reserve(self, cap)) {
            return -1;
        }
    }
    move(&self->data[self->size * self->stride], value);
    self->size++;
    return 0;
}

static inline void *urq_vec_struct_pop(
    urq_vec_struct_t *self, void *dst, void (*move)(void *dst, void *src))
{
    move(dst, &self->data[self->size * self->stride]);
    self->size--;
    return dst;
}

static inline int urq_vec_struct_reserve(urq_vec_struct_t *self, size_t size)
{
    if (self->capacity < size) {
        void *new_data = urq_realloc(self->data, size * self->stride);
        if (new_data == NULL) {
            return -1;
        }
        self->capacity = size;
        self->data = new_data;
    }
    return 0;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__VEC__STRUCT_H
