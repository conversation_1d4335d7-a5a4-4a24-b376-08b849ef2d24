#pragma once

#include <stdint.h>
#include <stdio.h>

#ifndef URQ__IP_H
#define URQ__IP_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 将 4 个字节的 ip 地址转换为 uint32_t
/// @param a 第一个字节
/// @param b 第二个字节
/// @param c 第三个字节
/// @param d 第四个字节
/// @returns ip 地址
static inline uint32_t urq_ip_make4(uint8_t a, uint8_t b, uint8_t c, uint8_t d)
    __attribute__((__warn_unused_result__()));

/// @brief 将 ip4 地址转换为字符串
///
/// @param ip  ip 地址
/// @param buf 存储字符串的缓冲区
/// @returns 返回 buf 的指针
static inline const char *urq_ip4_to_string(uint32_t ip, char buf[16]);

// -------------------------- impl --------------------------

static inline uint32_t urq_ip_make4(uint8_t a, uint8_t b, uint8_t c, uint8_t d)
{
    return (
        (((uint32_t)a << 24) | ((uint32_t)b << 16) | ((uint32_t)c << 8) |
         (uint32_t)d));
}

static inline const char *urq_ip4_to_string(uint32_t ip, char buf[16])
{
    snprintf(
        buf, 16, "%d.%d.%d.%d", (ip >> 24) & 0xFF, (ip >> 16) & 0xFF,
        (ip >> 8) & 0xFF, ip & 0xFF);
    return buf;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__IP_H
