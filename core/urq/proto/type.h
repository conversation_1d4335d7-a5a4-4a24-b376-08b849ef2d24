#pragma once

#ifndef URQ__PROTO__TYPE_H
#define URQ__PROTO__TYPE_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 协议类型
/// 区别于设备类型，一台设备可能支持多种协议
/// 不同的设备也可能使用相同的协议
///
/// 但实际连接中使用的协议类型是确定的一种
typedef enum {
    URQ_PROTO_TYPE_UNSPECIFIED = 0,
    //主站，如果是COM，则使用RTU，否则使用以太网TCP
    URQ_PROTO_TYPE_MODBUS_MASTER = 1,
    //从站，如果是COM，则使用RTU，否则使用以太网TCP
    URQ_PROTO_TYPE_MODBUS_SLAVE = 2,
    URQ_PROTO_TYPE_MODBUS_MASTER_ASCII = 3,
    // MODBUS从站
    URQ_PROTO_TYPE_MODBUS_SLAVE_ASCII = 4,

    URQ_PROTO_TYPE_SIEMENS_PPI = 10,    //西门子
    URQ_PROTO_TYPE_SIEMENS_MPI2 = 11,   //西门子
    URQ_PROTO_TYPE_SIEMENS_MPI3 = 12,   //西门子
    URQ_PROTO_TYPE_SIEMENS_MPI4 = 13,   //西门子
    URQ_PROTO_TYPE_SIEMENS_S7COMM = 14, //西门子

    URQ_PROTO_TYPE_MITSUBISHI_MC = 20,   //三菱
    URQ_PROTO_TYPE_MITSUBISHI_MC1E = 21, //三菱
    URQ_PROTO_TYPE_MITSUBISHI_MC2E = 22, //三菱
    URQ_PROTO_TYPE_MITSUBISHI_MC3E = 23, //三菱
    URQ_PROTO_TYPE_MITSUBISHI_MC4C = 24, //三菱
    URQ_PROTO_TYPE_MITSUBISHI_SLMP = 25, //三菱

    URQ_PROTO_TYPE_OMRON_FINS = 30,         //欧姆龙
    URQ_PROTO_TYPE_OMRON_HOSTLINK = 31,     //欧姆龙
    URQ_PROTO_TYPE_OMRON_ENTERNET_CIP = 32, //欧姆龙

    URQ_PROTO_TYPE_FATEK = 40,                //永宏
    URQ_PROTO_TYPE_VIGOR_VSPROTOCOL = 41,     //丰炜
    URQ_PROTO_TYPE_PANASONIC_NEWTOCOL = 42,   //松下
    URQ_PROTO_TYPE_ROCKWELL_IDEC_SERIAL = 43, //罗克韦尔
    URQ_PROTO_TYPE_AIBUS = 44,                //宇电

    URQ_PROTO_TYPE_MAXCOMM = 90,      // MAXCOMM，连接另一台HMI设备
    URQ_PROTO_TYPE_FREE = 98,         //自由协议
    URQ_PROTO_TYPE_PASS_THROUGH = 99, //透传协议（仅透传用）

    URQ_PROTO_TYPE_ZND_VAR = 100,  // znd var
    URQ_PROTO_TYPE_ZND_FILE = 101, // znd file
} urq_proto_type_t;

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__PROTO__TYPE_H
