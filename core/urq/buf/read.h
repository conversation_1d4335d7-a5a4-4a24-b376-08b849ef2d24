#pragma once

#include <stdint.h>
#include <string.h>

#ifndef URQ__BUF__READ_H
#define URQ__BUF__READ_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 读取的偏移
    uint32_t cursor;
    /// @brief 数据大小
    uint32_t size;
    /// @brief 数据
    const uint8_t *data;
} urq_buf_read_t;

#define urq_buf_read_make(buf_size, buf_data)                                  \
    ((urq_buf_read_t){.cursor = 0, .size = buf_size, .data = buf_data})

/// @brief 仍有数据
__attribute__((__nonnull__(1), __warn_unused_result__())) static inline int
urq_buf_read_has_data(const urq_buf_read_t *self)
{
    return self->cursor < self->size;
}

/// @brief i8
__attribute__((__nonnull__(1), __warn_unused_result__())) static inline int8_t
urq_buf_read_i8(urq_buf_read_t *self)
{
    return *(int8_t *)&self->data[self->cursor++];
}

/// @brief u8
__attribute__((__nonnull__(1), __warn_unused_result__())) static inline uint8_t
urq_buf_read_u8(urq_buf_read_t *self)
{
    return self->data[self->cursor++];
}

/// @brief i16
__attribute__((__nonnull__(1), __warn_unused_result__())) static inline int16_t
urq_buf_read_i16(urq_buf_read_t *self)
{
    int16_t value = self->data[self->cursor];
    value |= (int16_t)(self->data[self->cursor + 1] << 8);
    self->cursor += 2;
    return value;
}

/// @brief u16
__attribute__((__nonnull__(1), __warn_unused_result__())) static inline uint16_t
urq_buf_read_u16(urq_buf_read_t *self)
{
    int16_t value = urq_buf_read_i16(self);
    return *(uint16_t *)&value;
}

/// @brief i32
__attribute__((__nonnull__(1), __warn_unused_result__())) static inline int32_t
urq_buf_read_i32(urq_buf_read_t *self)
{
    int32_t value = self->data[self->cursor];
    value |= self->data[self->cursor + 1] << 8;
    value |= self->data[self->cursor + 2] << 16;
    value |= self->data[self->cursor + 3] << 24;
    self->cursor += 4;
    return value;
}

/// @brief u32
__attribute__((__nonnull__(1), __warn_unused_result__())) static inline uint32_t
urq_buf_read_u32(urq_buf_read_t *self)
{
    int32_t value = urq_buf_read_i32(self);
    return *(uint32_t *)&value;
}

/// @brief read bytes
__attribute__((__nonnull__(1))) static inline uint8_t *urq_buf_read_bytes(
    urq_buf_read_t *const self, uint32_t size, uint8_t *dst)
{
    memcpy(dst, self->data + self->cursor, size);
    self->cursor += size;
    return dst;
}

/// @brief 跳过给定长度的数据
__attribute__((__nonnull__(1))) static inline const uint8_t *urq_buf_read_skip(
    urq_buf_read_t *const self, uint32_t size)
{
    const uint8_t *cursor = self->data + self->cursor;
    self->cursor += size;

    return cursor;
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__BUF__READ_H
