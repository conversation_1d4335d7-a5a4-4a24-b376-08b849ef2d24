#pragma once

#include "urq/compile.h"
#include <stddef.h>
#include <stdint.h>
#include <string.h>

#ifndef URQ__BUF__WRITER_H
#define URQ__BUF__WRITER_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 这个结构并不管理数据，只负责写入
/// @details 写入的数据是小端序的
typedef struct {
    /// @brief 数据大小
    uint32_t size;
    /// @brief 当前的指针位置
    uint32_t cursor;
    /// @brief 数据
    uint8_t *data;
} urq_buf_writer_t;

#define urq_buf_writer_make(data_size, data_body)                              \
    &(urq_buf_writer_t) { .size = data_size, .cursor = 0, .data = data_body }

/// @brief 写入 i8
static inline void urq_buf_writer_i8(urq_buf_writer_t *buf, int8_t value)
{
    buf->data[buf->cursor] = *(uint8_t *)&value;
    ++buf->cursor;
}

/// @brief 写入 u8
static inline void urq_buf_writer_u8(urq_buf_writer_t *buf, uint8_t value)
{
    buf->data[buf->cursor] = value;
    ++buf->cursor;
}

/// @brief 写入 i16
static inline void urq_buf_writer_i16(urq_buf_writer_t *buf, int16_t value)
{
    uint8_t *p = (uint8_t *)&value;
#if URQ_COMPILE_ENDIAN == URQ_COMPILE_ENDIAN_LITTLE
    buf->data[buf->cursor] = p[0];
    buf->data[buf->cursor + 1] = p[1];
#else
    buf->data[buf->cursor] = p[1];
    buf->data[buf->cursor + 1] = p[0];
#endif
    buf->cursor += 2;
}

/// @brief 以小端序写入 u16
static inline void urq_buf_writer_u16(urq_buf_writer_t *buf, uint16_t value)
{
    urq_buf_writer_i16(buf, *(int16_t *)&value);
}

/// @brief 写入 i32
static inline void urq_buf_writer_i32(urq_buf_writer_t *buf, int32_t value)
{
    uint8_t *p = (uint8_t *)&value;
#if URQ_COMPILE_ENDIAN == URQ_COMPILE_ENDIAN_LITTLE
    buf->data[buf->cursor] = p[0];
    buf->data[buf->cursor + 1] = p[1];
    buf->data[buf->cursor + 2] = p[2];
    buf->data[buf->cursor + 3] = p[3];
#else
    buf->data[buf->cursor] = p[3];
    buf->data[buf->cursor + 1] = p[2];
    buf->data[buf->cursor + 2] = p[1];
    buf->data[buf->cursor + 3] = p[0];
#endif
    buf->cursor += 4;
}

/// @brief 写入 u32
static inline void urq_buf_writer_u32(urq_buf_writer_t *buf, uint32_t value)
{
    urq_buf_writer_i32(buf, *(int32_t *)&value);
}

/// @brief 写入 i64
static inline void urq_buf_writer_i64(urq_buf_writer_t *buf, int64_t value)
{
    uint8_t *p = (uint8_t *)&value;
#if URQ_COMPILE_ENDIAN == URQ_COMPILE_ENDIAN_LITTLE
    buf->data[buf->cursor] = p[0];
    buf->data[buf->cursor + 1] = p[1];
    buf->data[buf->cursor + 2] = p[2];
    buf->data[buf->cursor + 3] = p[3];
    buf->data[buf->cursor + 4] = p[4];
    buf->data[buf->cursor + 5] = p[5];
    buf->data[buf->cursor + 6] = p[6];
    buf->data[buf->cursor + 7] = p[7];
#else
    buf->data[buf->cursor] = p[7];
    buf->data[buf->cursor + 1] = p[6];
    buf->data[buf->cursor + 2] = p[5];
    buf->data[buf->cursor + 3] = p[4];
    buf->data[buf->cursor + 4] = p[3];
    buf->data[buf->cursor + 5] = p[2];
    buf->data[buf->cursor + 6] = p[1];
    buf->data[buf->cursor + 7] = p[0];
#endif
    buf->cursor += 8;
}

/// @brief 写入 u64
static inline void urq_buf_writer_u64(urq_buf_writer_t *buf, uint64_t value)
{
    urq_buf_writer_i64(buf, *(int64_t *)&value);
}

/// @brief 写入 float
static inline void urq_buf_writer_f32(urq_buf_writer_t *buf, float value)
{
    uint8_t *p = (uint8_t *)&value;
    buf->data[buf->cursor] = p[0];
    buf->data[buf->cursor + 1] = p[1];
    buf->data[buf->cursor + 2] = p[2];
    buf->data[buf->cursor + 3] = p[3];

    buf->cursor += 4;
}

/// @brief 写入 double
static inline void urq_buf_writer_f64(urq_buf_writer_t *buf, double value)
{
    uint8_t *p = (uint8_t *)&value;

    buf->data[buf->cursor] = p[0];
    buf->data[buf->cursor + 1] = p[1];
    buf->data[buf->cursor + 2] = p[2];
    buf->data[buf->cursor + 3] = p[3];
    buf->data[buf->cursor + 4] = p[4];
    buf->data[buf->cursor + 5] = p[5];
    buf->data[buf->cursor + 6] = p[6];
    buf->data[buf->cursor + 7] = p[7];

    buf->cursor += 8;
}

/// @brief 写入数据 uint8_t *
__attribute__((__nonnull__(1, 3))) static inline void urq_buf_writer_data(
    urq_buf_writer_t *buf, uint32_t size, const uint8_t *data)
{
    uint8_t *dst = buf->data + buf->cursor;
    memcpy(dst, data, size);
    buf->cursor += size;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__BUF__WRITER_H
