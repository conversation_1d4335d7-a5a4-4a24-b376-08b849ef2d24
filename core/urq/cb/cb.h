#pragma once

#ifndef URQ__CB__CB_H
#define URQ__CB__CB_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 没有参数的回调
/// @returns void
typedef void (*urq_cb0_t)(void);

/// @brief 一个参数的回调
/// @param arg1 参数1
/// @returns void
typedef void (*urq_cb1_t)(void *arg1);

/// @brief 两个参数的回调
/// @param arg1 参数1
/// @param arg2 参数2
/// @returns void
typedef void (*urq_cb2_t)(void *arg1, void *arg2);

/// @brief 三个参数的回调
/// @param arg1 参数1
/// @param arg2 参数2
/// @param arg3 参数3
/// @returns void
typedef void (*urq_cb3_t)(void *arg1, void *arg2, void *arg3);

/// @brief 四个参数的回调
/// @param arg1 参数1
/// @param arg2 参数2
/// @param arg3 参数3
/// @param arg4 参数4
/// @returns void
typedef void (*urq_cb4_t)(void *arg1, void *arg2, void *arg3, void *arg4);

/// @brief 回调方法
/// @details 带有参数的回调使用 `urq_cb_data_t` 类型表示
typedef union {
    urq_cb0_t cb0;
    urq_cb1_t cb1;
    urq_cb2_t cb2;
    urq_cb3_t cb3;
    urq_cb4_t cb4;
} urq_cb_t;

#ifdef __cplusplus
}
#endif
#endif // URQ__CB__CB_H
