#pragma once

#include "urq/cb/cb.h"
#include "urq/preload.h"
#include <stdint.h>
#include <string.h>

#ifndef URQ__CB__DATA_H
#define URQ__CB__DATA_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 回调数据
typedef struct {
    uint8_t argc;  // 参数个数
    urq_cb_t cb;   // 回调方法
    urq_cb_t free; // 回调参数的释放方法
} urq_cb_data_t;

/// @brief 创建一个 0 参数的回调数据
static inline urq_cb_data_t *urq_cb_data_new0(urq_cb_t cb, urq_cb_t free)
{
    urq_used(cb);
    urq_used(free);
    return NULL;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__CB__DATA_H
