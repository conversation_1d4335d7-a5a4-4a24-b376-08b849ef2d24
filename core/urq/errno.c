#include "urq/errno.h"
#include <string.h>

/// @brief 错误的名称
static const char *urq_errno_name[] = {
    "MIN",
    // 常用
    "UNKNOWN",
    "NOT IMPL",
    "PENDING",
    "NOT FOUND",
    // 连接
    "CLOSED",

    // 错误码最大值
    "MAX",
};

/// @brief 获取错误的名称
/// @param errno 错误码
/// @returns 错误名称
const char *urq_errno_to_string(int no)
{
    if (no > URQ_EMIN && no < URQ_ERRNO_MAX) {
        return urq_errno_name[no - URQ_EMIN];
    }
    return strerror(no);
}
