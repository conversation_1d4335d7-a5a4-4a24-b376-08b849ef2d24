#pragma once

#include <stdint.h>
#include <time.h>

#ifndef URQ__TIME__TIME_H
#define URQ__TIME__TIME_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 时间类型，单位为毫秒
typedef int64_t urq_time_t;

/// @brief 系统与服务器的时间偏移量(ms)
extern urq_time_t urq_time_offset_ms;

/// @brief 当前时间(ms)
extern urq_time_t urq_time_now_ms;

/// @brief 将 ms 转为 struct timespec
/// @param ms 毫秒
/// @param mut_ts 返回的 struct timespec
static inline void urq_time_ms2ts(urq_time_t ms, struct timespec *mut_ts)
    __attribute__((__nonnull__(2)));

/// @brief 将 struct timespec 转为 ms
/// @param ts 输入的 struct timespec
/// @return 返回的 ms
static inline urq_time_t urq_time_ts2ms(struct timespec *ts)
    __attribute__((__nonnull__(1)));

/// @brief 获取当前时间(ms)
#define urq_time_now() urq_time_now_ms

// -------------------------------------------------------------

static inline void urq_time_ms2ts(urq_time_t ms, struct timespec *mut_ts)
{
    mut_ts->tv_sec = ms / 1000;
    mut_ts->tv_nsec = (ms % 1000) * 1000000;
}

static inline urq_time_t urq_time_ts2ms(struct timespec *ts)
{
    return ts->tv_sec * 1000 + ts->tv_nsec / 1000000;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__TIME__TIME_H
