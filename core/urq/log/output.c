#include "urq/log/output.h"
#include "urq/compile.h"
#include <stdarg.h>
#include <stdint.h>
#include <stdio.h>

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM

#define TITLE   ""
#define RESET   ""
#define VERBOSE ""
#define TRACE   ""
#define DEBUG   ""
#define INFO    ""
#define WARN    ""
#define ERROR   ""
#define FATAL   ""

#else

#define TITLE   "\x1b[30m" // black
#define RESET   "\x1b[0m"
#define VERBOSE "\x1b[37m" // white
#define TRACE   "\x1b[36m" // cyan
#define DEBUG   "\x1b[34m" // blue
#define INFO    "\x1b[32m" // green
#define WARN    "\x1b[33m" // yellow
#define ERROR   "\x1b[31m" // red
#define FATAL   "\x1b[91m" // bright red

#endif

/// 输出 verbose 级别的日志
void urq_log_output_verbose(
    const char *const file, int line, const char *format, ...)
{
    va_list args;
    va_start(args, format);
    fprintf(stdout, TITLE "[VERBOSE] %s:%d" RESET "\n" VERBOSE, file, line);
    vfprintf(stdout, format, args);
    fprintf(stdout, RESET);
}

/// 输出 trace 级别的日志
void urq_log_output_trace(
    const char *const file, int line, const char *format, ...)
{
    va_list args;
    va_start(args, format);
    fprintf(stdout, TITLE "[TRACE] %s:%d" RESET "\n" TRACE, file, line);
    vfprintf(stdout, format, args);
    fprintf(stdout, RESET);
}

/// 输出 debug 级别的日志
void urq_log_output_debug(
    const char *const file, int line, const char *format, ...)
{
    va_list args;
    va_start(args, format);
    fprintf(stdout, TITLE "[DEBUG] %s:%d" RESET "\n" DEBUG, file, line);
    vfprintf(stdout, format, args);
    fprintf(stdout, RESET);
}

/// 输出 info 级别的日志
void urq_log_output_info(
    const char *const file, int line, const char *format, ...)
{
    va_list args;
    va_start(args, format);
    fprintf(stdout, TITLE "[INFO] %s:%d" RESET "\n" INFO, file, line);
    vfprintf(stdout, format, args);
    fprintf(stdout, RESET);
}

/// 输出 warn 级别的日志
void urq_log_output_warn(
    const char *const file, int line, const char *format, ...)
{
    va_list args;
    va_start(args, format);
    fprintf(stdout, TITLE "[WARN] %s:%d" RESET "\n" WARN, file, line);
    vfprintf(stdout, format, args);
    fprintf(stdout, RESET);
}

/// 输出 error 级别的日志
void urq_log_output_error(
    const char *const file, int line, const char *format, ...)
{
    va_list args;
    va_start(args, format);
    fprintf(stderr, TITLE "[ERROR] %s:%d" RESET "\n" ERROR, file, line);
    vfprintf(stderr, format, args);
    fprintf(stderr, RESET);
}

/// 输出 fatal 级别的日志
void urq_log_output_fatal(
    const char *const file, int line, const char *format, ...)
{
    va_list args;
    va_start(args, format);
    fprintf(stderr, TITLE "[FATAL] %s:%d" RESET "\n" FATAL, file, line);
    vfprintf(stderr, format, args);
    fprintf(stderr, RESET);
}

/// 输出内存的十六进制表示
void urq_log_output_mem(
    const char *const file, int line, uint32_t size, const void *const ptr)
{
    uint8_t *p = (uint8_t *)ptr;
    uint8_t value = 0;
    int count = 0;
    printf(TITLE "[MEM] %s:%d" RESET "\n" VERBOSE, file, line);
    if (size == 0) {
        printf("mem, size: 0\n");
        return;
    }
    if (ptr == NULL) {
        printf("mem, ptr: NULL\n");
        return;
    }
    for (uint32_t i = 0; i < size; i++) {
        count++;
        value = p[i];
        printf("%02x", value);
        if (count == 16) {
            printf("\n");
            count = 0;
            continue;
        }
        if (count % 4 == 0) {
            printf(", ");
            continue;
        }
        printf(" ");
    }
    printf("\n");
}
