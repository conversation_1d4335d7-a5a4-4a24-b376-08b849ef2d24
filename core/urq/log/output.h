#pragma once

#include <stdint.h>

#ifndef URQ__LOG__OUTPUT_H
#define URQ__LOG__OUTPUT_H
#ifdef __cplusplus
extern "C" {
#endif

/// 输出 verbose 级别的日志
void urq_log_output_verbose(
    const char *const file, int line, const char *format, ...)
    __attribute__((__format__(printf, 3, 4)));

/// 输出 trace 级别的日志
void urq_log_output_trace(
    const char *const file, int line, const char *format, ...)
    __attribute__((__format__(printf, 3, 4)));

/// 输出 debug 级别的日志
void urq_log_output_debug(
    const char *const file, int line, const char *format, ...)
    __attribute__((__format__(printf, 3, 4)));

/// 输出 info 级别的日志
void urq_log_output_info(
    const char *const file, int line, const char *format, ...)
    __attribute__((__format__(printf, 3, 4)));

/// 输出 warn 级别的日志
void urq_log_output_warn(
    const char *const file, int line, const char *format, ...)
    __attribute__((__format__(printf, 3, 4)));

/// 输出 error 级别的日志
void urq_log_output_error(
    const char *const file, int line, const char *format, ...)
    __attribute__((__format__(printf, 3, 4)));

/// 输出 fatal 级别的日志
void urq_log_output_fatal(
    const char *const file, int line, const char *format, ...)
    __attribute__((__format__(printf, 3, 4)));

/// 输出内存的十六进制表示
void urq_log_output_mem(
    const char *const file, int line, uint32_t size, const void *const ptr);

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__LOG__OUTPUT_H
