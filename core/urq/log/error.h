#pragma once

#include "urq/log/output.h" // IWYU pragma: export

#ifndef URQ__LOG__ERROR_H
#define URQ__LOG__ERROR_H
#ifdef __cplusplus
extern "C" {
#endif

#ifdef URQ__LOG
#pragma message "URQ__LOG is already defined"
#else
#define URQ__LOG
#endif // #ifdef URQ__LOG

/// 输出 verbose 级别的日志
#define log_v(...)

/// 输出 debug 级别的日志
#define log_d(...)

/// 输出 info 级别的日志
#define log_i(...)

/// 输出 trace 级别的日志
#define log_t(...)

/// 输出 warn 级别的日志
#define log_w(...)

/// 输出 error 级别的日志
#define log_e(...) urq_log_output_error(__FILE__, __LINE__, __VA_ARGS__)

/// 输出 fatal 级别的日志
#define log_f(...) urq_log_output_fatal(__FILE__, __LINE__, __VA_ARGS__)

/// 输出内存的十六进制表示
#define log_mem(size, ptr) urq_log_output_mem(__FILE__, __LINE__, size, ptr)

#ifdef __cplusplus
}
#endif

#endif // #ifndef URQ__LOG__ERROR_H
