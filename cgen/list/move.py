
def declare_v1(struct_name: str):
    return f"""
/// @brief 移动整个列表
static inline void {struct_name}_move(
    {struct_name}_t *dst, //
    {struct_name}_t *src  //
    ) __attribute__((__nonnull__(1, 2)));
"""


def impl_v1(struct_name: str):
    return f"""
static inline void {struct_name}_move(
    {struct_name}_t *dst, {struct_name}_t *src)
{{
    {struct_name}_free_inplace(dst);
    dst->size = src->size;
    dst->data = src->data;

    src->size = 0;
    src->data = NULL;
}}
"""
