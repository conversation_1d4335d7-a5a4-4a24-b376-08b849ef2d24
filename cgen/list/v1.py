
import os
from . import include, guard, struct, init, free, move


def v1(
        script_file: str,
        out_file: str,
        type_name: str,
        is_pointer: bool,
        free_inplace_fn: str,
        includes: list[str] = [],
        size_type: str = "size_t",
       ):
    """
    # params: 
    - script_file:     执行脚本文件
    - out_file:        输出文件名
    - name_prefix:     需要生成的列表名称，不用带 _t 后缀
    - type_name:       列表中元素的类型
    - free_inplace_fn: 释放列表元素的函数名
    - includes:        需要额外包含的头文件列表
    - size_type:       列表中元素的个数类型
    """

    struct_name = ""
    if is_pointer:
        struct_name = f"{type_name}_ptr_list"
    else:
        struct_name = f"{type_name}_list"

    out = ""
    out += "// gen by cgen/list/v1.py\n"
    out += "#pragma once\n"
    out += include.v1(includes)
    out += guard.head_v1(script_file)
    out += struct.v1(size_type=size_type, type_name=type_name, is_pointer=is_pointer, struct_name=struct_name)
    out += init.inplace_declare_v1(struct_name=struct_name)
    out += free.inplace_declare_v1(struct_name=struct_name)
    out += move.declare_v1(struct_name=struct_name)

    out += "\n// ========================= impl =========================\n//\n"
    out += init.inplace_impl_v1(struct_name=struct_name)
    out += free.inplace_impl_v1(struct_name=struct_name, is_pointer=is_pointer, free_inplace_fn=free_inplace_fn)
    out += move.impl_v1(struct_name=struct_name)

    out += guard.tail_v1(script_file)

    dir = os.path.dirname(script_file)
    with open(f"{dir}/{out_file}", "w") as f:
        f.write(out)
