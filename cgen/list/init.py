

def inplace_declare_v1(struct_name: str):
    """
    声明原地初始化函数
    """
    return f"""
/// @brief 原地初始化列表
static inline void {struct_name}_init_inplace({struct_name}_t *self)
    __attribute__((__nonnull__(1)));
"""

def inplace_impl_v1(struct_name: str):
    """
    实现原地初始化函数
    """
    return f"""
static inline void {struct_name}_init_inplace({struct_name}_t *self)
{{
    self->size = 0;
    self->data = NULL;
}}
"""