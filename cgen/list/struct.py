
def v1(size_type: str, type_name: str, is_pointer: bool, struct_name: str):
    size = f"{size_type} size;"
    data = ""
    if is_pointer:
        data = f"urq_arr_t({type_name}_t *) data;"
    else:
        data = f"urq_arr_t({type_name}_t) data;"

    if len(size) > len(data):
        data = data + " " * (len(size) - len(data))
    elif len(size) < len(data):
        size = size + " " * (len(data) - len(size))

    size += f" // {type_name} 的个数"
    data += f" // {type_name} 的数组"

    return f"""
/// @brief {type_name} 列表
typedef struct {{
    {size}
    {data}
}} {struct_name}_t;
"""
