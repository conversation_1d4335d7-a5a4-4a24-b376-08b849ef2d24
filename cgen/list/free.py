
def inplace_declare_v1(struct_name: str):
    return f"""
/// @brief 原地释放列表
static inline void {struct_name}_free_inplace({struct_name}_t *self)
    __attribute__((__nonnull__(1)));
"""

def inplace_impl_v1(struct_name: str, is_pointer: bool, free_inplace_fn: str):
    free= ""
    if is_pointer:
        free = f"{free_inplace_fn}(self->data[i])"
    else:
        free = f"{free_inplace_fn}(&self->data[i])"

    return f"""
static inline void {struct_name}_free_inplace({struct_name}_t *self)
{{
    for (size_t i = 0; i < self->size; ++i) {{
        {free};
    }}
    urq_free_if_not_null(self->data);
}}
"""
