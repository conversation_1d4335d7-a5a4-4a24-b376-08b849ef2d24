# 设置 znd proto 模块找到了
set(znd_proto_FOUND TRUE)

# 设置版本号
set(znd_proto_VERSION_MAJOR 1)
set(znd_proto_VERSION_MINOR 0)
set(znd_proto_VERSION_PATCH 0)
set(znd_proto_VERSION "${znd_proto_VERSION_MAJOR}.${znd_proto_VERSION_MINOR}.${znd_proto_VERSION_PATCH}")

# 设置 znd proto 模块的头文件路径
set(znd_proto_INCLUDE_DIRS "${CMAKE_CURRENT_LIST_DIR}/include")

# 设置 znd proto 模块的库文件路径
if(WIN32)
    set(znd_proto_LIBRARIES "${CMAKE_CURRENT_LIST_DIR}/libznd_proto.lib")
else()
    set(znd_proto_LIBRARIES "${CMAKE_CURRENT_LIST_DIR}/libznd_proto.a")
endif()