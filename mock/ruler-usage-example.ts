// 刻度尺组件使用示例
import { 
  horizontalRulerMockData, 
  verticalRulerMockData, 
  arcRulerMockData,
  allRulerMockData 
} from './ruler-mock-data';
import { Widget } from '../gen/ts/znd/project/v1/widget';

// 创建页面组件列表的示例
export function createRulerDemoPage() {
  return {
    widgets: allRulerMockData,
    widgetVariableReference: {},
    widgetActionList: {},
  };
}

// 动态创建水平刻度尺的工厂函数
export function createHorizontalRuler(
  id: number,
  x: number,
  y: number,
  width: number,
  height: number,
  minValue: number,
  maxValue: number,
  currentValue?: number
): Widget {
  return {
    ...horizontalRulerMockData,
    idU16: id,
    location: {
      leftI16: x,
      topI16: y,
    },
    size: {
      widthI16: width,
      heightI16: height,
    },
    widget: {
      $case: "widgetRuler",
      value: {
        ...horizontalRulerMockData.widget!.value,
        scaleConfig: {
          ...horizontalRulerMockData.widget!.value.scaleConfig,
          minValue: {
            from: { $case: "valueI32", value: minValue },
          },
          maxValue: {
            from: { $case: "valueI32", value: maxValue },
          },
        },
        currentValue: currentValue !== undefined ? {
          from: { $case: "valueI32", value: currentValue },
        } : undefined,
      },
    },
  };
}

// 动态创建垂直刻度尺的工厂函数（适用于量杯等）
export function createVerticalRuler(
  id: number,
  x: number,
  y: number,
  width: number,
  height: number,
  minValue: number,
  maxValue: number,
  unit: string = "ml",
  currentValue?: number
): Widget {
  return {
    ...verticalRulerMockData,
    idU16: id,
    name: `垂直刻度尺_${unit}`,
    location: {
      leftI16: x,
      topI16: y,
    },
    size: {
      widthI16: width,
      heightI16: height,
    },
    widget: {
      $case: "widgetRuler",
      value: {
        ...verticalRulerMockData.widget!.value,
        scaleConfig: {
          ...verticalRulerMockData.widget!.value.scaleConfig,
          minValue: {
            from: { $case: "valueI32", value: minValue },
          },
          maxValue: {
            from: { $case: "valueI32", value: maxValue },
          },
        },
        currentValue: currentValue !== undefined ? {
          from: { $case: "valueI32", value: currentValue },
        } : undefined,
      },
    },
  };
}

// 动态创建弧形刻度尺的工厂函数（适用于仪表盘等）
export function createArcRuler(
  id: number,
  x: number,
  y: number,
  size: number,
  minValue: number,
  maxValue: number,
  startAngle: number = 135,
  endAngle: number = 405,
  currentValue?: number
): Widget {
  return {
    ...arcRulerMockData,
    idU16: id,
    location: {
      leftI16: x,
      topI16: y,
    },
    size: {
      widthI16: size,
      heightI16: size,
    },
    widget: {
      $case: "widgetRuler",
      value: {
        ...arcRulerMockData.widget!.value,
        startAngleU16: startAngle,
        endAngleU16: endAngle,
        radiusU16: Math.floor(size * 0.4),
        scaleConfig: {
          ...arcRulerMockData.widget!.value.scaleConfig,
          scaleRadiusU8: Math.floor(size * 0.4),
          minValue: {
            from: { $case: "valueI32", value: minValue },
          },
          maxValue: {
            from: { $case: "valueI32", value: maxValue },
          },
        },
        currentValue: currentValue !== undefined ? {
          from: { $case: "valueI32", value: currentValue },
        } : undefined,
      },
    },
  };
}

// 使用示例
export const usageExamples = {
  // 温度计刻度尺
  thermometer: createVerticalRuler(2001, 100, 50, 40, 200, -10, 50, "°C", 25),
  
  // 压力表刻度尺
  pressureGauge: createArcRuler(2002, 300, 100, 150, 0, 10, 135, 405, 6.5),
  
  // 长度测量尺
  lengthRuler: createHorizontalRuler(2003, 50, 300, 400, 30, 0, 50, 12.5),
  
  // 液体容量刻度
  liquidMeasure: createVerticalRuler(2004, 500, 50, 50, 250, 0, 1000, "ml", 350),
};

// 序列化为JSON的示例
export function serializeRulerToJson(widget: Widget): string {
  return JSON.stringify(widget, null, 2);
}

// 从JSON反序列化的示例
export function deserializeRulerFromJson(json: string): Widget {
  return JSON.parse(json) as Widget;
}

// 验证刻度尺配置的辅助函数
export function validateRulerConfig(widget: Widget): boolean {
  if (widget.widget?.$case !== "widgetRuler") {
    return false;
  }
  
  const ruler = widget.widget.value;
  
  // 检查基本配置
  if (!ruler.scaleConfig) {
    return false;
  }
  
  // 检查刻度范围
  const minVal = ruler.scaleConfig.minValue?.from;
  const maxVal = ruler.scaleConfig.maxValue?.from;
  
  if (!minVal || !maxVal) {
    return false;
  }
  
  // 检查弧形刻度尺的角度配置
  if (ruler.rulerType === 3 || ruler.rulerType === 4) { // ARC or CIRCLE
    if (ruler.startAngleU16 >= ruler.endAngleU16 && ruler.endAngleU16 <= 360) {
      return false;
    }
  }
  
  return true;
}

// 导出所有示例
export default {
  createRulerDemoPage,
  createHorizontalRuler,
  createVerticalRuler,
  createArcRuler,
  usageExamples,
  serializeRulerToJson,
  deserializeRulerFromJson,
  validateRulerConfig,
};
