{"c header file guard": {"scope": "c,cpp", "prefix": "cguard", "body": ["#pragma once", "", "#ifndef $1", "#define $1", "#ifdef __cplusplus", "extern \"C\" {", "#endif", "", "$2", "", "#ifdef __cplusplus", "}", "#endif", "#endif // #ifndef $1", ""], "description": "c header file macro"}, "new class": {"scope": "c,cpp", "prefix": "classd", "body": ["class $1{", "public:", "    inline $1() noexcept = default;", "    inline ~$1() noexcept = default;", "    inline $1(const $1 &other) noexcept = delete; // 禁止拷贝构造", "    inline $1 &operator=(const $1 &other) noexcept = delete; // 禁止拷贝赋值", "    inline $1(const $1 &&other) noexcept = delete; // 禁止移动构造", "    inline $1 &operator=($1 &&other) noexcept = delete; // 禁止移动赋值", "};"], "description": "new class"}, "warn unuse return": {"scope": "c,cpp", "prefix": "aresult", "body": ["__attribute__((__warn_unused_result__()))"], "description": "warn unuse return"}, "attribute malloc": {"scope": "c,cpp", "prefix": "amalloc", "body": ["__attribute__((__malloc__()))"], "description": "attribute malloc"}, "attribute nonnull": {"scope": "c,cpp", "prefix": "<PERSON><PERSON><PERSON><PERSON>", "body": ["__attribute__((__nonnull__()))"], "description": "attribute malloc"}}