{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lldb",
            "request": "launch",
            "name": "ui",
            "program": "${workspaceFolder}/build/linux_64/debug/ui/urq-ui",
            "args": [
                "--project",
                "${workspaceFolder}/asset"
            ],
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "preLaunchTask": "build",
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "ui:no-build",
            "program": "${workspaceFolder}/build/linux_64/debug/ui/urq-ui",
            "args": [
                "--project",
                "${workspaceFolder}/asset"
            ],
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "preLaunchTask": "servers",
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "urq-ui-server",
            "program": "${workspaceFolder}/build/linux_64/debug/example/urq-ui-server",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "preLaunchTask": "build",
            "env": {
                "LSAN_OPTIONS": "verbosity=1:log_threads=1",
            }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "urq-test",
            "program": "${workspaceFolder}//build/linux_64/debug/lib-test/urq-lib-test-var-table",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "preLaunchTask": "build",
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "ui:client",
            "program": "${workspaceFolder}/build/linux_64/debug/ui/urq-ui",
            "args": [
                "--project",
                "${workspaceFolder}/asset"
            ],
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "preLaunchTask": "ui-server",
        },
    ]
}