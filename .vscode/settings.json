{
    "search.exclude": {
        ".cache": true,
        ".tmp": true,
        "asset": true,
        "build": true,
        "Doxyfile": true,
        ".clang-format": true,
        ".clang-tidy": true,
        ".clangd": true,
        ".gitignre": true,
        "**/node_modules": true,
        "**/bower_components": true,
        "**/*.code-search": true
    },
    "files.associations": {
        // "**/*.h.in": "text",
        // "**/*.hpp.in": "text"
        "**/*.h.in": "cpp",
        "**/*.hpp.in": "cpp"
    },
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true,
    "[cpp]": {
        "editor.defaultFormatter": "xaver.clang-format"
    },
    "[c]": {
        "editor.defaultFormatter": "xaver.clang-format"
    },
    "clangd.arguments": [
        "--clang-tidy"
    ],
    "terminal.integrated.profiles.linux": {
        "urq-ui": {
            "path": "/bin/bash",
            "args": [
                "--rcfile",
                "${workspaceFolder}/scripts/complete"
            ]
        }
    },
    "terminal.integrated.defaultProfile.linux": "urq-ui",
    // TODO
    "vim.normalModeKeyBindingsNonRecursive": [
        {
            "before": [
                "z",
                "M"
            ],
            "commands": [
                "editor.foldAll"
            ]
        },
        {
            "before": [
                "z",
                "R"
            ],
            "commands": [
                "editor.unfoldAll"
            ]
        },
        {
            "before": [
                "z",
                "c"
            ],
            "commands": [
                "editor.fold"
            ]
        },
        {
            "before": [
                "z",
                "o"
            ],
            "commands": [
                "editor.unfold"
            ]
        }
    ],
    "editor.foldingStrategy": "auto", // 推荐自动折叠支持函数
    "editor.foldingImportsByDefault": true, // 默认折叠 import（可选）
    "editor.foldLevel": 1 // 折叠层级
}