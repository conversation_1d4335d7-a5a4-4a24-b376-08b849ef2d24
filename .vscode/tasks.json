{"version": "2.0.0", "tasks": [{"type": "shell", "label": "build", "command": "./md", "dependsOn": ["ui-server", "znd-server"]}, {"type": "shell", "label": "servers", "command": "echo", "args": ["servers start"], "dependsOn": ["ui-server", "znd-server"], "group": "test"}, {"label": "ui-server", "command": "${workspaceFolder}/build/linux_64/debug/example/urq-ui-server", "isBackground": true, "problemMatcher": {"owner": "ui-server", "fileLocation": "relative", "pattern": {"regexp": "^ERROR:.*$", "file": 1, "location": 2, "severity": 3, "code": 4, "message": 5}, "background": {"activeOnStart": true, "beginsPattern": "^start ui-server", "endsPattern": "^start mouse event server,.*"}}}, {"label": "znd-server", "command": "${workspaceFolder}/asset/znd-server", "args": ["--project", "${workspaceFolder}/asset"], "isBackground": true, "problemMatcher": {"owner": "znd-server", "fileLocation": "relative", "pattern": {"regexp": "^ERROR:.*$", "file": 1, "location": 2, "severity": 3, "code": 4, "message": 5}, "background": {"activeOnStart": true, "beginsPattern": "^znd sv starting...$", "endsPattern": "^znd sv started$"}}}]}