// #include "urq/value/value.h"
#include "urq_test_c.h"
#include <stdbool.h>

test_unit(normal)
{
    //
    // urq_value_t v1;
    // urq_value_t v2;
    // bool changed;

    // urq_value_init_inplace(&v1);
    // urq_value_init_inplace(&v2);

    // v1.type = URQ_VALUE_TYPE_U8;
    // v1.data.u8 = 1;

    // v2.type = URQ_VALUE_TYPE_U8;
    // v2.data.u8 = 2;

    // assert_eq(urq_value_set(&v1, &v2, &changed), 0);
    // assert_eq(changed, true);
    // assert_eq(v1.data.u8, 2);
}

run_tests(normal)