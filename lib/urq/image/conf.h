#pragma once

#include "urq/graphic/graphic.h"
#include "urq/position.h"
#include "urq/size.h"
#include "urq/style/style.h"

#ifndef URQ__IMAGE__CONF_H
#define URQ__IMAGE__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 图片配置
typedef struct {
    bool auto_generated;
} urq_image_widget_conf_t;

static inline void urq_image_widget_conf_init_inplace(
    urq_image_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_image_widget_conf_free_inplace(
    urq_image_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================
void urq_image_widget_conf_init_inplace(urq_image_widget_conf_t *self)
{
    self->auto_generated = false;
}

void urq_image_widget_conf_free_inplace(urq_image_widget_conf_t *self)
{
    self->auto_generated = false;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__IMAGE__CONF_H
