#pragma once

#include "lvgl.h"
#include "urq/preload.h"
#include "urq/scroll_direction.h"

#ifndef URQ_MARQUEE_H
#define URQ_MARQUEE_H

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 是否启用
    bool enable;
    /// @brief 间隔
    uint8_t interval;
    /// @brief 滚动距离
    uint16_t scroll_distance;
    /// @brief 滚动方向
    urq_scroll_direction_t scroll_direction;
    /// @brief 跑马灯动画
    lv_anim_t *marquee_anim;
    /// @brief [optional]文本对象
    lv_obj_t *obj;
    /// @brief 动画开始位置
    lv_coord_t start_pos;
    /// @brief 动画结束位置
    lv_coord_t end_pos;
    /// @brief 是否循环
    bool loop;
} urq_marquee_t;

static inline void urq_marquee_init(urq_marquee_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_marquee_free(urq_marquee_t *self)
    __attribute__((__nonnull__(1)));

// ========================= impl =========================

void urq_marquee_init(urq_marquee_t *self)
{
    self->enable = false;
    self->interval = 0;
    self->scroll_distance = 0;
    self->marquee_anim = NULL;
    self->scroll_direction = URQ_SCROLL_DIRECTION_UNSPECIFIED;
    self->obj = NULL;
    self->start_pos = 0;
    self->end_pos = 0;
    self->loop = false;
}

void urq_marquee_free(urq_marquee_t *self)
{
    if (self->marquee_anim != NULL) {
        urq_free(self->marquee_anim);
    }
}
#ifdef __cplusplus
}
#endif

#endif
