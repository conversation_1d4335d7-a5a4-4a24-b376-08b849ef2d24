#pragma once

#include "urq/keyboard/base_info_list.h"
#include "urq/keyboard/btnmatrix_ctrl_list.h"
#include "urq/keyboard/default_keyboard.h"
#include "urq/style/style.h"
#include "urq/conf/list/str.h"
#include "urq/conf/map/string.h"
#include <stdint.h>

#ifndef URQ__KEYBOARD__CONF_H
#define URQ__KEYBOARD__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    /// @brief 未设置、自定义
    URQ_KEYBOARD_LAYOUT_UNSPECIFIED = 0,
    /// @brief 小写字母
    URQ_KEYBOARD_LAYOUT_LOWER_LETTER = 1,
    /// @brief 大写字母
    URQ_KEYBOARD_LAYOUT_UPPER_LETTER = 2,
    /// @brief 数字
    URQ_KEYBOARD_LAYOUT_NUMBER = 3,
    /// @brief 特殊字符
    URQ_KEYBOARD_LAYOUT_SPECIAL_CHARACTER = 4,
    /// @brief 自定义
    URQ_KEYBOARD_LAYOUT_CUSTOM = 5,
} urq_keyboard_type_t;

typedef struct {
    /// @brief 键盘类型
    urq_keyboard_type_t type;
    /// @brief 按键样式列表
    urq_base_info_list_t *key_styles;
    /// @brief 键盘样式
    urq_style_t *style;
    /// @brief 显示
    urq_str_list_t *value;
    /// @brief 键盘属性
    urq_btnmatrix_ctrl_list_t *ctrl;
    /// @brief 映射值
    urq_string_map_t *show_map;
} urq_keyboard_widget_conf_t;

static inline void urq_keyboard_widget_conf_init_inplace(
    urq_keyboard_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_keyboard_widget_conf_free_inplace(
    urq_keyboard_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_keyboard_widget_conf_init_inplace(urq_keyboard_widget_conf_t *self)
{
    urq_used(urq_keyboard_upper_letter);
    urq_used(urq_keyboard_lower_letter);
    urq_used(urq_keyboard_number);
    urq_used(urq_keyboard_lower_letter_ctrl);
    urq_used(urq_keyboard_number_ctrl);
    // urq_used(urq_keyboard_symbol);
    urq_used(urq_keyboard_symbol_ctrl);
    urq_used(urq_keyboard_number1);
    urq_used(urq_keyboard_number1_ctrl);
    self->type = URQ_KEYBOARD_LAYOUT_UNSPECIFIED;
    self->show_map = NULL;
    self->ctrl = NULL;
    self->key_styles = NULL;
    self->style = NULL;
    self->value = NULL;
}

void urq_keyboard_widget_conf_free_inplace(urq_keyboard_widget_conf_t *self)
{
    self->type = URQ_KEYBOARD_LAYOUT_UNSPECIFIED;
    urq_string_map_free(self->show_map);
    if (self->ctrl != NULL) {
        urq_btnmatrix_ctrl_list_free_inplace(self->ctrl);
        urq_free(self->ctrl);
    }
    if (self->key_styles != NULL) {
        urq_base_info_list_free_inplace(self->key_styles);
        urq_free(self->key_styles);
    }
    if (self->style != NULL) {
        urq_style_free_inplace(self->style);
        urq_free(self->style);
    }
    if (self->value != NULL) {
        urq_str_list_free_inplace(self->value);
        urq_free(self->value);
    }
}

#ifdef __cplusplus
}
#endif
#endif // URQ__KEYBOARD__CONF_H
