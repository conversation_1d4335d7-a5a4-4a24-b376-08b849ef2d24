#pragma once

#include <lvgl/src/font/lv_symbol_def.h>
#include <lvgl/src/widgets/lv_btnmatrix.h>
#include <stddef.h>

#ifndef URQ__KEYBOARD__DEFAULT_KEYBOARD_H
#define URQ__KEYBOARD__DEFAULT_KEYBOARD_H

#ifdef __cplusplus
extern "C" {
#endif

static const char *urq_keyboard_number[] = {
    "7",   "8",     "9",  LV_SYMBOL_BACKSPACE,
    "\n",  "4",     "5",  "6",
    "+/-", "\n",    "1",  "2",
    "3",   "Clear", "\n", ".",
    "0",   "Enter", "\n", ""};

static const lv_btnmatrix_ctrl_t urq_keyboard_number_ctrl[] = {
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "7"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "8"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "9"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // BACKSPACE
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "4"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "5"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "6"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "+/-"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "1"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "2"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "3"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // clear
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "."
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "0"
    2 | LV_BTNMATRIX_CTRL_CLICK_TRIG, // "Enter"
};

static const char *urq_keyboard_lower_letter[] = {
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "0",
    "\n",
    "q",
    "w",
    "e",
    "r",
    "t",
    "y",
    "u",
    "i",
    "o",
    "p",
    "\n",
    "a",
    "s",
    "d",
    "f",
    "g",
    "h",
    "j",
    "k",
    "l",
    "\n",
    "Caps",
    "z",
    "x",
    "c",
    "v",
    "b",
    "n",
    "m",
    LV_SYMBOL_BACKSPACE,
    "\n",
    "123",
    "Space",
    "Enter",
    "\n",
    "",
};

static const lv_btnmatrix_ctrl_t urq_keyboard_lower_letter_ctrl[] = {
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "1"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "2"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "3"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "4"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "5"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "6"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "7"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "8"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "9"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "0"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "q"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "w"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "e"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "r"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "t"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "y"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "u"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "i"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "o"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "p"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ""
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "a"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "s"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "d"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "f"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "g"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "h"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "j"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "k"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "l"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ""
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "Caps"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "z"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "x"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "c"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "v"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "b"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "n"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "m"
    5 | LV_BTNMATRIX_CTRL_CLICK_TRIG, // "backspace"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "123"
};

static const char *urq_keyboard_upper_letter[] = {
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "0",
    "\n",
    "Q",
    "W",
    "E",
    "R",
    "T",
    "Y",
    "U",
    "I",
    "O",
    "P",
    "\n",
    "A",
    "S",
    "D",
    "F",
    "G",
    "H",
    "J",
    "K",
    "L",
    "\n",
    "Caps",
    "Z",
    "X",
    "C",
    "V",
    "B",
    "N",
    "M",
    LV_SYMBOL_BACKSPACE,
    "\n",
    "123",
    "Space",
    "Enter",
    "\n",
    "",
};

static const char *urq_keyboard_number1[] = {
    "1",  "2",   "3",     "4",
    "5",  "6",   "7",     "8",
    "9",  "0",   "\n",    "!",
    "@",  "$",   "%",     "^",
    "&",  "*",   "(",     ")",
    "-",  "\n",  "=",     "[",
    "]",  "{",   "}",     "|",
    "\\", ":",   ",",     ";",
    "\n", ",",   ".",     "<",
    ">",  "/",   "?",     "《",
    "》", "【",  "】",    LV_SYMBOL_BACKSPACE,
    "\n", "abc", "Space", "Enter",
    "\n", ""};

static const lv_btnmatrix_ctrl_t urq_keyboard_number1_ctrl[] = {
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "1"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "2"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "3"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "4"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "5"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "6"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "7"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "8"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "9"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "0"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "!"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "@"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "$"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "%"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "^"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "&"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "*"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "¥"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "("
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ")"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "-"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "="
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "["
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "]"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "{"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "{"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "|"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "|"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ":"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ","
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "."
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "<"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ">"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "/"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "?"
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ""
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ""
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ""
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ""
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ""
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ""
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // ""
    5 | LV_BTNMATRIX_CTRL_CLICK_TRIG, // ""
    LV_BTNMATRIX_CTRL_CLICK_TRIG,     // "abc"
};

// static const char *urq_keyboard_symbol[] = {
//     "【",
//     "】",
//     "{",
//     "}",
//     "#",
//     "%",
//     "^",
//     "*",
//     "+",
//     "=",
//     "\n",
//     "_",
//     "——",
//     "\\",
//     "|",
//     "~",
//     "《",
//     "》",
//     "$",
//     "&",
//     "·",
//     "\n",
//     "123",
//     "…",
//     ",",
//     " ",
//     "?",
//     "‘",
//     LV_SYMBOL_BACKSPACE,
//     "Enter",
//     ""};

static const lv_btnmatrix_ctrl_t urq_keyboard_symbol_ctrl[] = {
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "【"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "】"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "{"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "}"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "#"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "%"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "^"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "*"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "+"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "="
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "_"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "——"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "\\"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "|"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "~"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "《"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "》"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "$"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "&"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "·"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "…"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "123"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // ","
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // " "
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "?"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "‘"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "BACKSPACE"
    LV_BTNMATRIX_CTRL_CLICK_TRIG, // "Enter"
};
#ifdef __cplusplus
}
#endif

#endif // URQ__KEYBOARD__DEFAULT_KEYBOARD_H
