#pragma once
#include "lvgl.h"
#include "urq/confirm_win.h"
#include "urq/data/all_type.h"
#include "urq/widget/type.h"

#ifndef URQ__KEYBOARD__TRIGGER_DATA_H
#define URQ__KEYBOARD__TRIGGER_DATA_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    urq_data_all_type_t *max_value;
    urq_data_all_type_t *min_value;
    urq_confirm_win_t confirm_win;
    lv_obj_t *input;
    urq_widget_type_t type;
    bool is_init;
} urq_keyboard_trigger_data_t;

static inline void urq_keyboard_trigger_data_init_inplace(
    urq_keyboard_trigger_data_t *self)
{
    urq_used(self);
    self->max_value = NULL;
    self->min_value = NULL;
    self->input = NULL;
    self->is_init = false;
}

static inline void urq_keyboard_trigger_data_free_inplace(
    urq_keyboard_trigger_data_t *self)
{
    if (self->max_value != NULL) {
        urq_data_all_type_free_inplace(self->max_value);
        urq_free(self->max_value);
    }
    if (self->min_value != NULL) {
        urq_data_all_type_free_inplace(self->min_value);
        urq_free(self->min_value);
    }
}

#ifdef __cplusplus
}
#endif
#endif // URQ__KEYBOARD__TRIGGER_DATA_H
