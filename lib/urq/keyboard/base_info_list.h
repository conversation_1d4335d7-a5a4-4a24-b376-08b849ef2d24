#include "urq/keyboard/base_conf.h"
#include "urq/preload.h"

#ifndef URQ__KEYBOARD__BASE_INFO_LIST_H
#define URQ__KEYBOARD__BASE_INFO_LIST_H

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    urq_arr_t(urq_keyboard_base_conf_t *) keys;
    size_t size;
} urq_base_info_list_t;

static inline void urq_base_info_list_free_inplace(urq_base_info_list_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_base_info_list_init_inplace(urq_base_info_list_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_base_info_list_init_inplace(urq_base_info_list_t *self)
{
    self->size = 0;
}

void urq_base_info_list_free_inplace(urq_base_info_list_t *self)
{
    self->size = 0;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__KEYBOARD__BASE_INFO_LIST_H
