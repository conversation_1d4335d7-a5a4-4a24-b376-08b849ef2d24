#include "urq/keyboard/map_info.h"
#include "urq/errno.h"

urq_keyboard_map_t *urq_keyboard_map_new(void)
{
    return kh_init_urq_keyboard_map();
}

void urq_keyboard_map_free(urq_keyboard_map_t *self)
{
    kh_destroy_urq_keyboard_map(self);
}

void urq_keyboard_map_resize(urq_keyboard_map_t *self, uint32_t size)
{
    kh_resize_urq_keyboard_map(self, size);
}

int urq_keyboard_map_add(
    urq_keyboard_map_t *const self, uint32_t id, urq_keyboard_info_t *info)
{
    int ret = 0;
    khiter_t k = kh_put_urq_keyboard_map(self, (khint32_t)id, &ret);
    if (ret == 1) {
        // 初始化数据
        kh_val(self, k) = info;
        return 0;
    } else {
        switch (ret) {
        case -1:
            errno = URQ_ENOMEM;
            return -1;
        case 0:
            errno = URQ_EEXISTS;
            return -1;
        default:
            errno = EINVAL;
            return -1;
        }
    }
}

urq_keyboard_info_t *urq_keyboard_map_get(
    const urq_keyboard_map_t *self, uint32_t id)
{

    khiter_t k = kh_get_urq_keyboard_map(self, (khint32_t)id);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
        return NULL;
    }
    return kh_val(self, k);
}
