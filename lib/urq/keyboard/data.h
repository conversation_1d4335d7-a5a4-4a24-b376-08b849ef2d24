#pragma once

#include "urq/preload.h"

#ifndef URQ__KEYBOARD__DATA_H
#define URQ__KEYBOARD__DATA_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 键盘数据
typedef struct {
    /// @brief 输入框值
    const char *input_value;
    /// @brief 最大值
    const char *max_value;
    /// @brief 最小值
    const char *min_value;
    /// @brief 页面ID
    urq_page_id_t page_id;
} urq_keyboard_data_t;

static inline void urq_keyboard_data_init_inplace(urq_keyboard_data_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_keyboard_data_free_inplace(urq_keyboard_data_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_keyboard_data_init_inplace(urq_keyboard_data_t *self)
{
    self->input_value = NULL;
    self->max_value = NULL;
    self->min_value = NULL;
    self->page_id = 0;
}

void urq_keyboard_data_free_inplace(urq_keyboard_data_t *self)
{
    self->input_value = NULL;
    self->max_value = NULL;
    self->min_value = NULL;
    self->page_id = 0;
}

#ifdef __cplusplus
}
#endif

#endif // URQ__KEYBOARD__DATA_H
