#pragma once

#include "urq/conf/list/i16_ptr.h"
#include "urq/widget/state.h"
#include <string.h>

#ifndef URQ__DEVICE__LOCAL_H
#define URQ__DEVICE__LOCAL_H
#ifdef __cplusplus
extern "C" {
#endif

#define URQ_DEVICE_LOCAL_REGISTERS_SIZE 32

/// @brief 本地变量
typedef struct {
    char name[16]; // 工程名
    /// 透明度
    uint8_t overlay_opa;
    /// @brief 默认display id
    int8_t display_id;
    /// @brief 当前状态
    urq_state_t state;
    /// @brief 系统变量
    urq_i16_ptr_list_t registers;
} urq_device_local_t;

/// @brief 系统变量地址
typedef enum {
    URQ_DEVICE_LOCAL_ADDR_LANGUAGE = 1, ///< 当前显示语言
    URQ_DEVICE_LOCAL_ADDR_THEME = 2,    ///< 当前显示主题

    /* 窗口相关配置 */
    URQ_DEVICE_LOCAL_ADDR_BASIC_PAGE_NO = 3,  ///< 当前基本窗口号
    URQ_DEVICE_LOCAL_ADDR_COMMON_PAGE_NO = 4, ///< 当前公共窗口号
    URQ_DEVICE_LOCAL_ADDR_POPUP_PAGE_NO_LIST =
        5, ///< 当前弹窗窗口号列表(最大10级弹窗)

    /* 用户相关配置 */
    URQ_DEVICE_LOCAL_ADDR_CURRENT_USER_NO = 6,      ///< 当前登录用户号
    URQ_DEVICE_LOCAL_ADDR_CURRENT_USER_ACCOUNT = 7, ///< 当前登录用户账号
    URQ_DEVICE_LOCAL_ADDR_CURRENT_USER_NAME = 8,    ///< 当前登录用户名
    URQ_DEVICE_LOCAL_ADDR_CURRENT_USER_MOBILE = 9,  ///< 当前登录用户手机
    URQ_DEVICE_LOCAL_ADDR_CURRENT_USER_PERMISSION =
        10,                                        ///< 当前登录用户具备的权限
    URQ_DEVICE_LOCAL_ADDR_CURRENT_USER_GROUP = 11, ///< 当前登录用户所属组
    URQ_DEVICE_LOCAL_ADDR_CURRENT_USER_AVATAR =
        12, ///< 当前登录用户头像(MDI-ICON)
    URQ_DEVICE_LOCAL_ADDR_CURRENT_USER_COLOR = 13, ///< 当前登录用户颜色

    /* 输入法相关配置 */
    URQ_DEVICE_LOCAL_ADDR_CURRENT_INPUT_METHOD =
        14, ///< 当前输入法(0-ASCII,1-拼音,2-五笔)
    URQ_DEVICE_LOCAL_ADDR_CURRENT_KEYBOARD_WINDOW_NO = 15, ///< 当前键盘窗口号
    URQ_DEVICE_LOCAL_ADDR_INPUT_CONTENT = 17,              ///< 输入过程中的内容
    URQ_DEVICE_LOCAL_ADDR_INPUT_CANDIDATE = 18,            ///< 输入候选字
    URQ_DEVICE_LOCAL_ADDR_INPUT_PASSWORD_MODE =
        19,                                       ///< 输入时是否显示为密码模式
    URQ_DEVICE_LOCAL_ADDR_NUMERIC_INPUT_MAX = 20, ///< 数值输入最大值
    URQ_DEVICE_LOCAL_ADDR_NUMERIC_INPUT_MIN = 21, ///< 数值输入最小值
    URQ_DEVICE_LOCAL_ADDR_ASCII_INPUT_MAX_LENGTH =
        21, ///< ASCII字符输入最长长度
    URQ_DEVICE_LOCAL_ADDR_ASCII_INPUT_MIN_LENGTH =
        22, ///< ASCII字符输入最短长度

    /* 操作确认相关配置 */
    URQ_DEVICE_LOCAL_ADDR_OPERATION_CONFIRM = 30, ///< 操作确认(1-确认,2-取消)
    URQ_DEVICE_LOCAL_ADDR_OPERATION_CONFIRM_WAIT_TIME =
        31 ///< 操作确认等待时间(0-65535)
} urq_device_local_addr_t;

static const char *_URQ_DEVICE_LOCAL_ADDR_STRS[] = {
    "NONE",
    "lang",
    "theme",
    "page",
    "basic_page_no",
    "common_page_no",
    "popup_page_no_list",
    "current_user_no",
    "current_user_account",
};

/// @brief 系统变量地址转字符串
/// @param addr 地址
/// @returns 地址字符串
static inline const char *urq_device_local_addr_to_string(
    urq_device_local_addr_t addr)
{
    return _URQ_DEVICE_LOCAL_ADDR_STRS[addr];
}

static inline void urq_device_local_addr_init_inplace(urq_device_local_t *self)
{
    self->registers.data = (int16_t *)urq_malloc(
        sizeof(int16_t) * URQ_DEVICE_LOCAL_REGISTERS_SIZE);
    self->registers.size = URQ_DEVICE_LOCAL_REGISTERS_SIZE;
    memset(
        self->registers.data, 0,
        sizeof(int16_t) * URQ_DEVICE_LOCAL_REGISTERS_SIZE);
    self->state = 0;
    self->overlay_opa = 0xFF;
    self->display_id = -1;
}

static inline void urq_device_local_addr_free_inplace(urq_device_local_t *self)
{
    urq_free(self->registers.data);
    self->registers.data = NULL;
    self->registers.size = 0;
}

static inline void urq_device_system_set(
    urq_device_local_t *self, urq_device_local_addr_t addr, int16_t value)
{
    self->registers.data[addr] = value;
}

static inline int16_t urq_device_system_get(
    urq_device_local_t *self, urq_device_local_addr_t addr)
{
    return self->registers.data[addr];
}

#define URQ_SET_DEVICE_SYSTEM_VAR(var_sys, addr, value)                        \
    var_sys.registers.data[URQ_DEVICE_LOCAL_ADDR_##addr] = value

#define URQ_GET_DEVICE_SYSTEM_VAR(var_sys, addr)                               \
    var_sys.registers.data[URQ_DEVICE_LOCAL_ADDR_##addr]

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__DEVICE__LOCAL_H
