#pragma once

#include "urq/device/id.h"
#include "urq/proto/type.h"
#include <stdint.h>
#ifndef URQ__DEVICE__CONF_H
#define URQ__DEVICE__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 设备配置
typedef struct {
    /// @brief 设备的唯一标识
    urq_device_id_t id;
    /// @brief 设备编号
    int32_t no;
    /// @brief 协议类型
    urq_proto_type_t protocol;
} urq_device_conf_t;

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__DEVICE__CONF_H
