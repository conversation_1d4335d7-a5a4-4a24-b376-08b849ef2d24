#pragma once

#include "klib/khash.h"
#include "urq/device/conf.h"

#ifndef URQ__DEVICE__MAP_H
#define URQ__DEVICE__MAP_H
#ifdef __cplusplus
extern "C" {
#endif

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告
/// @brief 项目地址标签表
KHASH_MAP_INIT_INT(urq_device_map, urq_device_conf_t)
#pragma GCC diagnostic pop // 恢复之前的警告状态

/// @brief 设备与设备配置的表
typedef khash_t(urq_device_map) urq_device_map_t;

/// @brief 创建新的设备表
///
/// @return void
static inline urq_device_map_t *urq_device_map_new(void)
    __attribute__((__warn_unused_result__()));

/// @brief 释放设备表
///
/// @param self 设备表
/// @return void
static inline void urq_device_map_free(urq_device_map_t *self)
    __attribute__((__nonnull__(1)));

//  ------------------------------------------------------

static inline urq_device_map_t *urq_device_map_new(void)
{
    return kh_init_urq_device_map();
}

static inline void urq_device_map_free(urq_device_map_t *self)
{
    kh_destroy(urq_device_map, self);
}

static inline size_t urq_device_map_size(urq_device_map_t *self)
{
    return kh_size(self);
}

static inline urq_device_id_t urq_device_map_key(
    urq_device_map_t *self, size_t index)
{
    return (urq_device_id_t)kh_key(self, index);
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__DEVICE__MAP_H
