#pragma once

#ifndef URQ__CONN__RES_H
#define URQ__CONN__RES_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    /// @brief 响应关闭连接
    URQ_CONN_RES_CLOSE = 0,
    /// @brief 响应获取资源
    URQ_CONN_RES_ASSET = 1,
    /// @brief 响应加载资源
    URQ_CONN_RES_LOAD_RESOURCE = 2,
    /// @brief 响应执行动作
    URQ_CONN_RES_EXEC_ACTION = 3,
    /// @brief 响应画面变量返回
    URQ_CONN_RES_PAGE_VAR = 4,
    /// @brief 状态寄存器变化
    URQ_CONN_RES_STATUS_REGISTER = 5,

    /// @brief MAX, auto increment
    URQ_CONN_RES_MAX,
} urq_conn_res_t;

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__CONN__RES_H
