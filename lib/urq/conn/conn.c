#include "urq/conn/conn.h"
#include "urq/compile.h"
#include "urq/conn/parser.h"
#include "urq/log/verbose.h"
#include "urq/sleep.h"
#include "urq/socket/socket.h"

/// 连接断开时的回调，并不清除全部资源
/// 仅仅清除一些缓存数据等待重新连接
static void _socket_close_cb(void *ref_ctx)
{
    log_i("socket close\n");
    urq_conn_t *self = (urq_conn_t *)ref_ctx;

    urq_conn_parser_on_close(&self->parser);

    while (true) {
        if (urq_conn_connect(self) == 0) {
            break;
        }
        urq_sleep(1000);
    }
}

void urq_conn_init(urq_conn_t *self)
{
    urq_socket_init(&self->socket);
    urq_conn_parser_init_inplace(&self->parser);
    self->parser.socket = &self->socket;

    // 连接关闭时的回调
    urq_socket_set_cb_ctx(&self->socket, self);
    urq_socket_set_cb_close(&self->socket, _socket_close_cb);
}

void urq_conn_poll(urq_conn_t *self)
{
#if URQ_EDITOR == 0
    if (urq_conn_parser_parse(&self->parser)) {
        log_e("parse error\n");
        return;
    }
#endif
}
