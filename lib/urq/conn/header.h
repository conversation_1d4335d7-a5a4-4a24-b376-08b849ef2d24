#pragma once

#include "urq/buf/writer.h"
#include "urq/conn/req.h"
#include <stdint.h>

#ifndef URQ__CONN__HEADER_H
#define URQ__CONN__HEADER_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 根据内容的长度获取数据包的包头大小
/// @details 每个发送到服务端的数据包都应该有个包头
/// @param data_size 内容数据的大小
/// @returns 包头大小
static inline uint32_t urq_conn_header_size(uint32_t data_size)
    __attribute__((__warn_unused_result__()));

/// @brief 设置数据包的包头
///
/// @param writer    数据包写入器
/// @param op        操作码
/// @param data_size 内容数据的大小
static inline void urq_conn_header_set(
    urq_buf_writer_t *writer, urq_conn_req_t op, uint32_t data_size)
    __attribute__((__nonnull__(1)));

// impl

static inline uint32_t urq_conn_header_size(uint32_t data_size)
{
    const uint32_t OP_SIZE = 1;                           // 操作码大小
    const uint32_t FLAG_SIZE = 1;                         // 长度掩码大小
    const uint32_t LITTLE_SIZE = OP_SIZE + FLAG_SIZE + 1; // 小数据包大小
    const uint32_t MIDDLE_SIZE = LITTLE_SIZE + 2; // 中等数据包大小
    const uint32_t BIG_SIZE = MIDDLE_SIZE + 4;    // 大数据包大小

    if (data_size < 255) {
        return LITTLE_SIZE;
    }
    if (data_size < 65535) {
        return MIDDLE_SIZE;
    }
    return BIG_SIZE;
}

static inline void urq_conn_header_set(
    urq_buf_writer_t *buf, urq_conn_req_t op, uint32_t data_size)
{
    urq_buf_writer_u8(buf, (uint8_t)op);
    urq_buf_writer_u8(buf, 0x12); // flag

    if (data_size <= 0xFF) {
        urq_buf_writer_u8(buf, (uint8_t)data_size);
    } else if (data_size <= 0xFFFF) {
        urq_buf_writer_u8(buf, 0);
        urq_buf_writer_u16(buf, (uint16_t)data_size);
    } else {
        urq_buf_writer_u8(buf, 0);
        urq_buf_writer_u16(buf, 0);
        urq_buf_writer_u32(buf, data_size);
    }
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__CONN__HEADER_H
