#pragma once

#include "urq/data/condition.h"
#include <stdint.h>

#ifndef URQ__DATA__CONDITION_LIST_H
#define URQ__DATA__CONDITION_LIST_H

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    urq_arr_t(urq_data_condition_t *) conditions;
    uint16_t size;
} urq_data_condition_list_t;

static inline void urq_data_condition_list_init_inplace(
    urq_data_condition_list_t *list) __attribute__((__nonnull__(1)));

static inline void urq_data_condition_list_free_inplace(
    urq_data_condition_list_t *list) __attribute__((__nonnull__(1)));

// impl

static inline void urq_data_condition_list_init_inplace(
    urq_data_condition_list_t *list)
{
    list->conditions = NULL;
    list->size = 0;
}

static inline void urq_data_condition_list_free_inplace(
    urq_data_condition_list_t *list)
{
    if (list->conditions != NULL) {
        for (size_t i = 0; i < list->size; i++) {
            urq_data_condition_free_inplace(list->conditions[i]);
            urq_free(list->conditions[i]);
        }
        urq_free(list->conditions);
    }
}

#ifdef __cplusplus
}
#endif
#endif // URQ__DATA__CONDITION_LIST_H
