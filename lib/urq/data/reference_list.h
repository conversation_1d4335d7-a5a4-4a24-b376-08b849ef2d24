#pragma once

#include "urq/data/reference.h"

#ifndef URQ__DATA__REFERENCE__LIST_H
#define URQ__DATA__REFERENCE__LIST_H

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    urq_arr_t(urq_data_reference_t *) data;
    size_t size;
} urq_data_reference_ptr_list_t;

static inline void urq_data_reference_ptr_list_init(
    urq_data_reference_ptr_list_t *self) __attribute__((__nonnull__(1)));

static inline void urq_data_reference_ptr_list_free(
    urq_data_reference_ptr_list_t *self) __attribute__((__nonnull__(1)));

// ========================= impl =========================

void urq_data_reference_ptr_list_init(urq_data_reference_ptr_list_t *self)
{
    self->data = NULL;
    self->size = 0;
}

void urq_data_reference_ptr_list_free(urq_data_reference_ptr_list_t *self)
{
    if (self->data != NULL) {
        for (size_t i = 0; i < self->size; i++) {
            urq_data_reference_free(self->data[i]);
            urq_free(self->data[i]);
        }
        urq_free(self->data);
        self->data = NULL;
    }
}

#ifdef __cplusplus
}
#endif
#endif // URQ__DATA__REFERENCE__LIST_H
