#pragma once

#include "urq/data/reference.h"

#ifndef URQ__DATA__TRANSFORM_H
#define URQ__DATA__TRANSFORM_H

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    DATA_FORMAT_TYPE_UNSPECIFIED = 0,
    DATA_FORMAT_TYPE_BOOL = 1,
    DATA_FORMAT_TYPE_8UINT = 2,
    DATA_FORMAT_TYPE_8INT = 3,
    DATA_FORMAT_TYPE_16UINT = 4,
    DATA_FORMAT_TYPE_16INT = 5,
    DATA_FORMAT_TYPE_32UINT = 6,
    DATA_FORMAT_TYPE_32INT = 7,
    DATA_FORMAT_TYPE_32FLOAT = 8,
    DATA_FORMAT_TYPE_32UFLOAT = 9,
    DATA_FORMAT_TYPE_16UBCD = 10,
    DATA_FORMAT_TYPE_16BCD = 11,
    <PERSON><PERSON><PERSON>_FORMAT_TYPE_32UBCD = 12,
    DAT<PERSON>_FORMAT_TYPE_32BCD = 13,
    DATA_FORMAT_TYPE_16UHEX = 14,
    DATA_FORMAT_TYPE_16HEX = 15,
    DATA_FORMAT_TYPE_32UHEX = 16,
    DATA_FORMAT_TYPE_32HEX = 17,
    DATA_FORMAT_TYPE_16UBINARY = 18,
    DATA_FORMAT_TYPE_16BINARY = 19,
    DATA_FORMAT_TYPE_32UBINARY = 20,
    DATA_FORMAT_TYPE_32BINARY = 21,
    DATA_FORMAT_TYPE_64UINT = 22,
    DATA_FORMAT_TYPE_64INT = 23,
    DATA_FORMAT_TYPE_64UDOUBLE = 24,
    DATA_FORMAT_TYPE_64DOUBLE = 25,
    // 日期时间，占8字节，64位
    DATA_FORMAT_TYPE_64DATETIME = 26,
    // 小精度日期时间，只到分钟级别，占4字节，32位
    DATA_FORMAT_TYPE_32DATETIME = 27,
    // 日期，占4字节，32位
    DATA_FORMAT_TYPE_DATE = 28,
    // 时间，占4字节，32位（时、分、秒各1个字节）
    DATA_FORMAT_TYPE_TIME = 29,
    // 时间戳，占32位
    DATA_FORMAT_TYPE_32TIMESTAMP = 30,
    // 时间戳，占64位
    DATA_FORMAT_TYPE_64TIMESTAMP = 31,
    // 字符串，UTF8
    DATA_FORMAT_TYPE_STRING_UTF8 = 32,
    // 字符串，Unicode
    DATA_FORMAT_TYPE_STRING_UNICODE = 33,
    // 字符串，GBK
    DATA_FORMAT_TYPE_STRING_GBK = 34,
    // 字符串，UTF8高低互换
    DATA_FORMAT_TYPE_STRING_UTF8_LH = 35,
    // 字符串，Unicode高低互换
    DATA_FORMAT_TYPE_STRING_UNICODE_LH = 36,
    // 字符串，GBK高低互换
    DATA_FORMAT_TYPE_STRING_GBK_LH = 37,
    // IP地址(4字节)
    DATA_FORMAT_TYPE_IP = 38,
    // 结构体
    DATA_FORMAT_TYPE_STRUCT = 39,
} urq_data_transform_type_t;

typedef struct {
    /// @brief 数据格式类型
    urq_data_transform_type_t type;
    /// @brief 偏移值
    urq_data_reference_t offset_value;
    /// @brief 系数
    urq_data_reference_t factor_value;
    // TODO: 线性转换
    // TODO: 条件输出
    // TODO: 表达式转换
} urq_data_transform_t;

#ifdef __cplusplus
}
#endif

#endif
