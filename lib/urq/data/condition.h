#pragma once

#include "urq/data/all_type.h"

#ifndef URQ__DATA__CONDITION_H
#define URQ__DATA__CONDITION_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 数据条件类型
typedef enum {
    // 未指定
    DATA_COMP_TYPE_UNSPECIFIED = 0,
    // 等于
    DATA_COMP_TYPE_EQUAL = 1,
    // 不等于
    DATA_COMP_TYPE_NOT_EQUAL = 2,
    // 大于
    DATA_COMP_TYPE_GREATER = 3,
    // 小于
    DATA_COMP_TYPE_LESS = 4,
    // 大于等于
    DATA_COMP_TYPE_GREATER_EQUAL = 5,
    // 小于等于
    DATA_COMP_TYPE_LESS_EQUAL = 6,
    // 在范围内（包括两个边界，LEFT>=RIGHT1 && VAR<=RIGHT2）
    DATA_COMP_TYPE_IN_RANGE = 7,
    // 不在范围内（超出两个边界，LEFT<RIGHT1 || VAR>RIGHT2）
    DATA_COMP_TYPE_OUT_RANGE = 8,
    // 在范围内（不包括两个边界，LEFT>RIGHT1 && VAR<RIGHT2）
    DATA_COMP_TYPE_IN_LESS_RANGE = 9,
    // 不在范围内（等于两个边界也不在范围内，LEFT<=RIGHT1 || LEFT>=RIGHT2）
    DATA_COMP_TYPE_OUT_OPEN_RANGE = 10
} urq_data_comp_type_t;

/// @brief 数据条件逻辑类型
typedef enum {
    DATA_LOGIC_TYPE_UNSPECIFIED = 0,
    DATA_LOGIC_TYPE_AND = 1,
    DATA_LOGIC_TYPE_OR = 2,
} urq_data_logic_type_t;

/// @brief 数据条件
typedef struct {

    urq_data_all_type_t *left_value;
    urq_data_all_type_t *right_value1;
    urq_data_all_type_t *right_value2;

    /// @brief 比较类型
    urq_data_comp_type_t comp_type;
    /// @brief [optional]逻辑类型1
    urq_data_logic_type_t logic_type;

    /// @brief [optional]左括号数量
    int8_t left_parentheses;
    /// @brief [optional]右括号数量
    int8_t right_parentheses;
} urq_data_condition_t;

static inline void urq_data_condition_init_inplace(
    urq_data_condition_t *condition) __attribute__((__nonnull__(1)));

static inline void urq_data_condition_free_inplace(
    urq_data_condition_t *condition) __attribute__((__nonnull__(1)));

// impl

static inline void urq_data_condition_init_inplace(
    urq_data_condition_t *condition)
{
    condition->left_value = NULL;
    condition->right_value1 = NULL;
    condition->right_value2 = NULL;
    condition->comp_type = DATA_COMP_TYPE_UNSPECIFIED;
    condition->logic_type = DATA_LOGIC_TYPE_UNSPECIFIED;
    condition->left_parentheses = 0;
    condition->right_parentheses = 0;
}

static inline void urq_data_condition_free_inplace(
    urq_data_condition_t *condition)
{
    if (condition->left_value != NULL) {
        urq_data_all_type_free_inplace(condition->left_value);
        urq_free(condition->left_value);
    }
    if (condition->right_value1 != NULL) {
        urq_data_all_type_free_inplace(condition->right_value1);
        urq_free(condition->right_value1);
    }
    if (condition->right_value2 != NULL) {
        urq_data_all_type_free_inplace(condition->right_value2);
        urq_free(condition->right_value2);
    }
}

#ifdef __cplusplus
}
#endif
#endif // URQ__DATA_CONDITION_H
