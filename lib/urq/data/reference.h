#pragma once

#include "urq/data/all_type.h"
#include "urq/preload.h"
#include <stdbool.h>

#ifndef URQ__DATA__REFERENCE_H
#define URQ__DATA__REFERENCE_H

#ifdef __cplusplus
extern "C" {
#endif

struct urq_atag_t;

typedef enum {
    DATA_REFERENCE_TYPE_UNSPECIFIED = 0,
    DATA_REFERENCE_TYPE_REGISTER_IDX = 1,
    DATA_REFERENCE_TYPE_BOOL = 2,
    DATA_REFERENCE_TYPE_INT = 3,
    DATA_REFERENCE_TYPE_UINT = 4,
    DATA_REFERENCE_TYPE_FLOAT = 5,
    DATA_REFERENCE_TYPE_DOUBLE = 6,
    DATA_REFERENCE_TYPE_STRING = 7,
    DATA_REFERENCE_TYPE_LVOBJ = 8,
} urq_data_reference_type_t;

typedef struct {
    urq_data_all_type_t *all_type;
    uint8_t index_register;
} urq_data_reference_t;

static inline void urq_data_reference_init_inplace(urq_data_reference_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_data_reference_free_inplace(urq_data_reference_t *self)
    __attribute__((__nonnull__(1)));

// ========================= impl =========================
void urq_data_reference_init_inplace(urq_data_reference_t *self)
{
    self->all_type = NULL;
    self->index_register = 0;
}

void urq_data_reference_free_inplace(urq_data_reference_t *self)
{
    urq_used(self);
    if (self->all_type != NULL) {
        urq_data_all_type_free_inplace(self->all_type);
        urq_free(self->all_type);
    }
}

#ifdef __cplusplus
}
#endif

#endif
