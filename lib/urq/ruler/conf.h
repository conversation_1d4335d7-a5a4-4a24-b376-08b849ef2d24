#pragma once

#include "urq/color.h"
#include "urq/curve/scale.h"
#include "urq/font.h"
#include "urq/ruler/type.h"
#include "urq/style/line.h"
#include "urq/style/point.h"

#ifndef URQ__RULER__CONF_H
#define URQ__RULER__CONF_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 刻度尺配置
typedef struct {
    /// @brief 刻度尺类型
    urq_ruler_type_t type;
    /// @brief 刻度方向
    urq_ruler_direction_t direction;
    /// @brief 标签位置
    urq_ruler_label_position_t label_position;
    
    /// @brief 基础刻度配置（复用现有的刻度结构）
    urq_curve_scale_t scale;
    
    /// @brief 刻度尺主线样式
    urq_style_line_t *main_line;
    /// @brief 刻度尺背景颜色
    urq_color_ref_t background_color;
    
    /// @brief 标签字体
    urq_font_t *label_font;
    /// @brief 标签颜色
    urq_color_ref_t label_color;
    /// @brief 标签偏移距离
    lv_coord_t label_offset;
    
    /// @brief 弧形刻度尺专用：起始角度（度）
    uint16_t start_angle;
    /// @brief 弧形刻度尺专用：结束角度（度）
    uint16_t end_angle;
    /// @brief 弧形刻度尺专用：半径
    lv_coord_t radius;
    /// @brief 弧形刻度尺专用：中心点X偏移
    lv_coord_t center_offset_x;
    /// @brief 弧形刻度尺专用：中心点Y偏移
    lv_coord_t center_offset_y;
    
    /// @brief 是否显示标签
    bool show_labels;
    /// @brief 是否显示主线
    bool show_main_line;
    /// @brief 是否显示网格线
    bool show_grid_lines;
    /// @brief 是否自动计算刻度间距
    bool auto_spacing;
    
    /// @brief 自定义标签格式化函数指针
    char *(*label_formatter)(lv_coord_t value, void *user_data);
    /// @brief 用户数据指针
    void *user_data;
} urq_ruler_widget_conf_t;

/// @brief 初始化刻度尺配置
/// @param self 配置结构指针
static inline void urq_ruler_widget_conf_init_inplace(urq_ruler_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 释放刻度尺配置
/// @param self 配置结构指针
static inline void urq_ruler_widget_conf_free_inplace(urq_ruler_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

void urq_ruler_widget_conf_init_inplace(urq_ruler_widget_conf_t *self)
{
    self->type = URQ_RULER_TYPE_HORIZONTAL;
    self->direction = URQ_RULER_DIRECTION_BOTTOM_RIGHT;
    self->label_position = URQ_RULER_LABEL_POSITION_OUTSIDE;
    
    urq_curve_scale_init_inplace(&self->scale);
    
    self->main_line = NULL;
    urq_color_ref_init_inplace(&self->background_color);
    
    self->label_font = NULL;
    urq_color_ref_init_inplace(&self->label_color);
    self->label_offset = 5;
    
    self->start_angle = 0;
    self->end_angle = 360;
    self->radius = 50;
    self->center_offset_x = 0;
    self->center_offset_y = 0;
    
    self->show_labels = true;
    self->show_main_line = true;
    self->show_grid_lines = false;
    self->auto_spacing = true;
    
    self->label_formatter = NULL;
    self->user_data = NULL;
}

void urq_ruler_widget_conf_free_inplace(urq_ruler_widget_conf_t *self)
{
    urq_curve_scale_free_inplace(&self->scale);
    
    if (self->main_line != NULL) {
        urq_style_line_free_inplace(self->main_line);
        urq_free(self->main_line);
        self->main_line = NULL;
    }
}

#ifdef __cplusplus
}
#endif

#endif // URQ__RULER__CONF_H
