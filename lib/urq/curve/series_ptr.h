
#pragma once

#include "urq/preload.h"
#include "urq/curve/series.h"

#ifndef URQ__CURVE__SERIES_PTR_H
#define URQ__CURVE__SERIES_PTR_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    uint8_t size;
    urq_arr_t(urq_curve_series_t*) data;
} urq_curve_series_ptr_list_t;

/// @brief 原地初始化样式列表
static inline void urq_curve_series_ptr_list_init_inplace(urq_curve_series_ptr_list_t *list)
    __attribute__((__nonnull__(1)));

/// @brief 原地释放样式列表
static inline void urq_curve_series_ptr_list_free_inplace(urq_curve_series_ptr_list_t *list)
    __attribute__((__nonnull__(1)));

// impl

static inline void urq_curve_series_ptr_list_init_inplace(urq_curve_series_ptr_list_t *list)
{
    list->size = 0;
    list->data = NULL;
}

static inline void urq_curve_series_ptr_list_free_inplace(urq_curve_series_ptr_list_t *list)
{
    urq_used(list);
}


#ifdef __cplusplus
}
#endif
#endif // URQ__CURVE__SERIES_PTR_H
