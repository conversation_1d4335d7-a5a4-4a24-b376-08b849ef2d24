#pragma once

#include "urq/data_resource.h"
#include "urq/font.h"
#include "urq/preload.h"

#ifndef URQ__OPTION_LIST__CONF_H
#define URQ__OPTION_LIST__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 数据来源
    urq_data_resource_type_t data_resource_type;
    /// @brief 选项
    char *options;
    /// @brief 选项值
    char **options_value;
    /// @brief 字体
    // urq_font_t *font;
    /// @brief 选中颜色
    urq_color_ref_t selected_color;
    /// @brief 行间距
    uint8_t row_spacing;
} urq_option_list_conf_t;

static inline void urq_option_list_conf_init_inplace(
    urq_option_list_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_option_list_conf_free_inplace(
    urq_option_list_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_option_list_conf_init_inplace(urq_option_list_conf_t *self)
{
    // self->font = NULL;
    self->options = NULL;
    self->options_value = NULL;
    self->data_resource_type = URQ_DATA_RESOURCE_TYPE_UNSPECIFIED;
    urq_color_ref_init_inplace(&self->selected_color);
    self->row_spacing = 0;
}

void urq_option_list_conf_free_inplace(urq_option_list_conf_t *self)
{
    // if (self->font != NULL) {
    //     urq_font_free_inplace(self->font);
    //     self->font = NULL;
    // }
    if (self->options != NULL) {
        urq_free(self->options);
        self->options = NULL;
    }
    if (self->options_value != NULL) {
        urq_free(self->options_value);
        self->options_value = NULL;
    }
}

#ifdef __cplusplus
}
#endif
#endif
