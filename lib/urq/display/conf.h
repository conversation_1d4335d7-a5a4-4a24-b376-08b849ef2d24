#pragma once

#include "urq/keyboard/map_info.h"
#include "urq/page/conf_map.h"
#include "urq/preload.h"
#include "urq/rotate.h"
#include "urq/conf/map/lvgl_pair.h"
#include "urq/style/style.h"
#include <string.h>

#ifndef URQ__DISPLAY__CONF_H
#define URQ__DISPLAY__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 页面列表
    urq_page_group_conf_map_t *pages;
    /// @brief [optional] display size
    urq_size_t size;
    /// @brief 顶层容器
    lv_obj_t *top_layer;
    /// @brief 画面容器
    lv_obj_t *page_layer;
    /// @brief 公共容器
    lv_obj_t *common_layer;
    /// @brief 其他功能容器
    lv_obj_t *func_layer;
    /// @brief 键盘信息
    urq_keyboard_map_t *keyboard_map;
    /// @brief 克隆组件
    urq_lvgl_pair_map_t *clone_map;
    urq_style_t *style;
    /// @brief [optional] display name
    char *name; // display name
    /// @brief 主画面ID
    urq_page_id_t main_page_id;
    /// @brief [optional] 公共画面窗口
    urq_page_id_t common_page_id;
    /// @brief rotate type
    urq_rotate_type_t rotate_type;
    /// @brief 公共页面是否在上方
    bool common_on_top;
} urq_display_conf_t;

/// @brief 原地初始化显示配置
static inline void urq_display_conf_init_inplace(urq_display_conf_t *const self)
    __attribute__((__nonnull__(1)));

/// @brief 原地释放显示配置
static inline void urq_display_conf_free_inplace(urq_display_conf_t *self)
    __attribute__((__nonnull__(1)));

// impl

static inline void urq_display_conf_init_inplace(urq_display_conf_t *const self)
{
    self->name = NULL;
    self->top_layer = NULL;
    self->style = NULL;
    self->page_layer = NULL;
    self->common_layer = NULL;
    self->func_layer = NULL;
    self->main_page_id = 0;
    self->common_page_id = 0;
    self->rotate_type = URQ_ROTATE_TYPE_0;
    self->common_on_top = false;
    self->keyboard_map = NULL;
    self->clone_map = NULL;
    self->size.w = 0;
    self->size.h = 0;
    self->pages = NULL;
}

static inline void urq_display_conf_free_inplace(urq_display_conf_t *self)
{
    // TODO
    // if(self->name != NULL) {
    //     printf("=============< %s\n", self->name);
    //     urq_free(self->name);
    //     self->name = NULL;
    // }

    if (self->pages != NULL) {
        urq_page_group_conf_map_free(self->pages);
        self->pages = NULL;
    }

    if (self->keyboard_map != NULL) {
        urq_keyboard_map_free(self->keyboard_map);
        self->keyboard_map = NULL;
    }

    if (self->clone_map != NULL) {
        urq_lvgl_pair_map_free(self->clone_map);
        self->clone_map = NULL;
    }
    self->size.w = 0;
    self->size.h = 0;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__DISPLAY__CONF_H
