#pragma once

#include "urq/keyboard/conf.h"
#include "urq/text/ptr_list.h"

#ifndef URQ__BUTTON__CONF_H
#define URQ__BUTTON__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 按钮配置
    urq_keyboard_base_conf_t *button_define;
    /// @brief 按钮功能码
    urq_keyboard_func_code_t code;
    /// @brief 按钮实际值
    const char *value;
} urq_button_widget_conf_t;

static inline void urq_button_widget_conf_init_inplace(
    urq_button_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_button_widget_conf_free_inplace(
    urq_button_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_button_widget_conf_init_inplace(urq_button_widget_conf_t *self)
{
    self->button_define = NULL;
    self->value = NULL;
    self->code = URQ_BUTTON_FUNCTION_CODE_UNSPECIFIED;
}

void urq_button_widget_conf_free_inplace(urq_button_widget_conf_t *self)
{
    if (self->button_define != NULL) {
        urq_keyboard_base_conf_free_inplace(self->button_define);
        urq_free(self->button_define);
    }

    if (self->value != NULL) {
        urq_free((void *)self->value);
    }
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__BUTTON__CONF_H
