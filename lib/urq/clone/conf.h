#pragma once

#include "urq/size.h"

#ifndef URQ__CLONE__CONF_H
#define URQ__CLONE__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 组件的位置
    lv_point_t pos;
    /// @brief 组件的大小
    urq_size_t size;
    /// @brief 克隆元件所在的页面ID
    uint16_t page_id_u16;
    /// @brief 克隆元件的ID
    uint16_t widget_id_u16;
} urq_clone_widget_conf_t;

static inline void urq_clone_widget_conf_init_inplace(
    urq_clone_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_clone_widget_conf_free_inplace(
    urq_clone_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_clone_widget_conf_init_inplace(urq_clone_widget_conf_t *self)
{
    self->page_id_u16 = 0;
    self->widget_id_u16 = 0;
}

void urq_clone_widget_conf_free_inplace(urq_clone_widget_conf_t *self)
{
    self->page_id_u16 = 0;
    self->widget_id_u16 = 0;
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__BUTTON__CONF_H
