#!/bin/env python3

import os
import sys

script_dir = os.path.dirname(os.path.abspath(__file__))

def gen(name:str, typename:str, include:str, init:str|None,deinit:str|None, move:str|None):
    out_file = os.path.join(script_dir, name + ".h")

    content="""
// clang-format off

#pragma once

#include "urq/vec/helper.h"
#include <stdlib.h>
{include}

#ifndef URQ__VEC__{name:upper}_H
#define URQ__VEC__{name:upper}_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 指针数组
typedef struct {
    /// @brief 数组大小
    size_t size;
    /// @brief 数组容量
    size_t capacity;
    /// @brief 数组数据
    {type} *data;
} urq_vec_{name}_t;

/// @brief 初始化指针数组
/// @param mut_vec 指针数组
/// @param destructor 析构函数
static inline void urq_vec_{name}_init(urq_vec_{name}_t *mut_vec) {
    mut_vec->size = 0;
    mut_vec->capacity = 0;
    mut_vec->data = NULL;
}

/// @brief 取消初始化指针数组
/// @param mut_vec 指针数组
static inline void urq_vec_{name}_deinit(urq_vec_{name}_t *mut_vec) {
    {deinit}
    if (mut_vec->data != NULL) {
        free(mut_vec->data);
    }
}

/// @brief 扩容
/// @param mut_vec 指针数组
/// @param capacity 新的容量
/// @returns 0 成功，-1 失败
static inline int urq_vec_{name}_reserve(urq_vec_{name}_t *mut_vec, size_t capacity) {
    if (mut_vec->capacity < capacity) {
        {type} *new_data =
            ({type} *)realloc(mut_vec->data, capacity * sizeof({type}));
        {init}
        if (new_data == NULL) {
            return -1;
        }
        mut_vec->data = new_data;
        mut_vec->capacity = capacity;
    }
    return 0;
}

/// @brief 添加元素
/// @param mut_vec 指针数组
/// @param item 元素
static inline int urq_vec_{name}_push(urq_vec_{name}_t *mut_vec, {type} item) {
    if (mut_vec->size >= mut_vec->capacity) {
        if (urq_vec_{name}_reserve(mut_vec, urq_vec_helper_grow_size(mut_vec->capacity, mut_vec->size))) {
            return -1;
        }
    }
    mut_vec->data[mut_vec->size] = item;
    mut_vec->size++;
    return 0;
}

/// @brief 弹出元素
/// @param mut_vec 指针数组
/// @returns 元素
static inline {type} urq_vec_{name}_pop(urq_vec_{name}_t *mut_vec) {
    return mut_vec->data[--mut_vec->size];
}

#ifdef __cplusplus
}
#endif
#endif // URQ__VEC__{name:upper}_H

// clang-format on
    """

    init_str = ""
    if init:
        init_str = f"""for( size_t i = mut_vec->size; i < capacity; i++) {{
                {init}(&new_data[i]);
            }}"""

    deinit_str = ""
    if deinit:
        deinit_str = f"""for (size_t i = 0; i < mut_vec->size; i++) {{
                {deinit}(&mut_vec->data[i]);
            }}"""

    move_str = ""
    if move:
        move_str = f"""{move}(&mut_vec->data[mut_vec->size], &item);"""

    content = content.replace("{include}", include)
    content = content.replace("{name}", name)
    content = content.replace("{name:upper}", name.upper())
    content = content.replace("{type}", typename)
    content = content.replace("{init}", init_str)
    content = content.replace("{deinit}", deinit_str)


    with open(out_file, "w") as f:
        f.write(content)


gen(
    name="uint8",
    typename="uint8_t",
    include="#include <stdint.h>",
    init=None,
    deinit=None,
    move=None,
)



gen(
    name = "ptr",
    typename = "void *",
    include = "",
    init=None,
    deinit=None,
    move=None,
)

gen(
    name = "conf_property_button", 
    typename = "urq_conf_property_button_t", 
    include = """#include "urq/conf/property/button.h" """,
    init = "urq_conf_property_button_init",
    deinit = "urq_conf_property_button_deinit",
    move = "urq_conf_property_button_move",
)
