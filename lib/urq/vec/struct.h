#pragma once

#include "urq/preload.h"
#include "urq/vec/helper.h"
#include <errno.h>
#include <stddef.h>
#include <stdint.h>
#include <string.h>

#ifndef URQ__VEC__STRUCT_H
#define URQ__VEC__STRUCT_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 结构体数组
typedef struct {
    /// @brief 结构体大小
    size_t struct_size;
    /// @brief 容量
    size_t capacity;
    /// @brief 长度
    size_t size;
    /// @brief 数据
    uint8_t *data;
    /// @brief 原地初始化方法
    void (*init_inplace)(void *data);
    /// @brief 原地析构方法
    void (*free_inplace)(void *data);
} urq_vec_struct_t;

/// @brief 创建结构体数组
/// @param vec         结构体数组
/// @param struct_size 结构体大小
/// @param init_inplace 原地初始化方法
/// @param free_inplace 原地析构方法
/// @returns 结构体数组
static inline void urq_vec_struct_new_inplace(
    urq_vec_struct_t *vec, size_t struct_size, void (*init_inplace)(void *data),
    void (*free_inplace)(void *data))
{
    vec->struct_size = struct_size;
    vec->capacity = 0;
    vec->size = 0;
    vec->data = NULL;
    vec->init_inplace = init_inplace;
    vec->free_inplace = free_inplace;
}

/// @brief 释放结构体数组
/// @param vec 结构体数组
static inline void urq_vec_struct_free_inplace(urq_vec_struct_t *vec)
{
    if (vec->capacity > 0) {
        for (size_t i = 0; i < vec->size; i++) {
            vec->free_inplace(vec->data + i * vec->struct_size);
        }
        urq_free(vec->data);
    }
}

static inline int urq_vec_struct_reserve(urq_vec_struct_t *vec, size_t capacity)
{
    if (capacity <= vec->capacity) {
        return 0;
    }
    uint8_t *data =
        (uint8_t *)urq_realloc(vec->data, capacity * vec->struct_size);
    if (data == NULL) {
        errno = ENOMEM;
        return -1;
    }
    vec->data = data;
    vec->capacity = capacity;
    return 0;
}

static inline int urq_vec_struct_push(urq_vec_struct_t *vec, void *data)
{
    if (vec->size >= vec->capacity) {
        size_t cap = urq_vec_helper_grow_size(vec->capacity, vec->size);
        if (urq_vec_struct_reserve(vec, cap)) {
            return -1;
        }
    }
    void *dst = vec->data + vec->size * vec->struct_size;
    vec->init_inplace(dst);
    vec->size++;
    memcpy(dst, data, vec->struct_size);
    return 0;
}

static inline void *_urq_vec_struct_at(urq_vec_struct_t *vec, size_t index)
{
    if (index >= vec->size) {
        return NULL;
    }
    return vec->data + index * vec->struct_size;
}

#define urq_vec_struct_at(t, vec, index) (t *)_urq_vec_struct_at(vec, index)

#ifdef __cplusplus
}
#endif
#endif // URQ__VEC__STRUCT_H
