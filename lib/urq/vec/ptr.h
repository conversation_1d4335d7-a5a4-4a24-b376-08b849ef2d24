#pragma once

#include "urq/preload.h"
#include "urq/vec/helper.h"
#include <stdlib.h>

#ifndef URQ__VEC__PTR_H
#define URQ__VEC__PTR_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 指针数组
typedef struct {
    /// @brief 数组大小
    size_t size;
    /// @brief 数组容量
    size_t capacity;
    /// @brief 数组数据
    void **data;
    /// @brief 析构方法
    void (*free)(void *data);
} urq_vec_ptr_t;

/// @brief 初始化指针数组
/// @param mut_vec 指针数组
/// @param destructor 析构函数
static inline void urq_vec_ptr_init(urq_vec_ptr_t *mut_vec)
{
    mut_vec->size = 0;
    mut_vec->capacity = 0;
    mut_vec->data = NULL;
    mut_vec->free = NULL;
}

/// @brief 取消初始化指针数组
/// @param mut_vec 指针数组
static inline void urq_vec_ptr_deinit(urq_vec_ptr_t *mut_vec)
{
    if (mut_vec->data != NULL) {
        if (mut_vec->free != NULL) {
            for (size_t i = 0; i < mut_vec->size; i++) {
                mut_vec->free(mut_vec->data[i]);
            }
        }
        urq_free(mut_vec->data);
    }
}

/// @brief 扩容
/// @param mut_vec 指针数组
/// @param capacity 新的容量
/// @returns 0 成功，-1 失败
static inline int urq_vec_ptr_reserve(urq_vec_ptr_t *mut_vec, size_t capacity)
{
    if (mut_vec->capacity < capacity) {
        void **new_data =
            (void **)urq_realloc(mut_vec->data, capacity * sizeof(void *));
        if (new_data == NULL) {
            return -1;
        }
        mut_vec->data = new_data;
        mut_vec->capacity = capacity;
    }
    return 0;
}

/// @brief 添加元素
/// @param mut_vec 指针数组
/// @param item 元素
static inline int urq_vec_ptr_push(urq_vec_ptr_t *mut_vec, void *item)
{
    if (mut_vec->size >= mut_vec->capacity) {
        if (urq_vec_ptr_reserve(
                mut_vec,
                urq_vec_helper_grow_size(mut_vec->capacity, mut_vec->size))) {
            return -1;
        }
    }
    mut_vec->data[mut_vec->size] = item;
    mut_vec->size++;
    return 0;
}

/// @brief 弹出元素
/// @param mut_vec 指针数组
/// @returns 元素
static inline void *urq_vec_ptr_pop(urq_vec_ptr_t *mut_vec)
{
    return mut_vec->data[--mut_vec->size];
}

#ifdef __cplusplus
}
#endif
#endif // URQ__VEC__PTR_H
