#pragma once

#include "urq/conn/conn.h"
#include "urq/page/page.h"
#include "urq/req/header.h"
#include "urq/req/op.h"
#include "urq/widget/event.h"
#include "urq/widget/id.h"

#ifndef URQ__REQ__ACTION_H
#define URQ__REQ__ACTION_H
#ifdef __cplusplus
extern "C" {
#endif

static inline void urq_req_wirte_set_action(
    void *page, void *widget_id, void *event_code)
{
    urq_page_t *const page_t = (urq_page_t *)page;
    urq_widget_id_t widget_id_t = (urq_widget_id_t)(intptr_t)widget_id;
    lv_event_code_t event_code_t = (lv_event_code_t)(intptr_t)event_code;

    urq_event_code_t code = URQ_EVENT_CODE_UNSPECIFIED;
    if (event_code_t == LV_EVENT_PRESSED) {
        code = URQ_EVENT_CODE_WIDGET_PRESS;
    } else if (event_code_t == LV_EVENT_RELEASED) {
        code = URQ_EVENT_CODE_WIDGET_RELEASE;
        //} else if (event_code_t == LV_EVENT_CLICKED) {
        //    code = URQ_EVENT_CODE_WIDGET_CLICK;
    } else {
        return;
    }

    const uint32_t body_size = 5;
    const uint32_t size = body_size + urq_req_header_size(body_size);

    uint8_t buffer[size];
    urq_buf_writer_t *buf = urq_buf_writer_make(size, buffer);

    urq_req_header_set(buf, URQ_REQ_OP_SET_ACTION, body_size);

    /// 页面ID
    urq_buf_writer_u16(buf, page_t->id);
    /// 元件ID
    urq_buf_writer_u16(buf, widget_id_t);
    /// 事件代码
    urq_buf_writer_u8(buf, (uint8_t)code);

    if (urq_conn_write(&page_t->project->conn, size, buffer)) {
        return;
    }
}

// static inline int urq_req_wirte_set_action(
//     urq_conn_t *const conn, urq_page_id_t page_id, urq_widget_id_t widget_id,
//     urq_event_code_t event_code)
//{
//     urq_used(conn);
//     urq_used(page_id);
//     urq_used(widget_id);
//     urq_used(event_code);
//     const uint32_t body_size = 5;
//     const uint32_t size = body_size + urq_req_header_size(body_size);
//
//     uint8_t buffer[size];
//     urq_buf_writer_t *buf = urq_buf_writer_make(size, buffer);
//
//     urq_req_header_set(buf, URQ_REQ_OP_SET_ACTION, body_size);
//
//     /// 页面ID
//     urq_buf_writer_u16(buf, page_id);
//     /// 元件ID
//     urq_buf_writer_u16(buf, widget_id);
//     /// 事件代码
//     urq_buf_writer_u8(buf, (uint8_t)event_code);
//
//     return urq_conn_write(conn, size, buffer);
// }

#ifdef __cplusplus
}
#endif
#endif // #ifdef __cplusplus
