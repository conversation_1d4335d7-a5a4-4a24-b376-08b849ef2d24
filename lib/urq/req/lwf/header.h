#pragma once

#include "urq/buf/writer.h"
#include "urq/req/header.h"
#include "urq/req/lwf/req.h"
#include <stdint.h>

#ifndef URQ__REQ__LWF__HEADER_H
#define URQ__REQ__LWF__HEADER_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 根据内容的长度获取数据包的包头大小
/// @details 每个发送到服务端的数据包都应该有个包头
/// @param data_size 内容数据的大小
/// @returns 包头大小
static inline uint32_t urq_req_lwf_header_size(uint32_t data_size)
    __attribute__((__warn_unused_result__()));

/// @brief 写入低频操作的头部信息
/// @param buf  缓冲区
/// @param req  请求类型
/// @param ext  扩展
/// @param len  长度
/// @returns void
static inline void urq_req_lwf_header_set(
    urq_buf_writer_t *buf, urq_req_lwf_req_t req, uint32_t ext, uint32_t len)
    __attribute__((__nonnull__(1)));

// -------------------------------------------------

static inline uint32_t urq_req_lwf_header_size(uint32_t data_size)
{
    return 6 + urq_req_header_size(data_size + 6);
}

static inline void urq_req_lwf_header_set(
    urq_buf_writer_t *buf, urq_req_lwf_req_t req, uint32_t ext, uint32_t len)
{
    urq_used(len);
    // urq_req_header_set(buf, URQ_REQ_OP_LOW_FREQUENCY, len + 6);
    urq_buf_writer_u16(buf, (uint16_t)req);
    urq_buf_writer_u32(buf, ext);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__REQ__LWF__HEADER_H
