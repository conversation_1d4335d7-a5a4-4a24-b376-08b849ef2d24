#pragma once

#include "urq/conn/conn.h"
#include "urq/req/header.h"

#ifndef URQ__REQ__PAGE_H
#define URQ__REQ__PAGE_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 写入页面
/// @param conn 连接
/// @param page_id 页面 id
/// @returns 是否写入成功
/// @note
// 1. 新打开的窗口数量，长度为 1 字节，范围为 0 -255。
// 2. 新打开的窗口列表，每个窗口 id 占用 2
// 字节，新打开的窗口会接立即接收到一次当前画面用到的所有变量的值的推送。
// 3. 之前已经打开的窗口列表，每个窗口 id 占用 2
// 字节，之前已经打开的窗口不会马上推送变量。
static inline int urq_req_wirte_set_page(
    urq_conn_t *const conn, urq_page_id_t page_id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

static inline int urq_req_wirte_set_page_list(
    urq_conn_t *const conn, urq_page_id_t page_id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));
// -------------------------------------------------

static inline int urq_req_wirte_set_page(
    urq_conn_t *const conn, urq_page_id_t page_id)
{
    const uint32_t body_size = 3;

    const uint32_t size = body_size + urq_req_header_size(body_size);

    uint8_t buffer[size];
    urq_buf_writer_t *buf = urq_buf_writer_make(size, buffer);

    // 设置整个请求头
    urq_req_header_set(buf, URQ_REQ_OP_WRITE, body_size);

    // TODO 请求数量请求内容
    urq_buf_writer_u8(buf, (uint8_t)0x01);
    urq_buf_writer_u16(buf, (uint16_t)page_id);

    return urq_conn_write(conn, size, buffer);
}

static inline int urq_req_wirte_set_page_list(
    urq_conn_t *const conn, urq_page_id_t page_id)
{
    urq_used(conn);
    urq_used(page_id);
    return 0;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__REQ__PAGE_H
