
#pragma once

#include "urq/conn/conn.h"
#include "urq/path.h"
#include "urq/req/lwf/header.h"
#include <string.h>

#ifndef URQ__REQ__FILE_H
#define URQ__REQ__FILE_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 写入文件
/// @param conn 连接
/// @param file_id 文件 id
/// @returns 是否写入成功
/// @note
// 1. 新打开的窗口数量，长度为 1 字节，范围为 0 -255。
// 2. 新打开的窗口列表，每个窗口 id 占用 2
// 字节，新打开的窗口会接立即接收到一次当前画面用到的所有变量的值的推送。
// 3. 之前已经打开的窗口列表，每个窗口 id 占用 2
// 字节，之前已经打开的窗口不会马上推送变量。

/// @brief 写入文件
/// @param conn 连接
/// @param project_root_path 工程根路径
/// @param file_id_p 文件 id
/// @param file_id 文件 id
/// @returns 是否写入成功
static inline int urq_req_wirte_set_file_id(
    urq_conn_t *const conn, const char *project_root_path, int file_id_p,
    int file_id, uint8_t other)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 写入文件
/// @param conn 连接
/// @param filename 文件名
/// @returns 是否写入成功
static inline int urq_req_wirte_set_file(
    urq_conn_t *const conn, const char *filename)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

// -------------------------------------------------

int urq_req_wirte_set_file_id(
    urq_conn_t *const conn, const char *project_root_path, int file_id_p,
    int file_id, uint8_t other)
{
    char path[URQ_PATH_MAX] = {0};
    if (snprintf(
            path, URQ_PATH_MAX, "%s/%d/%d", project_root_path + 2, file_id_p,
            file_id) == -1) {
        return -1;
    }
    printf("[urq_req_wirte_set_file_id] load path: %s\n", path);
    const uint32_t body_size = (uint32_t)strlen(path);

    const uint32_t size = body_size + urq_req_lwf_header_size(body_size);

    uint8_t buffer[size];
    urq_buf_writer_t *buf = urq_buf_writer_make(size, buffer);

    uint32_t ext = 0;
    ext = other;
    ext = ext << 8;
    ext = ext | (uint8_t)file_id;
    ext = ext << 8;
    ext = ext | (uint8_t)file_id_p;

    // 设置整个请求头
    urq_req_lwf_header_set(buf, URQ_REQ_LWF_REQ_ASSET, ext, body_size);

    // 写入文件路径
    const uint8_t *data = (uint8_t *)path;
    urq_buf_writer_data(buf, body_size, data);

    return urq_conn_write(conn, size, buffer);
}

int urq_req_wirte_set_file(urq_conn_t *const conn, const char *filename)
{
    const uint32_t body_size = (uint32_t)strlen(filename);

    const uint32_t size = body_size + urq_req_lwf_header_size(body_size);

    uint8_t buffer[size];
    urq_buf_writer_t *buf = urq_buf_writer_make(size, buffer);

    // 设置整个请求头
    urq_req_lwf_header_set(buf, URQ_REQ_LWF_REQ_ASSET, 0, body_size);

    // TODO 请求数量请求内容
    // urq_buf_writer_u16(buf, (uint16_t)0x0001);
    const uint8_t *data = (uint8_t *)filename;
    urq_buf_writer_data(buf, body_size, data);
    // urq_used(filename);
    // urq_buf_writer_str(buf, filename);

    return urq_conn_write(conn, size, buffer);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__REQ__FILE_H
