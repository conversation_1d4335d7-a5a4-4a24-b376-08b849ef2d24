#pragma once

#include "urq/color.h"
#include "urq/curve/scale.h"
#include "urq/style/line.h"
#include "urq/style/point.h"

#ifndef URQ__METER__CONF_H
#define URQ__METER__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 图片配置
typedef struct {
    /// @brief 刻度
    urq_curve_scale_t scale;
    /// @brief 指针配置
    urq_style_line_t pointer_config;
    /// @brief 指针圆心配置
    urq_style_point_t *pointer_point_config;

    /// @brief 标签颜色
    urq_color_ref_t scale_label_color;
    /// @brief 下限颜色
    urq_color_ref_t lower_limit_color;
    /// @brief 上限颜色
    urq_color_ref_t upper_limit_color;
    /// @brief 中间颜色
    urq_color_ref_t middle_color;
    /// @brief 起始角度
    uint16_t start_rangle;
    /// @brief 结束角度
    uint16_t end_rangle;
    /// @brief 下限值
    uint16_t lower_limit;
    /// @brief 上限值
    uint16_t upper_limit;
    /// @brief 宽度
    uint8_t limit_width;
    /// @brief 半径
    uint8_t limit_radius;
    /// @brief 小数位数
    uint8_t decimal_places;
    /// @brief 标签半径
    uint8_t label_radius;

} urq_meter_widget_conf_t;

static inline void urq_meter_widget_conf_init_inplace(
    urq_meter_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_meter_widget_conf_free_inplace(
    urq_meter_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_meter_widget_conf_init_inplace(urq_meter_widget_conf_t *self)
{
    urq_curve_scale_init_inplace(&self->scale);
    urq_style_line_init_inplace(&self->pointer_config);
    self->pointer_point_config = NULL;
    urq_color_ref_init_inplace(&self->scale_label_color);
    urq_color_ref_init_inplace(&self->lower_limit_color);
    urq_color_ref_init_inplace(&self->upper_limit_color);
    urq_color_ref_init_inplace(&self->middle_color);
    self->start_rangle = 0;
    self->end_rangle = 360;
    self->lower_limit = 40;
    self->upper_limit = 80;
    self->limit_width = 1;
    self->limit_radius = 0;
    self->decimal_places = 0;
    self->label_radius = 0;
}

void urq_meter_widget_conf_free_inplace(urq_meter_widget_conf_t *self)
{
    if (self->pointer_point_config != NULL) {
        urq_style_point_free_inplace(self->pointer_point_config);
        urq_free(self->pointer_point_config);
    }
    self->pointer_point_config = NULL;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__IMAGE__CONF_H
