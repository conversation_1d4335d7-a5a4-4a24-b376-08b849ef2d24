#pragma once

#include "urq/preload.h"
#include "urq/style/style.h"

#ifndef URQ__STYLE__POINT_H
#define URQ__STYLE__POINT_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 点样式
typedef struct {
    /// @brief 边框
    urq_style_t *style;
    /// @brief 半径
    uint8_t radius;
    /// @brief 宽度
    uint8_t width;
    /// @brief 高度
    uint8_t height;
} urq_style_point_t;

static inline void urq_style_point_init_inplace(urq_style_point_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_style_point_free_inplace(urq_style_point_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

static inline void urq_style_point_init_inplace(urq_style_point_t *self)
{
    self->style = NULL;
    self->radius = 0;
    self->width = 0;
    self->height = 0;
}

static inline void urq_style_point_free_inplace(urq_style_point_t *self)
{
    urq_used(self);
}

#ifdef __cplusplus
}
#endif

#endif
