#pragma once

#include "lvgl.h"
#include "urq/font.h"
#include "urq/style/border.h"
#include "urq/style/shadow.h"

#ifndef URQ__STYLE__STYLE_H
#define URQ__STYLE__STYLE_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 单个样式
typedef struct {
    urq_font_t font;            // 字体
    urq_style_border_t *border; // 边框
    urq_style_shadow_t *shadow; // 阴影
    urq_color_ref_t bg;         // 背景颜色
} urq_style_t;

static inline void urq_style_init_inplace(urq_style_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_style_free_inplace(urq_style_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_style_init_inplace(urq_style_t *self)
{
    self->border = NULL;
    self->shadow = NULL;
    urq_color_ref_init_inplace(&self->bg);
    urq_font_init_inplace(&self->font);
}

void urq_style_free_inplace(urq_style_t *self)
{
    if (self->border != NULL) {
        urq_style_border_free_inplace(self->border);
        urq_free(self->border);
    }
    if (self->shadow != NULL) {
        urq_style_shadow_free_inplace(self->shadow);
        urq_free(self->shadow);
    }
    urq_font_free_inplace(&self->font);
    self->border = NULL;
    self->shadow = NULL;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__STYLE__STYLE_H
