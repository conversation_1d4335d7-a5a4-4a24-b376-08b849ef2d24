#pragma once

#include "urq/color.h"
#include "urq/preload.h"

#ifndef URQ__STYLE__BORDER_H
#define URQ__STYLE__BORDER_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 边框
typedef struct {
    /// @brief 颜色
    urq_color_ref_t color;
    /// @brief 宽度
    int16_t width;
    /// @brief 左边
    bool left;
    /// @brief 上边
    bool top;
    /// @brief 右边
    bool right;
    /// @brief 下边
    bool bottom;
    /// @brief 内部
    bool internal;
} urq_style_border_t;

static inline void urq_style_border_init_inplace(urq_style_border_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_style_border_free_inplace(urq_style_border_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

static inline void urq_style_border_init_inplace(urq_style_border_t *self)
{
    urq_color_ref_init_inplace(&self->color);
    self->width = 0;
    self->left = false;
    self->top = false;
    self->right = false;
    self->bottom = false;
    self->internal = false;
}

static inline void urq_style_border_free_inplace(urq_style_border_t *self)
{
    urq_used(self);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__STYLE__BORDER_H
