#pragma once

#include "lvgl.h"
#include "urq/color.h"
#include "urq/preload.h"

#ifndef URQ__STYLE__LINE_H
#define URQ__STYLE__LINE_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 线样式
typedef struct {
    /// @brief 线颜色
    urq_color_ref_t color;
    /// @brief 虚线间隔
    uint8_t dash_gap;
    /// @brief 虚线宽度
    uint8_t dash_width;
    /// @brief 线宽度
    uint8_t width;
    /// @brief 长度
    int8_t length;
    /// @brief 起点是否圆角
    bool round_start;
    /// @brief 终点是否圆角
    bool round_end;
    /// @brief 是否投影
    bool shadow;
} urq_style_line_t;

static inline void urq_style_line_init_inplace(urq_style_line_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_style_line_free_inplace(urq_style_line_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

static inline void urq_style_line_init_inplace(urq_style_line_t *self)
{
    self->round_start = false;
    self->round_end = false;
    self->dash_gap = 0;
    self->dash_width = 0;
    self->width = 1;
    self->length = -1;
    urq_color_ref_init_inplace(&self->color);
}

static inline void urq_style_line_free_inplace(urq_style_line_t *self)
{
    urq_used(self);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__STYLE__LINE_H
