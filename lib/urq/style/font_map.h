#pragma once

#include "klib/khash.h"
#include <stdint.h>

#ifndef URQ__STYLE__FONT_MAP_H
#define URQ__STYLE__FONT_MAP_H
#ifdef __cplusplus
extern "C" {
#endif

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告

/// @brief 字体ID表
KHASH_MAP_INIT_INT(urq_font_map, uint8_t)
#pragma GCC diagnostic pop // 恢复之前的警告状态

/// @brief 字体ID表
typedef khash_t(urq_font_map) urq_font_map_t;

/// @brief 创建新的字体ID表
///
/// @return 新的字体ID表
urq_font_map_t *urq_font_map_new(void)
    __attribute__((__warn_unused_result__()));

/// @brief 释放字体ID表
///
/// @param self 字体ID表
/// @return void
void urq_font_map_free(urq_font_map_t *self) __attribute__((__nonnull__(1)));

/// @brief 调整大小
///
/// @param self 字体ID表
/// @param size 大小
/// @return void
void urq_font_map_resize(urq_font_map_t *self, uint32_t size)
    __attribute__((__nonnull__(1)));

/// @brief 添加一个字体ID
///
/// @param self     字体ID表
/// @return 是否添加成功
int urq_font_map_add(urq_font_map_t *const self, int32_t id, uint8_t font_id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 获取字体ID
///
/// @param self      字体ID表
/// @param id        字体ID
/// @param out_value 字体ID值
/// @return 是否获取成功
int8_t urq_font_map_get(const urq_font_map_t *self, uint32_t id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

#ifdef __cplusplus
}
#endif

#endif // URQ__FONT__MAP_H
