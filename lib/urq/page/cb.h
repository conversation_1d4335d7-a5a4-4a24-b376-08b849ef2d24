#pragma once

#include "urq/cb/cb.h"
#include <stddef.h>
#ifndef URQ__PAGE__CB_H
#define URQ__PAGE__CB_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 页面回调数据
typedef struct urq_page_cb_ring urq_page_cb_ring_t;

/// @brief 创建一个回调环
/// @param capacity 环的大小
/// @return 成功返回环的指针，失败返回NULL
urq_page_cb_ring_t *urq_page_cb_ring_new();

/// @brief 释放回调环
/// @param self 环
void urq_page_cb_ring_free(urq_page_cb_ring_t *self);

/// @brief 执行回调
/// @param self 环
void urq_page_cb_ring_exec(urq_page_cb_ring_t *self);

/// @brief 添加回调： 0 参数
/// @param self 环
/// @param cb   回调方法
/// @return 成功返回0，失败返回-1
int urq_page_cb_ring_add0(urq_page_cb_ring_t *self, urq_cb0_t cb);

/// @brief 添加回调： 1 参数
/// @param self 环
/// @param cb   回调方法
/// @param free 回调参数的释放方法
/// @param arg1 参数1
/// @return 成功返回0，失败返回-1
int urq_page_cb_ring_add1(
    urq_page_cb_ring_t *self, urq_cb1_t cb, urq_cb1_t free, void *arg1);

/// @brief 添加回调： 2 参数
/// @param self 环
/// @param cb   回调方法
/// @param free 回调参数的释放方法
/// @param arg1 参数1
/// @param arg2 参数2
/// @return 成功返回0，失败返回-1
int urq_page_cb_ring_add2(
    urq_page_cb_ring_t *self, urq_cb2_t cb, urq_cb2_t free, void *arg1,
    void *arg2);

/// @brief 添加回调： 3 参数
/// @param self 环
/// @param cb   回调方法
/// @param free 回调参数的释放方法
/// @param arg1 参数1
/// @param arg2 参数2
/// @param arg3 参数3
/// @return 成功返回0，失败返回-1
int urq_page_cb_ring_add3(
    urq_page_cb_ring_t *self, urq_cb3_t cb, urq_cb3_t free, void *arg1,
    void *arg2, void *arg3);

#ifdef __cplusplus
}
#endif
#endif // URQ__PAGE__CB_H
