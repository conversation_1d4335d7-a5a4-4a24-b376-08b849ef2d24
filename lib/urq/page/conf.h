#pragma once

#include "urq/page/group_conf.h"
#include "urq/preload.h"
#include <stddef.h>

#ifndef URQ__PAGE__CONF_H
#define URQ__PAGE__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 画面配置
typedef struct {
    urq_page_id_t id;     // 画面的唯一编号
    urq_page_type_t type; // 画面类型
} urq_page_conf_t;

/// @brief 原地初始化画面
/// @param self 需要初始化的画面
/// @returns void
static inline void urq_page_conf_init_inplace(urq_page_conf_t *self)
{
    urq_used(self);
    // self->name = NULL;
    // urq_vec_i32_init_inplace(&self->group_id_list);
}

/// @brief 取消初始化画面
/// @param self 需要初始化的画面
/// @returns void
__attribute__((__nonnull__(1))) static inline void urq_page_conf_free_inplace(
    urq_page_conf_t *self)
{
    urq_used(self);
    // urq_free_if_not_null(self->name);
    // urq_vec_i32_free_inplace(&self->group_id_list);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__PAGE__CONF_H
