#include "urq/page/cb.h"
#include "urq/log/debug_items.hpp"
#include "urq/log/info.hpp"
#include "urq/preload.h"
#include "urq/ring.hpp"
#include <cstddef>
#include <cstdint>

typedef struct {
    uint8_t argc;  // 参数个数
    urq_cb_t cb;   // 回调方法
    urq_cb_t free; // 回调参数的释放方法
} urq_page_cb_data_t;

static inline urq_page_cb_data_t *create_data(size_t argc)
{
    return (urq_page_cb_data_t *)urq_malloc(
        sizeof(urq_page_cb_data_t) + argc * sizeof(void *));
}

static inline void **get_argv(urq_page_cb_data_t *self)
{
    return (void **)(self + 1);
}

static inline void urq_page_cb_data_free(urq_page_cb_data_t *self)
{
    auto **argv = get_argv(self);
    log_v("free, data: ", self, ", argv: ", argv, log_endl);
    switch (self->argc) {
    case 0:
        break;
    case 1:
        self->free.cb1(argv[0]);
        break;
    case 2:
        self->free.cb2(argv[0], argv[1]);
        break;
    case 3:
        self->free.cb3(argv[0], argv[1], argv[2]);
        break;
    case 4:
        self->free.cb4(argv[0], argv[1], argv[2], argv[3]);
        break;
    default:
        log_e("invalid argc: %d\n", self->argc);
        break;
    }
    urq_free(self);
}

using Ring = urq::Ring<urq_page_cb_data_t *, 1024>;

inline static Ring &as_ring(urq_page_cb_ring_t *self) { return *(Ring *)self; }

urq_page_cb_ring_t *urq_page_cb_ring_new()
{
    auto *ptr = new Ring();
    log_i("malloc ring: ", ptr, log_endl);
    return (urq_page_cb_ring_t *)ptr;
}

void urq_page_cb_ring_free(urq_page_cb_ring_t *self)
{
    log_i("free ring: ", self, log_endl);
    auto &ring = as_ring(self);
    while (!ring.empty()) {
        auto *data = ring.take();
        urq_page_cb_data_free(data);
    }
    delete &ring;
}

void urq_page_cb_ring_exec(urq_page_cb_ring_t *self)
{
    void **argv;
    auto &ring = as_ring(self);
    auto end = ring.end();
    while (ring.begin() != end) {
        auto *data = ring.take();
        argv = get_argv(data);
        log_d(
            "cb, argc: ", data->argc, ", data: ", data, ", argv: ", argv,
            log_endl);
        switch (data->argc) {
        case 0:
            data->cb.cb0();
            break;
        case 1:
            data->cb.cb1(argv[0]);
            break;
        case 2:
            data->cb.cb2(argv[0], argv[1]);
            break;
        case 3:
            data->cb.cb3(argv[0], argv[1], argv[2]);
            break;
        case 4:
            data->cb.cb4(argv[0], argv[1], argv[2], argv[3]);
            break;
        default:
            log_e("invalid argc: %d\n", data->argc);
            break;
        }
        urq_page_cb_data_free(data);
    }
}

/// @brief 添加回调： 0 参数
/// @param ring 环
/// @param cb 回调方法
/// @return 成功返回0，失败返回-1
int urq_page_cb_ring_add0(urq_page_cb_ring_t *self, urq_cb0_t cb)
{
    auto &ring = as_ring(self);
    auto *data = create_data(0);

    data->argc = 0;
    data->cb.cb0 = cb;
    data->free.cb0 = urq_noop0;

    return ring.push(data);
}

int urq_page_cb_ring_add1(
    urq_page_cb_ring_t *self, urq_cb1_t cb, urq_cb1_t free, void *arg1)
{
    auto &ring = as_ring(self);
    auto *data = create_data(1);
    void **argv = get_argv(data);

    data->argc = 1;
    data->cb.cb1 = cb;
    data->free.cb1 = free;
    argv[0] = arg1;

    return ring.push(data);
}

int urq_page_cb_ring_add2(
    urq_page_cb_ring_t *self, urq_cb2_t cb, urq_cb2_t free, void *arg1,
    void *arg2)
{
    auto &ring = as_ring(self);
    auto *data = create_data(2);
    void **argv = get_argv(data);

    log_v("add2, data: ", data, ", argv: ", argv, log_endl);

    data->argc = 2;
    data->cb.cb2 = cb;
    data->free.cb2 = free;
    argv[0] = arg1;
    argv[1] = arg2;

    return ring.push(data);
}

int urq_page_cb_ring_add3(
    urq_page_cb_ring_t *self, urq_cb3_t cb, urq_cb3_t free, void *arg1,
    void *arg2, void *arg3)
{
    auto &ring = as_ring(self);
    auto *data = create_data(3);
    void **argv = get_argv(data);

    log_v("add3, data: ", data, ", argv: ", argv, log_endl);

    data->argc = 3;
    data->cb.cb3 = cb;
    data->free.cb3 = free;
    argv[0] = arg1;
    argv[1] = arg2;
    argv[2] = arg3;

    return ring.push(data);
}