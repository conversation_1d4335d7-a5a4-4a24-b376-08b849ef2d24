#include "urq/page/conf_map.h"
#include "urq/errno.h"

urq_page_group_conf_map_t *urq_page_group_conf_map_new(void)
{
    return kh_init_urq_page_group_conf_map();
}

void urq_page_group_conf_map_free(urq_page_group_conf_map_t *self)
{
    kh_destroy_urq_page_group_conf_map(self);
}

void urq_page_group_conf_map_resize(
    urq_page_group_conf_map_t *self, uint32_t size)
{
    kh_resize_urq_page_group_conf_map(self, size);
}

int urq_page_group_conf_map_add(
    urq_page_group_conf_map_t *const self, uint32_t id,
    urq_page_group_conf_t *conf)
{
    int ret = 0;
    khiter_t k = kh_put_urq_page_group_conf_map(self, (khint32_t)id, &ret);
    if (ret == 1) {
        // 初始化数据
        kh_val(self, k) = *conf;
        return 0;
    } else {
        switch (ret) {
        case -1:
            errno = URQ_ENOMEM;
            return -1;
        case 0:
            errno = URQ_EEXISTS;
            return -1;
        default:
            errno = EINVAL;
            return -1;
        }
    }
}

urq_page_group_conf_t *urq_page_group_conf_map_get(
    const urq_page_group_conf_map_t *self, uint32_t id)
{
    khiter_t k = kh_get_urq_page_group_conf_map(self, id);
    if (k == kh_end(self)) {
        return NULL;
    }
    return &kh_val(self, k);
}
