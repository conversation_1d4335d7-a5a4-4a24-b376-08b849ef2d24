#pragma once

#include "urq/project/conf_font.h"
#include <stddef.h>

#ifndef URQ__PROJECT__CONF_FONT_LIST_H
#define URQ__PROJECT__CONF_FONT_LIST_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 工程字体列表
typedef struct {
    size_t count;                   // 字体数量
    urq_project_conf_font_t *fonts; // 字体列表
} urq_project_conf_font_list_t;

/// @brief 原地初始化工程字体列表
static inline void urq_project_conf_font_list_init_inplace(
    urq_project_conf_font_list_t *self) __attribute__((__nonnull__(1)));

/// @brief 原地释放工程字体列表
static inline void urq_project_conf_font_list_free_inplace(
    urq_project_conf_font_list_t *self) __attribute__((__nonnull__(1)));

// impl

static inline void urq_project_conf_font_list_init_inplace(
    urq_project_conf_font_list_t *self)
{
    self->count = 0;
    self->fonts = NULL;
}

static inline void urq_project_conf_font_list_free_inplace(
    urq_project_conf_font_list_t *self)
{
    for (size_t i = 0; i < self->count; i++) {
        urq_project_conf_font_free_inplace(&self->fonts[i]);
    }
    urq_free_if_not_null(self->fonts);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__PROJECT__CONF_FONT_LIST_H
