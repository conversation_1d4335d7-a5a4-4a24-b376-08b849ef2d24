#pragma once

#include <stdint.h>

#ifndef URQ__PROJECT__CONF_FONT_H
#define URQ__PROJECT__CONF_FONT_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 工程字体配置
typedef struct {
    int32_t id; // 字体 id
} urq_project_conf_font_t;

/// @brief 原地初始化工程字体
static inline void urq_project_conf_font_init_inplace(
    urq_project_conf_font_t *self) __attribute__((__nonnull__(1)));

/// @brief 原地释放工程字体
static inline void urq_project_conf_font_free_inplace(
    urq_project_conf_font_t *self) __attribute__((__nonnull__(1)));

static inline void urq_project_conf_font_init_inplace(
    urq_project_conf_font_t *self)
{
    self->id = -1;
}

static inline void urq_project_conf_font_free_inplace(
    urq_project_conf_font_t *self)
{
    self->id = -1;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__PROJECT__CONF_FONT_H
