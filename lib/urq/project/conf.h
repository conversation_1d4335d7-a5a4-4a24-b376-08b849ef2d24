#pragma once

#include "lvgl.h"
#include "urq/preload.h"
#include "urq/project/conf_font_list.h"
#include <stddef.h>

#ifndef URQ__PROJECT__CONF_H
#define URQ__PROJECT__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 工程配置
typedef struct {
    int local_display_id;                   // 本地主屏幕
    char *id;                               // 工程的唯一 id
    lv_color_t bg_color;                    // 背景颜色
    urq_project_conf_font_list_t font_list; // 字体列表
} urq_project_conf_t;

/// @brief 原地初始化工程配置
static inline void urq_project_conf_init_inplace(urq_project_conf_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 原地释放工程配置
static inline void urq_project_conf_free_inplace(urq_project_conf_t *self)
    __attribute__((__nonnull__(1)));

// impl

static inline void urq_project_conf_init_inplace(urq_project_conf_t *self)
{
    self->local_display_id = -1;
    self->id = NULL;
    urq_project_conf_font_list_init_inplace(&self->font_list);
}

static inline void urq_project_conf_free_inplace(urq_project_conf_t *self)
{
    urq_free_if_not_null(self->id);
    urq_project_conf_font_list_free_inplace(&self->font_list);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__PROJECT__CONF_H
