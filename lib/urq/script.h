#pragma once

#include "urq/data/condition_list.h"

#ifndef URQ__SCRIPT_H
#define URQ__SCRIPT_H

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    SCRIPT_TYPE_UNSPECIFIED = 0,
    SCRIPT_TYPE_LUA = 1,
    SCRIPT_TYPE_PYTHON = 2,
    SCRIPT_TYPE_JS = 3,
    SCRIPT_TYPE_C = 4,
    SCRIPT_TYPE_LD = 5,
    SCRIPT_TYPE_ST = 6,
} urq_script_type_t;

typedef struct {
    /// @brief 条件列表
    urq_data_condition_list_t *conditions;
    /// @brief [optional]脚本类型
    urq_script_type_t type;
    /// @brief [optional]脚本代码
    const char *code;
} urq_script_t;

static inline void urq_script_init_inplace(urq_script_t *script)
    __attribute__((__nonnull__(1)));

static inline void urq_script_free_inplace(urq_script_t *script)
    __attribute__((__nonnull__(1)));

// impl

static inline void urq_script_init_inplace(urq_script_t *script)
{
    script->conditions = NULL;
    script->type = SCRIPT_TYPE_UNSPECIFIED;
    script->code = NULL;
}

static inline void urq_script_free_inplace(urq_script_t *script)
{
    if (script->conditions != NULL) {
        urq_data_condition_list_free_inplace(script->conditions);
        urq_free(script->conditions);
    }

    if (script->code != NULL) {
        urq_free((void *)script->code);
    }
}

#ifdef __cplusplus
}
#endif

#endif // URQ__SCRIPT_H
