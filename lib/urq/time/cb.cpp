#include "urq/time/cb.h"
#include "urq/frame/frame.h"
#include "urq/log/debug.h"
#include "urq/preload.h"
#include "urq/time/time.h"
#include <cstdint>
#include <cstdlib>
#include <stdint.h>
#include <unordered_map>
#include <unordered_set>

/// @brief 定时回调数据
typedef struct {
    uint8_t argc;        // 参数个数
    uint32_t poll_count; // 在哪次 poll 执行
    urq_cb_t cb;         // 回调函数
    urq_cb_t free;       // 回调参数的释放函数，取消时调用
} urq_time_data_t;

typedef uint64_t urq_poll_t;

#define POLL_INTERVAL (URQ_FRAME_TIME / 2)

/// @brief 执行 poll 的次数
static urq_poll_t _poll_count = 0;

/// @brief 定时回调数据表，key 为 poll_count, value 为回调 set
static std::unordered_map<urq_poll_t, std::unordered_set<urq_time_data_t *>>
    urq_time_data_map;

static inline urq_time_data_t *malloc_data(int argc)
{
    return (urq_time_data_t *)urq_malloc(
        sizeof(urq_time_data_t) + (size_t)argc * sizeof(void *));
}

static inline void **get_argv(urq_time_data_t *data)
{
    return (void **)((uint8_t *)data + sizeof(urq_time_data_t));
}

static inline urq_poll_t to_poll_count(urq_time_t ms)
{
    urq_poll_t t = (urq_poll_t)(ms / POLL_INTERVAL);
    if (t == 0) {
        return _poll_count + 1;
    } else {
        return _poll_count + t;
    }
}

void *urq_time_cb_add0(urq_time_t ms, urq_cb0_t cb)
{
    urq_poll_t pc = to_poll_count(ms);
    auto *data = malloc_data(0);

    data->argc = 0;
    data->poll_count = (uint32_t)pc;
    data->cb.cb0 = cb;
    data->free.cb0 = urq_noop0;

    if (urq_time_data_map.find(pc) == urq_time_data_map.end()) {
        log_d("create new set, poll count: %lu\n", (unsigned long)pc);
        urq_time_data_map[pc] = std::unordered_set<urq_time_data_t *>();
    }

    urq_time_data_map[pc].insert(data);
    log_d(
        "add cb0, poll count: %lu, cb size: %lu\n", (unsigned long)pc,
        (unsigned long)urq_time_data_map[pc].size());

    return data;
}

void *urq_time_cb_add1(urq_time_t ms, urq_cb1_t cb, urq_cb1_t free, void *arg1)
{
    urq_poll_t pc = to_poll_count(ms);
    auto *data = malloc_data(1);

    data->argc = 1;
    data->poll_count = (uint32_t)pc;
    data->cb.cb1 = cb;
    data->free.cb1 = free;

    void **argv = get_argv(data);
    argv[0] = arg1;

    if (urq_time_data_map.find(pc) == urq_time_data_map.end()) {
        urq_time_data_map[pc] = std::unordered_set<urq_time_data_t *>();
        log_d("create new set, poll count: %lu\n", (unsigned long)pc);
    }

    urq_time_data_map[pc].insert(data);
    log_d(
        "add cb1, poll count: %lu, cb size: %lu\n", (unsigned long)pc,
        (unsigned long)urq_time_data_map[pc].size());

    return data;
}

void *urq_time_cb_add2(
    urq_time_t ms, urq_cb2_t cb, urq_cb2_t free, void *arg1, void *arg2)
{
    urq_poll_t pc = to_poll_count(ms);
    auto *data = malloc_data(2);

    data->argc = 2;
    data->poll_count = (uint32_t)pc;
    data->cb.cb2 = cb;
    data->free.cb2 = free;

    void **argv = get_argv(data);
    argv[0] = arg1;
    argv[1] = arg2;

    if (urq_time_data_map.find(pc) == urq_time_data_map.end()) {
        urq_time_data_map[pc] = std::unordered_set<urq_time_data_t *>();
        log_d("create new set, poll count: %lu\n", (unsigned long)pc);
    }

    urq_time_data_map[pc].insert(data);
    log_d(
        "add cb2, poll count: %lu, cb size: %lu\n", (unsigned long)pc,
        (unsigned long)urq_time_data_map[pc].size());

    return data;
}

void urq_time_cb_exec(urq_time_data_t *data)
{
    void **argv = get_argv(data);
    switch (data->argc) {
    case 0:
        data->cb.cb0();
        break;
    case 1:
        data->cb.cb1(argv[0]);
        break;
    case 2:
        data->cb.cb2(argv[0], argv[1]);
        break;
    case 3:
        data->cb.cb3(argv[0], argv[1], argv[2]);
        break;
    case 4:
        data->cb.cb4(argv[0], argv[1], argv[2], argv[3]);
        break;
    default:
        log_e("invalid argc: %d\n", data->argc);
        break;
    }
}

void urq_time_cb_poll(void)
{
    _poll_count += 1;

    auto set = urq_time_data_map.find(_poll_count);
    if (set == urq_time_data_map.end()) {
        log_v("no callback, pc: %lu\n", _poll_count);
        return;
    }

    log_i(
        "poll_count: %lu, cbs: %lu\n", (unsigned long)_poll_count,
        (unsigned long)set->second.size());
    for (auto it = set->second.begin(); it != set->second.end(); it++) {
        urq_time_cb_exec(*it);
    }
    urq_time_data_map.erase(_poll_count);
}