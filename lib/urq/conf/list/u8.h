
#pragma once

#include "urq/preload.h"
#include <stdint.h>

#ifndef URQ__CONF__LIST__U8_H
#define URQ__CONF__LIST__U8_H

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    urq_arr_t(uint8_t) data;
    size_t size;
} urq_u8_list_t;

static inline void urq_u8_list_init_inplace(urq_u8_list_t *list)
    __attribute__((__nonnull__(1)));

static inline void urq_u8_list_free_inplace(urq_u8_list_t *list)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_u8_list_init_inplace(urq_u8_list_t *list)
{
    list->data = NULL;
    list->size = 0;
}

void urq_u8_list_free_inplace(urq_u8_list_t *list)
{
    urq_free(list->data);
    list->data = NULL;
    list->size = 0;
}

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__UTIL__LIST__U8_H
