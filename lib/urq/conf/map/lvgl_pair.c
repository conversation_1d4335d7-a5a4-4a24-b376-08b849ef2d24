
#include "urq/conf/map/lvgl_pair.h"
#include "urq/errno.h"
#include <stdio.h>

urq_lvgl_pair_map_t *urq_lvgl_pair_map_new(void)
{
    return kh_init_urq_lvgl_pair_map();
}

void urq_lvgl_pair_map_free(urq_lvgl_pair_map_t *self)
{
    // for (uint32_t i = 0; i < kh_size(self); i++) {
    //     urq_lvgl_map_t *map = kh_val(self, i);
    //     if (map != NULL) {
    //         urq_lvgl_map_free(map);
    //         kh_val(self, i) = NULL;
    //     }
    // }
    kh_destroy_urq_lvgl_pair_map(self);
}

void urq_lvgl_pair_map_resize(urq_lvgl_pair_map_t *self, uint32_t size)
{
    kh_resize_urq_lvgl_pair_map(self, size);
}

int urq_lvgl_pair_map_add(
    urq_lvgl_pair_map_t *const self, uint32_t p_id, uint32_t id,
    lv_obj_t *widget)
{
    int ret = 0;
    bool noneed_create = urq_lvgl_pair_map_has_map(self, p_id);
    khiter_t k = kh_put_urq_lvgl_pair_map(self, (khint32_t)p_id, &ret);
    printf("urq_lvgl_pair_map_add: %d\n", ret);
    if (ret == 1) {
        // 初始化数据
        if (!noneed_create) {
            kh_val(self, k) = urq_lvgl_map_new();
        }
        urq_lvgl_map_t *map = kh_val(self, k);
        urq_lvgl_map_add(map, id, widget);
        return 0;
    } else {
        switch (ret) {
        case -1:
            errno = URQ_ENOMEM;
            return -1;
        case 0:
            errno = URQ_EEXISTS;
            return -1;
        default:
            errno = EINVAL;
            return -1;
        }
    }
}

void urq_lvgl_pair_map_del(
    urq_lvgl_pair_map_t *const self, uint32_t p_id, uint32_t id)
{
    khiter_t k = kh_get_urq_lvgl_pair_map(self, (khint32_t)p_id);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
        return;
    }
    urq_lvgl_map_t *map = kh_val(self, k);
    urq_lvgl_map_del(map, id);
}

lv_obj_t *urq_lvgl_pair_map_get_lv_obj(
    const urq_lvgl_pair_map_t *self, uint32_t p_id, uint32_t id)
{
    khiter_t k = kh_get_urq_lvgl_pair_map(self, (khint32_t)p_id);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
        return NULL;
    }
    urq_lvgl_map_t *map = kh_val(self, k);

    return urq_lvgl_map_get(map, id);
}

urq_lvgl_map_t *urq_lvgl_pair_map_get_map(
    const urq_lvgl_pair_map_t *self, uint32_t p_id)
{
    if (urq_lvgl_pair_map_has_map(self, p_id)) {
        return kh_val(self, kh_get_urq_lvgl_pair_map(self, (khint32_t)p_id));
    }
    return NULL;
}

bool urq_lvgl_pair_map_has_lv_obj(
    const urq_lvgl_pair_map_t *self, uint32_t p_id, uint32_t id)
{
    khiter_t k = kh_get_urq_lvgl_pair_map(self, (khint32_t)p_id);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
        return false;
    }
    urq_lvgl_map_t *map = kh_val(self, k);
    return urq_lvgl_map_has(map, id);
}

bool urq_lvgl_pair_map_has_map(const urq_lvgl_pair_map_t *self, uint32_t p_id)
{
    khiter_t k = kh_get_urq_lvgl_pair_map(self, (khint32_t)p_id);
    return k != kh_end(self);
}
