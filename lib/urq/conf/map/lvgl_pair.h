#pragma once

#include "klib/khash.h"
#include "lvgl.h"
#include "urq/conf/map/lvgl.h"
#include <stdint.h>

#ifndef URQ__CONF__MAP__LVGL_PAIR_H
#define URQ__CONF__MAP__LVGL_PAIR_H

#ifdef __cplusplus
extern "C" {
#endif

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告

/// @brief lvgl 表
KHASH_MAP_INIT_INT(urq_lvgl_pair_map, urq_lvgl_map_t *)
#pragma GCC diagnostic pop // 恢复之前的警告状态

/// @brief lvgl 表
typedef khash_t(urq_lvgl_pair_map) urq_lvgl_pair_map_t;

/// @brief 创建新的组件表
///
/// @return 新的组件表
urq_lvgl_pair_map_t *urq_lvgl_pair_map_new(void)
    __attribute__((__warn_unused_result__()));

/// @brief 释放组件表
///
/// @param self 组件表
/// @return void
void urq_lvgl_pair_map_free(urq_lvgl_pair_map_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 调整大小
///
/// @param self 组件表
/// @param size 大小
/// @return void
void urq_lvgl_pair_map_resize(urq_lvgl_pair_map_t *self, uint32_t size)
    __attribute__((__nonnull__(1)));

/// @brief 添加一个 lvgl 组件
int urq_lvgl_pair_map_add(
    urq_lvgl_pair_map_t *const self, uint32_t p_id, uint32_t id,
    lv_obj_t *widget) __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 删除一个 lvgl 组件
void urq_lvgl_pair_map_del(
    urq_lvgl_pair_map_t *const self, uint32_t p_id, uint32_t id)
    __attribute__((__nonnull__(1)));

/// @brief 获取 lvgl 组件
///
/// @param self      多语言表
/// @param id        组件ID
/// @param out_value 组件
/// @return 是否获取成功
lv_obj_t *urq_lvgl_pair_map_get_lv_obj(
    const urq_lvgl_pair_map_t *self, uint32_t p_id, uint32_t id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 获取 lvgl 组件表
///
/// @param self 多语言表
/// @param p_id 父组件ID
/// @return 组件表
urq_lvgl_map_t *urq_lvgl_pair_map_get_map(
    const urq_lvgl_pair_map_t *self, uint32_t p_id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 判断 lvgl 组件是否存在
///
/// @param self 多语言表
/// @param id   组件ID
/// @return 是否存在
bool urq_lvgl_pair_map_has_lv_obj(
    const urq_lvgl_pair_map_t *self, uint32_t p_id, uint32_t id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 判断 lvgl 组件表是否存在
///
/// @param self 多语言表
/// @param p_id 父组件ID
/// @return 是否存在
bool urq_lvgl_pair_map_has_map(const urq_lvgl_pair_map_t *self, uint32_t p_id)
    __attribute__((__nonnull__(1)));

#ifdef __cplusplus
}
#endif

#endif // #ifndef URQ__UTIL__MAP__LVGL_PAIR_H
