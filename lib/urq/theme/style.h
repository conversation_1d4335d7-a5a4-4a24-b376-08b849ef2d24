#pragma once

#include "klib/khash.h"
#include "urq/style/style.h"
#include <stdint.h>

#ifndef URQ__THEME__STYLE_H
#define URQ__THEME__STYLE_H
#ifdef __cplusplus
extern "C" {
#endif

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告

/// @brief 主题样式表
KHASH_MAP_INIT_INT(urq_theme_style, urq_style_t *)
#pragma GCC diagnostic pop // 恢复之前的警告状态

/// @brief 主题样式表
typedef khash_t(urq_theme_style) urq_theme_style_t;

/// @brief 创建新的主题样式表
///
/// @return 新的主题样式表
urq_theme_style_t *urq_theme_style_new(void)
    __attribute__((__warn_unused_result__()));

/// @brief 释放主题样式表
///
/// @param self 主题样式表
/// @return void
void urq_theme_style_free(urq_theme_style_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 调整大小
///
/// @param self 主题样式表
/// @param size 大小
/// @return void
void urq_theme_style_resize(urq_theme_style_t *self, uint32_t size)
    __attribute__((__nonnull__(1)));

/// @brief 添加一个主题样式
///
/// @param self     主题样式表
/// @return 是否添加成功
int urq_theme_style_add(
    urq_theme_style_t *const self, int id, const urq_style_t *const style)
    __attribute__((__nonnull__(1, 3), __warn_unused_result__()));

/// @brief 获取主题样式
///
/// @param self      主题样式表
/// @param id        主题样式ID
/// @param out_value 主题样式值
/// @return 是否获取成功
urq_style_t *urq_theme_style_get(const urq_theme_style_t *self, uint32_t id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

#ifdef __cplusplus
}
#endif

#endif // URQ__THEME__STYLE_H
