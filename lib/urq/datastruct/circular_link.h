#pragma once

#include <stddef.h>

#ifndef _DATASTRUCT_LINK_H_
#define _DATASTRUCT_LINK_H_

#ifdef __cplusplus
extern "C" {
#endif

#define CLINK_MAX_SIZE 6

typedef struct Node {
    int value;
    struct Node *next;
    struct Node *prev;
} Node;

typedef struct {
    Node *head;
    Node *tail;
    size_t size;
} urq_clink_t;

urq_clink_t *urq_clink_create(void);

void urq_clink_append(urq_clink_t *link, int v);

void urq_clink_clean(urq_clink_t *link);

int urq_clink_back_value(urq_clink_t *link);

int urq_clink_front_value(urq_clink_t *link);

int urq_clink_head_value(urq_clink_t *link);

void urq_clink_pop_back(urq_clink_t *link);

void urq_clink_pop_front(urq_clink_t *link);

void urq_clink_to_string(urq_clink_t *link);

int urq_clink_contains(urq_clink_t *link, int id);

#ifdef __cplusplus
}
#endif
#endif
