#include "lv_circular.h"
#include "urq/preload.h"
#include <string.h>

urq_circular_t *urq_circular_create(void)
{
    urq_circular_t *link = urq_malloc(sizeof(urq_circular_t));
    link->head = link->tail = link->count = 0;
    for (size_t idx = 0; idx < CIRCULAR_LIMIT; ++idx) {
        link->data[idx] = NULL;
    }
    link->full = false;

    return link;
}

void urq_circular_add(urq_circular_t *link, lv_obj_t *v)
{
    if (link == NULL || v == NULL)
        return;
    if (link->full) {
        // 队列已满，覆盖头部元素
        link->head = (link->head + 1) % CIRCULAR_LIMIT;
        link->count--;
    }

    link->data[link->tail] = v;
    link->tail = (link->tail + 1) % CIRCULAR_LIMIT;

    if (!link->full && link->tail == link->head) {
        link->full = true;
    }
    link->count =
        link->count + 1 > CIRCULAR_LIMIT ? CIRCULAR_LIMIT : link->count + 1;
}

bool urq_circular_contian(urq_circular_t *link, lv_obj_t *v)
{
    if (link == NULL || v == NULL)
        return false;
    for (int idx = 0; idx < link->count; ++idx) {
        if (v == link->data[idx])
            return true;
    }
    return false;
}

void urq_circular_pop_back(urq_circular_t *link)
{
    if (link == NULL || link->count == 0)
        return;

    link->tail = (link->tail - 1 + CIRCULAR_LIMIT) % CIRCULAR_LIMIT;
    link->data[link->tail] = NULL;

    link->count--;
    link->full = false;
}

lv_obj_t *urq_circular_back_value(urq_circular_t *link)
{
    if (link == NULL || link->count <= 0)
        return NULL;

    return link->data[link->tail];
}

lv_obj_t *urq_circular_front_value(urq_circular_t *link)
{
    if (link == NULL || link->count <= 1)
        return NULL;
    return link->data[link->tail - 1];
}

int urq_circular_size(urq_circular_t *link)
{
    if (link == NULL)
        return 0;
    return link->count;
}
