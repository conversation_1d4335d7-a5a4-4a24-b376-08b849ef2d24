
#include "lvgl.h"

#ifndef URQ__LV_CIRCULAR_H
#define URQ__LV_CIRCULAR_H

#ifdef __cplusplus
extern "C" {
#endif

#define CIRCULAR_LIMIT 5

typedef struct {
    lv_obj_t *data[CIRCULAR_LIMIT];
    int head;
    int tail;
    int count;
    bool full;
} urq_circular_t;

urq_circular_t *urq_circular_create(void);

void urq_circular_add(urq_circular_t *link, lv_obj_t *v);

void urq_circular_pop_back(urq_circular_t *link);

bool urq_circular_contian(urq_circular_t *link, lv_obj_t *v);

lv_obj_t *urq_circular_back_value(urq_circular_t *link);

lv_obj_t *urq_circular_front_value(urq_circular_t *link);

int urq_circular_size(urq_circular_t *link);

#ifdef __cplusplus
}
#endif
#endif
