#pragma once

#include "urq/preload.h"
#include "lvgl.h"
#include <stdbool.h>

#ifndef URQ__POINT_CIRCULAR_H
#define URQ__POINT_CIRCULAR_H

#ifdef __cplusplus
extern "C" {
#endif



typedef struct {
    int64_t x;
    lv_coord_t y;
} urq_point_t;

// 定义循环队列结构
typedef struct {
    urq_point_t *array;     // 存储元素的数组
    int capacity;   // 队列最大容量
    int size;       // 当前元素数量
    int front;      // 队头索引
    int rear;       // 队尾索引
} urq_point_cqueue_t;

/// @brief 初始化队列
/// @param capacity 队列最大容量
/// @return 队列
urq_point_cqueue_t* urq_point_cqueue_create(int capacity);

/// @brief 检查队列是否为空
/// @param queue 队列
/// @return 是否为空
bool urq_point_cqueue_is_empty(urq_point_cqueue_t *queue);

/// @brief 检查队列是否已满
/// @param queue 队列
/// @return 是否已满
bool urq_point_cqueue_is_full(urq_point_cqueue_t *queue);

/// @brief 入队操作：如果队列满，自动删除队头元素并添加新元素到队尾
/// @param queue 队列
/// @param value 值
/// @return 是否成功
bool urq_point_cqueue_enqueue(urq_point_cqueue_t *queue, urq_point_t value);

/// @brief 删除队头
/// @param queue 队列
/// @param value 值
/// @return 是否成功
bool urq_point_cqueue_pop_front(urq_point_cqueue_t *queue);

/// @brief 出队操作：从队头移除元素并返回
/// @param queue 队列
/// @param value 值
/// @return 是否成功
bool urq_point_cqueue_dequeue(urq_point_cqueue_t *queue, urq_point_t *value);

/// @brief 索引取值：根据索引获取元素（索引0对应队头）
/// @param queue 队列
/// @param index 索引
/// @param value 值
/// @return 是否成功
bool urq_point_cqueue_get_element_at(urq_point_cqueue_t *queue, int index, urq_point_t *value);

/// @brief 遍历队列：从队头到队尾打印所有元素
/// @param queue 队列
void urq_point_cqueue_traverse(urq_point_cqueue_t *queue);

/// @brief 销毁队列
/// @param queue 队列
void urq_point_cqueue_destroy(urq_point_cqueue_t *queue);

#ifdef __cplusplus
}
#endif
#endif
