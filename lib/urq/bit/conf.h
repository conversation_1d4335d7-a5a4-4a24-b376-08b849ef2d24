#pragma once

#include "urq/text/ptr_list.h"
#include <stdint.h>

#ifndef URQ__BIT__CONF_H
#define URQ__BIT__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 文本
    bool a;
} urq_bit_widget_conf_t;

static inline void urq_bit_widget_conf_init_inplace(urq_bit_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_bit_widget_conf_free_inplace(urq_bit_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_bit_widget_conf_init_inplace(urq_bit_widget_conf_t *self)
{
    urq_used(self);
}

void urq_bit_widget_conf_free_inplace(urq_bit_widget_conf_t *self)
{
    urq_used(self);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__BIT__CONF_H
