#pragma once

#include "urq/compile.h"
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include <sys/select.h>
#include <sys/un.h>
#include <unistd.h>

#ifndef URQ__SOCKET__SOCKET_H
#define URQ__SOCKET__SOCKET_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief socket 关闭时的回调
/// @param ref_ctx 回调的数据
/// @return void
typedef void(urq_socket_cb_close_t)(void *ref_ctx);

/// @brief socket 缓存大小
#define URQ_CONN_SOCKET_CACHE_SIZE 128

/// @brief socket 用于描述前后端的连接
/// 不包含数据处理
typedef struct {
    // 配置

    /// @brief 服务器地址
    uint32_t ip;
    /// @brief 服务器端口
    uint16_t port;

    // 缓存

    /// @brief 缓存
    uint8_t cache[URQ_CONN_SOCKET_CACHE_SIZE];
    /// @brief 可以读的缓存游标
    uint8_t *cache_cursor;
    /// @brief 缓存截止位置
    uint8_t *cache_end;

    // 回调

    /// @brief 回调数据
    void *ref_ctx;
    /// @brief 关闭时的回调
    urq_socket_cb_close_t *cb_close;

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
    /// @brief wasm 实例的 id
    int32_t iid;
    /// @brief 文件描述符
    int fd;
    /// @brief 用于回调的数据
    ///
    /// 所有的回调函数的第一个参数都是这个数据
    void *data;
#else
    /// @brief 文件描述符
    int fd;
#endif
} urq_socket_t;

/// @brief  初始化连接
static inline void urq_socket_init(urq_socket_t *socket)
    __attribute__((__nonnull__(1)));

/// @brief 反初始化连接
void urq_socket_deinit(urq_socket_t *socket) __attribute__((__nonnull__(1)));

/// @brief 连接到服务器
/// @param self 连接
/// @returns 是否连接成功
/// -  0: 连接成功
/// - -1: 连接失败
/// -  1: 连接中
int urq_socket_connect(urq_socket_t *self) __attribute__((__nonnull__(1)))
__attribute__((__warn_unused_result__()));

/// @brief 是否已经连接好了
/// @param self 连接
/// @return 是否已经连接好了
static inline bool urq_socket_connected(urq_socket_t *self)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 将数据接收到缓存中
/// @param self   连接
/// @param size   需要的长度
/// @param enough 是否足够
/// @returns 是否出错了
int urq_socket_cache_recv(urq_socket_t *self, uint32_t size, int *enough)
    __attribute__((__nonnull__(1, 3), __warn_unused_result__()));

/// @brief 从缓存中读取数据
///  在使用这个方法之前需要先调用 urq_socket_cache_recv
///  以确保有数据可读，否则会读到错误的数据
/// @param self 连接
/// @param size 读取的长度，长度无法超过缓存的长度
/// @param data 读取到的数据
/// @returns void
void urq_socket_cache_read(urq_socket_t *self, uint32_t size, void *data)
    __attribute__((__nonnull__(1, 3)));

/// @brief 从缓存中读取 u8
///  在使用这个方法之前需要先调用 urq_socket_cache_recv
///  以确保有数据可读，否则会读到错误的数据
/// @param self 连接
/// @returns 读取到的值
uint8_t urq_socket_cache_read_u8(urq_socket_t *self)
    __attribute__((__nonnull__(1))) __attribute__((__warn_unused_result__()));

/// @brief 从缓存中读取 u16
///  在使用这个方法之前需要先调用 urq_socket_cache_recv
///  以确保有数据可读，否则会读到错误的数据
/// @param self 连接
/// @returns 读取到的值
uint16_t urq_socket_cache_read_u16(urq_socket_t *self)
    __attribute__((__nonnull__(1))) __attribute__((__warn_unused_result__()));

/// @brief 从缓存中读取 u32
///  在使用这个方法之前需要先调用 urq_socket_cache_recv
///  以确保有数据可读，否则会读到错误的数据
/// @param self 连接
/// @returns 读取到的值
uint32_t urq_socket_cache_read_u32(urq_socket_t *self)
    __attribute__((__nonnull__(1))) __attribute__((__warn_unused_result__()));

/// @brief 读取数据
///
/// 读取之前不需要检查是否可读，如果不可读，读取到的长度会是 0
///
/// @param self 连接
/// @param size 读取的长度
/// @param out_data 读取到的数据
/// @param out_size 读取到的数据长度
/// @return 读取是否成功
int urq_socket_read(
    urq_socket_t *self, uint32_t size, void *out_data, uint32_t *out_size)
    __attribute__((__nonnull__(1, 3, 4)))
    __attribute__((__warn_unused_result__()));

/// @brief 设置连接
/// @param self 连接
/// @param iid    wasm id
/// @param ip     服务器地址
/// @param port   服务器端口
/// @return 设置是否成功
int urq_socket_set_host(
    urq_socket_t *self, int32_t iid, uint32_t ip, uint16_t port)
    __attribute__((__nonnull__(1))) __attribute__((__warn_unused_result__()));

/// @brief 设置回调数据
/// @param self 连接
/// @param ref_ctx 回调数据
static inline void urq_socket_set_cb_ctx(urq_socket_t *self, void *ref_ctx)
    __attribute__((__nonnull__(1)));

/// @brief 设置关闭时的回调
/// @param self 连接
/// @param cb_close 关闭时的回调
static inline void urq_socket_set_cb_close(
    urq_socket_t *self, urq_socket_cb_close_t *cb_close)
    __attribute__((__nonnull__(1)));

/// @brief 写入所有数据
/// @param self 连接
/// @param size 数据长度
/// @param data 数据
/// @return 写入是否成功
int urq_socket_write(urq_socket_t *self, uint32_t size, const void *data)
    __attribute__((__nonnull__(1, 3)))
    __attribute__((__warn_unused_result__()));

// -----------------------------------------------------------

static inline void urq_socket_init(urq_socket_t *self)
{
#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
    self->fd = -1;
#else
    self->fd = -1;
    self->cache_cursor = self->cache;
    self->cache_end = self->cache;
#endif
}

static inline bool urq_socket_connected(urq_socket_t *self)
{
    return self->fd != -1 && self->fd != -2;
}

static inline void urq_socket_set_cb_ctx(urq_socket_t *self, void *ref_ctx)
{
    self->ref_ctx = ref_ctx;
}

static inline void urq_socket_set_cb_close(
    urq_socket_t *self, urq_socket_cb_close_t *cb_close)
{
    self->cb_close = cb_close;
}

#ifdef __cplusplus
}
#endif
#endif // URQ__SOCKET__SOCKET_H
