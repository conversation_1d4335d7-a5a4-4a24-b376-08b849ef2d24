#include "urq/socket/socket.h"
#include "urq/errno.h"
#include "urq/log/verbose.h"
#include "urq/preload.h"
#include <errno.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <stdbool.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/select.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>

typedef struct {
    uint8_t major;
    uint8_t minor;
    uint8_t patch;
    uint8_t build;
} _handshake_data_t;

/// @brief 连接断开时执行清理方法
/// @param self 连接
/// @returns void
static inline void _on_disconnect(urq_socket_t *self)
{
    self->fd = -1;
    self->cache_cursor = self->cache;
    self->cache_end = self->cache;
    if (self->cb_close) {
        self->cb_close(self->ref_ctx);
    }
}

/// @brief 获取缓存大小
/// @param self 连接
/// @returns 缓存大小
static inline ssize_t _urq_socket_cache_size(urq_socket_t *self)
{
    return self->cache_end - self->cache_cursor;
}

/// @brief 执行握手
/// @param fd 文件描述符
/// @returns 是否握手成功
static int urq_socket_handshake(int fd)
{
    uint8_t data[4] = {0x00, 0x00, 0x00, 0x01};
    ssize_t len;
    len = send(fd, data, sizeof(data), 0);
    if (len != sizeof(data)) {
        log_i(
            "handshake send error, len: %ld, errno: %s\n", len,
            strerror(errno));
        return -1;
    }

    _handshake_data_t buf;
    len = recv(fd, (void *)&buf, sizeof(buf), 0);
    if (len != sizeof(buf)) {
        log_i(
            "handshake receive error, len: %ld, errno: %s\n", len,
            strerror(errno));
        return -1;
    }
    log_d(
        "handshake success, version: %d.%d.%d.%d\n", buf.major, buf.minor,
        buf.patch, buf.build);
    return 0;
}

// // 创建 tcp 连接
// static int _connect(uint32_t ip, uint16_t port)
// {
//     int fd = socket(AF_INET, SOCK_STREAM, 0);
//     if (fd == -1) {
//         return -1;
//     }

//     struct sockaddr_in addr;
//     addr.sin_family = AF_INET;
//     addr.sin_port = htons(port);
//     addr.sin_addr.s_addr = htonl(ip);

//     if (connect(fd, (struct sockaddr *)&addr, sizeof(addr)) == -1) {
//         close(fd);
//         return -1;
//     }
//     if (urq_socket_handshake(fd) == -1) {
//         close(fd);
//         return -1;
//     }
//     return fd;
// }

void urq_socket_deinit(urq_socket_t *self)
{
    if (self->fd != -1) {
        close(self->fd);
        self->fd = -1;
    }
}

int urq_socket_cache_recv(urq_socket_t *self, uint32_t size, int *enough)
{
    if (!urq_socket_connected(self)) {
        return -1;
    }

    if (_urq_socket_cache_size(self) >= size) {
        // 如果数据够长
        *enough = 1;
        return 0;
    }

    if (self->cache_cursor != self->cache) {
        if (self->cache_cursor == self->cache_end) {
            // 如果缓存中没有数据
            self->cache_cursor = self->cache;
            self->cache_end = self->cache;
        } else {
            memmove(
                self->cache, self->cache_cursor,
                (size_t)(self->cache_end - self->cache_cursor));

            self->cache_end -= self->cache_cursor - self->cache;
            self->cache_cursor = self->cache;
        }
    }

    ssize_t len = recv(
        self->fd, self->cache_end,
        (uint32_t)(URQ_CONN_SOCKET_CACHE_SIZE + self->cache - self->cache_end),
        0);
    if (len == -1) {
        if (errno == EAGAIN || errno == EWOULDBLOCK) {
            *enough = 0;
            return 0;
        }
        log_e("read error, errno: %d(%s)\n", errno, strerror(errno));
        _on_disconnect(self);
        return -1;
    }

    if (len == 0) {
        log_e("connection closed, errno: %d(%s)\n", errno, strerror(errno));
        _on_disconnect(self);
        return -1;
    }
    self->cache_end += len;

    if (_urq_socket_cache_size(self) >= size) {
        *enough = 1;
        return 0;
    }
    *enough = 0;
    return -1;
}

int urq_socket_connect(urq_socket_t *self)
{
    int fd;
    if (self->fd != -1) {
        return 0;
    }

    fd = socket(AF_INET, SOCK_STREAM, 0);
    if (fd == -1) {
        return -1;
    }
    struct sockaddr_in addr;
    addr.sin_family = AF_INET;
    addr.sin_port = htons(self->port);
    addr.sin_addr.s_addr = htonl(self->ip);

    if (connect(fd, (struct sockaddr *)&addr, sizeof(addr)) == -1) {
        close(fd);
        return -1;
    }

    if (urq_socket_handshake(fd) == -1) {
        close(fd);
        return -1;
    }

    // 设置非阻塞
    int flags = fcntl(fd, F_GETFL, 0);
    fcntl(fd, F_SETFL, flags | O_NONBLOCK);

    self->fd = fd;
    return 0;
}

int urq_socket_read(
    urq_socket_t *self, uint32_t size, void *out_data, uint32_t *out_size)
{
    ssize_t len;

    if (self->fd == -1) {
        return -1;
    }
    if (self->cache_cursor != self->cache_end) {
        len = self->cache_end - self->cache_cursor;
        len = len < size ? len : size;
        memcpy(out_data, self->cache_cursor, (size_t)len);
        self->cache_cursor += len;
        *out_size = (uint32_t)len;
        return 0;
    }
    len = recv(self->fd, out_data, size, 0);
    if (len == -1) {
        log_e("read error, errno: %d(%s)\n", errno, strerror(errno));
        return -1;
    }
    if (len == 0) {
        log_e("read error, errno: %d(%s)\n", errno, strerror(errno));
        return -1;
    }
    *out_size = (uint32_t)len;
    return 0;
}

int urq_socket_set_host(
    urq_socket_t *self, int32_t iid, uint32_t ip, uint16_t port)
{
#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
    self->iid = iid;
#else
    urq_used(iid);
#endif
    self->ip = ip;
    self->port = port;
    return 0;
}

int urq_socket_write(urq_socket_t *self, uint32_t size, const void *data)
{
    if (self->fd == -1) {
        errno = URQ_ECLOSED;
        return -1;
    }
    log_v("write, len: %u\n", size);
    log_mem(size, data);

    size_t total = 0;
    ssize_t sended = 0;
    while (total < size) {
        sended = send(self->fd, data, size, 0);
        if (sended == -1) {
            log_d("send error, errno: %d(%s)\n", errno, strerror(errno));
            return -1;
        }
        if (sended == 0) {
            log_d("send error, errno: %d(%s)\n", errno, strerror(errno));
            return -1;
        }
        total += (uint32_t)sended;
    }
    return 0;
}
