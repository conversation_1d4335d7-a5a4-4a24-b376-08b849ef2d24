#include "urq/socket/socket.h"
#include "urq/log/verbose.h"
#include "urq/web.h"
#include <errno.h>
#include <stdint.h>
#include <string.h>

/// @brief 获取缓存大小
/// @param self 连接
/// @returns 缓存大小
static inline uint32_t _urq_socket_cache_size(urq_socket_t *self)
{
    return self->cache_end - self->cache_cursor;
}

void urq_socket_deinit(urq_socket_t *socket)
{
    if (socket->fd != -1) {
        urq_web_socket_delete(socket->fd);
        socket->fd = -1;
    }
}

int urq_socket_set_host(
    urq_socket_t *socket, int32_t iid, uint32_t ip, uint16_t port)
{
    socket->iid = iid;
    int32_t fd = urq_web_socket_create(socket->iid);
    if (fd == -1) {
        return -1;
    }
    socket->fd = fd;
    urq_web_socket_set_host(socket->fd, ip, port);
    return 0;
}

int urq_socket_connect(urq_socket_t *self)
{
    return urq_web_socket_connect(self->fd);
}

int urq_socket_cache_recv(urq_socket_t *self, uint32_t size, int *enough)
{
    if (self->fd == -1) {
        return -1;
    }

    if (_urq_socket_cache_size(self) >= size) {
        // 如果数据够长
        *enough = 1;
        return 0;
    }

    if (self->cache_cursor != self->cache) {
        if (self->cache_cursor == self->cache_end) {
            // 如果缓存中没有数据
            self->cache_cursor = self->cache;
            self->cache_end = self->cache;
        } else {
            memmove(
                self->cache, self->cache_cursor,
                self->cache_end - self->cache_cursor);

            self->cache_end -= self->cache_cursor - self->cache;
            self->cache_cursor = self->cache;
        }
    }

    uint32_t len;
    if (urq_web_socket_read(
            self->fd,
            URQ_CONN_SOCKET_CACHE_SIZE + self->cache - self->cache_end,
            self->cache_end, &len)) {
        log_e("read error, errno: %d(%s)\n", errno, strerror(errno));
        return -1;
    }
    if (len == 0) {
        *enough = 0;
        return 0;
    }

    log_v("read len: %u\n", len);
    log_mem(len, self->cache_end);

    self->cache_end += len;
    if (_urq_socket_cache_size(self) >= size) {
        // 如果数据够长
        *enough = 1;
        return 0;
    }
    *enough = 0;
    return 0;
}

int urq_socket_read(
    urq_socket_t *self, uint32_t size, void *out_data, uint32_t *out_size)
{
    uint32_t len;
    if (self->cache_cursor != self->cache_end) {
        len = self->cache_end - self->cache_cursor;
        len = len < size ? len : size;
        memcpy(out_data, self->cache_cursor, len);
        self->cache_cursor += len;
        *out_size = len;
        return 0;
    }
    return urq_web_socket_read(self->fd, size, out_data, out_size);
}

int urq_socket_write(urq_socket_t *socket, uint32_t len, const void *data)
{
    return urq_web_socket_write(socket->fd, data, len);
}