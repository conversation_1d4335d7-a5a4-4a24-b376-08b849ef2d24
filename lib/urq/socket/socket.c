#include "urq/compile.h"
#include <stdint.h>

#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
#include "urq/socket/socket.wasm.c"
#else
#include "urq/socket/socket.linux.c"
#endif

void urq_socket_cache_read(urq_socket_t *self, uint32_t size, void *data)
{
    log_v(
        "socket cache read, len: %u, read len: %d\n",
        (int)(self->cache_end - self->cache_cursor), size);

    memcpy(data, self->cache_cursor, size);
    self->cache_cursor += size;
}

uint8_t urq_socket_cache_read_u8(urq_socket_t *self)
{
    uint8_t value = self->cache_cursor[0];
    self->cache_cursor += 1;
    return value;
}

uint16_t urq_socket_cache_read_u16(urq_socket_t *self)
{
    uint16_t value = (uint16_t)self->cache_cursor[0] |
                     (uint16_t)(self->cache_cursor[1] << 8);
    self->cache_cursor += 2;
    return value;
}

uint32_t urq_socket_cache_read_u32(urq_socket_t *self)
{
    uint32_t value = (uint32_t)self->cache_cursor[0] |
                     (uint32_t)(self->cache_cursor[1] << 8) |
                     (uint32_t)(self->cache_cursor[2] << 16) |
                     (uint32_t)(self->cache_cursor[3] << 24);
    self->cache_cursor += 4;
    return value;
}
