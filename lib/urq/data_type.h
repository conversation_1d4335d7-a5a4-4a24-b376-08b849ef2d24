#pragma once

#ifndef URQ__DATA__TYPE_H
#define URQ__DATA__TYPE_H

#ifdef __cplusplus
extern "C" {
#endif

enum urq_data_format_type_t {
    URQ_DATA_FORMAT_TYPE_UNSPECIFIED = 0,
    URQ_DATA_FORMAT_TYPE_BOOL = 1,
    URQ_DATA_FORMAT_TYPE_8UINT = 2,
    URQ_DATA_FORMAT_TYPE_8INT = 3,
    URQ_DATA_FORMAT_TYPE_16UINT = 4,
    URQ_DATA_FORMAT_TYPE_16INT = 5,
    URQ_DATA_FORMAT_TYPE_32UINT = 6,
    URQ_DATA_FORMAT_TYPE_32INT = 7,
    URQ_DATA_FORMAT_TYPE_32FLOAT = 8,
    URQ_DATA_FORMAT_TYPE_16BCD = 9,
    URQ_DATA_FORMAT_TYPE_32BCD = 10,
    URQ_DATA_FORMAT_TYPE_16HEX = 11,
    URQ_DATA_FORMAT_TYPE_32HEX = 12,
    URQ_DATA_FORMAT_TYPE_16BINARY = 13,
    URQ_DATA_FORMAT_TYPE_32BINARY = 14,
    URQ_DATA_FORMAT_TYPE_64UINT = 15,
    URQ_DATA_FORMAT_TYPE_64INT = 16,
    URQ_DATA_FORMAT_TYPE_64DOUBLE = 17,
    URQ_DATA_FORMAT_TYPE_64DATETIME = 18,
    URQ_DATA_FORMAT_TYPE_32DATETIME = 19,
    URQ_DATA_FORMAT_TYPE_DATE = 20,
    URQ_DATA_FORMAT_TYPE_TIME = 21,
    URQ_DATA_FORMAT_TYPE_32TIMESTAMP = 22,
    URQ_DATA_FORMAT_TYPE_64TIMESTAMP = 23,
    URQ_DATA_FORMAT_TYPE_STRING_UTF8 = 24,
    URQ_DATA_FORMAT_TYPE_STRING_UNICODE = 25,
    URQ_DATA_FORMAT_TYPE_STRING_GBK = 26,
    URQ_DATA_FORMAT_TYPE_STRING_UTF8_LH = 27,
    URQ_DATA_FORMAT_TYPE_STRING_UNICODE_LH = 28,
    URQ_DATA_FORMAT_TYPE_STRING_GBK_LH = 29,
    URQ_DATA_FORMAT_TYPE_STRUCT = 30,
    URQ_DATA_FORMAT_TYPE_ARRAY = 31,
};

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__DATA__TYPE_H
