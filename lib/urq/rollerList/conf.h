#pragma once

#include "urq/optionList/conf.h"
#include "urq/preload.h"

#ifndef URQ__ROLLER_LIST__CONF_H
#define URQ__ROLLER_LIST__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 列表
    urq_option_list_conf_t list;
    /// @brief 滚动模式
    lv_roller_mode_t mode;
    /// @brief 显示数量
    uint8_t view_count;
    /// @brief 默认索引
    uint8_t def_index;
} urq_rollerList_widget_conf_t;

static inline void urq_rollerList_widget_conf_init_inplace(
    urq_rollerList_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_rollerList_widget_conf_free_inplace(
    urq_rollerList_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_rollerList_widget_conf_init_inplace(urq_rollerList_widget_conf_t *self)
{
    self->mode = LV_ROLLER_MODE_INFINITE;
    self->view_count = 0;
    self->def_index = 0;
    urq_option_list_conf_init_inplace(&self->list);
}

void urq_rollerList_widget_conf_free_inplace(urq_rollerList_widget_conf_t *self)
{
    urq_option_list_conf_free_inplace(&self->list);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__ROLLER_LIST__CONF_H
