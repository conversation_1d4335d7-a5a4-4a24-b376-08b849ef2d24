#pragma once

#include "urq/preload.h"
#include "urq/widget/conf.h"

#ifndef URQ__WIDGET__LIST_PTR_H
#define URQ__WIDGET__LIST_PTR_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    uint16_t size;
    urq_arr_t(urq_widget_conf_t *) data;
} urq_widget_conf_ptr_list_t;

/// @brief 原地初始化样式列表
static inline void urq_widget_conf_ptr_list_init_inplace(urq_widget_conf_ptr_list_t *list)
    __attribute__((__nonnull__(1)));

/// @brief 原地释放样式列表
static inline void urq_widget_conf_ptr_list_free_inplace(urq_widget_conf_ptr_list_t *list)
    __attribute__((__nonnull__(1)));

/// @brief 移动样式列表
static inline void urq_widget_conf_ptr_list_move(
    urq_widget_conf_ptr_list_t *dst, urq_widget_conf_ptr_list_t *src)
    __attribute__((__nonnull__(1, 2)));

// impl

static inline void urq_widget_conf_ptr_list_init_inplace(urq_widget_conf_ptr_list_t *list)
{
    list->size = 0;
    list->data = NULL;
}

static inline void urq_widget_conf_ptr_list_free_inplace(urq_widget_conf_ptr_list_t *list)
{
    urq_used(list);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__widget_conf__LIST_H
