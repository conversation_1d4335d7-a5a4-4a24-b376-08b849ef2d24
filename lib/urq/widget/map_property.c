#include "urq/widget/map_property.h"
#include "urq/errno.h"

/// @brief 创建新的组件表
///
/// @return 新的组件表
urq_widget_property_map_t *urq_widget_property_map_new(void)
{
    return kh_init_urq_widget_property_map();
}

void urq_widget_property_map_free(urq_widget_property_map_t *self)
{
    for (khiter_t k = kh_begin(self); k != kh_end(self); ++k) {
        void *atag = kh_value(self, k);
        free(atag);
    }
    kh_destroy_urq_widget_property_map(self);
}

void urq_widget_property_map_resize(
    urq_widget_property_map_t *self, uint32_t size)
{
    kh_resize_urq_widget_property_map(self, size);
}

int urq_widget_property_map_add(
    urq_widget_property_map_t *const self, uint32_t id, void *atag)
{
    // 处理外层
    int ret = 0;
    khiter_t k = kh_put_urq_widget_property_map(self, (khint32_t)id, &ret);
    if (ret) {
        kh_value(self, k) = atag;
    } else {
        switch (ret) {
        case -1:
            errno = URQ_ENOMEM;
            return -1;
        default:
            errno = EINVAL;
            return -1;
        }
    }

    return 0;
}

void *urq_widget_property_map_get(
    const urq_widget_property_map_t *self, uint32_t id)
{
    khiter_t k = kh_get_urq_widget_property_map(self, (khint32_t)id);
    if (k != kh_end(self)) {
        return kh_value(self, k);
    }
    return NULL;
}
