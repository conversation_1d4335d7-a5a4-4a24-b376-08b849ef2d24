#pragma once

#include <stddef.h>

#ifndef URQ__WIDGET__EVENT_H
#define URQ__WIDGET__EVENT_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 事件代码
typedef enum {
    /// @brief 无事件
    URQ_EVENT_CODE_UNSPECIFIED = 0,
    /// @brief 元件按下
    URQ_EVENT_CODE_WIDGET_PRESS = 1,
    /// @brief 元件松开
    URQ_EVENT_CODE_WIDGET_RELEASE = 2,
    /// @brief
    /// 元件点击（点击事件表示同时触发按下和松开，实际动作列表没有对应的执行时机）
    URQ_EVENT_CODE_WIDGET_CLICK = 3,
    /// @brief 元件输入(需要带上值和类型)
    URQ_EVENT_CODE_WIDGET_INPUT = 4,
    /// @brief 页面打开
    URQ_EVENT_CODE_PAGE_OPEN = 5,
    /// @brief 页面关闭
    URQ_EVENT_CODE_PAGE_CLOSE = 6,
} urq_event_code_t;

#ifdef __cplusplus
}
#endif // #ifdef __cplusplus
#endif
