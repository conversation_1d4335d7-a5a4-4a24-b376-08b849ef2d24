#pragma once

#include "urq/action/ptr_list.h"

#ifndef URQ__WIDGET__ACTIONS_H
#define URQ__WIDGET__ACTIONS_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 执行前动作
    urq_action_ptr_list_t before_actions;
    /// @brief 按下执行动作
    urq_action_ptr_list_t press_actions;
    /// @brief 抬起执行动作
    urq_action_ptr_list_t release_actions;
    /// @brief 执行后动作
    urq_action_ptr_list_t after_success_actions;
    /// @brief 执行失败动作
    urq_action_ptr_list_t after_failure_actions;
} urq_widget_actions_t;

#ifdef __cplusplus
}
#endif
#endif
