#pragma once

#include "klib/khash.h"
#include "lvgl.h"

#ifndef URQ__WIDGET__MAP__PROPERTY_H
#define URQ__WIDGET__MAP__PROPERTY_H
#ifdef __cplusplus
extern "C" {
#endif

/// std::map<图元ID, lv_obj>
// std::pair<属性ID, urq_widget_data>
// std::map<图元ID, std::pair<属性ID, urq_widget_data>>

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告

/// @brief 组件表
// TODO value 原为atag
KHASH_MAP_INIT_INT(urq_widget_property_map, void *)

typedef khash_t(urq_widget_property_map) urq_widget_property_map_t;
#pragma GCC diagnostic pop // 恢复之前的警告状态

/// @brief 创建新的组件表
///
/// @return 新的组件表
urq_widget_property_map_t *urq_widget_property_map_new(void)
    __attribute__((__warn_unused_result__()));

/// @brief 释放组件表
///
/// @param self 组件表
/// @return void
void urq_widget_property_map_free(urq_widget_property_map_t *self)
    __attribute__((__nonnull__(1)));

/// @brief 调整大小
///
/// @param self 组件表
/// @param size 大小
/// @return void
void urq_widget_property_map_resize(
    urq_widget_property_map_t *self, uint32_t size)
    __attribute__((__nonnull__(1)));

/// @brief 添加一个组件
///
/// @param self     组件表
/// @param id       图元ID
/// @param inner_id 属性ID
/// @param widget   组件
/// @return 是否添加成功
int urq_widget_property_map_add(
    urq_widget_property_map_t *const self, uint32_t id, void *atag)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 获取组件
///
/// @param self      组件表
/// @param id        图元ID
/// @param inner_id  属性ID
/// @return 组件
void *urq_widget_property_map_get(
    const urq_widget_property_map_t *self, uint32_t id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ__WIDGET__MAP__PROPERTY_H
