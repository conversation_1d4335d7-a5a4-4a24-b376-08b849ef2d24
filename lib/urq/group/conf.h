#pragma once

#include "urq/widget/conf_list.h"
#include <stdint.h>

#ifndef URQ__GROUP__CONF_H
#define URQ__GROUP__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct urq_group_widget_conf_t{
    urq_widget_conf_list_t widgets;
} urq_group_widget_conf_t;

static inline void urq_group_widget_conf_init_inplace(urq_group_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_group_widget_conf_free_inplace(urq_group_widget_conf_t *self)
    __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_group_widget_conf_init_inplace(urq_group_widget_conf_t *self)
{
    urq_widget_conf_list_init_inplace(&self->widgets);
}

void urq_group_widget_conf_free_inplace(urq_group_widget_conf_t *self)
{
    urq_widget_conf_list_init_inplace(&self->widgets);
}

#ifdef __cplusplus
}
#endif
#endif // URQ__group__CONF_H
