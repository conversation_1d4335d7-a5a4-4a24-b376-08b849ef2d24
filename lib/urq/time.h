#pragma once

#ifndef URQ__TIME_H
#define URQ__TIME_H
#ifdef __cplusplus
extern "C" {
#endif

//static const char* s_time_format_str[] = {
//    "",
//    "HH:MM:SS",
//    "HH:MM",
//    "MM:SS",
//};
//
//static const char* s_date_format_str[] = {
//    "",
//    "MM/DD/YYYY",
//    "DD/MM/YYYY",
//    "YYYY/MM/DD",
//    "MM/DD",
//    "DD/MM",
//    "DD",
//    "MM.DD.YYYY",
//    "DD.MM.YYYY",
//    "YYYY.MM.DD",
//    "MM.DD",
//    "DD.MM",
//    "DD",
//    "MM-DD-YYYY",
//    "DD-MM-YYYY",
//    "YYYY-MM-DD",
//    "MM-DD",
//    "DD-MM",
//    "DD",
//};


typedef enum {
    URQ_TIME_FORMAT_UNSPECIFIED = 0,
    // 时间格式 HH:MM:SS
    URQ_TIME_FORMAT_HMS = 1,
    // 时间格式 HH:MM
    URQ_TIME_FORMAT_HM = 2,
    // 时间格式 MM:SS
    URQ_TIME_FORMAT_MS = 3,
} urq_time_format_t;

typedef enum {
    // 未设置 
    URQ_DATE_FORMAT_UNSPECIFIED = 0,
    // 分隔符 /
    // 日期格式 MM/DD/YYYY
    URQ_DATE_FORMAT_MDY = 1,
    // 日期格式 DD/MM/YYYY
    URQ_DATE_FORMAT_DMY= 2,
    // 日期格式 YYYY/MM/DD
    URQ_DATE_FORMAT_YMD = 3,
    // 日期格式 MM/DD
    URQ_DATE_FORMAT_MD = 4,
    // 日期格式 DD/MM
    URQ_DATE_FORMAT_DM = 5,
    // 日期格式 DD
    URQ_DATE_FORMAT_D = 6,

    /// 分隔符 .
    // 日期格式 MM.DD.YYYY
    URQ_DATE_FORMAT_MDY_POINT = 7,
    // 日期格式 DD.MM.YYYY
    URQ_DATE_FORMAT_DMY_POINT = 8,
    // 日期格式 YYYY.MM.DD
    URQ_DATE_FORMAT_YMD_POINT = 9,
    // 日期格式 MM.DD
    URQ_DATE_FORMAT_MD_POINT = 10,
    // 日期格式 DD.MM
    URQ_DATE_FORMAT_DM_POINT = 11,
    // 日期格式 DD
    URQ_DATE_FORMAT_D_POINT = 12,

    // 分隔符 -
    // 日期格式 MM-DD-YYYY
    URQ_DATE_FORMAT_MDY_DASH = 13,
    // 日期格式 DD-MM-YYYY
    URQ_DATE_FORMAT_DMY_DASH = 14,
    // 日期格式 YYYY-MM-DD
    URQ_DATE_FORMAT_YMD_DASH = 15,
    // 日期格式 MM-DD
    URQ_DATE_FORMAT_MD_DASH = 16,
    // 日期格式 DD-MM
    URQ_DATE_FORMAT_DM_DASH = 17,
    // 日期格式 DD
    URQ_DATE_FORMAT_D_DASH = 18,
} urq_date_format_t;

//static const char* urq_date_format_to_string(urq_date_format_t format)
//{
//    return s_date_format_str[format];
//}
//
//static const char* urq_time_format_to_string(urq_time_format_t format)
//{
//    return s_time_format_str[format];
//}





#ifdef __cplusplus
}
#endif
#endif // #ifndef 
