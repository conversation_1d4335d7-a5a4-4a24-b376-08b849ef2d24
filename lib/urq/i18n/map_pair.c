#include "urq/i18n/map_pair.h"
#include "urq/errno.h"
#include <stdbool.h>

urq_i18n_map_pair_t *urq_i18n_map_pair_new(void)
{
    return kh_init_urq_i18n_map_pair();
}

void urq_i18n_map_pair_free(urq_i18n_map_pair_t *self)
{
    kh_destroy_urq_i18n_map_pair(self);
}

void urq_i18n_map_pair_resize(urq_i18n_map_pair_t *self, uint32_t size)
{
    kh_resize_urq_i18n_map_pair(self, size);
}

int urq_i18n_map_pair_add(
    urq_i18n_map_pair_t *const self, uint32_t id, uint32_t lang_id,
    const char *text)
{
    int ret = 0;
    khiter_t k = kh_put_urq_i18n_map_pair(self, (khint32_t)id, &ret);
    if (ret == 1) {
        // 初始化数据
        urq_i18n_map_t *map = kh_val(self, k);
        if (map == NULL) {
            kh_val(self, k) = urq_i18n_map_new();
        }
        urq_i18n_map_add(map, lang_id, text);
        return 0;
    } else {
        switch (ret) {
        case -1:
            errno = URQ_ENOMEM;
            return -1;
        case 0:
            errno = URQ_EEXISTS;
            return -1;
        default:
            errno = EINVAL;
            return -1;
        }
    }
}

const char *urq_i18n_map_pair_get(
    const urq_i18n_map_pair_t *self, uint32_t id, uint32_t lang_id)
{
    khiter_t k = kh_get_urq_i18n_map_pair(self, (khint32_t)id);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
        return NULL;
    }
    urq_i18n_map_t *map = kh_val(self, k);
    return urq_i18n_map_get(map, lang_id);
}
