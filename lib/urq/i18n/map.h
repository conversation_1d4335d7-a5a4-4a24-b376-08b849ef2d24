#pragma once

#include "klib/khash.h"
#include <stdint.h>

#ifndef URQ__I18N__MAP_H
#define URQ__I18N__MAP_H
#ifdef __cplusplus
extern "C" {
#endif

#pragma GCC diagnostic push                   // 保存当前警告状态
#pragma GCC diagnostic ignored "-Wconversion" // 忽略 -Wconversion 相关警告

/// @brief 多语言表
KHASH_MAP_INIT_INT(urq_i18n_map, char *)
#pragma GCC diagnostic pop // 恢复之前的警告状态

/// @brief 多语言表
typedef khash_t(urq_i18n_map) urq_i18n_map_t;

/// @brief 创建新的多语言表
///
/// @return 新的多语言表
urq_i18n_map_t *urq_i18n_map_new(void)
    __attribute__((__warn_unused_result__()));

/// @brief 释放多语言表
///
/// @param self 多语言表
/// @return void
void urq_i18n_map_free(urq_i18n_map_t *self) __attribute__((__nonnull__(1)));

/// @brief 调整大小
///
/// @param self 多语言表
/// @param size 大小
/// @return void
void urq_i18n_map_resize(urq_i18n_map_t *self, uint32_t size)
    __attribute__((__nonnull__(1)));

/// @brief 添加一个多语言
///
/// @param self 多语言表
/// @param id   多语言ID
/// @param text 多语言文本
/// @return 是否添加成功
int urq_i18n_map_add(urq_i18n_map_t *const self, uint32_t id, const char *text)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

/// @brief 获取多语言
///
/// @param self      多语言表
/// @param id        多语言ID
/// @param out_value 多语言文本
/// @return 是否获取成功
const char *urq_i18n_map_get(const urq_i18n_map_t *self, uint32_t id)
    __attribute__((__nonnull__(1), __warn_unused_result__()));

#ifdef __cplusplus
}
#endif

#endif // URQ__I18N__MAP_H
