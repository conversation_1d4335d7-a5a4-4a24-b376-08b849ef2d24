#include "urq/i18n/map.h"
#include "urq/errno.h"
#include "urq/preload.h"
#include <stdio.h>
urq_i18n_map_t *urq_i18n_map_new(void) { return kh_init_urq_i18n_map(); }

void urq_i18n_map_free(urq_i18n_map_t *self)
{
    khiter_t k = kh_begin(self);
    khiter_t end = kh_end(self);
    for (; k != end; ++k) {
        if (kh_exist(self, k)) {
            char *val = kh_val(self, k);
            if (val != NULL) {
                urq_free(val); 
            }
        }
    }
    kh_destroy_urq_i18n_map(self);
}

void urq_i18n_map_resize(urq_i18n_map_t *self, uint32_t size)
{
    kh_resize_urq_i18n_map(self, size);
}

int urq_i18n_map_add(urq_i18n_map_t *const self, uint32_t id, const char *text)
{
    int ret = 0;
    khiter_t k = kh_put_urq_i18n_map(self, (khint32_t)id, &ret);
    if (ret == 1) {
        // 初始化数据
        char *v = (char *)urq_malloc(strlen(text) + 1);
        if (v == NULL) {
            errno = URQ_ENOMEM;
            return -1;
        }
        strcpy(v, text);
        kh_val(self, k) = v;
        return 0;
    } else {
        switch (ret) {
        case -1:
            errno = URQ_ENOMEM;
            return -1;
        case 0:
            errno = URQ_EEXISTS;
            return -1;
        default:
            errno = EINVAL;
            return -1;
        }
    }
}

const char *urq_i18n_map_get(const urq_i18n_map_t *self, uint32_t id)
{

    khiter_t k = kh_get_urq_i18n_map(self, (khint32_t)id);
    if (k == kh_end(self)) {
        errno = URQ_ENOENT;
        return NULL;
    }
    return kh_val(self, k);
}
