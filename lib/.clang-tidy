# https://clang.llvm.org/extra/clang-tidy/

Checks: "-*,readability-identifier-naming"
WarningsAsErrors: ""
HeaderFilterRegex: "/urq/.+\\.h$"
FormatStyle: llvm
User: ""
CheckOptions:
    - key: readability-identifier-naming.ClassCase
      value: CamelCase
    - key: readability-identifier-naming.ClassIgnoredRegexp
      value: "^_.+"

    - key: readability-identifier-naming.MethodCase
      value: lower_case

    - key: readability-identifier-naming.MemberCase
      value: lower_case

    - key: readability-identifier-naming.ParameterCase
      value: lower_case
