cmake_minimum_required(VERSION 3.22)

# 如果 URQ_ROOT_PROJECT 未设置，则添加 project
if(NOT URQ_ROOT_PROJECT)
    message(STATUS "urq_cxx build as top level project")
    project("urq_cxx" VERSION 0.0.1)
else()
    message(STATUS "urq_cxx build as sub project")
endif()

include("${CMAKE_CURRENT_LIST_DIR}/../cmake/configure-file.cmake")

# 创建一个库
file(GLOB_RECURSE SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp"
)

set(URQ_CURRENT_BUILD urq_cxx)
add_library(${URQ_CURRENT_BUILD} STATIC ${SOURCES})

target_include_directories(${URQ_CURRENT_BUILD} PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})

configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/urq/compile.hpp.in" 
    "${CMAKE_CURRENT_SOURCE_DIR}/urq/compile.hpp" 
)
