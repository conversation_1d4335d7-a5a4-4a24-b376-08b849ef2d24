#pragma once

#include <algorithm>
#include <type_traits>

#ifndef URQ__RANGE_HPP
#define URQ__RANGE_HPP
namespace urq {

/// @brief 范围
template <typename T> class Range {
    static_assert(std::is_integral_v<T>, "T must be integral type");

    T m_start; // 起始地址，包含

    T m_end; // 结束地址，不包含
public:
    /// @brief 构造函数
    inline Range(T start, T end) noexcept : m_start(start), m_end(end) {}
    /// @brief 析构函数
    inline ~Range() noexcept = default;
    /// @brief 拷贝构造函数
    inline Range(const Range &other) noexcept
        : m_start(other.m_start), m_end(other.m_end)
    {
    }
    /// @brief 移动构造函数
    inline Range(Range &&other) noexcept
        : m_start(other.m_start), m_end(other.m_end)
    {
    }
    /// @brief 拷贝赋值函数
    inline Range &operator=(const Range &other) noexcept
    {
        m_start = other.m_start;
        m_end = other.m_end;
        return *this;
    }
    /// @brief 移动赋值函数
    inline Range &operator=(Range &&other) noexcept
    {
        m_start = other.m_start;
        m_end = other.m_end;
        return *this;
    }

    /// @brief 获取起始地址
    inline T start() const noexcept { return m_start; }

    /// @brief 获取结束地址
    inline T end() const noexcept { return m_end; }

    /// @brief 获取长度
    inline T length() const noexcept { return m_end - m_start; }

    /// @brief 是否有交集
    inline bool has_intersection(T start, T end) const noexcept
    {
        return m_start < end && start < m_end;
    }

    /// @brief 是否有交集
    inline bool has_intersection(const Range &other) const noexcept
    {
        return has_intersection(other.m_start, other.m_end);
    }

    /// @brief 合并范围，取并集
    inline Range merge(T start, T end) const noexcept
    {
        return Range(std::min(m_start, start), std::max(m_end, end));
    }

    /// @brief 合并范围，取并集
    inline Range merge(const Range &other) const noexcept
    {
        return merge(other.m_start, other.m_end);
    }
};

} // namespace urq
#endif // URQ__RANGE_HPP
