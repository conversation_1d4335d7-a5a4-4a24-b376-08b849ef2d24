#pragma once

#include "urq/debug.hpp"
#include <iostream>

namespace urq::log {

template <typename T1>
inline typename std::enable_if<urq::IsDebuggable<T1>::value, void>::type
debug_items(T1 &&i1)
{
    urq::debug(std::cout, std::forward<T1>(i1));
}

template <typename T1, typename T2>
inline typename std::enable_if<
    urq::IsDebuggable<T1>::value && urq::IsDebuggable<T2>::value, void>::type
debug_items(T1 &&i1, T2 &&i2)
{
    urq::debug(std::cout, std::forward<T1>(i1));
    urq::debug(std::cout, std::forward<T2>(i2));
}

template <typename T1, typename T2, typename T3>
inline typename std::enable_if<
    urq::IsDebuggable<T1>::value && urq::IsDebuggable<T2>::value &&
        urq::IsDebuggable<T3>::value,
    void>::type
debug_items(T1 &&i1, T2 &&i2, T3 &&i3)
{
    urq::debug(std::cout, std::forward<T1>(i1));
    urq::debug(std::cout, std::forward<T2>(i2));
    urq::debug(std::cout, std::forward<T3>(i3));
}

template <typename T1, typename T2, typename T3, typename T4>
inline typename std::enable_if<
    urq::IsDebuggable<T1>::value && urq::IsDebuggable<T2>::value &&
        urq::IsDebuggable<T3>::value && urq::IsDebuggable<T4>::value,
    void>::type
debug_items(T1 &&i1, T2 &&i2, T3 &&i3, T4 &&i4)
{
    urq::debug(std::cout, std::forward<T1>(i1));
    urq::debug(std::cout, std::forward<T2>(i2));
    urq::debug(std::cout, std::forward<T3>(i3));
    urq::debug(std::cout, std::forward<T4>(i4));
}

template <typename T1, typename T2, typename T3, typename T4, typename T5>
inline typename std::enable_if<
    urq::IsDebuggable<T1>::value && urq::IsDebuggable<T2>::value &&
        urq::IsDebuggable<T3>::value && urq::IsDebuggable<T4>::value &&
        urq::IsDebuggable<T5>::value,
    void>::type
debug_items(T1 &&i1, T2 &&i2, T3 &&i3, T4 &&i4, T5 &&i5)
{
    urq::debug(std::cout, std::forward<T1>(i1));
    urq::debug(std::cout, std::forward<T2>(i2));
    urq::debug(std::cout, std::forward<T3>(i3));
    urq::debug(std::cout, std::forward<T4>(i4));
    urq::debug(std::cout, std::forward<T5>(i5));
}

template <
    typename T1, typename T2, typename T3, typename T4, typename T5,
    typename T6>
inline typename std::enable_if<
    urq::IsDebuggable<T1>::value && urq::IsDebuggable<T2>::value &&
        urq::IsDebuggable<T3>::value && urq::IsDebuggable<T4>::value &&
        urq::IsDebuggable<T5>::value && urq::IsDebuggable<T6>::value,
    void>::type
debug_items(T1 &&i1, T2 &&i2, T3 &&i3, T4 &&i4, T5 &&i5, T6 &&i6)
{
    urq::debug(std::cout, std::forward<T1>(i1));
    urq::debug(std::cout, std::forward<T2>(i2));
    urq::debug(std::cout, std::forward<T3>(i3));
    urq::debug(std::cout, std::forward<T4>(i4));
    urq::debug(std::cout, std::forward<T5>(i5));
    urq::debug(std::cout, std::forward<T5>(i6));
}

template <
    typename T1, typename T2, typename T3, typename T4, typename T5,
    typename T6, typename T7>
inline typename std::enable_if<
    urq::IsDebuggable<T1>::value && urq::IsDebuggable<T2>::value &&
        urq::IsDebuggable<T3>::value && urq::IsDebuggable<T4>::value &&
        urq::IsDebuggable<T5>::value && urq::IsDebuggable<T6>::value &&
        urq::IsDebuggable<T7>::value,
    void>::type
debug_items(T1 &&i1, T2 &&i2, T3 &&i3, T4 &&i4, T5 &&i5, T6 &&i6, T7 &&i7)
{
    urq::debug(std::cout, std::forward<T1>(i1));
    urq::debug(std::cout, std::forward<T2>(i2));
    urq::debug(std::cout, std::forward<T3>(i3));
    urq::debug(std::cout, std::forward<T4>(i4));
    urq::debug(std::cout, std::forward<T5>(i5));
    urq::debug(std::cout, std::forward<T6>(i6));
    urq::debug(std::cout, std::forward<T7>(i7));
}

template <
    typename T1, typename T2, typename T3, typename T4, typename T5,
    typename T6, typename T7, typename T8>
inline typename std::enable_if<
    urq::IsDebuggable<T1>::value && urq::IsDebuggable<T2>::value &&
        urq::IsDebuggable<T3>::value && urq::IsDebuggable<T4>::value &&
        urq::IsDebuggable<T5>::value && urq::IsDebuggable<T6>::value &&
        urq::IsDebuggable<T7>::value && urq::IsDebuggable<T8>::value,
    void>::type
debug_items(
    T1 &&i1, T2 &&i2, T3 &&i3, T4 &&i4, T5 &&i5, T6 &&i6, T7 &&i7, T8 &&i8)
{
    urq::debug(std::cout, std::forward<T1>(i1));
    urq::debug(std::cout, std::forward<T2>(i2));
    urq::debug(std::cout, std::forward<T3>(i3));
    urq::debug(std::cout, std::forward<T4>(i4));
    urq::debug(std::cout, std::forward<T5>(i5));
    urq::debug(std::cout, std::forward<T6>(i6));
    urq::debug(std::cout, std::forward<T7>(i7));
    urq::debug(std::cout, std::forward<T8>(i8));
}

template <
    typename T1, typename T2, typename T3, typename T4, typename T5,
    typename T6, typename T7, typename T8, typename T9>
inline typename std::enable_if<
    urq::IsDebuggable<T1>::value && urq::IsDebuggable<T2>::value &&
        urq::IsDebuggable<T3>::value && urq::IsDebuggable<T4>::value &&
        urq::IsDebuggable<T5>::value && urq::IsDebuggable<T6>::value &&
        urq::IsDebuggable<T7>::value && urq::IsDebuggable<T8>::value &&
        urq::IsDebuggable<T9>::value,
    void>::type
debug_items(
    T1 &&i1, T2 &&i2, T3 &&i3, T4 &&i4, T5 &&i5, T6 &&i6, T7 &&i7, T8 &&i8,
    T9 &&i9)
{
    urq::debug(std::cout, std::forward<T1>(i1));
    urq::debug(std::cout, std::forward<T2>(i2));
    urq::debug(std::cout, std::forward<T3>(i3));
    urq::debug(std::cout, std::forward<T4>(i4));
    urq::debug(std::cout, std::forward<T5>(i5));
    urq::debug(std::cout, std::forward<T6>(i6));
    urq::debug(std::cout, std::forward<T7>(i7));
    urq::debug(std::cout, std::forward<T8>(i8));
    urq::debug(std::cout, std::forward<T9>(i9));
}

inline void output_header(
    const char *const level, const char *const color, const char *const file,
    const int line)
{
    std::cout << "\033[30m" << level << file << ":" << line << "\033[0m\n"
              << color;
}

#define log_endl "\033[0m\n"

} // namespace urq::log
