#pragma once

#include "urq/log/debug_items.hpp" // IWYU pragma: export

#ifdef __cplusplus
extern "C" {
#endif

#ifdef URQ_LOG_MACROS_H // 保证日志宏只被定义一次
#pragma message("could not include log macros twice")
#else
#define URQ_LOG_MACROS_H
#endif

#define log_v(...)

#define log_d(...)                                                             \
    urq::log::output_header("[DEBUG]", "\033[34m", __FILE__, __LINE__);        \
    urq::log::debug_items(__VA_ARGS__)

#define log_i(...)                                                             \
    urq::log::output_header("[INFO]", "\033[32m", __FILE__, __LINE__);         \
    urq::log::debug_items(__VA_ARGS__)

#define log_t(...)                                                             \
    urq::log::output_header("[TRACE]", "\033[36m", __FILE__, __LINE__);        \
    urq::log::debug_items(__VA_ARGS__)

#define log_w(...)                                                             \
    urq::log::output_header("[WARN]", "\033[33m", __FILE__, __LINE__);         \
    urq::log::debug_items(__VA_ARGS__)

#define log_e(...)                                                             \
    urq::log::output_header("[ERROR]", "\033[31m", __FILE__, __LINE__);        \
    urq::log::debug_items(__VA_ARGS__)

#define log_f(...)                                                             \
    urq::log::output_header("[FATAL]", "\033[91m", __FILE__, __LINE__);        \
    urq::log::debug_items(__VA_ARGS__)

#ifdef __cplusplus
}
#endif
