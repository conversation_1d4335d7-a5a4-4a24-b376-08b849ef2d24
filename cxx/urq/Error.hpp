#pragma once

#include "urq/debug.hpp" // IWYU pragma: keep
#include <cstdint>
#include <exception>
#include <iostream>
#include <memory>
#include <string>
#include <vector>

namespace urq {

class Error : std::exception {
private:
    /// 错误的唯一 id
    std::vector<std::int64_t> m_ids;
    /// 错误发生的时间
    std::string m_message;
    /// 错误的原因
    std::unique_ptr<Error> m_cause;

public:
    /// @brief 构造一个错误
    ///
    /// @param id 错误 id
    /// @param message 错误信息
    inline Error(const std::int64_t id, const char *const message) noexcept
        : m_ids(1), m_message(message), m_cause{nullptr}
    {
        m_ids[0] = id;
    }

    /// @brief 构造一个错误
    ///
    /// @param id 错误 id
    /// @param message 错误信息
    inline Error(const std::int64_t id, std::string message) noexcept
        : m_ids(1), m_message(std::move(message)), m_cause{nullptr}
    {
        m_ids[0] = id;
    }

    /// @brief 移动构造函数
    inline Error(Error &&other) noexcept
        : m_ids(std::move(other.m_ids))
        , m_message(std::move(other.m_message))
        , m_cause(std::move(other.m_cause))
    {
    }

    /// @brief 移动赋值运算符
    inline Error &operator=(Error &&other) noexcept
    {
        if (this != &other) {
            m_ids = std::move(other.m_ids);
            m_message = std::move(other.m_message);
            m_cause = std::move(other.m_cause);
        }
        return *this;
    }

    /// @brief 复制构造函数
    Error(const Error &other) = delete;
    /// @brief 复制赋值运算符
    Error &operator=(const Error &other) = delete;

    /// @brief 析构函数
    inline ~Error() noexcept override = default;

    /// @brief 错误的唯一编码，用于定位错误的来源
    inline std::vector<std::int64_t> &ids() noexcept { return m_ids; }

    /// @brief 错误信息
    inline const std::string &message() const noexcept { return m_message; }

    /// @brief 错误的原因
    inline Error *cause() const noexcept
    {
        if (m_cause) {
            return m_cause.get();
        }
        return nullptr;
    }

    /// @brief 设置错误的原因
    inline void cause(Error &&cause) noexcept
    {
        m_cause = std::make_unique<Error>(std::move(cause));
    }

    /// @brief 重写 what 方法
    inline const char *what() const noexcept override
    {
        return m_message.c_str();
    }

    /// @brief 实现错误信息的输出
    friend std::ostream &operator<<(std::ostream &os, const Error &error)
    {
        bool first = true;
        os << "Error(";
        for (auto id : error.m_ids) {
            if (first) {
                first = false;
            } else {
                os << ", ";
            }
            os << id;
        }
        os << "): " << error.m_message;
        if (error.m_cause != nullptr) {
            os << "\nCause: " << error.m_cause->message();
        }
        return os;
    }
};

template <> inline void debug(std::ostream &os, const Error &e) noexcept
{
    os << e;
}

} // namespace urq
