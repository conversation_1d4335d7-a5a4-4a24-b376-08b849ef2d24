#pragma once

#ifndef URQ__COMPILE_HPP
#define URQ__COMPILE_HPP
#ifdef __cplusplus
extern "C" {
#endif

#ifndef URQ_COMPILE_OPTIONS
#define URQ_COMPILE_OPTIONS

// clang-format off

// 编译模式
#define URQ_COMPILE_MODE_DEBUG   0                  // 调试模式
#define URQ_COMPILE_MODE_RELEASE 1                  // 发布模式
#cmakedefine URQ_COMPILE_MODE    @URQ_COMPILE_MODE@ // 当前编译模式


// 编译平台
#define URQ_COMPILE_PLATFORM_LINUX_64 1                      // x86_64 linux 兼容平台
#define URQ_COMPILE_PLATFORM_ARM_V7HF 2                      // arm v7 hf 兼容平台
#define URQ_COMPILE_PLATFORM_WASM     3                      // wasm 平台
#cmakedefine URQ_COMPILE_PLATFORM     @URQ_COMPILE_PLATFORM@ // 当前使用的平台

// cpu 位宽
#define URQ_COMPILE_BITS_32 1                              // 32 位
#define URQ_COMPILE_BITS_64 2                              // 64 位

// 字节序
#define URQ_COMPILE_ENDIAN_LITTLE 1                        // 小端字节序
#define URQ_COMPILE_ENDIAN_BIG    2                        // 大端字节序
#if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_LINUX_64
#define URQ_COMPILE_BITS URQ_COMPILE_BITS_64               // 使用 64 位
#define URQ_COMPILE_ENDIAN URQ_COMPILE_ENDIAN_LITTLE       // 使用小端字节序
#elif URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_ARM_V7HF
#define URQ_COMPILE_BITS URQ_COMPILE_BITS_64               // 使用 32 位
#define URQ_COMPILE_ENDIAN URQ_COMPILE_ENDIAN_LITTLE       // 使用小端字节序
#elif URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
#define URQ_COMPILE_BITS URQ_COMPILE_BITS_32               // 使用 32 位
#define URQ_COMPILE_ENDIAN URQ_COMPILE_ENDIAN_LITTLE       // 使用小端字节序
#else
#error "Unsupported platform"
#endif

// 其它编译选项
#define URQ_COMPILE_SDL @URQ_COMPILE_SDL@ // 是否使用 SDL，如果这个宏被定义，则使用 SDL 库

// clang-format on

#endif // #ifndef URQ_COMPILE_OPTIONS

#ifdef __cplusplus
}
#endif
#endif // URQ__COMPILE_HPP