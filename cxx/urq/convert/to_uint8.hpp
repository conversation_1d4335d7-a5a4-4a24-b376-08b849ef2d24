#pragma once

#include <cstdint>
#include <utility>

#ifndef URQ__CONVERT__TO_UINT8_HPP
#define URQ__CONVERT__TO_UINT8_HPP
namespace urq::convert {

/// @brief 将 T 转为 uint8_t
template <typename T> inline uint8_t to_uint8(T value) = delete;

/// @brief 将 int8_t 转为 uint8_t
template <> inline uint8_t to_uint8(int8_t v) { return v; }

/// @brief 将 int 转为 uint8_t
template <> inline uint8_t to_uint8(int v) { return static_cast<uint8_t>(v); }

// 断言给定的类型可以做为 debug 的第二个参数
template <typename T, typename = void> struct IntoU8Able : std::false_type {
};

template <typename T>
struct IntoU8Able<T, std::void_t<decltype(to_uint8(std::declval<T>()))>>
    : std::true_type {
};

static_assert(!IntoU8Able<float>::value, "float is IntoU8Able");
static_assert(IntoU8Able<int8_t>::value, "int8_t is not IntoU8Able");
static_assert(IntoU8Able<int>::value, "int is not IntoU8Able");

} // namespace urq::convert
#endif // URQ__CONVERT__TO_UINT8_HPP
