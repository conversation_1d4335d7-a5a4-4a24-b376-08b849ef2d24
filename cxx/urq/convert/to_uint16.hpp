#pragma once

#include <cstdint>
#include <utility>

#ifndef URQ__CONVERT__TO_UINT16_HPP
#define URQ__CONVERT__TO_UINT16_HPP
namespace urq::convert {

/// @brief 将 T 转为 uint16_t
template <typename T> inline uint16_t to_uint16(T value) = delete;

/// @brief 将 uint8_t 转为 uint16_t
template <> inline uint16_t to_uint16(uint8_t v) { return v; }

/// @brief 将 int8_t 转为 uint16_t
template <> inline uint16_t to_uint16(int8_t v) { return v; }

/// @brief 将 int16_t 转为 uint16_t
template <> inline uint16_t to_uint16(int16_t v) { return v; }

/// @brief 将 int32_t 转为 uint16_t
template <> inline uint16_t to_uint16(int32_t v) { return v; }

/// @brief 将 uint32_t 转为 uint16_t
template <> inline uint16_t to_uint16(uint32_t v) { return v; }

// 断言

template <typename T, typename = void> struct IntoU16Able : std::false_type {
};

template <typename T>
struct IntoU16Able<T, std::void_t<decltype(to_uint16(std::declval<T>()))>>
    : std::true_type {
};

static_assert(!IntoU16Able<float>::value, "float is IntoU16Able");
static_assert(IntoU16Able<uint8_t>::value, "uint8_t is not IntoU16Able");
static_assert(IntoU16Able<int8_t>::value, "int8_t is not IntoU16Able");
static_assert(IntoU16Able<int16_t>::value, "int16_t is not IntoU16Able");
static_assert(IntoU16Able<int32_t>::value, "int32_t is not IntoU16Able");
static_assert(IntoU16Able<uint32_t>::value, "uint32_t is not IntoU16Able");

} // namespace urq::convert
#endif // URQ__CONVERT__TO_UINT16_HPP
