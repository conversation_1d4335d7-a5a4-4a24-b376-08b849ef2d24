#pragma once

#include "urq/compile.hpp" // IWYU pragma: keep
#include <type_traits>

#ifndef URQ__DEBUG_HPP
#define URQ__DEBUG_HPP

#include <ostream>

namespace urq {

/// @brief 可以被 log 输出的内容
template <typename T>
inline void debug(std::ostream &os, const T &value) noexcept = delete;

// std::string
template <>
inline void debug(std::ostream &os, const std::string &value) noexcept
{
    os << value;
}

// char[]
template <size_t N>
inline void debug(std::ostream &os, const char (&value)[N]) noexcept
{
    os << value;
}

// static_assert(
//     IsDebuggable<const char[1]>::value, "char[] should be debuggable");

// bool
template <> inline void debug(std::ostream &os, const bool &value) noexcept
{
    os << (value ? "true" : "false");
}

// char
template <> inline void debug(std::ostream &os, const char &value) noexcept
{
    os << value;
}

// const char *
template <>
inline void debug(std::ostream &os, const char *const &value) noexcept
{
    os << value;
}

// char *
template <> inline void debug(std::ostream &os, char *const &value) noexcept
{
    os << value;
}

// int8_t
template <> inline void debug(std::ostream &os, const int8_t &value) noexcept
{
    os << (int)value;
}

// uint8_t
template <> inline void debug(std::ostream &os, const uint8_t &value) noexcept
{
    os << (int)value;
}

// int16_t
template <> inline void debug(std::ostream &os, const int16_t &value) noexcept
{
    os << value;
}

// uint16_t
template <> inline void debug(std::ostream &os, const uint16_t &value) noexcept
{
    os << value;
}

// int32_t
template <> inline void debug(std::ostream &os, const int32_t &value) noexcept
{
    os << value;
}

// uint32_t
template <> inline void debug(std::ostream &os, const uint32_t &value) noexcept
{
    os << value;
}

// int64_t
template <> inline void debug(std::ostream &os, const int64_t &value) noexcept
{
    os << value;
}

// uint64_t
template <> inline void debug(std::ostream &os, const uint64_t &value) noexcept
{
    os << value;
}

// #if URQ_COMPILE_PLATFORM == URQ_COMPILE_PLATFORM_WASM
// template <>
// inline void debug(std::ostream &os, const unsigned long &value) noexcept
// {
//     os << value;
// }
// #endif

// f32
template <> inline void debug(std::ostream &os, const float &value) noexcept
{
    os << value;
}

// f64
template <> inline void debug(std::ostream &os, const double &value) noexcept
{
    os << value;
}

// void *
template <>
inline void debug(std::ostream &os, const void *const &value) noexcept
{
    os << value;
}
// static_assert(IsDebuggable<void *>::value, "void * should be debuggable");

// const T *
template <typename T>
inline void debug(std::ostream &os, const T *const &value) noexcept
{
    os << value;
}

// T *
template <typename T>
inline void debug(std::ostream &os, T *const &value) noexcept
{
    os << static_cast<void *>(value);
}

template <typename T, typename = void> struct IsDebuggable : std::false_type {
};

template <typename T>
struct IsDebuggable<
    T, std::void_t<decltype(debug(
           std::declval<std::ostream &>(), std::declval<T>()))>>
    : std::true_type {
};

} // namespace urq

#endif // URQ__DEBUG_HPP
