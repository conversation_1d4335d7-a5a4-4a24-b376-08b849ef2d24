#pragma once

#include "urq/compile.hpp"
#include <cstdint>
#include <iostream>
#include <type_traits>

#ifndef URQ__RING_HPP
#define URQ__RING_HPP
namespace urq {

/// @brief 环形缓冲区
template <class T, int N> class Ring {

    template <class S = T>
    using FreeInplace =
        std::conditional_t<std::is_pointer_v<S>, void (*)(T), void (*)(T *)>;

    /// @brief 开始位置
    uint32_t m_begin{0};

public:
    /// @brief 容量
    static constexpr uint32_t capacity{N};

private:
    /// @brief 数据
    T m_data[N + 1]; // 环形缓冲区多一个位置，用于判断满了

    /// @brief 结束位置
    uint32_t m_end{0};

    /// @brief 元素释放函数
    FreeInplace<T> m_free_inplace{nullptr};

public:
    /// @brief 构造函数
    inline Ring() noexcept = default;

    /// @brief 析构函数
    inline ~Ring() noexcept
    {
        if (m_free_inplace == nullptr) {
            return;
        } else {
            std::cerr << "m_free_inplace is not nullptr" << std::endl;
        }
    }

    /// @brief 起始位置
    inline uint32_t begin() const noexcept { return m_begin; }

    /// @brief 结束位置
    inline uint32_t end() const noexcept { return m_end; }

    /// @brief 是否是空的
    inline bool empty() const noexcept { return m_begin == m_end; }

    /// @brief 是否是满的
    inline bool full()
    {
        return m_end + 1 == m_begin || (m_begin == 0 && m_end == N);
    }

    /// @brief 添加数据
    /// @param t 数据
    /// @returns 0 成功，-1 失败
    inline int push(T t) noexcept
    {
        if (full()) {
            return -1;
        }
        m_data[m_end] = t;
        m_end++;
        if (m_end >= N + 1) {
            m_end = 0;
        }
        return 0;
    }

    /// @brief 弹出数据
    /// @returns 数据
    inline T take() noexcept
    {
        if (empty()) {
            return T{};
        }
        T t = m_data[m_begin];
        m_begin++;
        if (m_begin >= N + 1) {
            m_begin = 0;
        }
        return t;
    }

    /// @brief 大小
    uint32_t size()
    {
        return m_end >= m_begin ? m_end - m_begin : N + 1 - m_begin + m_end;
    }

#if URQ_COMPILE_MODE == URQ_COMPILE_MODE_DEBUG
    /// @brief 获取 data
    T *data() noexcept { return m_data; }
#endif
};

} // namespace urq
#endif // URQ__RING_HPP
