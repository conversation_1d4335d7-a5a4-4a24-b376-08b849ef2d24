cmake_minimum_required(VERSION 3.22)

if(NOT URQ_ROOT_PROJECT)
    message(STATUS "urq_example  build as top level project")
    project("urq_example" VERSION 0.0.1)
else()
    message(STATUS "urq_example  build as sub project")
endif()

if(${URQ_BUILD_PLATFORM} STREQUAL "linux_64")
    set(URQ_CURRENT_BUILD "urq_ui_server")
    add_executable(${URQ_CURRENT_BUILD} server.c)
    set_property(TARGET ${URQ_CURRENT_BUILD} PROPERTY OUTPUT_NAME "urq-ui-server")
    # target_compile_features(${URQ_CURRENT_BUILD} PUBLIC c_std_11)

    set(SDL2_DIR "${CMAKE_PREFIX_PATH}/SDL2")
    find_package(SDL2 2.0.20 REQUIRED)
    target_include_directories(${URQ_CURRENT_BUILD} SYSTEM PUBLIC "${SDL2_INCLUDE_DIRS}")
    target_link_libraries(${URQ_CURRENT_BUILD} ${SDL2_LIBRARIES})
endif()
