#include "SDL_events.h"
#include "SDL_pixels.h"
#include "SDL_render.h"
#include "SDL_video.h"
#include <SDL2/SDL.h>
#include <errno.h>
#include <fcntl.h>
#include <pthread.h>
#include <signal.h>
#include <stdbool.h>
#include <stdio.h>
#include <sys/mman.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>

#define urq_used(x)         ((void)x)
#define FRAME_BUFFER_WIDTH  800                    // 帧缓冲区宽度
#define FRAME_BUFFER_HEIGHT 600                    // 帧缓冲区高度
#define FRAME_BUFFER_SIZE   800 * 600 * 4          // 共享内存大小
#define SHM_PATH            "/urq.framebuffer"     // 共享内存路径
#define EVENT_PATH          "/tmp/urq.mouse.event" // 事件 unix socket 路径

/// @brief 应用程序
typedef struct {
    bool running; // 是否运行
    /// @brief 鼠标事件接收套接字，-1 表示未连接, 其它时候需要将
    /// SDL 事件发送给该套接字
    int mouse_fd;
    /// @brief 鼠标事件接收套接字互斥锁
    pthread_mutex_t mutex;
} urq_app_t;

static void _app_lock(urq_app_t *app)
{
    if (pthread_mutex_lock(&app->mutex)) {
        perror("lock error: ");
        exit(1);
    }
    printf("lock success\n");
}

static void _app_unlock(urq_app_t *app)
{
    if (pthread_mutex_unlock(&app->mutex)) {
        perror("unlock error: ");
        exit(1);
    }
    printf("unlock success\n");
}

/// @brief 事件类型
typedef enum {
    EVENT_MOUSEBUTTONDOWN = 1, // 鼠标按下
    EVENT_MOUSEBUTTONUP,       // 鼠标抬起
    EVENT_KEYDOWN,             // 键盘按下
    EVENT_KEYUP,               // 键盘抬起
} socket_event_type_t;

/// @brief 事件
typedef struct {
    int down; // 是否按下
    int x;    // 鼠标 x 坐标
    int y;    // 鼠标 y 坐标
} urq_lv_mouse_event_t;

/// @brief 启动服务器侦听 unix socket
static void *_start_server(void *arg)
{
    urq_app_t *app = (urq_app_t *)arg;
    int server_fd;
    struct sockaddr_un server_addr, client_addr;
    socklen_t client_len;

    // 创建 Unix 域套接字
    server_fd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (server_fd < 0) {
        perror("socket");
        exit(EXIT_FAILURE);
    }

    // 清零并设置服务器地址结构
    memset(&server_addr, 0, sizeof(struct sockaddr_un));
    server_addr.sun_family = AF_UNIX;
    strncpy(server_addr.sun_path, EVENT_PATH, sizeof(server_addr.sun_path) - 1);

    // 如果套接字文件存在，则删除
    if (access(EVENT_PATH, F_OK) == 0) {
        if (unlink(EVENT_PATH)) {
            perror(
                "delete mouse event server socket failed, file: " EVENT_PATH);
            close(server_fd);
            exit(EXIT_FAILURE);
        } else {
            printf("delete old mouse event server socket, file: " EVENT_PATH
                   "\n");
        }
    }

    // 绑定套接字到地址
    if (bind(server_fd, (struct sockaddr *)&server_addr, sizeof(server_addr)) <
        0) {
        perror("bind");
        close(server_fd);
        exit(EXIT_FAILURE);
    }

    // 监听连接
    if (listen(server_fd, 1) < 0) {
        perror("listen");
        close(server_fd);
        exit(EXIT_FAILURE);
    }

    printf("start mouse event server, listen socket: %s\n", EVENT_PATH);

    while (true) {
        // 接受客户端连接
        client_len = sizeof(client_addr);
        if (!app->running) {
            break;
        }
        int fd =
            accept(server_fd, (struct sockaddr *)&client_addr, &client_len);
        if (fd < 0) {
            perror("accept mouse event server socket failed\n");
            close(server_fd);
            exit(EXIT_FAILURE);
        }

        _app_lock(app);
        if (app->mouse_fd != -1) {
            printf(
                "close old mouse event server socket, fd: %d\n", app->mouse_fd);
            close(app->mouse_fd);
        }
        printf("connect new mouse event server socket, fd: %d\n", fd);
        app->mouse_fd = fd;
        _app_unlock(app);
    }

    printf("close mouse event server socket\n");
    close(server_fd);
    // 删除套接字文件
    if (unlink(EVENT_PATH)) {
        perror("delete mouse event server socket failed, file: " EVENT_PATH);
    } else {
        printf("delete mouse event server socket, file: " EVENT_PATH "\n");
    }
    return NULL;
}

/// @brief 创建连接到服务器以关闭
static void *_close_mouse_event_fn(void *arg)
{
    urq_used(arg);
    while (true) {
        printf("try to connect mouse event server\n");
        struct sockaddr_un server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sun_family = AF_UNIX;
        strncpy(
            server_addr.sun_path, EVENT_PATH, sizeof(server_addr.sun_path) - 1);

        int fd = socket(AF_UNIX, SOCK_STREAM, 0);
        if (fd < 0) {
            perror("close socket create failed: ");
        }
        if (fd > 0) {
            if (connect(
                    fd, (struct sockaddr *)&server_addr, sizeof(server_addr)) <
                0) {
                close(fd);
                if (errno == ENOENT) {
                    return NULL;
                }
                perror("close error: ");
            }
            close(fd);
        }
        sleep(1);
    }
}

/// @brief 创建连接到服务器以关闭
static void _close_server(urq_app_t *app)
{
    app->running = false;
    pthread_t close_thread;
    pthread_create(&close_thread, NULL, _close_mouse_event_fn, NULL);
    pthread_join(close_thread, NULL);
}

/// @brief 打开共享内存
static void *_open_share_memory(void)
{
    int fd;
    // 判断共享内存是否存在

    if (access("/dev/shm" SHM_PATH, F_OK) == 0) {
        fd = shm_open(SHM_PATH, O_RDWR, 0600);
        if (fd < 0) {
            printf("open share memory failed: %s\n", strerror(errno));
            return NULL;
        }
    } else {
        // 创建共享内存
        fd = shm_open(SHM_PATH, O_CREAT | O_RDWR | O_EXCL, 0777);
        if (fd < 0) {
            printf("create share memory failed: %s\n", strerror(errno));
            return NULL;
        }
        ftruncate(fd, FRAME_BUFFER_SIZE);
    }

    // 映射内存
    void *ptr = mmap(
        NULL, FRAME_BUFFER_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
    if (ptr == MAP_FAILED) {
        return NULL;
    }

    // 使用完后清理
    // munmap(ptr, FRAME_SIZE);
    // shm_unlink(SHM_PATH);

    return ptr;
}

/// @brief 初始化SDL
static int _init_sdl(
    SDL_Window **window, SDL_Renderer **render, SDL_Texture **texture)
{
    static const int WIDTH = 800;
    static const int HEIGHT = 600;

    urq_used(texture);

    if (SDL_Init(SDL_INIT_VIDEO) < 0) {
        printf("SDL init failed: %s\n", SDL_GetError());
        return -1;
    }

    *window = SDL_CreateWindow(
        "URQ UI SERVER", SDL_WINDOWPOS_UNDEFINED, SDL_WINDOWPOS_UNDEFINED,
        WIDTH, HEIGHT, SDL_WINDOW_SHOWN);

    if (*window == NULL) {
        printf("create window failed: %s\n", SDL_GetError());
        return -1;
    }

    *render = SDL_CreateRenderer(*window, -1, SDL_RENDERER_ACCELERATED);
    if (*render == NULL) {
        printf("create renderer failed: %s\n", SDL_GetError());
        return -1;
    }

    return 0;
}

/// @brief 渲染画面
static void _render(
    SDL_Renderer *render, SDL_Texture *texture, void *framebuffer)
{
    // 清除屏幕
    SDL_SetRenderDrawColor(render, 0, 0, 0, 255);
    SDL_RenderClear(render);

    // 绘制纹理
    SDL_UpdateTexture(
        texture, NULL, framebuffer, FRAME_BUFFER_WIDTH * sizeof(Uint32));

    // 绘制纹理
    SDL_Rect dest_rect = {
        .x = 0, // x坐标居中
        .y = 0, // y坐标居中
        .w = FRAME_BUFFER_WIDTH,
        .h = FRAME_BUFFER_HEIGHT};

    SDL_RenderCopy(render, texture, NULL, &dest_rect);
    SDL_RenderPresent(render);
}

/// @brief 发送鼠标事件
static void _send_mouse_event(urq_app_t *app, SDL_Event *event)
{
    urq_lv_mouse_event_t socket_event;
    ssize_t send_size;

    socket_event.down = event->type == SDL_MOUSEBUTTONDOWN ? 1 : 0;
    socket_event.x = event->button.x;
    socket_event.y = event->button.y;

    _app_lock(app);
    if (app->mouse_fd == -1) {
        _app_unlock(app);
        printf("mouse_fd is -1, mouse event is not sent\n");
        return;
    }
    printf(
        "send event, fd: %d, ev: %s, %d:%d\n", app->mouse_fd,
        socket_event.down ? "down" : "up", socket_event.x, socket_event.y);

    send_size = send(app->mouse_fd, &socket_event, sizeof(socket_event), 0);
    printf("sent size: %ld\n", send_size);
    if (send_size != sizeof(urq_lv_mouse_event_t)) {
        printf(
            "send event failed, fd: %d, sent: %ld, expected: %ld, error: %s\n",
            app->mouse_fd, send_size, sizeof(urq_lv_mouse_event_t),
            strerror(errno));
        close(app->mouse_fd);
        app->mouse_fd = -1;
        _app_unlock(app);
        return;
    }
    _app_unlock(app);
    printf(
        "send event: %s, %d:%d\n", socket_event.down ? "down" : "up",
        socket_event.x, socket_event.y);
    return;
}

/// @brief 退出事件回调
static bool _quit_event_cb(SDL_Renderer *render, urq_app_t *app)
{
    _app_lock(app);
    if (app->mouse_fd == -1) {
        _app_unlock(app);
        printf("quit\n");
        return true;
    }
    close(app->mouse_fd);
    app->mouse_fd = -1;
    _app_unlock(app);
    printf("client closed\n");
    // 清除屏幕
    SDL_SetRenderDrawColor(render, 0, 0, 0, 255);
    SDL_RenderClear(render);
    SDL_RenderPresent(render);
    printf("render clear\n");
    return false;
}

/// @brief 处理事件并返回是否需要退出
/// @param app 应用程序
/// @return 是否需要退出, true 需要退出, false 不需要退出
static bool _handle_event(SDL_Renderer *render, urq_app_t *app)
{
    static SDL_Event event; // SDL 事件
    // static urq_lv_mouse_event_t socket_event; // 鼠标事件
    // static ssize_t send_size;                 // 发送大小

    while (SDL_PollEvent(&event)) {
        switch (event.type) {
        case SDL_QUIT:
            return _quit_event_cb(render, app);
        case SDL_MOUSEBUTTONDOWN:
        case SDL_MOUSEBUTTONUP:
            _send_mouse_event(app, &event);
            break;
        case SDL_KEYDOWN:
        case SDL_KEYUP:
            printf(
                "key, down: %s, sym: %d\n",
                event.type == SDL_KEYDOWN ? "down" : "up",
                event.key.keysym.sym);
            break;
        default:
            break;
        }
    }
    return false;
}

/// @brief 清理资源
static inline void _cleanup(
    SDL_Window *window, SDL_Renderer *render, SDL_Texture *texture,
    void *framebuffer)
{
    if (framebuffer) {
        munmap(framebuffer, FRAME_BUFFER_SIZE);
        // shm_unlink(SHM_PATH);
    }
    if (texture)
        SDL_DestroyTexture(texture);
    if (render)
        SDL_DestroyRenderer(render);
    if (window)
        SDL_DestroyWindow(window);
    SDL_Quit();
}

/// @brief ui 主循环
static int _ui_loop(urq_app_t *app)
{
    // SDL_Event event;
    SDL_Window *window = NULL;
    SDL_Renderer *render = NULL;
    SDL_Texture *texture = NULL;
    void *framebuffer = NULL;

    framebuffer = _open_share_memory();
    if (framebuffer == NULL) {
        return 1;
    }

    if (_init_sdl(&window, &render, &texture)) {
        _cleanup(window, render, texture, framebuffer);
        return 1;
    }

    texture = SDL_CreateTexture(
        render, SDL_PIXELFORMAT_ARGB8888, SDL_TEXTUREACCESS_TARGET,
        FRAME_BUFFER_WIDTH, FRAME_BUFFER_HEIGHT);
    if (texture == NULL) {
        printf("create texture failed: %s\n", SDL_GetError());
        return -1;
    }

    // 主循环
    while (true) {
        if (_handle_event(render, app)) {
            break;
        }
        if (app->mouse_fd != -1) {
            _render(render, texture, framebuffer);
        }
        SDL_Delay(5);
    }

    _cleanup(window, render, texture, framebuffer);
    return 0;
}

// 信号处理函数
void signal_handler(int signum)
{
    printf("Received signal %d, exiting...\n", signum);
}

int main(int argc, char *argv[])
{
    urq_used(argc);
    urq_used(argv);
    urq_app_t app;
    pthread_t server_thread;
    pthread_mutex_init(&app.mutex, NULL);

    // 注册信号处理
    // signal(SIGINT, signal_handler);
    // signal(SIGTERM, signal_handler);
    signal(SIGPIPE, signal_handler);

    system("clear"); // 清除终端
    printf("start ui-server\n");

    app.running = true;
    app.mouse_fd = -1;

    pthread_create(&server_thread, NULL, _start_server, &app);
    _ui_loop(&app);

    _close_server(&app);
    pthread_join(server_thread, NULL);
    return 0;
}
