// @ts-check
import * as node_fs from "node:fs";
import * as node_path from "node:path";
import { defineConfig } from "vite";

const mime_type = {
    ".html": "text/html",
    ".js": "application/javascript",
    ".css": "text/css",
    ".png": "image/png",
    ".svg": "image/svg+xml",
    ".wasm": "application/wasm",
    ".map": "application/json",
    ".c": "text/x-c",
    ".h": "text/x-c",
    ".cpp": "text/x-cpp",
    ".hpp": "text/x-cpp",
};

function findFolders() {
    const urq = node_path.resolve(__dirname, "..");
    const urq_parent = node_path.dirname(urq);
    const ui = node_path.resolve(urq, "ui");
    if (!node_fs.existsSync(ui)) {
        console.error("project ui not found: %s", ui);
        return;
    }
    const asset = node_path.resolve(ui, "asset");
    return { urq, ui, asset, urq_parent };
}

/**
 * 运行时插件
 * @returns {import("vite").PluginOption[]}
 */
function runtimePlugin() {
    const folders = findFolders();
    console.log(folders);
    if (folders === undefined) {
        return [];
    }

    /** @type {import("vite").Connect.NextHandleFunction} */
    const proto = (req, res, next) => {
        const url = req.url ?? "";
        if (!url.startsWith("/p/project-1/")) {
            return next();
        }
        console.log("proto: %s", url);
        const file = node_path.join(folders.asset, url);
        if (!node_fs.existsSync(file)) {
            console.log("asset not found: %s", file);
            res.statusCode = 404;
            res.write("404 Not Found");
            res.end();
            return;
        }
        const content = node_fs.readFileSync(file);
        res.setHeader("Content-Type", "application/octet-stream");
        res.setHeader("Content-Length", content.length);
        res.write(content);
        res.end();
        return;
    };

    /** @type {import("vite").Connect.NextHandleFunction} */
    const code = (req, res, next) => {
        if (!req.url?.startsWith("/urq/")) {
            return next();
        }

        const file = node_path.join(folders.urq_parent, req.url ?? "");
        if (!node_fs.existsSync(file)) {
            console.log("code not found: %s, %s", req.url, file);
            return next();
        }
        const mime = mime_type[node_path.extname(file)] ?? "application/octet-stream";
        const stat = node_fs.statSync(file);
        if (!stat.isFile()) {
            return next();
        }
        const etag = stat.mtimeMs.toString() + "-" + stat.size.toString();
        if (req.headers["if-none-match"] === etag) {
            res.statusCode = 304;
            res.end();
            return;
        }
        res.setHeader("Content-Type", mime);
        res.setHeader("ETag", etag);
        if (req.headers["if-none-match"] === etag) {
            res.statusCode = 304;
            res.end();
            return;
        }
        res.write(node_fs.readFileSync(file));
        res.end();
        return;
    };
    return [
        {
            name: "asset",
            apply: "serve",
            configureServer: (server) => {
                server.middlewares.use(proto);
                // server.middlewares.use(wasm);
                server.middlewares.use(code);
                server.watcher.add(node_path.join(folders.ui, "build/editor/debug/ui"));
            },
            // resolveId: (id) => {
            //     console.log("resolveId: %s", id);
            //     return id;
            // },
        },
    ];
}

export default defineConfig({
    // publicDir: node_path.resolve(root, "asset"),
    build: {
        emptyOutDir: false,
        lib: {
            entry: "src/runtime/index.ts",
            formats: ["es", "cjs"],
            fileName: "zendia-runtime",
        },
        minify: false,
        target: "es2022",
    },
    server: {
        host: "0.0.0.0",
    },
    plugins: [...runtimePlugin()],
});
