cmake_minimum_required(VERSION 3.22)

# 如果 URQ_ROOT_PROJECT 未设置，则添加 project
if(NOT URQ_ROOT_PROJECT)
    message(STATUS "urq_parse build as top level project")
    project("urq_parse" VERSION 0.0.1)
else()
    message(STATUS "urq_parse build as sub project")
endif()

include("${CMAKE_CURRENT_LIST_DIR}/../cmake/configure-file.cmake")
# include("${CMAKE_CURRENT_LIST_DIR}/../cmake/common.cmake")

# 创建一个库
file(GLOB_RECURSE SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.c"
    "${CMAKE_CURRENT_SOURCE_DIR}/znd/*.cc"
)

# 忽略 linux 平台特定的 .c 文件
list(FILTER SOURCES EXCLUDE REGEX ".+\\.[a-z]+\\.c$")

set(URQ_CURRENT_BUILD urq_parse)
add_library(${URQ_CURRENT_BUILD} STATIC ${SOURCES})

target_link_libraries(${URQ_CURRENT_BUILD} PUBLIC urq_lib)
target_include_directories(${URQ_CURRENT_BUILD} PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})

set(lvgl_DIR "${CMAKE_PREFIX_PATH}/lvgl")
find_package(lvgl 8.4 REQUIRED)
target_include_directories(${URQ_CURRENT_BUILD} PUBLIC "${lvgl_INCLUDE_DIRS}")
target_link_libraries(${URQ_CURRENT_BUILD} PUBLIC ${lvgl_LIBRARIES})

set(protobuf_DIR "${CMAKE_PREFIX_PATH}/protobuf")
find_package(protobuf REQUIRED)
target_include_directories(${URQ_CURRENT_BUILD} PUBLIC "${protobuf_INCLUDE_DIRS}")
target_link_libraries(${URQ_CURRENT_BUILD} PUBLIC ${protobuf_LIBRARIES})

