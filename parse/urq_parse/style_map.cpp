#include "urq_parse/style_map.h"
#include "urq/device/local.h"
#include "urq_parse/common/set_font.hpp"
#include "urq_parse/common/set_style.hpp"
#include <cstddef>
#include <cstdint>
#include <sys/types.h>

namespace urq::parse {

using znd::project::v1::ProjectStyleConfig;

static inline int set_project_style(
    urq_widget_parse_context_t &widget_ctx, uint16_t theme,
    const uint8_t *const data, size_t size) noexcept
{
    // TODO theme
    ProjectStyleConfig sty_conf;
    try {
        sty_conf.ParseFromArray(data, (int)size);
    } catch (const std::exception &e) {
        errno = EINVAL;
        return -1;
    }

    if (URQ_GET_DEVICE_SYSTEM_VAR(widget_ctx.var_sys, THEME) < 0) {
        URQ_SET_DEVICE_SYSTEM_VAR(
            widget_ctx.var_sys, THEME, (int16_t)sty_conf.default_theme_no_u8());
        theme = (uint16_t)URQ_GET_DEVICE_SYSTEM_VAR(widget_ctx.var_sys, THEME);
    }

    if (URQ_GET_DEVICE_SYSTEM_VAR(widget_ctx.var_sys, LANGUAGE) < 0) {
        URQ_SET_DEVICE_SYSTEM_VAR(
            widget_ctx.var_sys, LANGUAGE,
            (int16_t)sty_conf.default_language_no_u8());
    }

    auto it = sty_conf.theme_color_values();
    // 添加颜色
    for (auto &iter : it) {
        // 取出颜色ID
        uint8_t color_id = (uint8_t)((uint16_t)iter.first & 0xFF);
        uint8_t color_theme = (uint8_t)(((uint16_t)iter.first >> 8) & 0xFF);
        urq_color_rgba_t color = {iter.second};
        if (color_theme == theme) {
            if (urq_theme_color_add(widget_ctx.theme_color, color_id, color)) {
                return -1;
            }
        }
    }

    // 语言ID
    widget_ctx.language_list.data =
        (uint8_t *)urq_malloc((size_t)sty_conf.languages().size());
    widget_ctx.language_list.size = (size_t)sty_conf.languages().size();
    for (int i = 0; i < sty_conf.languages().size(); i++) {
        widget_ctx.language_list.data[i] =
            (uint8_t)sty_conf.languages()[i].language_type();
    }

    if (widget_ctx.language_list.size <= 0 ||
        URQ_GET_DEVICE_SYSTEM_VAR(widget_ctx.var_sys, LANGUAGE) < 0 ||
        URQ_GET_DEVICE_SYSTEM_VAR(widget_ctx.var_sys, LANGUAGE) >=
            (int16_t)widget_ctx.language_list.size) {
        return -1;
    }

    URQ_SET_DEVICE_SYSTEM_VAR(
        widget_ctx.var_sys, LANGUAGE,
        widget_ctx.language_list
            .data[URQ_GET_DEVICE_SYSTEM_VAR(widget_ctx.var_sys, LANGUAGE)]);

    // 添加字体
    // common::set_font(
    //     widget_ctx, widget_ctx.font, sty_conf.default_font_properties());

    if (sty_conf.font_file_map().size() > 0) {
        if (widget_ctx.font_map != NULL) {
            urq_font_map_free(widget_ctx.font_map);
        }
        widget_ctx.font_map = urq_font_map_new();
    }

    for (auto &iter : sty_conf.font_file_map()) {
        if (urq_font_map_add(
                widget_ctx.font_map, (int32_t)iter.first,
                (uint8_t)iter.second)) {
            return -1;
        }
    }

    // 添加样式
    for (const auto &iter : sty_conf.style_templates()) {
        auto style = iter.second.properties();
        urq_style_t *style_conf =
            (urq_style_t *)urq_malloc(sizeof(urq_style_t));
        urq_style_init_inplace(style_conf);

        if (style.has_background_color())
            common::set_color(
                widget_ctx, style_conf->bg, style.background_color());

        if (style.has_border_props())
            common::set_style_border(
                widget_ctx, style_conf->border, style.border_props());

        if (style.has_shadow_props())
            common::set_style_shadow(
                widget_ctx, style_conf->shadow, style.shadow_props());

        if (urq_theme_style_add(
                widget_ctx.theme_style, (int)iter.first, style_conf)) {
            urq_style_free_inplace(style_conf);
            urq_free(style_conf);
            continue;
        }
    }

    return 0;
}

} // namespace urq::parse

int urq_parse__style_map(
    urq_widget_parse_context_t *context, uint16_t theme,
    const uint8_t *const data, size_t size)
{
    return urq::parse::set_project_style(*context, theme, data, size);
}