#include "i18n_map.h"
#include "urq/errno.h"
#include "urq/i18n/id.h"
#include "urq/i18n/map.h"
#include "urq/log/verbose.h"
#include "znd/project/v1/text.pb.h"
#include <assert.h>
#include <cerrno>
#include <cstddef>

using znd::project::v1::TextTagLibrary;

int urq_parse__i18n_map(
    const uint8_t *const data, size_t size,
    const urq_i18n_lang_id_t language_id, urq_i18n_map_t *mut_map)
{
    log_v("begin parse i18n map, lang id: %d\n", language_id);
    if (language_id < 0)
        return -1;
    TextTagLibrary conf;
    try {
        conf.ParseFromArray(data, (int)size);
        log_v("parse i18n map success\n");
    } catch (const std::exception &e) {
        log_e("parse i18n map failed: %s\n", e.what());
        errno = EINVAL;
        return -1;
    }

    log_v("parse tags, size: %d\n", conf.tags_size());
    urq_i18n_map_resize(mut_map, (uint32_t)conf.tags_size());

    for (const auto &iter : conf.tags()) {
        auto &tag = iter.second;
        std::string rt = "";

        if (tag.has_single_lang()) {
            rt = tag.single_lang();
        } else if (tag.has_multi_lang()) {
            auto &multi_lang = tag.multi_lang().content();
            auto it = multi_lang.find((uint32_t)language_id);
            if (it == multi_lang.end()) {
                log_e("multi lang not found\n");
                continue;
            }
            rt = it->second;
        }

        if (rt == "") {
            log_e("i18n tag empty\n");
            continue;
        }

        if (urq_i18n_map_add(mut_map, (uint32_t)iter.first, rt.c_str())) {
            log_e("add i18n map failed\n");
            return -1;
        }

        log_v(
            "i18n add, [lang: %d] id: %d, str: %s\n", language_id, iter.first,
            rt.c_str());
    }

    return 0;
}
