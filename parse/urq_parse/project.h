#pragma once

#include "urq/project/conf.h"

#ifndef URQ_PARSE__PROJECT_H
#define URQ_PARSE__PROJECT_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 解析工程配置
/// @param ref_asset   工程配置文件
/// @param out_project 输出工程配置，需要已经初始化过
/// @return 0 成功，-1 失败
int urq_parse__project(
    const uint8_t *const data, size_t size,
    urq_project_conf_t *const out_project)
    __attribute__((__nonnull__(1, 3), __warn_unused_result__()));

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ_PARSE__PROJECT_H
