#pragma once

#include "urq/device/map.h"

#ifndef URQ_PARSE__DEVICE_H
#define URQ_PARSE__DEVICE_H
#ifdef __cplusplus
extern "C" {
#endif

/// @brief 解析设备列表
///
/// @param ref_file 设备配置文件
/// @returns 设备列表
urq_device_map_t *urq_parse__device(const uint8_t *const data, size_t size)
    __attribute__((__nonnull__(1))) __attribute__((__warn_unused_result__()));

#ifdef __cplusplus
}
#endif
#endif // #ifndef URQ_PARSE__DEVICE_H
