#pragma once

#include "urq/page/group_conf.h"
#include "urq/preload.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/ListBuilder.hpp"
#include "urq_parse/common/set_graphic.hpp"
#include "urq_parse/common/set_style.hpp"
#include "znd/project/v1/display.pb.h"
#include <cstdint>

#ifndef URQ_PARSE__SET_PAGE_HPP
#define URQ_PARSE__SET_PAGE_HPP
namespace urq::parse {

/// @brief 设置配置
/// 传进来的的配置需要是已经初始化过的
static inline int set_page(
    Context &ctx, urq_page_group_conf_t &dst, urq_page_id_t page_id,
    znd::project::v1::PageInfo const &proto)
{
    ListBuilder<int32_t> builder;
    if (builder.set_capacity((size_t)proto.stack_page_ids_size() + 1)) {
        return -1;
    }
    for (const auto &id : proto.stack_page_ids()) {
        builder.push((int32_t)id);
    }
    builder.push(page_id);

    dst.type = (urq_page_type_t)proto.page_type();
    if (proto.has_style()) {
        if (common::set_style(ctx, dst.style, proto.style(), true)) {
            return -1;
        }
    }
    if (proto.has_background_graphic()) {
        // 背景图片
        if (common::set_graphic(ctx, dst.graphic, proto.background_graphic())) {
            return -1;
        }
    }

    builder.build(dst.group_id_list.size, dst.group_id_list.data);

    dst.id = page_id;
    if (proto.has_page_no_u16())
        dst.page_no = (urq_page_id_t)proto.page_no_u16();

    dst.popup_type = (urq_page_popup_type_t)proto.popup_type();

    if (proto.has_size()) {
        dst.size.w = (lv_coord_t)proto.size().width_i16();
        dst.size.h = (lv_coord_t)proto.size().height_i16();
    } else {
        dst.size.w = -1;
        dst.size.h = -1;
    }
    return 0;
}

} // namespace urq::parse

#endif // URQ_PARSE__SET_PAGE_HPP