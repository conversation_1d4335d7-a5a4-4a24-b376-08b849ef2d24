#include "urq_parse/set_node_list.hpp"
#include "urq/widget/conf.h"
#include "urq_parse/ListBuilder.hpp"
#include "urq_parse/set_node.hpp"
#include <cstddef>

namespace urq::parse {

    using google::protobuf::RepeatedPtrField;

int set_node_list(
    Context &ctx, urq_widget_conf_list_t &conf,
    RepeatedPtrField<Widget> const &proto) noexcept
{
    ListBuilder<urq_widget_conf_t *> builder(urq_widget_conf_free_inplace);
    builder.set_capacity((size_t)proto.size());
    urq_widget_conf_t *ptr;

    for (auto &iter : proto) {
        ptr = nullptr;

        if (set_node(ctx, ptr, iter)) {
            return -1;
        }
        builder.push(ptr);
    }
    builder.build(conf.size, conf.data);
    return 0;
}

} // namespace urq::parse
