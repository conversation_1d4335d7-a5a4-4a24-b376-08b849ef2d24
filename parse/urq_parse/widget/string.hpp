#pragma once

#include "urq/string/conf.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_font.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__WIDGET__STRING_HPP
#define URQ_PARSE__WIDGET__STRING_HPP

namespace urq::parse::widget {

static inline int set_string(
    Context &ctx, urq_string_conf_t *&ptr,
    const ::znd::project::v1::Widget &proto) noexcept
{
    urq_used(ctx);
    ptr = malloc<urq_string_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_string_conf_init_inplace(ptr);
    auto string = proto.widget_string();
    // ptr->value = urq_strdup(string.current_value().c_str());
    ptr->support_input = string.support_input();
    ptr->keyboard_page_id = (urq_page_id_t)string.keyboard_page_id_u16();
    ptr->max_length = (uint16_t)string.max_length_u16();
    ptr->min_length = (uint16_t)string.min_length_u16();
    ptr->show_scrollbar = string.show_scrollbar();
    ptr->pwd_mode = string.password_mode();
    // if (string.has_font()) {
    //     ::urq::parse::common::set_font(ctx, ptr->font, string.font());
    // }

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__STRING_HPP
