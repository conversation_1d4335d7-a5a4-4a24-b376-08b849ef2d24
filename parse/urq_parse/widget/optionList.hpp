#pragma once

#include "urq/optionList/conf.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_font.hpp"
#include "urq_parse/common/set_style.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__WIDGET__OPTION_LIST_HPP
#define URQ_PARSE__WIDGET__OPTION_LIST_HPP

namespace urq::parse::widget {

/// @brief 滚动条输入
static inline int set_optionList(
    Context &ctx, urq_option_list_conf_t &ptr,
    const znd::project::v1::WidgetOptionList &proto) noexcept
{

    if (proto.options_size() != proto.options_value_size() ||
        proto.options_size() <= 0) {
        return -1;
    }
    ptr.data_resource_type =
        (urq_data_resource_type_t)proto.data_resource_type();
    std::string options;
    for (int i = 0; i < proto.options_size(); i++) {
        if (proto.options(i).size() >= 16) {
            return -1;
        }
        options += proto.options(i);
        if (i < proto.options_size() - 1) {
            options += "\n";
        }
    }
    ptr.options = malloc<char>(options.size() + 1);
    strcpy(ptr.options, options.c_str());

    ptr.options_value = malloc<char *>((size_t)proto.options_value_size());
    for (int i = 0; i < proto.options_value_size(); i++) {
        ptr.options_value[i] = malloc<char>(proto.options_value(i).size() + 1);
        strcpy(ptr.options_value[i], proto.options_value(i).c_str());
    }

    // if (proto.has_font()) {
    //     ::urq::parse::common::set_font(ctx, ptr.font, proto.font());
    // }
    if (proto.has_selected_color()) {
        ::urq::parse::common::set_color(
            ctx, ptr.selected_color, proto.selected_color());
    }
    ptr.row_spacing = (uint8_t)proto.row_spacing_u8();

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__IMAGE_HPP
