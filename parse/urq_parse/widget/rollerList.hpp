#pragma once

#include "urq/rollerList/conf.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"
#include "urq_parse/widget/optionList.hpp"

#ifndef URQ_PARSE__WIDGET__ROLLER_LIST_HPP
#define URQ_PARSE__WIDGET__ROLLER_LIST_HPP

namespace urq::parse::widget {

/// @brief 滚动条输入
static inline int set_rollerList(
    Context &ctx, urq_rollerList_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_rollerList_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_rollerList_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);

    auto &rollerList = proto.widget_roller_list();
    set_optionList(ctx, ptr->list, rollerList.list());
    ptr->mode = (lv_roller_mode_t)rollerList.roller_list_mode();
    ptr->view_count = (uint8_t)rollerList.view_count_u8();
    ptr->def_index = (uint8_t)rollerList.def_index_u8();

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__ROLLER_LIST_HPP
