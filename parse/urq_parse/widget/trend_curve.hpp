#pragma once

#include "urq/curve/trend_curve.h"
#include "urq/preload.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"
#include "urq_parse/widget/curve.hpp"

#ifndef URQ_PARSE__WIDGET__TREND_CURVE_HPP
#define URQ_PARSE__WIDGET__TREND_CURVE_HPP

namespace urq::parse::widget {

/// @brief 曲线输入
static inline int set_trend_curve(
    Context &ctx, urq_trend_curve_widget_conf_t *&ptr,
    const ::znd::project::v1::Widget &proto) noexcept
{
    auto trend_curve = proto.widget_trend_curve();
    ptr =
        (urq_trend_curve_widget_conf_t *)urq_malloc(sizeof(urq_trend_curve_widget_conf_t));
    if (ptr == nullptr) {
        return -1;
    }
    urq_trend_curve_widget_conf_init_inplace(ptr);

    ptr->conf = (urq_curve_widget_conf_t *)urq_malloc(sizeof(urq_curve_widget_conf_t));
    urq_curve_widget_conf_init_inplace(ptr->conf);
    set_curve(ctx, ptr->conf, trend_curve.curve());

    ptr->pause_resume_time = (uint8_t)trend_curve.pause_resume_time_u8();
    ptr->use_relative_time = trend_curve.use_relative_time();
    ptr->use_time_label = trend_curve.use_time_label();
    ptr->is_history = trend_curve.is_history();
    ptr->time_format = (urq_time_format_t)trend_curve.time_format();
    ptr->date_format = (urq_date_format_t)trend_curve.date_format();

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__IMAGE_HPP
