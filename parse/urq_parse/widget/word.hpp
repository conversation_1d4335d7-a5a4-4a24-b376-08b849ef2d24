#pragma once

#include "urq/word/conf.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__WIDGET__WORD_HPP
#define URQ_PARSE__WIDGET__WORD_HPP

namespace urq::parse::widget {

/// @brief 位显示输入
static inline int set_word(
    Context &ctx, urq_word_widget_conf_t *&ptr,
    const ::znd::project::v1::Widget &proto) noexcept
{
    urq_used(ctx);
    urq_used(proto);
    ptr = malloc<urq_word_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_word_widget_conf_init_inplace(ptr);

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__WORD_HPP
