#pragma once

#include "urq/bit/conf.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__BIT_HPP
#define URQ_PARSE__WIDGET__BIT_HPP

namespace urq::parse::widget {

/// @brief 位显示输入
static inline int set_bit(
    Context &ctx, urq_bit_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_bit_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_bit_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);
    urq_used(proto);

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__BIT_HPP
