#pragma once

#include "urq/number/conf.h"
#include "urq/preload.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_data.hpp"
#include "urq_parse/common/set_font.hpp"
#include "urq_parse/common/set_style.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__WIDGET__NUMBER_HPP
#define URQ_PARSE__WIDGET__NUMBER_HPP

namespace urq::parse::widget {

static inline int set_number(
    Context &ctx, urq_number_conf_t *&ptr,
    const ::znd::project::v1::Widget &proto) noexcept
{

    urq_used(ctx);
    urq_used(proto);
    ptr = malloc<urq_number_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_number_conf_init_inplace(ptr);
    auto number = proto.widget_number();
    // 值
    ::urq::parse::common::set_data_all_type(
        ctx, ptr->value, number.current_value());
    // 最大值
    ::urq::parse::common::set_data_all_type(
        ctx, ptr->max_value, number.max_value());
    // 最小值
    ::urq::parse::common::set_data_all_type(
        ctx, ptr->min_value, number.min_value());

    // 是否支持输入
    ptr->support_input = number.support_input();
    // 键盘页面id
    ptr->keyboard_page_id = (urq_page_id_t)number.keyboard_page_id_u16();
    // 数据格式
    // ptr->data_format = number.data_format();
    // 整数位数
    ptr->integer_digits = (uint8_t)number.integer_digits_u8();
    // 小数位数
    ptr->decimal_digits = (uint8_t)number.decimal_digits_u8();
    // 是否隐藏前导零
    ptr->hide_leading_zero = number.hide_leading_zero();
    // 是否隐藏小数未尾零
    ptr->hide_trailing_zero = number.hide_trailing_zero();
    // 是否显示正号
    ptr->show_plus_sign = number.show_plus_sign();
    // 小于最小值的颜色
    ::urq::parse::common::set_color(
        ctx, ptr->less_than_min_color, number.less_than_min_color());
    // 小于最小值的闪烁频率
    ptr->less_than_min_flash_time =
        (uint8_t)number.less_than_min_flash_time_u8();
    // 大于最大值的颜色
    ::urq::parse::common::set_color(
        ctx, ptr->greater_than_max_color, number.greater_than_max_color());
    // 大于最大值的闪烁频率
    ptr->greater_than_max_flash_time =
        (uint8_t)number.greater_than_max_flash_time_u8();
    // 密码模式
    ptr->pwd_mode = number.password_mode();
    // 字体
    // if (number.has_font()) {
    //     ::urq::parse::common::set_font(ctx, ptr->font, number.font());
    // }
    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__NUMBER_HPP
