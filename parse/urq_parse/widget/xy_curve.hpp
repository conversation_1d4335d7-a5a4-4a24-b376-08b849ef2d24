#pragma once

#include "urq/curve/xy_curve.h"
#include "urq/preload.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"
#include "urq_parse/widget/curve.hpp"

#ifndef URQ_PARSE__WIDGET__XY_CURVE_HPP
#define URQ_PARSE__WIDGET__XY_CURVE_HPP

namespace urq::parse::widget {

/// @brief 曲线输入
static inline int set_xy_curve(
    Context &ctx, urq_xy_curve_widget_conf_t *&ptr,
    const ::znd::project::v1::Widget &proto) noexcept
{
    auto xy_curve = proto.widget_xy_curve();
    ptr =
        (urq_xy_curve_widget_conf_t *)urq_malloc(sizeof(urq_xy_curve_widget_conf_t));
    if (ptr == nullptr) {
        return -1;
    }
    urq_xy_curve_widget_conf_init_inplace(ptr);

    ptr->conf = (urq_curve_widget_conf_t *)urq_malloc(sizeof(urq_curve_widget_conf_t));
    urq_curve_widget_conf_init_inplace(ptr->conf);
    set_curve(ctx, ptr->conf, xy_curve.curve());

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__IMAGE_HPP
