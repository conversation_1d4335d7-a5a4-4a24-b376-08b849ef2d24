#pragma once

#include "urq/ruler/conf.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_scale.hpp"
#include "urq_parse/common/set_line.hpp"
#include "urq_parse/common/set_color.hpp"
#include "urq_parse/common/set_data.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__RULER_HPP
#define URQ_PARSE__WIDGET__RULER_HPP

namespace urq::parse::widget {

/// @brief 从NumberValue获取数值
static inline lv_coord_t get_number_value(const znd::project::v1::NumberValue &proto) noexcept
{
    switch (proto.from_case()) {
    case znd::project::v1::NumberValue::kValueI8:
        return (lv_coord_t)proto.value_i8();
    case znd::project::v1::NumberValue::kValueU8:
        return (lv_coord_t)proto.value_u8();
    case znd::project::v1::NumberValue::kValueI16:
        return (lv_coord_t)proto.value_i16();
    case znd::project::v1::NumberValue::kValueU16:
        return (lv_coord_t)proto.value_u16();
    case znd::project::v1::NumberValue::kValueI32:
        return (lv_coord_t)proto.value_i32();
    case znd::project::v1::NumberValue::kValueU32:
        return (lv_coord_t)proto.value_u32();
    case znd::project::v1::NumberValue::kValueFloat:
        return (lv_coord_t)proto.value_float();
    case znd::project::v1::NumberValue::kValueDouble:
        return (lv_coord_t)proto.value_double();
    default:
        return 0;
    }
}

/// @brief 刻度尺组件解析
static inline int set_ruler(
    Context &ctx, urq_ruler_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_ruler_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_ruler_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);

    auto &ruler = proto.widget_ruler();

    // 设置刻度尺类型
    switch (ruler.ruler_type()) {
        case znd::project::v1::RULER_TYPE_HORIZONTAL:
            ptr->type = URQ_RULER_TYPE_HORIZONTAL;
            break;
        case znd::project::v1::RULER_TYPE_VERTICAL:
            ptr->type = URQ_RULER_TYPE_VERTICAL;
            break;
        case znd::project::v1::RULER_TYPE_ARC:
            ptr->type = URQ_RULER_TYPE_ARC;
            break;
        case znd::project::v1::RULER_TYPE_CIRCLE:
            ptr->type = URQ_RULER_TYPE_CIRCLE;
            break;
        default:
            ptr->type = URQ_RULER_TYPE_HORIZONTAL;
            break;
    }

    // 设置刻度方向
    switch (ruler.direction()) {
        case znd::project::v1::RULER_DIRECTION_TOP_LEFT:
            ptr->direction = URQ_RULER_DIRECTION_TOP_LEFT;
            break;
        case znd::project::v1::RULER_DIRECTION_BOTTOM_RIGHT:
            ptr->direction = URQ_RULER_DIRECTION_BOTTOM_RIGHT;
            break;
        case znd::project::v1::RULER_DIRECTION_BOTH:
            ptr->direction = URQ_RULER_DIRECTION_BOTH;
            break;
        default:
            ptr->direction = URQ_RULER_DIRECTION_BOTTOM_RIGHT;
            break;
    }

    // 设置标签位置
    switch (ruler.label_position()) {
        case znd::project::v1::RULER_LABEL_POSITION_OUTSIDE:
            ptr->label_position = URQ_RULER_LABEL_POSITION_OUTSIDE;
            break;
        case znd::project::v1::RULER_LABEL_POSITION_INSIDE:
            ptr->label_position = URQ_RULER_LABEL_POSITION_INSIDE;
            break;
        case znd::project::v1::RULER_LABEL_POSITION_CENTER:
            ptr->label_position = URQ_RULER_LABEL_POSITION_CENTER;
            break;
        default:
            ptr->label_position = URQ_RULER_LABEL_POSITION_OUTSIDE;
            break;
    }

    // 解析刻度配置
    if (ruler.has_scale_config()) {
        auto &scale_config = ruler.scale_config();
        
        // 设置刻度显示类型
        switch (scale_config.scale_show_type()) {
            case znd::project::v1::ScaleValueConfig::SCALE_SHOW_TYPE_POINT:
                ptr->scale.show_type = URQ_CURVE_SCALE_SHOW_TYPE_POINT;
                break;
            case znd::project::v1::ScaleValueConfig::SCALE_SHOW_TYPE_TIME:
                ptr->scale.show_type = URQ_CURVE_SCALE_SHOW_TYPE_TIME;
                break;
            default:
                ptr->scale.show_type = URQ_CURVE_SCALE_SHOW_TYPE_NONE;
                break;
        }

        // 设置主刻度
        if (scale_config.has_scale_main()) {
            auto &main_scale = scale_config.scale_main();
            ptr->scale.main_tick_count = (uint8_t)main_scale.scale_count_u8();
            ptr->scale.main_tick_width = (uint8_t)main_scale.scale_width_u8();
            ptr->scale.main_tick_len = (uint8_t)main_scale.scale_draw_len();
            
            if (main_scale.has_color()) {
                urq::parse::common::set_color(ctx, ptr->scale.main_color, main_scale.color());
            }
        }

        // 设置次刻度
        if (scale_config.has_scale_sec()) {
            auto &sub_scale = scale_config.scale_sec();
            ptr->scale.sub_tick_count = (uint8_t)sub_scale.scale_count_u8();
            ptr->scale.sub_tick_width = (uint8_t)sub_scale.scale_width_u8();
            ptr->scale.sub_tick_len = (uint8_t)sub_scale.scale_draw_len();
            
            if (sub_scale.has_color()) {
                urq::parse::common::set_color(ctx, ptr->scale.sub_color, sub_scale.color());
            }
        }

        // 设置刻度范围
        if (scale_config.has_min_value()) {
            ptr->scale.min_value = get_number_value(scale_config.min_value());
        }
        if (scale_config.has_max_value()) {
            ptr->scale.max_value = get_number_value(scale_config.max_value());
        }

        // 设置网格线配置
        if (scale_config.has_grid_line_config()) {
            ptr->scale.line = malloc<urq_style_line_t>();
            if (ptr->scale.line != nullptr) {
                urq_style_line_init_inplace(ptr->scale.line);
                urq::parse::common::set_line(ctx, *ptr->scale.line, scale_config.grid_line_config());
            }
        }
    }

    // 解析主线配置
    if (ruler.has_main_line()) {
        ptr->main_line = malloc<urq_style_line_t>();
        if (ptr->main_line != nullptr) {
            urq_style_line_init_inplace(ptr->main_line);
            urq::parse::common::set_line(ctx, *ptr->main_line, ruler.main_line());
        }
    }

    // 解析背景颜色
    if (ruler.has_background_color()) {
        urq::parse::common::set_color(ctx, ptr->background_color, ruler.background_color());
    }

    // 解析标签颜色
    if (ruler.has_label_color()) {
        urq::parse::common::set_color(ctx, ptr->label_color, ruler.label_color());
    }

    // 设置标签偏移
    ptr->label_offset = (lv_coord_t)ruler.label_offset_u8();

    // 弧形刻度尺专用参数
    ptr->start_angle = (uint16_t)ruler.start_angle_u16();
    ptr->end_angle = (uint16_t)ruler.end_angle_u16();
    ptr->radius = (lv_coord_t)ruler.radius_u16();
    ptr->center_offset_x = (lv_coord_t)ruler.center_offset_x_i16();
    ptr->center_offset_y = (lv_coord_t)ruler.center_offset_y_i16();

    // 设置显示选项
    ptr->show_labels = ruler.show_labels();
    ptr->show_main_line = ruler.show_main_line();
    ptr->show_grid_lines = ruler.show_grid_lines();
    ptr->auto_spacing = ruler.auto_spacing();

    // TODO: 解析自定义标记列表
    // for (const auto &mark : ruler.custom_marks()) {
    //     // 处理自定义标记
    // }

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__RULER_HPP
