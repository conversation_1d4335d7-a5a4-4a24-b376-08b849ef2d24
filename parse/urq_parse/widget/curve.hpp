#pragma once

#include "urq/curve/conf.h"
#include "urq/preload.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_font.hpp"
#include "urq_parse/common/set_scale.hpp"
#include "urq_parse/common/set_series.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__WIDGET__CURVE_HPP
#define URQ_PARSE__WIDGET__CURVE_HPP

namespace urq::parse::widget {

/// @brief 曲线输入
static inline int set_curve(
    Context &ctx, urq_curve_widget_conf_t *&ptr,
    const ::znd::project::v1::WidgetCurve &proto) noexcept
{
    ptr =
        (urq_curve_widget_conf_t *)urq_malloc(sizeof(urq_curve_widget_conf_t));
    if (ptr == nullptr) {
        return -1;
    }
    urq_curve_widget_conf_init_inplace(ptr);

    /// 序列
    if (proto.series_config_size() > 0) {
        ::urq::parse::common::set_series_list(
            ctx, ptr->series_conf, proto.series_config());
    }

    // 游标
    if (proto.enable_cursor()) {
        ptr->cursor_conf =
            (urq_curve_cursor_t *)urq_malloc(sizeof(urq_curve_cursor_t));
        urq_curve_cursor_init_inplace(ptr->cursor_conf);
        ptr->cursor_conf->cursor_width = (uint8_t)proto.cursor_width_u8();
        ::urq::parse::common::set_color(
            ctx, ptr->cursor_conf->color, proto.cursor_color());
    }

    if (proto.has_scale_value_config_x()) {
        ::urq::parse::common::set_scale(
            ctx, ptr->x_scale, proto.scale_value_config_x());
    }

    if (proto.has_scale_value_config_y()) {
        ::urq::parse::common::set_scale(
            ctx, ptr->y_scale, proto.scale_value_config_y());
    }

    if (proto.has_scale_value_config_y2()) {
        ::urq::parse::common::set_scale(
            ctx, ptr->y_scale2, proto.scale_value_config_y2());
    }

    if (ptr->y_scale != NULL) {
        ptr->offset_x = ptr->y_scale->main_tick_len > ptr->y_scale->sub_tick_len
                            ? ptr->y_scale->main_tick_len
                            : ptr->y_scale->sub_tick_len;
        ptr->offset_x += ptr->y_scale->draw_tick_len;
    }

    if (ptr->y_scale2 != NULL) {
        ptr->offset_x2 =
            ptr->y_scale2->main_tick_len > ptr->y_scale2->sub_tick_len
                ? ptr->y_scale2->main_tick_len
                : ptr->y_scale2->sub_tick_len;
        ptr->offset_x2 += ptr->y_scale2->draw_tick_len;
    }

    if (ptr->x_scale != NULL) {
        ptr->offset_y = ptr->x_scale->main_tick_len > ptr->x_scale->sub_tick_len
                            ? ptr->x_scale->main_tick_len
                            : ptr->x_scale->sub_tick_len;
        ptr->offset_y += ptr->x_scale->draw_tick_len;
    }

    // if(proto.has_scale_value_font()) {
    //     common::set_font(ctx, ptr->font, proto.scale_value_font());
    // }

    // 类型
    ptr->type = (urq_curve_type_t)proto.curve_type();

    // 设置显示范围类型
    if (proto.has_point_count_u16()) {
        ptr->show_range_type = URQ_CURVE_SHOW_TYPE_POINT;
        ptr->range_value = (uint16_t)proto.point_count_u16();
    } else if (proto.has_point_distance_u16()) {
        ptr->show_range_type = URQ_CURVE_SHOW_TYPE_PIXEL_DISTANCE;
        ptr->range_value = (uint16_t)proto.point_distance_u16();
    } else if (proto.has_range_second_u16()) {
        ptr->show_range_type = URQ_CURVE_SHOW_TYPE_TIME_RANGE;
        ptr->range_value = (uint16_t)proto.range_second_u16();
    } else if (proto.has_range_minute_u16()) {
        ptr->show_range_type = URQ_CURVE_SHOW_TYPE_TIME_RANGE;
        ptr->range_value = (uint16_t)proto.range_minute_u16();
    }

    // 配置方式
    ptr->draw_direction = (urq_scroll_direction_t)proto.draw_direction();
    ptr->time_format = (urq_time_format_t)proto.time_format();
    ptr->date_format = (urq_date_format_t)proto.date_format();
    ptr->use_current_time = proto.use_current_time();

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__IMAGE_HPP
