
#pragma once

#include "urq/clone/conf.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__WIDGET__CLONE_HPP
#define URQ_PARSE__WIDGET__CLONE_HPP

namespace urq::parse::widget {

/// @brief 位显示输入
static inline int set_clone(
    Context &ctx, urq_clone_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_clone_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_clone_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);

    auto &clone = proto.widget_clone();
    ptr->page_id_u16 = (uint16_t)clone.page_id_u16();
    ptr->widget_id_u16 = (uint16_t)clone.widget_id_u16();

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__CLONE_HPP
