#pragma once

#include "urq/device/local.h"
#include "urq/i18n/id.h"
#include "urq/i18n/map.h"
#include "urq/widget/conf.h"
#include "urq/widget/parse_context.h"

#ifndef URQ_PARSE__CONTEXT_HPP
#define URQ_PARSE__CONTEXT_HPP
namespace urq::parse {

class Context {
    /// @brief 配置解析上下文
    const urq_widget_parse_context_t &m_context;
    /// @brief 系统变量
    const urq_device_local_t &m_var_system;
    /// @brief 工程地址标签表
    // const urq_atag_project_t *m_atags;
    /// @brief 当前页面，如果不是在解析页面的时候，该值为 -1
    const urq_page_id_t m_page_id;
    /// @brief 当前配置
    urq_widget_conf_t *m_conf = nullptr;

public:
    /// @brief 构造函数
    inline Context() noexcept = delete;

    /// @brief 构造函数
    ///
    /// @param context 配置解析上下文
    /// @param sys     系统变量
    /// @param page_id 当前页面
    inline Context(
        const urq_widget_parse_context_t &context,
        const urq_device_local_t &sys, urq_page_id_t page_id) noexcept
        : m_context{context}, m_var_system{sys}, m_page_id{page_id}
    {
    }

    /// @brief 析构函数
    inline ~Context() noexcept {}

    /// @brief 拷贝构造函数
    inline Context(const Context &other) noexcept = delete;
    /// @brief 赋值运算符
    inline Context &operator=(const Context &other) noexcept = delete;
    /// @brief 移动构造函数
    inline Context(Context &&other) noexcept = delete;
    /// @brief 移动赋值运算符
    inline Context &operator=(Context &&other) noexcept = delete;

    /// @brief 获取语言
    inline const char *get_string(urq_i18n_id_t id) const noexcept
    {
        return urq_i18n_map_get(m_context.i18n_map, (uint32_t)id);
    }

    /// @brief 获取样式
    inline const urq_style_t *get_theme_style(uint32_t id) const noexcept
    {
        return urq_theme_style_get(m_context.theme_style, id);
    }

    /// @brief 获取颜色
    inline const urq_color_rgba_t *get_theme_color(uint32_t id) const noexcept
    {
        return urq_theme_color_get(m_context.theme_color, id);
    }

    /// @brief 获取语言 id
    inline urq_i18n_lang_id_t language_id() const noexcept
    {
        return (urq_i18n_lang_id_t)
            m_var_system.registers.data[URQ_DEVICE_LOCAL_ADDR_LANGUAGE];
    }

    /// @brief 获取解析上下文
    inline const urq_widget_parse_context_t &get_context() const noexcept
    {
        return m_context;
    }
    /// @brief 设置当前配置
    inline void set_conf(urq_widget_conf_t *conf) noexcept { m_conf = conf; }
    /// @brief 获取当前配置
    inline urq_widget_conf_t *get_conf() const noexcept { return m_conf; }

    /// @brief 当前画面
    inline urq_page_id_t page_id() const noexcept { return m_page_id; }

    inline uint8_t get_font_id(uint32_t id) const noexcept
    {
        uint16_t _v = (uint16_t)((uint16_t)id << 8 | (uint8_t)language_id());
        return (uint8_t)urq_font_map_get(m_context.font_map, _v);
    }

    /// @brief 查找地址标签
    // inline const urq_atag_t *find_atag(
    //     urq_device_id_t id, urq_atag_id_t tag_id) const noexcept
    //{
    //     const urq_atag_device_t *device =
    //         urq_atag_project_get_device(m_atags, id);
    //     if (device == nullptr) {
    //         return nullptr;
    //     }
    //     return urq_atag_device_get(device, tag_id);
    // }
};

} // namespace urq::parse
#endif // URQ_PARSE__CONTEXT_HPP