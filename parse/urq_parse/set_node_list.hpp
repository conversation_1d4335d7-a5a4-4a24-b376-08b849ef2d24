#pragma once

#include "urq/widget/conf_list.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__SET_NODE_LIST_HPP
#define URQ_PARSE__SET_NODE_LIST_HPP
namespace urq::parse {

/// @brief 设置节点列表
/// @param ctx   编译上下文
/// @param ptr   节点列表指针
/// @param proto 节点列表配置
/// @param size  祖先控件大小
/// @return 0 成功, -1 失败
int set_node_list(
    Context &ctx, urq_widget_conf_list_t &conf,
    google::protobuf::RepeatedPtrField<Widget> const &proto) noexcept;

} // namespace urq::parse
#endif // URQ_PARSE__SET_NODE_LIST_HPP