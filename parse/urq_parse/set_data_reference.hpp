#pragma once

#include "urq/data/reference.h"
#include "urq/data/reference_list.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/ListBuilder.hpp"
#include "urq_parse/set_data_all_type.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__SET_DATA_REFERENCE_H
#define URQ_PARSE__SET_DATA_REFERENCE_H

namespace urq::parse {

static inline int set_data_reference(
    Context &ctx, urq_data_reference_t *&conf, const DataReference &proto)
{
    urq_used(ctx);

    if (conf == NULL) {
        conf = malloc<urq_data_reference_t>();
        if (conf == NULL) {
            return -1;
        }
        urq_data_reference_init_inplace(conf);
    }
    if (set_data_all_type(ctx, conf->all_type, proto.value())) {
        return -1;
    }

    if (proto.has_index_register_u8()) {
        conf->index_register = (uint8_t)proto.index_register_u8();
    }

    return 0;
}

static inline int set_data_reference_list(
    Context &ctx, urq_data_reference_ptr_list_t *&dst,
    RepeatedPtrField<DataReference> const &proto)
{
    urq_used(ctx);
    if (proto.empty()) {
        return 0;
    }
    ListBuilder<urq_data_reference_t *> builder(
        urq_data_reference_free_inplace);
    if (builder.set_capacity((size_t)proto.size())) {
        return -1;
    }

    for (auto const &data_reference : proto) {
        urq_data_reference_t *_data_reference = nullptr;
        if (set_data_reference(ctx, _data_reference, data_reference)) {
            return -1;
        }
        builder.push(_data_reference);
    }

    builder.build(dst->size, dst->data);
    return 0;
}

} // namespace urq::parse

#endif
