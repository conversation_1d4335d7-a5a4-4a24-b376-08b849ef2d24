#pragma once

#include "urq/position_offset.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__SET_POS_OFFSET_HPP
#define URQ_PARSE__SET_POS_OFFSET_HPP

namespace urq::parse {

static inline void set_pos_offset(
    const urq_widget_parse_context_t &ctx, urq_position_offset_t *&dst,
    PositionOffset const &proto)
{
    urq_used(ctx);
    if (dst == nullptr) {
        dst = malloc<urq_position_offset_t>();
        if (dst == nullptr) {
            return;
        }
        urq_position_offset_init_inplace(dst);
    }
    dst->left = (lv_coord_t)proto.left_i16();
    dst->top = (lv_coord_t)proto.top_i16();
    dst->right = (lv_coord_t)proto.right_i16();
    dst->bottom = (lv_coord_t)proto.bottom_i16();
}

static inline void set_pos_offset(
    Context &ctx, urq_position_offset_t *&dst, PositionOffset const &proto)
{
    set_pos_offset(ctx.get_context(), dst, proto);
}

} // namespace urq::parse
#endif // URQ_PARSE__SET_POS_OFFSET_HPP
