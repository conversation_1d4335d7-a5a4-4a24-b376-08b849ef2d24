#include "urq_parse/widget_group.h"
#include "urq/preload.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/set_node_list.hpp"
#include "znd/project/v1/widget.pb.h"

int urq_parse__widget_group(
    urq_page_id_t group_id,                          //
    const uint8_t *const data,                       //
    size_t size,                                     //
    const urq_widget_parse_context_t *const ref_ctx, //
    const urq_device_local_t *const ref_var_system,  //
    // const urq_atag_project_t *const ref_atags,       //
    urq_widget_group_conf_t *const out_conf //
    )                                       //
{
    urq::parse::Context ctx(*ref_ctx, *ref_var_system, group_id);
    znd::project::v1::PageWidgets conf;
    try {
        conf.ParseFromArray(data, (int)size);
    } catch (const std::exception &e) {
        errno = EINVAL;
        return -1;
    }

    if (urq::parse::set_node_list(ctx, out_conf->widgets, conf.widgets())) {
        return -1;
    }

    return 0;
}
