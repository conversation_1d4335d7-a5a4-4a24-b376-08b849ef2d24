#pragma once

#include "urq/display/conf.h"
#include "urq/page/conf_map.h"
#include "urq/page/group_conf.h"
#include "urq/preload.h"
#include "urq/rotate.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/ListBuilder.hpp"
#include "urq_parse/common/set_style.hpp"
#include "urq_parse/set_page.hpp"
#include "urq_parse/util.hpp"
#include "urq_parse/widget/keyboard.hpp"
#include "znd/project/v1/display.pb.h"
#include <cstddef>

#ifndef URQ_PARSE__SET_DISPLAY_HPP
#define URQ_PARSE__SET_DISPLAY_HPP
namespace urq::parse {

/// @brief 设置单个 display
/// @param skip 是否跳过
/// @param conf  输出显示配置，需要已经初始化过
/// @param proto 显示配置
static inline int set_display(
    Context &ctx, urq_display_conf_t *conf,
    const znd::project::v1::Display &proto)
{
    urq_used(ctx);
    conf->main_page_id = (uint16_t)proto.main_page_id_u16();
    if (proto.has_common_page_id_u16())
        conf->common_page_id = (uint16_t)proto.common_page_id_u16();
    conf->rotate_type = (urq_rotate_type_t)proto.rotate();
    if (proto.has_size()) {
        conf->size.h = (lv_coord_t)proto.size().height_i16();
        conf->size.w = (lv_coord_t)proto.size().width_i16();
    }

    if (proto.name().size() > 0) {
        // conf->name = urq_strdup(proto.name().c_str());
    }

    if(proto.has_style()) 
        ::urq::parse::common::set_style(ctx, conf->style, proto.style());

    return 0;
}

/// @brief 设置显示配置
/// @param skip 是否跳过
/// @param conf  输出显示配置，需要已经初始化过
/// @param proto 显示配置
static inline int set_display(
    Context &ctx, urq_display_conf_t *conf,
    const znd::project::v1::DisplayList &proto)
{
    if (proto.displays_size() < 0) {
        return -1;
    }

    uint8_t display_id = (uint8_t)proto.default_display_id_u8();
    int8_t id = ctx.get_context().var_sys.display_id;
    if(id != -1)
        display_id = (uint8_t)id;

    /// set display
    for (const auto &iter : proto.displays()) {
        if (display_id == (int32_t)iter.first) {
            set_display(ctx, conf, iter.second);
            break;
        }
    }

    // set page
    if (conf->pages != NULL) {
        urq_page_group_conf_map_free(conf->pages);
    }

    conf->pages = urq_page_group_conf_map_new();
    urq_page_group_conf_map_resize(
        conf->pages, (uint32_t)proto.page_infos_size());

    for (const auto &iter : proto.page_infos()) {
        urq_page_group_conf_t dst;
        urq_page_group_conf_init_inplace(&dst);
        if (set_page(ctx, dst, (urq_page_id_t)iter.first, iter.second)) {
            return -1;
        }

        if (iter.second.has_keyboard_info()) {
            if (conf->keyboard_map != NULL) {
                urq_keyboard_map_free(conf->keyboard_map);
            }
            conf->keyboard_map = urq_keyboard_map_new();
            urq_keyboard_info_t *info = NULL;
            ::urq::parse::widget::set_keyboard_info(
                ctx, info, iter.second.keyboard_info());
            if (urq_keyboard_map_add(
                    conf->keyboard_map, (uint32_t)iter.first, info)) {
                return -1;
            }
        }

        if (dst.size.w == -1 || dst.size.h == -1) {
            dst.size.w = (lv_coord_t)iter.second.size().width_i16();
            dst.size.h = (lv_coord_t)iter.second.size().height_i16();
        }
        urq_page_group_conf_map_add(conf->pages, (uint32_t)iter.first, &dst);
    }

    return 0;
}

} // namespace urq::parse

#endif // URQ_PARSE__SET_DISPLAY_HPPISPLAY_LIST_HPP