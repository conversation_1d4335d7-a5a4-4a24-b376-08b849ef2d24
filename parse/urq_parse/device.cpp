#include "urq_parse/device.h"
#include "klib/khash.h"
#include "urq/device/conf.h"
#include "urq/device/map.h"
#include "urq/log/debug_items.hpp"
#include "urq/log/verbose.hpp"
#include "urq/preload.h"
#include "znd/project/v1/device.pb.h"
#include <cstddef>

using znd::project::v1::TunnelDeviceList;

/// @brief 解析设备列表
///
/// @param ref_file 设备配置文件
/// @returns 设备列表
urq_device_map_t *urq_parse__device(const uint8_t *const data, size_t size)
{
    int ret;

    TunnelDeviceList device_list;
    if (device_list.ParseFromArray(data, (int)size) == false) {
        return NULL;
    }

    urq_device_map_t *map = kh_init(urq_device_map);
    if (map == NULL) {
        return NULL;
    }
    kh_resize(urq_device_map, map, device_list.devices_size());
    log_v("buckets: ", kh_n_buckets(map), log_endl);

    // TODO 设备no
    for (auto &iter : device_list.devices()) {
        auto &conf = iter.second;
        urq_used(conf);
        // log_v("parse device: ", conf.no(), log_endl);
        khint_t k = kh_put(urq_device_map, map, iter.first, &ret);
        if (ret == -1) {
            kh_destroy(urq_device_map, map);
            return NULL;
        }
        urq_device_conf_t *dev = &kh_val(map, k);
        // dev->no = conf.no();
        dev->id = (urq_device_id_t)iter.first;
    }

    // TODO
    // for (auto &iter : device_list.device_no_map()) {
    //     khint_t k = kh_put(urq_device_map, map, iter.second, &ret);
    //     if (ret == -1) {
    //         kh_destroy(urq_device_map, map);
    //         return NULL;
    //     }
    //     urq_device_conf_t *dev = &kh_val(map, k);
    //     dev->no = iter.first;
    // }

    return reinterpret_cast<urq_device_map_t *>(map);
}