#pragma once

#include "google/protobuf/message_lite.h"
#include "lvgl.h"
#include "urq/preload.h"
#include "urq/todo.h"
#include "znd/project/v1/action.pb.h"
#include "znd/project/v1/active.pb.h"
// #include "znd/project/v1/property.pb.h"
#include "znd/project/v1/style.pb.h"
#include "znd/project/v1/widget.pb.h"
#include <cerrno>
#include <znd/project/v1/control.pb.h>
#include <znd/project/v1/variable.pb.h>

#ifndef URQ_PARSE__UTIL_HPP
#define URQ_PARSE__UTIL_HPP
namespace urq::parse {

using StyleProperties = ::znd::project::v1::StyleProperties;
using StyleBorder = ::znd::project::v1::StyleBorder;

using ::google::protobuf::Map;
using ::google::protobuf::RepeatedPtrField;
using ::znd::project::v1::Action;
using ::znd::project::v1::ActionBitOpera;
using ::znd::project::v1::ActionCopyDataParam;
using ::znd::project::v1::ActionHttp;
// using ::znd::project::v1::ActionKey;
using ::znd::project::v1::ActionPage;
using ::znd::project::v1::ActionPlaySound;
using ::znd::project::v1::ActionPushMqtt;
// using ::znd::project::v1::ActionScreenshotParam;
using ::znd::project::v1::ActionScript;
using ::znd::project::v1::ActionSystem;
using ::znd::project::v1::ActionTiming;
// using ::znd::project::v1::ActionWordOpera;
using ::znd::project::v1::AllTypeValue;
// using ::znd::project::v1::DataCompareCondition;
using ::znd::project::v1::DataReference;
using ::znd::project::v1::Direction;
using ::znd::project::v1::EnableControl;
// using ::znd::project::v1::FontProperties;
using ::znd::project::v1::NumberValue;
using ::znd::project::v1::PositionOffset;
using ::znd::project::v1::Widget;

template <typename T> inline T *malloc() { return (T *)urq_malloc(sizeof(T)); }

template <typename T> inline T *malloc(size_t count)
{
    return (T *)urq_malloc(sizeof(T) * count);
}

template <typename T> inline int malloc(T *&out)
{
    out = (T *)urq_malloc(sizeof(T));
    if (out == nullptr) {
        return -1;
    }
    return 0;
}

inline double get_data(NumberValue const &proto)
{
    switch (proto.from_case()) {
    case NumberValue::kValueI8:
        return static_cast<double>(proto.value_i8());
    case NumberValue::kValueU8:
        return static_cast<double>(proto.value_u8());
    case NumberValue::kValueI16:
        return static_cast<double>(proto.value_i16());
    case NumberValue::kValueU16:
        return static_cast<double>(proto.value_u16());
    case NumberValue::kValueI32:
        return static_cast<double>(proto.value_i32());
    case NumberValue::kValueU32:
        return static_cast<double>(proto.value_u32());
    case NumberValue::kValueFloat:
        return static_cast<double>(proto.value_float());
    case NumberValue::kValueDouble:
        return static_cast<double>(proto.value_double());
    default:
        return 0;
    }
}

} // namespace urq::parse
#endif // URQ_PARSE__UTIL_HPP