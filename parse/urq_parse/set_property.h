#pragma once

#include "urq/fs/file.h"
#include "urq/widget/map_property.h"
#include "urq/widget/parse_context.h"

#ifndef URQ_PARSE__SET_PROPERTY_H
#define URQ_PARSE__SET_PROPERTY_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 解析页面属性
/// @param context 上下文
/// @param property_map 属性映射
/// @param ref_file 文件
/// @return 是否成功
int urq_parse__page_property(
    urq_widget_parse_context_t *context,
    urq_widget_property_map_t *property_map, urq_fs_file_t *ref_file)
    __attribute__((__nonnull__(1, 2, 3)))
    __attribute__((__warn_unused_result__()));

#ifdef __cplusplus
}
#endif

#endif // URQ_PARSE__SET_PROPERTY_H
