#pragma once

#include "urq/data/condition_list.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/ListBuilder.hpp"
#include "urq_parse/set_data_all_type.hpp"
#include "urq_parse/util.hpp"
#include <cstddef>
#ifndef URQ__PARSE__SET_CONDITION_LIST_HPP
#define URQ__PARSE__SET_CONDITION_HPP

#ifdef __cplusplus
extern "C" {
#endif

namespace urq::parse {

static inline int set_condition(
    Context &ctx, urq_data_condition_t *dst, DataCompareCondition const &proto)
{
    urq_used(ctx);

    if (proto.has_left_value()) {
        set_data_all_type(ctx, dst->left_value, proto.left_value());
    }
    if (proto.has_right_value1()) {
        set_data_all_type(ctx, dst->right_value1, proto.right_value1());
    }
    if (proto.has_right_value2()) {
        set_data_all_type(ctx, dst->right_value2, proto.right_value2());
    }

    dst->comp_type = (urq_data_comp_type_t)proto.compare_type();
    dst->left_parentheses = (int8_t)proto.left_parentheses_u8();
    dst->right_parentheses = (int8_t)proto.right_parentheses_u8();
    dst->logic_type = (urq_data_logic_type_t)proto.prefix_logic_type();

    return 0;
}

static inline int set_condition_list(
    Context &ctx, urq_data_condition_list_t *&dst,
    RepeatedPtrField<DataCompareCondition> const &proto)
{
    urq_used(ctx);
    if (proto.empty()) {
        return 0;
    }
    if (dst == NULL) {
        dst = malloc<urq_data_condition_list_t>();
        if (dst == NULL) {
            return -1;
        }
        urq_data_condition_list_init_inplace(dst);
    }

    ListBuilder<urq_data_condition_t *> builder(
        urq_data_condition_free_inplace);
    if (builder.set_capacity((size_t)proto.size())) {
        return -1;
    }

    for (auto const &condition : proto) {
        urq_data_condition_t *_condition = malloc<urq_data_condition_t>();
        if (_condition == NULL) {
            return -1;
        }
        if (set_condition(ctx, _condition, condition)) {
            return -1;
        }
        builder.push(_condition);
    }

    builder.build(dst->size, dst->conditions);

    return 0;
}

} // namespace urq::parse

#ifdef __cplusplus
}
#endif
#endif // URQ__PARSE__SET_CONDITION_LIST_HPP
