#pragma once

#include "urq_parse/util.hpp"
#include <cstddef>
#include <stdexcept>
#include <type_traits>

#ifndef URQ_PARSE__LISTBUILDER_HPP
#define URQ_PARSE__LISTBUILDER_HPP
namespace urq::parse {

template <typename N> static inline void noop(N ptr) { urq_used(ptr); }

/// @brief 列表构建器
/// @tparam T 元素类型
template <typename T> class ListBuilder {

    template <typename S = T>
    using FreeInplace =
        std::conditional_t<std::is_pointer_v<S>, void (*)(T), void (*)(T *)>;

    /// @brief 列表
    T *m_list{nullptr};
    /// @brief 容量
    size_t m_capacity{0};
    /// @brief 大小
    size_t m_size{0};
    /// @brief 元素释放函数
    FreeInplace<T> m_free_inplace{nullptr};

public:
    /// @brief 构造函数
    /// @tparam S 元素类型
    /// @param free_inplace 元素释放函数
    template <typename S = T, std::enable_if_t<std::is_pod_v<S>, int> = 0>
    inline ListBuilder() noexcept : m_free_inplace(noop)
    {
    }

    /// @brief 构造函数
    /// @tparam S 元素类型
    /// @param free_inplace 元素释放函数
    inline ListBuilder(FreeInplace<T> free_inplace) noexcept
        : m_free_inplace(free_inplace)
    {
    }

    /// @brief 析构函数
    inline ~ListBuilder() noexcept
    {
        if constexpr (std::is_pointer_v<T>) {
            if (m_capacity > 0) {
                for (size_t i = 0; i < m_size; i++) {
                    m_free_inplace(m_list[i]);
                    urq_free(m_list[i]);
                }
                urq_free(m_list);
            }
        } else {
            if (m_capacity > 0) {
                for (size_t i = 0; i < m_size; i++) {
                    m_free_inplace(&m_list[i]);
                }
                urq_free(m_list);
            }
        }
    }

    /// @brief 预先分配外部容器的内存
    ///
    /// 确保外部容器内存分配成功
    ///
    /// 允许传入0
    /// @param capacity 容量
    /// @returns 0 成功，-1 失败
    inline int set_capacity(size_t capacity) noexcept
    {
        m_capacity = capacity;
        if (capacity == 0) {
            return 0;
        }
        m_list = urq::parse::malloc<T>(capacity);
        if (m_list == nullptr) {
            return -1;
        }
        return 0;
    }

    /// @brief 获取下一个元素的引用
    /// @tparam S 元素类型
    /// @returns 元素
    template <typename S = T>
    inline std::enable_if_t<!std::is_pointer_v<S>, T &> &next()
    {
        if (m_size == m_capacity) {
            throw std::runtime_error("out of capacity");
        }
        m_size++;
        return m_list[m_size - 1];
    }

    /// @brief 添加元素
    /// @tparam S 元素类型
    /// @param ptr 元素
    template <typename S = T>
    inline std::enable_if_t<std::is_pointer_v<S> || std::is_pod_v<S>, void>
    push(T ptr)
    {
        if (m_size == m_capacity) {
            throw std::runtime_error("out of capacity");
        }
        m_list[m_size] = ptr;
        m_size++;
    }

    /// @brief 构建列表
    /// @param size 大小
    /// @param list 列表
    template <typename S, std::enable_if_t<std::is_integral_v<S>, int> = 0>
    inline void build(S &size, T *&list) noexcept
    {
        size = (S)m_size;
        list = m_list;
        m_list = nullptr;
        m_size = 0;
        m_capacity = 0;
    }
};

} // namespace urq::parse
#endif // URQ_PARSE__LISTBUILDER_HPP
