#include "urq_parse/set_property.h"
#include "urq/widget/map_property.h"
#include "urq/widget/property_type.h"
#include "urq_parse/util.hpp"

namespace urq::parse {

// using znd::project::v1::AC;

// static inline int set_page_property(
//     urq_widget_parse_context_t &ctx, urq_widget_property_map_t *property_map,
//     urq_fs_file_t *file) noexcept
// {
//     urq_used(ctx);
//     PageWidgetPropertyAddress page_conf;
//     try {
//         page_conf.ParseFromArray(urq_fs_file_data(file), (int)file->size);
//     } catch (const std::exception &e) {
//         errno = EINVAL;
//         return -1;
//     }
//
//     for (auto &it : page_conf.widget_properties()) {
//         uint16_t widget_id = (uint16_t)it.first; // 图元ID
//         for (auto &addrs : it.second.address()) {
//             uint32_t v = (uint32_t)(widget_id << 24);
//             v |= (uint32_t)(addrs.first << 8);
//
//             // TODO atag 初始化
//             urq_atag_t atag;
//             // 属性+索引 tag
//             urq_widget_property_map_add(property_map, v, &atag);
//         }
//     }
//
//     return 0;
// }

} // namespace urq::parse

int urq_parse__page_property(
    urq_widget_parse_context_t *context,
    urq_widget_property_map_t *property_map, urq_fs_file_t *ref_file)
{
    urq_used(context);
    urq_used(property_map);
    urq_used(ref_file);
    return 0;
    //     return urq::parse::set_page_property(*context, property_map,
    //     ref_file);
}
