#pragma once

#include "urq/marquee.h"
#include "urq/preload.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"
#include <cstdint>

#ifndef URQ_PARSE__SET_MARQUEE_HPP
#define URQ_PARSE__SET_MARQUEE_HPP

namespace urq::parse {

static inline void set_marquee(
    const urq_widget_parse_context_t &ctx, urq_marquee_t *&dst,
    znd::project::v1::MarqueeDefine const &proto)
{
    urq_used(ctx);
    if (dst == nullptr) {
        dst = malloc<urq_marquee_t>();
        if (dst == nullptr) {
            return;
        }
        urq_marquee_init(dst);
    }

    dst->enable = proto.enable();
    dst->interval = (uint8_t)proto.interval_time_u8();
    dst->scroll_distance = (uint16_t)proto.scroll_distance_u16();

    dst->scroll_direction = (urq_scroll_direction_t)proto.scroll_direction();
    dst->loop = proto.loop();
}

static inline void set_marquee(
    Context &ctx, urq_marquee_t *&dst,
    znd::project::v1::MarqueeDefine const &proto)
{
    set_marquee(ctx.get_context(), dst, proto);
}

} // namespace urq::parse
#endif // URQ_PARSE__SET_MARQUEE_HPP
