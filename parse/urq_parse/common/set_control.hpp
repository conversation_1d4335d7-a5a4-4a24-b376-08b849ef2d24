
#pragma once

#include "urq/widget/control.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__COMMON__SET_CONTROL__HPP
#define URQ_PARSE__COMMON__SET_CONTROL__HPP

namespace urq::parse::common {

static inline int set_control(
    Context &ctx, urq_widget_control_conf_t *&ptr,
    const znd::project::v1::EnableControl &proto) noexcept
{
    urq_used(ctx);
    ptr = malloc<urq_widget_control_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_widget_control_conf_init_inplace(ptr);

    // set_condition_list(ctx, ptr->data_conditions, proto.data_conditions());

    ptr->permission_bits =
        malloc<urq_u8_list_t>((size_t)proto.permission_bits_u8_size());
    if (ptr->permission_bits == nullptr) {
        return -1;
    }
    urq_u8_list_init_inplace(ptr->permission_bits);
    for (int i = 0; i < proto.permission_bits_u8_size(); i++) {
        ptr->permission_bits->data[i] = (uint8_t)proto.permission_bits_u8(i);
    }

    ptr->terminal_location = malloc<urq_u8_list_t>(
        (size_t)proto.terminal_location_size() * sizeof(uint8_t));
    if (ptr->terminal_location == nullptr) {
        return -1;
    }
    urq_u8_list_init_inplace(ptr->terminal_location);
    for (int i = 0; i < proto.terminal_location_size(); i++) {
        ptr->terminal_location->data[i] =
            (urq_terminal_location_t)proto.terminal_location(i);
    }

    ptr->terminal_type = malloc<urq_u8_list_t>(
        (size_t)proto.terminal_types_size() * sizeof(uint8_t));
    if (ptr->terminal_type == nullptr) {
        return -1;
    }
    urq_u8_list_init_inplace(ptr->terminal_type);
    for (int i = 0; i < proto.terminal_types_size(); i++) {
        ptr->terminal_type->data[i] =
            (urq_terminal_type_t)proto.terminal_types(i);
    }

    ptr->disable_widget_mode = (urq_disable_widget_mode_t)proto.disable_mode();
    ptr->page_id = (urq_page_id_t)proto.disable_prompt_page_id_u16();
    return 0;
}

} // namespace urq::parse::common

#endif // #ifndef URQ_PARSE__COMMON__SET_CONTROL__HPP
