
#pragma once

#include "urq/preload.h"
#include "urq/style/bar.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_style.hpp"

#ifndef URQ_PARSE__COMMON__SET_BAR_HPP
#define URQ_PARSE__COMMON__SET_BAR_HPP

namespace urq::parse::common {

inline static int set_bar(
    Context &ctx, urq_style_bar_t *&dst,
    znd::project::v1::BarConfig const &proto)
{
    urq_used(ctx);

    if (dst == NULL) {
        dst = (urq_style_bar_t *)urq_malloc(sizeof(urq_style_bar_t));
        urq_style_bar_init_inplace(dst);
    }

    if (proto.has_style()) {
        set_style(ctx, dst->style, proto.style());
    }
    dst->round = (uint8_t)proto.round_u8();
    dst->width = (uint8_t)proto.width_u8();

    return 0;
}

} // namespace urq::parse::common
#endif // URQ_PARSE__COMMON__SET_BAR_HPP