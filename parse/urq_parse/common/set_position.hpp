
#pragma once

#include "lvgl.h"
#include "urq/position.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"
#include <sys/types.h>

#ifndef URQ_PARSE__SET_POSITION_HPP
#define URQ_PARSE__SET_POSITION_HPP
namespace urq::parse::common {

/// @brief 设置位置
inline static int set_position(
    Context &ctx, urq_position_t &dst,
    ::znd::project::v1::Location const &proto)
{
    urq_used(ctx);
    dst.x = (lv_coord_t)proto.left();
    dst.y = (lv_coord_t)proto.top();

    return 0;
}

} // namespace urq::parse::common
#endif // URQ_PARSE__SET_FONT_HPP
