
#pragma once

#include "urq/data/reference.h"
#include "urq/preload.h"
#include "urq/conf/list/data.h"
#include "urq/widget/state.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"
#include <cstddef>
#include <cstdint>
#include <znd/project/v1/widget.pb.h>

#ifndef URQ_PARSE__COMMON__SET_STATE_PROPERTY__HPP
#define URQ_PARSE__COMMON__SET_STATE_PROPERTY__HPP

namespace urq::parse::common {

static inline int set_state_property(
    Context &ctx, urq_widget_state_property_t *&ptr,
    const znd::project::v1::StateProperty &proto) noexcept
{
    ptr = malloc<urq_widget_state_property_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_widget_state_property_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);

    if (proto.has_auto_switch_state())
        ptr->auto_switch = proto.auto_switch_state().auto_switch();
    if (proto.has_error_state_u8())
        ptr->error_state = (uint8_t)proto.error_state_u8();
    ptr->state_count = (uint8_t)proto.state_count_u8();

    ptr->no_state_way = (urq_no_state_way_t)proto.no_state_way();
    // ptr->state_lsb = proto.state_lsb();
    ptr->state_offset_value = (int16_t)proto.state_offset_value_i16();

    if (proto.state_match_value_i16().size() > 0) {
        ptr->state_match_value =
            (urq_i16_list_t *)urq_malloc(sizeof(urq_i16_list_t));
        ptr->state_match_value->size =
            (size_t)proto.state_match_value_i16().size();
        ptr->state_match_value->data = (int16_t *)urq_malloc(
            sizeof(int16_t) * ptr->state_match_value->size);
        for (size_t idx = 0; idx < ptr->state_match_value->size; ++idx) {
            ptr->state_match_value->data[idx] =
                (int16_t)proto.state_match_value_i16().at((int)idx);
        }
    }
    if (proto.state_range_value_i16().size() > 0) {
        ptr->state_range_value =
            (urq_i16_list_t *)urq_malloc(sizeof(urq_i16_list_t));
        ptr->state_range_value->size =
            (size_t)proto.state_range_value_i16().size();
        ptr->state_range_value->data = (int16_t *)urq_malloc(
            sizeof(int16_t) * ptr->state_range_value->size);
        for (size_t idx = 0; idx < ptr->state_range_value->size; ++idx) {
            ptr->state_range_value->data[idx] =
                (int16_t)proto.state_range_value_i16().at((int)idx);
        }
    }
    if (proto.state_combine_bit().size() > 0) {
        ptr->state_combine_bit =
            (urq_bool_list_t *)urq_malloc(sizeof(urq_bool_list_t));
        ptr->state_combine_bit->size = (size_t)proto.state_combine_bit().size();
        ptr->state_combine_bit->data =
            (bool *)urq_malloc(sizeof(bool) * ptr->state_combine_bit->size);
        for (size_t idx = 0; idx < ptr->state_combine_bit->size; ++idx) {
            ptr->state_combine_bit->data[idx] =
                proto.state_combine_bit().at((int)idx);
        }
    }
    return 0;
}

} // namespace urq::parse::common

#endif // #ifndef URQ_PARSE__WIDGET__IMAGE_HPP
