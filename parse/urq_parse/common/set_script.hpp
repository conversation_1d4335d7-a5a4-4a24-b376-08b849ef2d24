
#pragma once

#include "urq/preload.h"
#include "urq/script.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__COMMON__SET_SCRIPT_HPP
#define URQ_PARSE__COMMON__SET_SCRIPT_HPP

namespace urq::parse::common {

// inline static int set_script(
//     Context &ctx, urq_script_t *&dst, znd::project::v1::Script const &proto)
//{
//     urq_used(ctx);
//
//     if (dst == NULL) {
//         dst = malloc<urq_script_t>();
//         if (dst == NULL) {
//             return -1;
//         }
//         urq_script_init_inplace(dst);
//     }
//
//     // TODO
//     // for (auto const &condition : proto.condition()) {
//     //     urq_data_condition_list_add_condition(
//     //         dst->conditions, condition.data_reference(),
//     //         condition.compare_type(), condition.compare_value());
//     // }
//
//     if (proto.has_type()) {
//         dst->type = (urq_script_type_t)proto.type();
//     }
//
//     if (proto.has_code()) {
//         dst->code = proto.code().c_str();
//     }
//
//     return 0;
// }

} // namespace urq::parse::common
#endif // URQ_PARSE__COMMON__SET_COLOR_HPP