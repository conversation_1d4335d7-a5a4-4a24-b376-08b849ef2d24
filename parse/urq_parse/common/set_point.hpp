
#pragma once

#include "urq/preload.h"
#include "urq/style/point.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_style.hpp"

#ifndef URQ_PARSE__COMMON__SET_POINT_HPP
#define URQ_PARSE__COMMON__SET_POINT_HPP

namespace urq::parse::common {

inline static int set_point(
    Context &ctx, urq_style_point_t *&dst,
    znd::project::v1::PointConfig const &proto)
{
    if (dst == NULL) {
        dst = (urq_style_point_t *)urq_malloc(sizeof(urq_style_point_t));
        urq_style_point_init_inplace(dst);
    }

    set_style(ctx, dst->style, proto.style());
    dst->radius = (uint8_t)proto.radius_u8();
    dst->width = (uint8_t)proto.width_u8();
    dst->height = (uint8_t)proto.height_u8();

    return 0;
}

} // namespace urq::parse::common
#endif // URQ_PARSE__COMMON__SET_POINT_HPP