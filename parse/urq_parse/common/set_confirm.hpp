

#pragma once

#include "urq/confirm_win.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__COMMON__SET_CONFIRM__HPP
#define URQ_PARSE__COMMON__SET_CONFIRM__HPP

namespace urq::parse::common {

static inline int set_confirm(
    Context &ctx, urq_confirm_win_t &ptr,
    const znd::project::v1::ConfirmParam &proto) noexcept
{
    urq_used(ctx);
    urq_confirm_win_init_inplace(&ptr);

    ptr.id = (uint16_t)proto.confirm_page_id_u16();
    ptr.wait_time = (uint16_t)proto.confirm_wait_time_u16();
    ptr.timeout_run = proto.confirm_timeout_run();

    return 0;
}

} // namespace urq::parse::common

#endif // #ifndef URQ_PARSE__COMMON__SET_CONTROL__HPP
