#pragma once

#include "urq/curve/scale.h"
#include "urq/preload.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_line.hpp"
#include "urq_parse/common/set_style.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__COMMON__SET_SCALE_HPP
#define URQ_PARSE__COMMON__SET_SCALE_HPP

namespace urq::parse::common {

inline static int set_scale(
    Context &ctx, urq_curve_scale_t &dst,
    znd::project::v1::ScaleValueConfig const &proto)
{
    urq_used(ctx);

    dst.show_type = (urq_curve_scale_show_type_t)proto.scale_show_type();

    // color width length num
    ::urq::parse::common::set_color(
        ctx, dst.main_color, proto.scale_main().color());
    ::urq::parse::common::set_color(
        ctx, dst.sub_color, proto.scale_sec().color());
    dst.main_tick_count = (uint8_t)proto.scale_main().scale_count_u8();
    dst.sub_tick_count = (uint8_t)proto.scale_sec().scale_count_u8();
    dst.main_tick_len = (uint8_t)proto.scale_main().scale_draw_len();
    dst.sub_tick_len = (uint8_t)proto.scale_sec().scale_draw_len();
    dst.main_tick_width = (uint8_t)proto.scale_main().scale_width_u8();
    dst.sub_tick_width = (uint8_t)proto.scale_sec().scale_width_u8();

    if (proto.grid_show_type() ==
        znd::project::v1::ScaleValueConfig_GridShowType_GRID_SHOW_TYPE_MAIN) {
        dst.grid_line_cnt = dst.main_tick_count;
    } else {
        dst.grid_line_cnt =
            (uint8_t)(dst.main_tick_count +
                      (dst.main_tick_count - 1) * (dst.sub_tick_count - 1));
    }

    if (proto.has_max_value()) {
        dst.max_value = (lv_coord_t)proto.max_value().value_i32();
    }
    if (proto.has_min_value()) {
        dst.min_value = (lv_coord_t)proto.min_value().value_i32();
    }

    if (proto.has_grid_line_config()) {
        ::urq::parse::common::set_line(ctx, dst.line, proto.grid_line_config());
    }

    return 0;
}

inline static int set_scale(
    Context &ctx, urq_curve_scale_t *&dst,
    znd::project::v1::ScaleValueConfig const &proto)
{
    urq_used(ctx);

    if (dst == NULL) {
        dst = (urq_curve_scale_t *)urq_malloc(sizeof(urq_curve_scale_t));
        urq_curve_scale_init_inplace(dst);
    }

    dst->show_type = (urq_curve_scale_show_type_t)proto.scale_show_type();
    ::urq::parse::common::set_color(
        ctx, dst->main_color, proto.scale_main().color());
    ::urq::parse::common::set_color(
        ctx, dst->sub_color, proto.scale_sec().color());
    dst->main_tick_count = (uint8_t)proto.scale_main().scale_count_u8();
    dst->sub_tick_count = (uint8_t)proto.scale_sec().scale_count_u8();
    dst->main_tick_len = (uint8_t)proto.scale_main().scale_draw_len();
    dst->sub_tick_len = (uint8_t)proto.scale_sec().scale_draw_len();
    dst->main_tick_width = (uint8_t)proto.scale_main().scale_width_u8();
    dst->sub_tick_width = (uint8_t)proto.scale_sec().scale_width_u8();

    if (proto.grid_show_type() ==
        znd::project::v1::ScaleValueConfig_GridShowType_GRID_SHOW_TYPE_MAIN) {
        dst->grid_line_cnt = dst->main_tick_count;
    } else {
        dst->grid_line_cnt =
            (uint8_t)(dst->main_tick_count +
                      (dst->main_tick_count - 1) * (dst->sub_tick_count - 1));
    }

    if (proto.has_max_value()) {
        dst->max_value = (lv_coord_t)proto.max_value().value_i32();
    }
    if (proto.has_min_value()) {
        dst->min_value = (lv_coord_t)proto.min_value().value_i32();
    }

    if (proto.has_grid_line_config()) {
        ::urq::parse::common::set_line(
            ctx, dst->line, proto.grid_line_config());
    }

    return 0;
}

} // namespace urq::parse::common
#endif // URQ_PARSE__COMMON__SET_COLOR_HPP