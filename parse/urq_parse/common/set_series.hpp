#pragma once

#include "urq/curve/series.h"
#include "urq/preload.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/ListBuilder.hpp"
#include "urq_parse/common/set_bar.hpp"
#include "urq_parse/common/set_line.hpp"
#include "urq_parse/common/set_point.hpp"
#include "urq_parse/common/set_style.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__COMMON__SET_SERIES_HPP
#define URQ_PARSE__COMMON__SET_SERIES_HPP

namespace urq::parse::common {

inline static int set_series(
    Context &ctx, urq_curve_series_t *&dst,
    znd::project::v1::WidgetCurve::SeriesConfig const &proto)
{
    urq_used(ctx);

    if (dst == NULL) {
        dst = (urq_curve_series_t *)urq_malloc(sizeof(urq_curve_series_t));
        urq_curve_series_init_inplace(dst);
    }

    set_color(ctx, dst->color, proto.color());
    if (proto.has_line_config()) {
        set_line(ctx, dst->line, proto.line_config());
    }
    if (proto.has_point_config()) {
        set_point(ctx, dst->point, proto.point_config());
    }
    if (proto.has_bar_config()) {
        set_bar(ctx, dst->bar, proto.bar_config());
    }

    if (proto.has_y_max_value()) {
        dst->y_max_value = (lv_coord_t)proto.y_max_value().value_i32();
    }
    if (proto.has_y_min_value()) {
        dst->y_min_value = (lv_coord_t)proto.y_min_value().value_i32();
    }
    if (proto.has_x_max_value()) {
        dst->x_max_value = (lv_coord_t)proto.x_max_value().value_i32();
    }
    if (proto.has_x_min_value()) {
        dst->x_min_value = (lv_coord_t)proto.x_min_value().value_i32();
    }

    dst->show_fade_mask = proto.show_fade_mask();

    return 0;
}

/// @brief 设置样式列表
static inline int set_series_list(
    Context &ctx, urq_curve_series_ptr_list_t &conf,
    RepeatedPtrField<znd::project::v1::WidgetCurve::SeriesConfig> const
        &proto) noexcept
{
    ListBuilder<urq_curve_series_t *> builder(urq_curve_series_free_inplace);
    builder.set_capacity((size_t)proto.size());

    for (int i = 0; i < proto.size(); i++) {
        auto &style = proto[i];
        urq_curve_series_t *series = NULL;
        if (set_series(ctx, series, style)) {
            return -1;
        }
        builder.push(series);
    }

    builder.build(conf.size, conf.data);
    return 0;
}

/// @brief 设置样式列表
static inline int set_series_list_ptr(
    Context &ctx, urq_curve_series_ptr_list_t *&conf,
    RepeatedPtrField<znd::project::v1::WidgetCurve::SeriesConfig> const
        &proto) noexcept
{
    ListBuilder<urq_curve_series_t *> builder(urq_curve_series_free_inplace);
    builder.set_capacity((size_t)proto.size());

    if (conf == NULL) {
        conf = (urq_curve_series_ptr_list_t *)urq_malloc(
            sizeof(urq_curve_series_ptr_list_t));
        urq_curve_series_ptr_list_init_inplace(conf);
    }

    for (int i = 0; i < proto.size(); i++) {
        auto &style = proto[i];
        urq_curve_series_t *series = NULL;
        if (set_series(ctx, series, style)) {
            return -1;
        }
        builder.push(series);
    }

    builder.build(conf->size, conf->data);
    return 0;
}

} // namespace urq::parse::common
#endif // URQ_PARSE__COMMON__SET_COLOR_HPP