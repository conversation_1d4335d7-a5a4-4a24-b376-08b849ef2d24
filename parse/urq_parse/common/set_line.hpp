#pragma once

#include "urq/preload.h"
#include "urq/style/line.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_style.hpp"

#ifndef URQ_PARSE__COMMON__SET_LINE_HPP
#define URQ_PARSE__COMMON__SET_LINE_HPP

namespace urq::parse::common {

inline static int set_line(
    Context &ctx, urq_style_line_t &dst,
    znd::project::v1::LineConfig const &proto)
{
    urq_used(ctx);

    dst.width = (uint8_t)proto.width_u8();
    dst.dash_width = (uint8_t)proto.dash_width_u8();
    dst.dash_gap = (uint8_t)proto.dash_gap_u8();
    dst.round_start = proto.round_start();
    dst.round_end = proto.round_end();
    set_color(ctx, dst.color, proto.color());

    return 0;
}

inline static int set_line(
    Context &ctx, urq_style_line_t *&dst,
    znd::project::v1::LineConfig const &proto)
{
    urq_used(ctx);
    if (dst == NULL) {
        dst = (urq_style_line_t *)urq_malloc(sizeof(urq_style_line_t));
        urq_style_line_init_inplace(dst);
    }

    dst->width = (uint8_t)proto.width_u8();
    dst->dash_width = (uint8_t)proto.dash_width_u8();
    dst->dash_gap = (uint8_t)proto.dash_gap_u8();
    dst->round_start = proto.round_start();
    dst->round_end = proto.round_end();
    set_color(ctx, dst->color, proto.color());

    return 0;
}

} // namespace urq::parse::common
#endif // URQ_PARSE__COMMON__SET_POINT_HPP