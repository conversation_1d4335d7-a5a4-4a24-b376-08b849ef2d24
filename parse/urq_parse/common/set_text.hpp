#pragma once

#include "urq/preload.h"
#include "urq/text/ptr_list.h"
#include "urq/text/text.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/ListBuilder.hpp"
#include "urq_parse/common/set_font.hpp"

#ifndef URQ_PARSE__COMMON__SET_TEXT_HPP
#define URQ_PARSE__COMMON__SET_TEXT_HPP
namespace urq::parse::common {

/// @brief 设置配置
static inline int set_text(
    Context &ctx, urq_text_t *&dst,
    znd::project::v1::TextReference const &proto)
{
    urq_used(ctx);
    if (dst == NULL) {
        dst = (urq_text_t *)urq_malloc(sizeof(urq_text_t));
        if (dst == NULL) {
            return -1;
        }
        urq_text_init_inplace(dst);
    }

    if (proto.has_tag_id_u16()) {
        dst->content =
            strdup(ctx.get_string((urq_i18n_id_t)proto.tag_id_u16()));
        dst->tag_id = (int16_t)proto.tag_id_u16();
    } else if (proto.has_input()) {
        dst->tag_id = -1;
        if (proto.input().has_single_lang()) {
            dst->content = strdup(proto.input().single_lang().c_str());
        } else if (proto.input().has_multi_lang()) {
            auto &multi_lang = proto.input().multi_lang().content();
            auto it = multi_lang.find((uint32_t)ctx.language_id());
            if (it == multi_lang.end()) {
                return -1;
            }
            dst->content = strdup(it->second.c_str());
        }
    } else {
        return -1;
    }

    printf(
        "[set_text]: [content: %s] [tagid: %d]\n", dst->content, dst->tag_id);

    // if (proto.has_multi_lang()) {
    // auto font_map = proto.multi_lang().font();
    // auto it = font_map.find((uint32_t)ctx.language_id());
    // if (it != font_map.end()) {
    // set_font(ctx, dst->font, it->second);
    // }
    // } else if (proto.has_single_lang()) {
    // set_font(ctx, dst->font, proto.single_lang());
    // }

    return 0;
}

/// @brief 设置文本列表
/// @param ctx 编译上下文
/// @param dst 目标动作列表
/// @param src 源动作列表
/// @returns 0 on success, -1 on failure
static inline int set_text_list(
    Context &ctx, urq_text_ptr_list_t *&dst,
    RepeatedPtrField<znd::project::v1::TextReference> const &proto) noexcept
{
    if (proto.empty()) {
        return 0;
    }
    ListBuilder<urq_text_t *> builder(urq_text_free_inplace);
    if (builder.set_capacity((size_t)proto.size())) {
        return -1;
    }
    if (dst == NULL) {
        dst = (urq_text_ptr_list_t *)urq_malloc(sizeof(urq_text_ptr_list_t));
        urq_text_ptr_list_init_inplace(dst);
    }

    for (auto const &_text : proto) {
        urq_text_t *text = nullptr;
        if (set_text(ctx, text, _text)) {
            return -1;
        }
        builder.push(text);
    }
    builder.build(dst->size, dst->data);
    return 0;
}

} // namespace urq::parse::common

#endif // URQ_PARSE__SET_PAGE_HPP