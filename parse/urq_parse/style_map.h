#pragma once

#include "urq/fs/file.h"
#include "urq/widget/parse_context.h"

#ifndef URQ_PARSE__STYLE_MAP_H
#define URQ_PARSE__STYLE_MAP_H

#ifdef __cplusplus
extern "C" {
#endif

int urq_parse__style_map(
    urq_widget_parse_context_t *context, uint16_t theme,
    const uint8_t *const data, size_t size)
    __attribute__((__nonnull__(1, 3), __warn_unused_result__()));

#ifdef __cplusplus
}
#endif

#endif // URQ_PARSE__STYLE_MAP_H
