#pragma once

#include "urq/preload.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"
#include <znd/project/v1/common.pb.h>

#ifndef URQ_PARSE__SET_DIR_HPP
#define URQ_PARSE__SET_DIR_HPP
namespace urq::parse {

/// @brief 设置方向
static inline int set_dir(
    Context &ctx, lv_dir_t &conf, const znd::project::v1::Direction &proto)
{
    urq_used(ctx);
    switch (proto) {
    case znd::project::v1::DIRECTION_UNSPECIFIED:
        conf = LV_DIR_NONE;
        break;
    case znd::project::v1::DIRECTION_LEFT:
        conf = LV_DIR_LEFT;
        break;
    case znd::project::v1::DIRECTION_RIGHT:
        conf = LV_DIR_RIGHT;
        break;
    case znd::project::v1::DIRECTION_UP:
        conf = LV_DIR_TOP;
        break;
    case znd::project::v1::DIRECTION_DOWN:
        conf = LV_DIR_BOTTOM;
        break;
    default:
        return -1;
    }
    return 0;
}

} // namespace urq::parse

#endif // URQ_PARSE__SET_DISPLAY_HPPISPLAY_LIST_HPP