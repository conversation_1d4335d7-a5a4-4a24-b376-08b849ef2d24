
#pragma once

#include "urq/data/all_type.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__SET_DATA_ALL_TYPE_H
#define URQ_PARSE__SET_DATA_ALL_TYPE_H

namespace urq::parse {



static inline int set_number_value(
    Context &ctx, urq_data_all_type_t *&conf, const NumberValue &proto, bool is_ptr)
{
    urq_used(ctx);
    urq_used(is_ptr);

    conf = malloc<urq_data_all_type_t>();
    if (conf == NULL) {
        return -1;
    }
    urq_data_all_type_init_inplace(conf);
    switch (proto.from_case()) {
    case NumberValue::kValueI8:
        conf->data_format = DATA_ALL_TYPE_INT8;
        conf->int8_value = (int8_t)proto.value_i8();
        break;
    case NumberValue::kValueU8:
        conf->data_format = DATA_ALL_TYPE_UINT8;
        conf->uint8_value = (uint8_t)proto.value_u8();
        break;
    case NumberValue::kValueI16:
        conf->data_format = DATA_ALL_TYPE_INT16;
        conf->int16_value = (int16_t)proto.value_i16();
        break;
    case NumberValue::kValueU16:
        conf->data_format = DATA_ALL_TYPE_UINT16;
        conf->uint16_value = (uint16_t)proto.value_u16();
        break;
    case NumberValue::kValueI32:
        conf->data_format = DATA_ALL_TYPE_INT32;
        conf->int32_value = (int32_t)proto.value_i32();
        break;
    case NumberValue::kValueU32:
        conf->data_format = DATA_ALL_TYPE_UINT32;
        conf->uint32_value = (uint32_t)proto.value_u32();
        break;
    case NumberValue::kValueFloat:
        conf->data_format = DATA_ALL_TYPE_FLOAT;
        conf->float_value = (float)proto.value_float();
        break;
    case NumberValue::kValueDouble:
        conf->data_format = DATA_ALL_TYPE_DOUBLE;
        conf->double_value = proto.value_double();
        break;
    default:
        return -1;
    }
    return 0;
}


static inline int set_number_value(
    Context &ctx, urq_data_all_type_t &conf, const NumberValue &proto   )
{
    urq_used(ctx);

    switch (proto.from_case()) {
    case NumberValue::kValueI8:
        conf.data_format = DATA_ALL_TYPE_INT8;
        conf.int8_value = (int8_t)proto.value_i8();
        break;
    case NumberValue::kValueU8:
        conf.data_format = DATA_ALL_TYPE_UINT8;
        conf.uint8_value = (uint8_t)proto.value_u8();
        break;
    case NumberValue::kValueI16:
        conf.data_format = DATA_ALL_TYPE_INT16;
        conf.int16_value = (int16_t)proto.value_i16();
        break;
    case NumberValue::kValueU16:
        conf.data_format = DATA_ALL_TYPE_UINT16;
        conf.uint16_value = (uint16_t)proto.value_u16();
        break;
    case NumberValue::kValueI32:
        conf.data_format = DATA_ALL_TYPE_INT32;
        conf.int32_value = (int32_t)proto.value_i32();
        break;
    case NumberValue::kValueU32:
        conf.data_format = DATA_ALL_TYPE_UINT32;
        conf.uint32_value = (uint32_t)proto.value_u32();
        break;
    case NumberValue::kValueFloat:
        conf.data_format = DATA_ALL_TYPE_FLOAT;
        conf.float_value = (float)proto.value_float();
        break;
    case NumberValue::kValueDouble:
        conf.data_format = DATA_ALL_TYPE_DOUBLE;
        conf.double_value = proto.value_double();
        break;
    default:
        return -1;
    }
    return 0;
}

static inline int set_data_all_type(
    Context &ctx, urq_data_all_type_t &conf, const AllTypeValue &proto)
{
    urq_used(ctx);

    switch (proto.from_case()) {
    case AllTypeValue::kValueI8:
        conf.data_format = DATA_ALL_TYPE_INT8;
        conf.int8_value = (int8_t)proto.value_i8();
        break;
    case AllTypeValue::kValueU8:
        conf.data_format = DATA_ALL_TYPE_UINT8;
        conf.uint8_value = (uint8_t)proto.value_u8();
        break;
    case AllTypeValue::kValueI16:
        conf.data_format = DATA_ALL_TYPE_INT16;
        conf.int16_value = (int16_t)proto.value_i16();
        break;
    case AllTypeValue::kValueU16:
        conf.data_format = DATA_ALL_TYPE_UINT16;
        conf.uint16_value = (uint16_t)proto.value_u16();
        break;
    case AllTypeValue::kValueI32:
        conf.data_format = DATA_ALL_TYPE_INT32;
        conf.int32_value = (int32_t)proto.value_i32();
        break;
    case AllTypeValue::kValueU32:
        conf.data_format = DATA_ALL_TYPE_UINT32;
        conf.uint32_value = (uint32_t)proto.value_u32();
        break;
    case AllTypeValue::kValueFloat:
        conf.data_format = DATA_ALL_TYPE_FLOAT;
        conf.float_value = (float)proto.value_float();
        break;
    case AllTypeValue::kValueDouble:
        conf.data_format = DATA_ALL_TYPE_DOUBLE;
        conf.double_value = proto.value_double();
        break;
    case AllTypeValue::kValueBool:
        conf.data_format = DATA_ALL_TYPE_BOOL;
        conf.bool_value = proto.value_bool();
        break;
    case AllTypeValue::kValueString:
        conf.data_format = DATA_ALL_TYPE_STRING;
        conf.string_value = proto.value_string().c_str();
        break;
    default:
        return -1;
    }
    return 0;
}

static inline int set_data_all_type(
    Context &ctx, urq_data_all_type_t *&conf, const AllTypeValue &proto)
{
    urq_used(ctx);

    if (conf == NULL) {
        conf = malloc<urq_data_all_type_t>();
        if (conf == NULL) {
            return -1;
        }
        urq_data_all_type_init_inplace(conf);
    }

    if (set_data_all_type(ctx, *conf, proto)) {
        return -1;
    }

    return 0;
}
} // namespace urq::parse

#endif
