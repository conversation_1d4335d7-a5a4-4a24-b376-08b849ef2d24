#pragma once

#include "urq/widget/conf.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__SET_NODE_HPP
#define URQ_PARSE__SET_NODE_HPP
namespace urq::parse {

/// @brief 设置单个组件
/// @param ctx    编译上下文
/// @param ptr    组件配置的指针
/// @param proto  组件 proto
/// @param size   祖先组件大小
/// @return 0 成功, -1 失败
int set_node(
    Context &ctx, urq_widget_conf_t *&ptr,
    Widget const &proto);

} // namespace urq::parse
#endif // URQ_PARSE__SET_NODE_HPP