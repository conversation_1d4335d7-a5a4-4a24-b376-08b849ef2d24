#include "urq_parse/project.h"
#include "urq/log/verbose.h"
#include "urq_parse/util.hpp"
#include "znd/project/v1/project.pb.h"

/// @brief 设置配置
/// 传进来的的配置需要是已经初始化过的
static inline int set_project(
    urq_project_conf_t &conf, ::znd::project::v1::ProjectInfo const &proto)
{
    urq_used(conf);
    urq_used(proto);
    // char id_str[32];
    // char *id;
    // size_t len;

    // snprintf(id_str, sizeof(id_str), "%x", proto.project_id_u32());
    // len = strlen(id_str);
    // id = urq::parse::malloc<char>(len + 1);
    // if (id == nullptr) {
    //     return -1;
    // }
    // strcpy(id, id_str);
    // // set_color(conf.bg_color, proto.background_color());
    // conf.id = id;
    // conf.local_display_id = 1;
    return 0;
}

int urq_parse__project(
    const uint8_t *const data, size_t size,
    urq_project_conf_t *const out_project)
{
    znd::project::v1::ProjectInfo conf;
    try {
        conf.ParseFromArray(data, (int)size);
    } catch (const std::exception &e) {
        errno = EINVAL;
        return -1;
    }
    return set_project(*out_project, conf);
}