
#pragma once

#include "urq/preload.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/util.hpp"

#ifndef URQ_PARSE__SET_SYMBOL_HPP
#define URQ_PARSE__SET_SYMBOL_HPP
namespace urq::parse {

/// @brief 设置方向
static inline int set_symbol(
    Context &ctx, const char **conf, const znd::project::v1::SymbolType &proto)
{
    urq_used(ctx);
    switch (proto) {
    case znd::project::v1::SymbolType::SYMBOL_TYPE_BULLET:
        *conf = LV_SYMBOL_BULLET;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_AUDIO:
        *conf = LV_SYMBOL_AUDIO;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_VIDEO:
        *conf = LV_SYMBOL_VIDEO;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_LIST:
        *conf = LV_SYMBOL_LIST;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_OK:
        *conf = LV_SYMBOL_OK;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_CLOSE:
        *conf = LV_SYMBOL_CLOSE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_POWER:
        *conf = LV_SYMBOL_POWER;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_SETTINGS:
        *conf = LV_SYMBOL_SETTINGS;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_HOME:
        *conf = LV_SYMBOL_HOME;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_DOWNLOAD:
        *conf = LV_SYMBOL_DOWNLOAD;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_DRIVE:
        *conf = LV_SYMBOL_DRIVE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_REFRESH:
        *conf = LV_SYMBOL_REFRESH;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_MUTE:
        *conf = LV_SYMBOL_MUTE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_VOLUME_MID:
        *conf = LV_SYMBOL_VOLUME_MID;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_VOLUME_MAX:
        *conf = LV_SYMBOL_VOLUME_MAX;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_IMAGE:
        *conf = LV_SYMBOL_IMAGE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_TINT:
        *conf = LV_SYMBOL_TINT;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_PREV:
        *conf = LV_SYMBOL_PREV;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_PLAY:
        *conf = LV_SYMBOL_PLAY;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_PAUSE:
        *conf = LV_SYMBOL_PAUSE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_STOP:
        *conf = LV_SYMBOL_STOP;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_EJECT:
        *conf = LV_SYMBOL_EJECT;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_LEFT:
        *conf = LV_SYMBOL_LEFT;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_RIGHT:
        *conf = LV_SYMBOL_RIGHT;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_PLUS:
        *conf = LV_SYMBOL_PLUS;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_MINUS:
        *conf = LV_SYMBOL_MINUS;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_EYE_OPEN:
        *conf = LV_SYMBOL_EYE_OPEN;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_EYE_CLOSE:
        *conf = LV_SYMBOL_EYE_CLOSE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_WARNING:
        *conf = LV_SYMBOL_WARNING;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_SHUFFLE:
        *conf = LV_SYMBOL_SHUFFLE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_UP:
        *conf = LV_SYMBOL_UP;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_DOWN:
        *conf = LV_SYMBOL_DOWN;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_LOOP:
        *conf = LV_SYMBOL_LOOP;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_DIRECTORY:
        *conf = LV_SYMBOL_DIRECTORY;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_UPLOAD:
        *conf = LV_SYMBOL_UPLOAD;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_CALL:
        *conf = LV_SYMBOL_CALL;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_CUT:
        *conf = LV_SYMBOL_CUT;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_COPY:
        *conf = LV_SYMBOL_COPY;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_SAVE:
        *conf = LV_SYMBOL_SAVE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_BARS:
        *conf = LV_SYMBOL_BARS;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_ENVELOPE:
        *conf = LV_SYMBOL_ENVELOPE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_CHARGE:
        *conf = LV_SYMBOL_CHARGE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_PASTE:
        *conf = LV_SYMBOL_PASTE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_BELL:
        *conf = LV_SYMBOL_BELL;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_KEYBOARD:
        *conf = LV_SYMBOL_KEYBOARD;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_GPS:
        *conf = LV_SYMBOL_GPS;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_FILE:
        *conf = LV_SYMBOL_FILE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_WIFI:
        *conf = LV_SYMBOL_WIFI;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_BATTERY_FULL:
        *conf = LV_SYMBOL_BATTERY_FULL;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_BATTERY_3:
        *conf = LV_SYMBOL_BATTERY_3;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_BATTERY_2:
        *conf = LV_SYMBOL_BATTERY_2;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_BATTERY_1:
        *conf = LV_SYMBOL_BATTERY_1;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_BATTERY_EMPTY:
        *conf = LV_SYMBOL_BATTERY_EMPTY;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_USB:
        *conf = LV_SYMBOL_USB;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_BLUETOOTH:
        *conf = LV_SYMBOL_BLUETOOTH;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_TRASH:
        *conf = LV_SYMBOL_TRASH;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_EDIT:
        *conf = LV_SYMBOL_EDIT;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_BACKSPACE:
        *conf = LV_SYMBOL_BACKSPACE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_SD_CARD:
        *conf = LV_SYMBOL_SD_CARD;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_NEW_LINE:
        *conf = LV_SYMBOL_NEW_LINE;
        break;
    case znd::project::v1::SymbolType::SYMBOL_TYPE_DUMMY:
        *conf = LV_SYMBOL_DUMMY;
        break;
    default:
        return -1;
    }
    return 0;
}

} // namespace urq::parse

#endif // URQ_PARSE__SET_SYMBOL_HPP