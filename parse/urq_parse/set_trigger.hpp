// #pragma once

// #include "urq/action/trigger/type.h"
// #include "urq_parse/Context.hpp"
// #include "urq_parse/set_condition_list.hpp"
// #include "urq_parse/set_data_reference.hpp"
// #include "urq_parse/util.hpp"
// #include <cstddef>

// #ifndef URQ__PARSE__SET_TRIGGER_HPP
// #define URQ__PARSE__SET_TRIGGER_HPP

// namespace urq::parse {

// static inline int set_trigger_press_down(
//     Context &ctx, urq_trigger_t *&dst, TriggerPressDown const &proto)
// {
//     urq_used(ctx);
//     if (dst == NULL) {
//         dst = malloc<urq_trigger_t>();
//         if (dst == NULL) {
//             return -1;
//         }
//         urq_trigger_init(dst);
//     }
//     dst->trigger_type = URQ_BEHAVIOR_TYPE_PRESS;
//     dst->trigger_param.press_down_time = proto.long_press_milliseconds();
//     return 0;
// }

// static inline int set_trigger_press_up(
//     Context &ctx, urq_trigger_t *&dst, TriggerPressUp const &proto)
// {
//     urq_used(ctx);
//     if (dst == NULL) {
//         dst = malloc<urq_trigger_t>();
//         if (dst == NULL) {
//             return -1;
//         }
//         urq_trigger_init(dst);
//     }
//     dst->trigger_type = URQ_BEHAVIOR_TYPE_RELEASE;
//     dst->trigger_param.press_up_time = proto.release_milliseconds();
//     return 0;
// }

// static inline int set_trigger_data_change(
//     Context &ctx, urq_trigger_t *&dst, TriggerDataChange const &proto)
// {
//     urq_used(ctx);
//     if (dst == NULL) {
//         dst = malloc<urq_trigger_t>();
//         if (dst == NULL) {
//             return -1;
//         }
//         urq_trigger_init(dst);
//     }
//     dst->trigger_type = URQ_BEHAVIOR_TYPE_DATA_CHANGE;
//     dst->trigger_param.data_change = malloc<urq_trigger_data_change_t>();
//     if (proto.data_references().empty()) {
//         return -1;
//     }

//     if (set_data_reference_list(
//             ctx, dst->trigger_param.data_change, proto.data_references())) {
//         return -1;
//     }

//     return 0;
// }

// static inline int set_trigger_condition(
//     Context &ctx, urq_trigger_t *&dst, TriggerCondition const &proto)
// {
//     urq_used(ctx);
//     if (dst == NULL) {
//         dst = malloc<urq_trigger_t>();
//         if (dst == NULL) {
//             return -1;
//         }
//         urq_trigger_init(dst);
//     }
//     dst->trigger_type = URQ_BEHAVIOR_TYPE_CONDITION;
//     dst->trigger_param.condition = malloc<urq_trigger_condition_t>();
//     if (proto.conditions().empty()) {
//         return -1;
//     }

//     if (set_condition_list(
//             ctx, dst->trigger_param.condition, proto.conditions())) {
//         return -1;
//     }

//     return 0;
// }

// static inline int set_trigger_state_change(
//     Context &ctx, urq_trigger_t *&dst, TriggerStateChange const &proto)
// {
//     urq_used(ctx);
//     if (dst == NULL) {
//         dst = malloc<urq_trigger_t>();
//         if (dst == NULL) {
//             return -1;
//         }
//         urq_trigger_init(dst);
//     }
//     dst->trigger_type = URQ_BEHAVIOR_TYPE_STATE_CHANGE;
//     dst->trigger_param.status_change = malloc<urq_trigger_status_change_t>();

//     if (proto.state_values().empty()) {
//         return -1;
//     }

//     dst->trigger_param.status_change->size =
//         (uint32_t)proto.state_values().size();

//     dst->trigger_param.status_change->data =
//         malloc<int32_t>(dst->trigger_param.status_change->size);

//     for (int i = 0; i < proto.state_values().size(); i++) {
//         dst->trigger_param.status_change->data[i] = proto.state_values(i);
//     }

//     return 0;
// }

// static inline int set_trigger_timer(
//     Context &ctx, urq_trigger_t *&dst, TriggerTimer const &proto)
// {
//     urq_used(ctx);
//     if (dst == NULL) {
//         dst = malloc<urq_trigger_t>();
//         if (dst == NULL) {
//             return -1;
//         }
//         urq_trigger_init(dst);
//     }
//     dst->trigger_type = URQ_BEHAVIOR_TYPE_TIMER;
//     dst->trigger_param.timer = malloc<urq_trigger_timer_t>();

//     if (set_data_reference(
//             ctx, dst->trigger_param.timer->enable, proto.enable())) {
//         return -1;
//     }
//     if (set_data_reference(
//             ctx, dst->trigger_param.timer->interval, proto.interval())) {
//         return -1;
//     }

//     return 0;
// }
// } // namespace urq::parse

// #endif // URQ__PARSE__SET_TRIGGER_HPP
