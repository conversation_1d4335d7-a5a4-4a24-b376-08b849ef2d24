#!/bin/bash

script_dir=$(realpath $(dirname $0))

# 更新 protobuf 
function copy_gen_ts() {
    local proto_folder=$(realpath $script_dir/../defs)

    if [ ! -d $proto_folder ]; then
        echo "Error: $proto_folder not found"
        exit 1
    fi

    cd $proto_folder
    echo "cd $(pwd)"    
    echo "git pull --rebase"
    # git pull --rebase

    local dst_dir=$script_dir/proto
    local dst=$dst_dir/src
    local src=$proto_folder/gen/ts/znd/project/v1

    if [ -d $dst ]; then
        echo "Removing $dst"
        rm -rf $dst
    fi

    if [ -d $dst_dir/dev ]; then
        echo "Removing $dst_dir/dev"
        rm -rf $dst_dir/dev
    fi

    echo "Copying $src to $dst"
    cp -r $src $dst

    tsc --project $dst_dir/tsconfig.json
}

# 更新 urq ui 的编译结果
function publish(){
    files=("editor.js" "editor.wasm")

    for file in ${files[@]}; do
        if [ -f "$script_dir/public/$file" ]; then
            echo "Removing $script_dir/public/$file"
            rm $script_dir/public/$file
        fi
        if [ -f "$script_dir/../ui/build/editor/debug/ui/$file" ]; then
            echo "Copying $script_dir/../ui/build/editor/debug/ui/$file to $script_dir/public/$file"
            cp $script_dir/../ui/build/editor/debug/ui/$file $script_dir/public/$file
        fi
    done
}

# 生成 mock 数据
function gen(){
    cd $script_dir
    npm run gen
}

# 启动 vite 服务器
function vite(){
    cd $script_dir
    npm run dev
}

case $1 in
    "proto")
        copy_gen_ts
        ;;
    "publish")
        publish
        ;;
    "gen")
        gen
        ;;
    "vite")
        vite
        ;;
    *)
        echo "Usage: urq-ts {update|publish|gen|vite}"
        echo "proto : 更新 protobuf"
        echo "publish: 更新 urq ui 的编译结果"
        echo "gen    : 生成 mock 数据"
        echo "vite   : 启动 vite 服务器"
        exit 1
        ;;
esac
