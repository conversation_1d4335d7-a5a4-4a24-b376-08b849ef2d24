version: v2
managed:
    enabled: true
    override:
        - file_option: go_package_prefix
          value: github.com/bufbuild/buf-tour/gen
plugins:
    # - remote: buf.build/protocolbuffers/go
    #   out: ../builder/defs
    #   opt: paths=source_relative
    # - remote: buf.build/connectrpc/go
    #   out: gen/go
    #   opt: paths=source_relative
    # - remote: buf.build/bufbuild/cpp
    #   out: gen/cpp
    #   opt: paths=source_relative

    # - remote: buf.build/bufbuild/es
    #   out: gen/es
    # - remote: buf.build/connectrpc/es
    #   out: gen/es
    # - remote: buf.build/community/neoeinstein-prost:v0.4.0
    #   out: gen/rust
    # - remote: buf.build/community/stephenh-ts-proto
    #   out: gen/ts
    - remote: buf.build/community/stephenh-ts-proto
      out: gen/ts
      opt:
        - oneof=unions-value
        - esModuleInterop=true
    #    - forceLong=bigint
    # - protoc_builtin: cpp
    #   protoc_path: protoc/protoc.exe
    #   out: gen/cpp
inputs:
    - directory: proto
