set(CMAKE_EXPORT_COMPILE_COMMANDS ON) # 生成编译数据
set(CMAKE_POSITION_INDEPENDENT_CODE ON) # 生成位置无关代码

if(NOT URQ_BUILD_PLATFORM)
    message(STATUS "platform set to default: linux_64")
    set(URQ_BUILD_PLATFORM "linux_64")
endif()

if(NOT URQ_BUILD_TYPE)
    message(STATUS "build type set to default: debug")
    set(URQ_BUILD_TYPE "debug")
endif()

if(NOT URQ_COMPILE_SDL)
    message(STATUS "SDL is not used")
    set(URQ_COMPILE_SDL 0)
endif()

if(NOT URQ_EDITOR)
    message(STATUS "URQ_EDITOR is not set")
    set(URQ_EDITOR 0)
endif()

# 添加 cmake config 的搜索路径
set(CMAKE_INSTALL_PREFIX "${PROJECT_SOURCE_DIR}/../lib/${URQ_BUILD_PLATFORM}")
# 变量配置
if("${URQ_BUILD_TYPE}" STREQUAL "release")
    set(CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}/release")
else()
    set(CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}/debug")
endif()
get_filename_component(CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}" REALPATH)
set(CMAKE_PREFIX_PATH "${CMAKE_INSTALL_PREFIX}")

# 如果编译参数不存在则添加
# @param flag 待添加的参数
# @param lang 需要添加参数的语言 c/cxx，默认为全部添加
function(cpm_add_compile_flag flag)
    if( ${ARGC} EQUAL 1 OR "${ARGV2}" STREQUAL "c")
        string(FIND "${CMAKE_C_FLAGS}" "${flag}" HasFlag)
        if(HasFlag EQUAL -1) 
            set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${flag}" PARENT_SCOPE)
        endif()
    endif()

    if( ${ARGC} EQUAL 1 OR "${ARGV2}" STREQUAL "cxx")
        string(FIND "${CMAKE_CXX_FLAGS}" "${flag}" HasFlag)
        if(HasFlag EQUAL -1) 
            set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${flag}" PARENT_SCOPE)
        endif()
    endif()
endfunction()

# 如果是开发模式
if("${URQ_BUILD_TYPE}" STREQUAL "debug")
    if("${URQ_BUILD_PLATFORM}" STREQUAL "linux_64")
        cpm_add_compile_flag("-fsanitize=address")  
        cpm_add_compile_flag("-fsanitize=undefined")  
        cpm_add_compile_flag("-fstack-protector-strong")  
        cpm_add_compile_flag("-Wall -Wextra -Wpedantic -Werror")
        cpm_add_compile_flag("-Wno-error=deprecated-declarations")
        cpm_add_compile_flag("-Wformat=2") # 严格检查 printf/scanf 格式字符串
        cpm_add_compile_flag("-Wformat-security") # 警告可能导致安全问题的格式化函数用法
        cpm_add_compile_flag("-Wformat-overflow") # 警告格式化函数可能导致缓冲区溢出的情况
        cpm_add_compile_flag("-Wuninitialized") # 检测未初始化的变量
        cpm_add_compile_flag("-Wshadow") # 检测变量名遮蔽（局部变量覆盖全局变量）
        cpm_add_compile_flag("-Wconversion") # 检查隐式类型转换的风险
        cpm_add_compile_flag("-Wnull-dereference") # 检测可能的空指针解引用
        # cpm_add_compile_flag("-Wdangling-else")
        # cpm_add_compile_flag("-Wrestrict")
        # cpm_add_compile_flag("-Wdisabled-optimization")
        # cpm_add_compile_flag("-Wunsafe-loop-optimizations")
        # cpm_add_compile_flag("-Wstack-protector")
        # cpm_add_compile_flag("-Wstack-usage")
        cpm_add_compile_flag("-Wunused -Wunused-macros")
        cpm_add_compile_flag("-Wmissing-attributes")
        cpm_add_compile_flag("-Wmissing-braces")
        cpm_add_compile_flag("-Wmismatched-dealloc")
        cpm_add_compile_flag("-Wmissing-field-initializers")
        # cpm_add_compile_flag("")
        # cpm_add_compile_flag("")
        cpm_add_compile_flag("-Wsign-compare") # 有符号和无符号整数比较的警告。
        cpm_add_compile_flag("-Wswitch") 
        cpm_add_compile_flag("-Wswitch-default") 
        # cpm_add_compile_flag("-Wswitch-enum") 
        cpm_add_compile_flag("-Wno-attributes") # 禁用对未知或不正确使用属性的警告
        cpm_add_compile_flag("-Wno-odr") # 禁用对链接时优化中违反单一定义规则（ODR）的警告。
        cpm_add_compile_flag("-Wno-unused-result") # 禁用对未使用返回值的警告
        cpm_add_compile_flag("-Wstrict-aliasing") # 警告可能违反严格别名规则的代码
        cpm_add_compile_flag("-fno-elide-constructors" "cxx")

        cpm_add_compile_flag("-g")
        cpm_add_compile_flag("--coverage")
        cpm_add_compile_flag("-fprofile-arcs")
        cpm_add_compile_flag("-ftest-coverage")
    endif()

    if("${URQ_BUILD_PLATFORM}" STREQUAL "wasm")
        cpm_add_compile_flag("-sNO_EXIT_RUNTIME=1")
        cpm_add_compile_flag("-Wno-unused-command-line-argument")

        # 启用最小化的 UBSan 运行时，适用于生产环境，减少攻击面。
        # cpm_add_compile_flag("-fsanitize-minimal-runtime")
        # 启用 UBSan，用于捕获代码中的未定义行为。
        # cpm_add_compile_flag("-fsanitize=undefined")  
        # 启用 ASan，用于捕获缓冲区溢出、内存泄漏和其他相关问题
        # cpm_add_compile_flag("-fsanitize=address")  
        # 启用 UBSan 的空指针检测（文中提到的特定用法）。
        # cpm_add_compile_flag("-fsanitize=null")

        # 当内存分配失败时，返回 NULL 而不是直接崩溃。
        # 未实现
        # cpm_add_compile_flag("-sABORTING_MALLOC=0")

        # 确保程序退出时运行内存泄漏检测（与 LeakSanitizer 结合使用）。
        # 未实现
        # cpm_add_compile_flag("-sEXIT_RUNTIME")

        # 仅启用泄漏检测，比 -fsanitize=address 更快，因为它不会对所有内存访问进行插桩。
        # cpm_add_compile_flag("-fsanitize=leak")

        # 启用 Emscripten 的 SAFE_HEAP 模式，用于检测特定的 WebAssembly 和 JavaScript 问题，例如空指针访问、未对齐访问等。
        # 与 ASan 互斥
        # cpm_add_compile_flag("-sSAFE_HEAP")
    elseif("${URQ_BUILD_PLATFORM}" STREQUAL "win_64")
        cpm_add_compile_flag("/DEBUG")
    else()

        # 启用 UBSan，用于捕获代码中的未定义行为。
        # cpm_add_compile_flag("-fsanitize=undefined")  
        # 启用 ASan，用于捕获缓冲区溢出、内存泄漏和其他相关问题
        # cpm_add_compile_flag("-fsanitize=address")  
    endif()
endif()

# 如果是发布模式
if("${CMAKE_BUILD_TYPE}" STREQUAL "Release")
    # 设置编译优化等级
    if("${URQ_BUILD_PLATFORM}" STREQUAL "win64")
        cpm_add_compile_flag("/O2")
    else()
        cpm_add_compile_flag("-O3")
    endif()

    if("${URQ_BUILD_PLATFORM}" STREQUAL "linux_64")
        cpm_add_compile_flag("-flto")
    endif()

    # cpm_add_compile_flag("-mfloat-abi=hard")
endif()