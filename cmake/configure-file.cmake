# 配置文件

string(TOUPPER "URQ_COMPILE_PLATFORM_${URQ_BUILD_PLATFORM}" URQ_COMPILE_PLATFORM) # URQ_COMPILE_PLATFORM_(...)
string(TOUPPER "URQ_COMPILE_MODE_${URQ_BUILD_TYPE}" URQ_COMPILE_MODE) # URQ_COMPILE_MODE_(DEBUG | RELEASE)

# configure_file(
#     "${CMAKE_CURRENT_SOURCE_DIR}/urq/platform.h.in" 
#     "${CMAKE_CURRENT_SOURCE_DIR}/urq/platform.h" 
# )

# configure_file(
#     "${CMAKE_CURRENT_SOURCE_DIR}/urq/compile.h.in" 
#     "${CMAKE_CURRENT_SOURCE_DIR}/urq/compile.h" 
# )
