
set(ZND_BUILD_PLATFORM "wasm")
set(ZND_BUILD_TYPE "debug")

file(TO_CMAKE_PATH "~/toolchain/emsdk" EMSDK_PATH)
message("EMSDK_PATH: ${EMSDK_PATH}")

set(CMAKE_C_COMPILER "${EMSDK_PATH}/upstream/emscripten/emcc")
set(CMAKE_CXX_COMPILER "${EMSDK_PATH}/upstream/emscripten/em++")

# 添加 -gsource-map compile flag
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -gsource-map -O0")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -gsource-map -O0")

set(CMAKE_SYSROOT "${EMSDK_PATH}/upstream/emscripten/cache/sysroot")
include_directories("${EMSDK_PATH}/upstream/emscripten/cache/sysroot/include")
include_directories("${EMSDK_PATH}/upstream/emscripten/cache/sysroot/include/c++/v1")

# set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wno-limited-postlink-optimizations") # 禁用掉 emsdk 的一些报错
# set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -s ASSERTIONS=0") # 禁用断言
# 是否捕获异常
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -s DISABLE_EXCEPTION_CATCHING=1")
# set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -sDEFAULT_LIBRARY_FUNCS_TO_INCLUDE=['$UTF8ToString']")
# set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','getValue','setValue']")  # ccall
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -sASYNCIFY")  # 支持 sleep
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -s EXPORT_ES6=1 -s MODULARIZE=1")  # 编译成 ES Module
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -sINITIAL_HEAP=268435456")  # 初始堆大小 268435456 = 256MB
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -sNO_DISABLE_EXCEPTION_CATCHING")  # 错误捕获
