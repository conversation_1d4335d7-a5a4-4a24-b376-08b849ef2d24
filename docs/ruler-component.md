# 刻度尺组件 (Ruler Widget)

刻度尺组件是一个通用的测量显示组件，支持多种形式的刻度显示，适用于各种测量场景。

## 功能特性

### 支持的刻度尺类型
- **水平刻度尺** (`RULER_TYPE_HORIZONTAL`): 适用于长度、距离等水平测量
- **垂直刻度尺** (`RULER_TYPE_VERTICAL`): 适用于量杯、温度计等垂直测量
- **弧形刻度尺** (`RULER_TYPE_ARC`): 适用于仪表盘、压力表等弧形显示
- **圆形刻度尺** (`RULER_TYPE_CIRCLE`): 适用于时钟、罗盘等圆形显示

### 刻度配置
- **主刻度**: 可配置数量、宽度、长度、颜色
- **次刻度**: 可配置数量、宽度、长度、颜色
- **刻度范围**: 支持最小值和最大值设置
- **刻度标签**: 支持显示/隐藏、位置调整、颜色设置

### 显示选项
- **刻度方向**: 上方/左侧、下方/右侧、两侧
- **标签位置**: 外侧、内侧、中心
- **主线显示**: 可选择显示主刻度线
- **网格线**: 可选择显示网格线
- **自动间距**: 自动计算刻度间距

### 高级功能
- **当前值指示**: 高亮显示当前测量值
- **自定义标记**: 添加特殊标记点（如警告线、目标值等）
- **自定义标签格式**: 支持自定义标签格式化函数

## 使用场景

### 1. 量杯刻度
```typescript
const cupRuler = createVerticalRuler(
  1001,        // ID
  100, 50,     // 位置 (x, y)
  60, 300,     // 大小 (width, height)
  0, 500,      // 刻度范围 (min, max)
  "ml",        // 单位
  150          // 当前值
);
```

### 2. 温度计
```typescript
const thermometer = createVerticalRuler(
  1002,
  200, 50,
  40, 200,
  -10, 50,
  "°C",
  25
);
```

### 3. 压力表
```typescript
const pressureGauge = createArcRuler(
  1003,
  300, 100,
  150,         // 大小
  0, 10,       // 压力范围
  135, 405,    // 起始和结束角度
  6.5          // 当前压力值
);
```

### 4. 长度测量尺
```typescript
const lengthRuler = createHorizontalRuler(
  1004,
  50, 300,
  400, 30,
  0, 50,
  12.5
);
```

## 配置参数

### 基础配置
- `rulerType`: 刻度尺类型
- `direction`: 刻度方向
- `labelPosition`: 标签位置

### 刻度配置 (`scaleConfig`)
- `show`: 是否显示刻度
- `scaleShowType`: 刻度显示类型（点/时间）
- `gridShowType`: 网格显示类型
- `scaleMain`: 主刻度配置
- `scaleSec`: 次刻度配置
- `minValue`/`maxValue`: 刻度范围

### 样式配置
- `mainLine`: 主线样式
- `backgroundColor`: 背景颜色
- `labelColor`: 标签颜色
- `labelOffset`: 标签偏移距离

### 弧形专用配置
- `startAngle`/`endAngle`: 起始和结束角度
- `radius`: 半径
- `centerOffsetX`/`centerOffsetY`: 中心点偏移

## API 接口

### C语言接口

#### 创建组件
```c
lv_obj_t *urq_ruler_widget_create(lv_obj_t *parent);
```

#### 配置组件
```c
void urq_ruler_widget_config(lv_obj_t *self, urq_widget_conf_t *conf);
```

#### 设置当前值
```c
void urq_ruler_widget_set_value(lv_obj_t *self, lv_coord_t value);
lv_coord_t urq_ruler_widget_get_value(lv_obj_t *self);
```

#### 设置刻度范围
```c
void urq_ruler_widget_set_range(lv_obj_t *self, lv_coord_t min_value, lv_coord_t max_value);
```

#### 添加自定义标记
```c
void urq_ruler_widget_add_mark(lv_obj_t *self, lv_coord_t value, const char *label, lv_color_t color);
void urq_ruler_widget_clear_marks(lv_obj_t *self);
```

### TypeScript接口

#### 工厂函数
```typescript
// 创建水平刻度尺
createHorizontalRuler(id, x, y, width, height, minValue, maxValue, currentValue?)

// 创建垂直刻度尺
createVerticalRuler(id, x, y, width, height, minValue, maxValue, unit?, currentValue?)

// 创建弧形刻度尺
createArcRuler(id, x, y, size, minValue, maxValue, startAngle?, endAngle?, currentValue?)
```

#### 验证函数
```typescript
validateRulerConfig(widget: Widget): boolean
```

## 实现细节

### 绘制流程
1. **主线绘制**: 根据刻度尺类型绘制主刻度线
2. **刻度绘制**: 绘制主刻度和次刻度
3. **标签绘制**: 根据配置绘制刻度标签
4. **当前值指示**: 绘制当前值指示器
5. **自定义标记**: 绘制用户定义的特殊标记

### 坐标计算
- **直线刻度**: 基于线性插值计算刻度位置
- **弧形刻度**: 基于角度和半径计算极坐标位置
- **标签位置**: 根据刻度位置和偏移量计算标签坐标

### 事件处理
- **点击事件**: 根据点击位置计算对应的刻度值
- **值变化事件**: 当前值改变时触发重绘

## 注意事项

1. **性能优化**: 大量刻度时建议使用合适的刻度间距
2. **显示精度**: 根据实际需求选择合适的数值精度
3. **角度范围**: 弧形刻度尺的角度范围应合理设置
4. **标签重叠**: 避免标签文本重叠，适当调整字体大小和间距
5. **内存管理**: 及时释放不再使用的自定义标记

## 扩展功能

### 自定义标签格式化
```c
char *custom_formatter(lv_coord_t value, void *user_data) {
    static char buffer[32];
    snprintf(buffer, sizeof(buffer), "%.1f°C", value / 10.0f);
    return buffer;
}

// 设置自定义格式化函数
ruler_conf->label_formatter = custom_formatter;
ruler_conf->user_data = some_context;
```

### 动态刻度更新
```c
// 动态更新刻度范围
urq_ruler_widget_set_range(ruler, new_min, new_max);

// 动态更新当前值
urq_ruler_widget_set_value(ruler, new_value);
```

这个刻度尺组件提供了丰富的配置选项和灵活的使用方式，能够满足各种测量显示需求。
