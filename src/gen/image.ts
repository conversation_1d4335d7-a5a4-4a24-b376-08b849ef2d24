import { <PERSON><PERSON> } from "jimp";
import * as node_fs from "node:fs";
import * as node_path from "node:path";
import { ProjectObjectType } from "../proto";

/**
 * 生成项目
 */
export async function generateImage(source: string, output: string) {
    const outputFolder = node_path.join(output, ProjectObjectType.PROJECT_OBJECT_TYPE_IMAGE_FILE.toString());
    source = source.replace(/[\\\/]urq_ts[\\\/]dev[\\\/]/, "/urq_ts/src/");
    source = source.replace(/[\\\/]zendia-runtime[\\\/]dev[\\\/]/, "/zendia-runtime/src/");
    const sourceFolder = node_path.join(source, "img");

    if (!node_fs.existsSync(sourceFolder)) {
        console.error("Source folder not found: ", sourceFolder);
        return;
    }
    const images = node_fs.readdirSync(sourceFolder);
    if (images.length === 0) {
        return;
    }
    if (!node_fs.existsSync(outputFolder)) {
        console.log("Creating directory: ", outputFolder);
        node_fs.mkdirSync(outputFolder);
    }

    for (const image of images) {
        const src = node_path.join(sourceFolder, image);
        const jimp = await Jimp.read(src);
        const width = jimp.bitmap.width;
        const height = jimp.bitmap.height;
        const output = new Uint8Array(4 + width * height * 4);
        const x = (5 | (width << 10) | (height << 21)) >>> 0;
        output[0] = 255 & x;
        output[1] = (65280 & x) >> 8;
        output[2] = (0xff0000 & x) >> 16;
        output[3] = (0xff000000 & x) >> 24;

        let idx = 4;
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const color = jimp.getPixelColor(x, y);
                const a = (color & 0xff000000) >> 24;
                const r = (color & 0xff0000) >> 16;
                const g = (color & 0xff00) >> 8;
                const b = color & 0xff;
                output[idx++] = g;
                output[idx++] = r;
                output[idx++] = a;
                output[idx++] = b;
            }
        }

        const dst = node_path.join(outputFolder, image.replace(/\..+$/, ".bin"));
        console.log("Copying image: ", src, " -> ", dst);
        node_fs.writeFileSync(dst, output);
    }
}
