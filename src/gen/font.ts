import * as convert from "lv_font_conv/lib/convert.js";
import * as node_fs from "node:fs";
import * as node_path from "node:path";
import { fontMap } from "../project-1/font";
import { ProjectObjectType } from "../proto";

/**
 * 字符列表
 */
export interface SymbolList {
    symbols: string;
}

/**
 * 字符编码范围
 */
export interface CharCodeRange {
    range: [start: number, end: number];
}

export interface FontOption {
    size: number;
    fonts: {
        [name: string]: Array<SymbolList | CharCodeRange>;
    };
}

export interface FontIdOptionMap {
    [id: number]: FontOption;
}

export function isSymbolOption(option: SymbolList | CharCodeRange): option is SymbolList {
    return "symbols" in option && typeof option.symbols === "string";
}

export function isRangeOption(option: SymbolList | CharCodeRange): option is CharCodeRange {
    return "range" in option;
}

function convertRange(range: CharCodeRange | SymbolList): convert.SymbolOption | convert.RangeOption {
    if (isSymbolOption(range)) {
        return range;
    }
    return { range: [range.range[0], range.range[1], range.range[0]] };
}

/**
 * 生成项目
 * @param input   输入目录
 * @param output  输出目录
 * @param project 工程名
 */
export async function generateFont(output: string, asset: string) {
    const fontFolder = node_path.join(asset, "font");
    const cacheFolder = node_path.join(asset, "../cache");
    const outputFolder = node_path.join(output, ProjectObjectType.PROJECT_OBJECT_TYPE_FONT_FILE.toString());

    if (!node_fs.existsSync(outputFolder)) {
        console.log("Creating directory: ", outputFolder);
        node_fs.mkdirSync(outputFolder);
    }

    // copy cache/font/MiSans-Normal/16 to ${output}/0
    const font16Path = node_path.join(cacheFolder, "font/MiSans-Normal/16");
    node_fs.copyFileSync(font16Path, node_path.join(outputFolder, "0"));

    for (const id in fontMap) {
        const option = fontMap[id]!;
        const outputFile = node_path.join(outputFolder, id);

        const sources: convert.FontSource[] = [];

        console.log("convert font, id: %d, ranges: ", id);
        for (const name in option.fonts) {
            const source_path = node_path.join(fontFolder, name + ".ttf");
            const source_bin = node_fs.readFileSync(source_path);
            const ranges = option.fonts[name];
            if (ranges === undefined) {
                continue;
            }
            for (const range of ranges) {
                if (isRangeOption(range)) {
                    console.log(
                        "\tfont: %s, range: 0x%s - 0x%s",
                        name,
                        range.range[0].toString(16),
                        range.range[1].toString(16)
                    );
                } else {
                    console.log("\tfont: %s, symbols: ", name, range.symbols);
                }
            }

            sources.push({
                source_path,
                source_bin,
                ranges: ranges.map(convertRange),
            });
        }

        const files = await convert.default({
            size: option.size,
            bpp: 1,
            format: "bin",
            output: outputFile,
            font: sources,
        });

        for (let [filename, data] of Object.entries(files)) {
            console.log("output: %s, size: %s", filename, data.length);
            node_fs.writeFileSync(filename, data);
        }
    }
}
