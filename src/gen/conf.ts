import node_fs from "fs";
import node_path from "path";

export interface Config {
    /** 是否生成服务端代码 */
    server: boolean;
}

function defaultConfig(): Config {
    return {
        server: false,
    };
}

/**
 * 加载配置
 * @returns
 */
function loadConfig(): Config {
    let pwd = node_path.join(process.cwd(), "conf.json");
    if (!node_fs.existsSync(pwd)) {
        return defaultConfig();
    }
    try {
        const conf = node_fs.readFileSync(pwd, "utf-8");
        return JSON.parse(conf) as Config;
    } catch (e) {
        console.error("Failed to load config", e);
        return defaultConfig();
    }
}

let config: Config | undefined;

export function getConfig(): Readonly<Config> {
    if (!config) {
        config = loadConfig();
    }
    return config;
}
