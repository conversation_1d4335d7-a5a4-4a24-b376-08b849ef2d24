import * as node_fs from "node:fs";
import * as node_path from "node:path";
import { project1 } from "../project-1";
import { ProjectObjectType } from "../proto";

/**
 * 生成项目
 */
export function generateDisplay(output: string) {
    const outputFolder = node_path.join(output, ProjectObjectType.PROJECT_OBJECT_TYPE_DISPLAY_LIST.toString());
    node_fs.mkdirSync(outputFolder);
    const outputFile = node_path.join(outputFolder, "0");
    node_fs.writeFileSync(outputFile, project1.display());
}
