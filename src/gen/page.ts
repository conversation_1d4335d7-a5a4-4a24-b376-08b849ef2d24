import * as node_fs from "node:fs";
import * as node_path from "node:path";
import { project1 } from "../project-1";
import { ProjectObjectType } from "../proto";

const WIDGET_DIR = "" + ProjectObjectType.PROJECT_OBJECT_TYPE_PAGE_WIDGETS;

/**
 * 生成项目
 */
export function generatePage(output: string) {
    const widgetFolder = node_path.join(output, WIDGET_DIR);
    if (!node_fs.existsSync(widgetFolder)) {
        console.log("Creating directory: ", widgetFolder);
        node_fs.mkdirSync(widgetFolder);
    }

    for (const id in project1.pages) {
        const fty = project1.pages[id]!;
        const widgets = fty();

        const widgetFile = node_path.join(widgetFolder, `${id}`);
        console.log("Writing widget file: ", widgetFile);
        node_fs.writeFileSync(widgetFile, widgets);
    }
}
