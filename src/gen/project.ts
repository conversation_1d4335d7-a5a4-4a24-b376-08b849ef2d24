import * as node_fs from "node:fs";
import * as node_path from "node:path";
import { project1 } from "../project-1/index";
import { ProjectObjectType } from "../proto";

/**
 * 生成项目
 */
export function generateProject(output: string) {
    if (!node_fs.existsSync(output)) {
        console.log("Creating directory: ", output);
        node_fs.mkdirSync(output, { recursive: true });
    }

    const outputFolder = node_path.join(output, ProjectObjectType.PROJECT_OBJECT_TYPE_PROJECT_INFO.toString());
    node_fs.mkdirSync(outputFolder);
    const outputFile = node_path.join(outputFolder, "0");
    console.log("Writing project file: ", outputFile);
    node_fs.writeFileSync(outputFile, project1.project());
}
