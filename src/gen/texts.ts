import * as node_fs from "node:fs";
import * as node_path from "node:path";
import { project1 } from "../project-1";
import { ProjectObjectType } from "../proto";

/**
 * 生成 texts
 */
export function generateTexts(output: string) {
    const outputFolder = node_path.join(output, ProjectObjectType.PROJECT_OBJECT_TYPE_TEXT_TAG_LIBRARY.toString());
    node_fs.mkdirSync(outputFolder);

    const outputFile = node_path.join(outputFolder, `0`);
    node_fs.writeFileSync(outputFile, project1.texts());

    // for (const i18n of project1.texts()) {
    //     const outputFile = node_path.join(outputFolder, `${i18n.id}`);
    //     console.log("Writing display file: ", outputFile);
    //     node_fs.writeFileSync(outputFile, i18n.build());
    // }
}
