import * as node_fs from "node:fs";
import * as node_path from "node:path";
import { projectData } from "../project-1";
import { ProjectObjectType } from "../proto";

/**
 * 生成 address tag
 */
export function generateAddress(output: string) {
    const outputFolder = node_path.join(output, ProjectObjectType.PROJECT_OBJECT_TYPE_VARIABLE.toString());
    console.log("address tag folder: ", outputFolder);
    node_fs.mkdirSync(outputFolder);

    for (const [id, data] of projectData.getDeviceVariables()) {
        const outputFile = node_path.join(outputFolder, `${id}`);
        console.log("address tag file: ", outputFile);
        node_fs.writeFileSync(outputFile, data);
    }
}
