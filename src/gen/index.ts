import * as node_fs from "node:fs";
import * as node_path from "node:path";
import { getConfig } from "./conf";
import { generateDevice } from "./device";
import { generateDisplay } from "./display";
import { generateFont } from "./font";
import { generateImage } from "./image";
import { generatePage } from "./page";
import { generateProject } from "./project";
import { generateStyle } from "./style";
import { generateAddress } from "./tags";
import { generateTexts } from "./texts";

/**
 * 查找输出目录
 * @returns
 */
function findOutputFolder() {
    const parentFolder = node_path.join(__dirname, "../../..");
    if (node_fs.existsSync(node_path.join(parentFolder, "ui/asset"))) {
        return node_path.join(parentFolder, "ui", "asset/p");
    }

    if (node_fs.existsSync(node_path.join(parentFolder, "maxbuilder/public"))) {
        return node_path.join(parentFolder, "maxbuilder/public/p");
    }
    if (node_fs.existsSync(node_path.join(parentFolder, "zd-station/public"))) {
        return node_path.join(parentFolder, "zd-station/public/p");
    }
    console.log("No output folder found. Please check the project structure.");
    process.exit(1);
}

async function main() {
    const assetFolder = node_path.join(__dirname, "../../asset");
    const outputFolder = findOutputFolder();
    const sourceFolder = node_path.join(__dirname, "../project-1");

    console.log("output directory: ", outputFolder);
    if (node_fs.existsSync(outputFolder)) {
        console.log("Cleaning directory: ", outputFolder);
        node_fs.rmSync(outputFolder, { recursive: true });
    }

    console.log("Creating directory: ", outputFolder);
    node_fs.mkdirSync(outputFolder, { recursive: true });

    const output = node_path.join(outputFolder, "project-1");

    generateProject(output);
    generateDisplay(output);
    generatePage(output);
    generateDevice(output);
    generateAddress(output);
    generateStyle(output);
    generateImage(sourceFolder, output);
    generateTexts(output);
    await generateFont(output, assetFolder);

    const conf = getConfig();
    if (conf.server) {
        console.log("Server mode");
    } else {
        console.log("Client mode");
    }
}

main();
