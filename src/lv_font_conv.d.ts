module "lv_font_conv/lib/convert.js" {
    export type Format = "dump" | "bin" | "lvgl";
    export const formats = ["dump", "bin", "lvgl"] as const;

    export interface SymbolOption {
        symbols: string;
    }

    export interface RangeOption {
        range: [start: number, end: number, mapped_start: number];
    }

    interface FontSource {
        source_path: string;
        source_bin: Buffer;
        ranges: ArrayLike<SymbolOption | RangeOption>;
    }

    export interface ConvertOptions {
        /**
         * 输出字体大小，像素
         */
        size: number;
        /**
         * 输出路径
         */
        output: string;
        /**
         * 每个像素的位数，用于抗锯齿
         */
        bpp: number;
        /**
         * 是否启用子像素渲染（水平像素布局）
         *
         * @default false
         */
        lcd?: boolean | undefined;
        /**
         * 是否启用子像素渲染（垂直像素布局）
         *
         * @default false
         */
        lcd_v?: boolean | undefined;
        /**
         * 是否尝试使用字体中的颜色信息来创建灰度图标，
         * 由于灰度是通过透明度来模拟的，所以结果会在对比背景上看起来很好
         *
         * @default false
         */
        use_color_info?: boolean | undefined;
        /**
         * 输出格式
         */
        format: Format;
        /**
         * 字体源路径，可以多次使用以合并不同字体
         */
        font: FontSource[];
        // autohint_off: unknown;
        // autohint_strong: unknown;
        // fast_kerning: boolean;
        // no_compress: boolean;
        // no_prefilter: boolean;
        // no_kerning: boolean;
        // lv_include: unknown;
        // lv_font_name: unknown;
        // full_info: boolean;
        // lv_fallback: unknown;
        // opts_string: string;
    }

    export default function convert(args: ConvertOption): Promise<Record<string, Buffer>>;
}
