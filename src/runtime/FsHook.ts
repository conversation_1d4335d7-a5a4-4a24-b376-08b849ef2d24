/**
 * 文件状态
 */
export enum FileState {
    /**
     * 等待中
     * - 可能是还没开始加载
     * - 也可能是正在加载
     * 总之过一会再来读吧
     */
    Pending = 0,
    /**
     * 加载完成，表示文件已经加载完成
     */
    Loaded = 1,
    /**
     * 出错了
     * - 可能是找不到文件
     * - 也可能是其它原因
     * 总之暂时不需要再次尝试读取这个文件了
     */
    Error = 2,
}

/**
 * 文件内容读取不到的状态列表
 */
export type FileStateNoData = FileState.Pending | FileState.Error;

export interface FsHook {
    /**
     * 开始加载文件
     * @param path 文件路径
     * @returns 如果文件已经加载，则返回 true，否则返回 false，文件加载出错时也返回 true
     */
    loadFile(path: string): boolean;

    /**
     * 读取文件
     * @param path 文件路径
     * @returns
     */
    readFile(path: string): FileStateNoData | Uint8Array;
}
