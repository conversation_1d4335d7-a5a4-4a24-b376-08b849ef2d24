import type { UrqModule } from "./UrqModule";
import { noop } from "./util";

export type char_t = number;
export type int8_t = number;
export type uint8_t = number;
export type int16_t = number;
export type uint16_t = number;
export type int32_t = number;
export type uint32_t = number;
export type int64_t = bigint;
export type uint64_t = bigint;
/** 裸指针，如果是具体类型的指针，使用 {@link reference_t} */
export type pointer_t = number;
/** 某个类型的指针，如果是 void *，使用 {@link pointer_t} */
export type reference_t<T> = T;
/** 空指针 */
export const NULL = 0 as pointer_t;

export interface InitOptions {
    noInitialRun: boolean;
}

export interface BaseEmModule {
    readonly HEAP8: Int8Array;
    readonly HEAP16: Int16Array;
    readonly HEAP32: Int32Array;
    readonly HEAPU8: Uint8Array;
    readonly HEAPU16: Uint16Array;
    readonly HEAPU32: Uint32Array;
    readonly HEAPF32: Float32Array;
    readonly HEAPF64: Float64Array;
    _main(): void;
}

export type BaseTypeName = "i8" | "i16" | "i32" | "f32" | "f64" | `${string} *`;

// export interface ExternEmModule {
//     setValue(ptr: pointer_t, value: number, type: BaseTypeName): void;

//     getValue(ptr: pointer_t, type: BaseTypeName): number;
// }

export interface Wasm extends BaseEmModule, UrqModule {}

const LIB_NAME = "editor";

function locateFile(path: string, scriptDirectory: string): string {
    console.log("locate file, path: %s, scriptDirectory: %s", path, scriptDirectory);
    if (path === `${LIB_NAME}.wasm`) {
        return scriptDirectory + path;
    }
    if (path === `${LIB_NAME}.wasm.map`) {
        return scriptDirectory + path;
    }
    throw new Error(`locateFile: ${path}`);
}

export const PACK_SIZE = 4;

export function calcAlignment(offset: number, next: number): number {
    const align = Math.ceil(offset / PACK_SIZE) * PACK_SIZE;
    if (align === offset) {
        return offset;
    }
    if (align - offset >= next) {
        return offset;
    }
    return align;
}

async function init(options: InitOptions): Promise<Wasm> {
    if (typeof options !== "object") {
        options = { noInitialRun: true };
    }

    const opts: any = typeof options === "object" ? options : { noInitialRun: true };
    opts.locateFile = locateFile;

    let rootFolder: string | undefined;

    if (window.znd_runtime_debug_c === true) {
        opts.print = function (text: string) {
            if (rootFolder !== undefined) {
                console.log(text.replace(rootFolder, location.href));
            } else {
                const match = text.match(/(\/home\/[\w\/]+\/)urq\/ui\/.+/);
                if (match !== null && match[1] !== undefined) {
                    rootFolder = match[1];
                    console.log(text.replace(rootFolder, location.href));
                } else {
                    console.log(text);
                }
            }
        };
        opts.printErr = function (text: string) {
            if (rootFolder !== undefined) {
                console.log(text.replace(rootFolder, location.href));
            } else {
                console.log(text);
                const match = text.match(/(\/home\/[\w\/]+\/)urq\/ui\/.+/);
                if (match !== null && match[1] !== undefined) {
                    rootFolder = match[1];
                    console.error(text.replace(rootFolder, location.href));
                } else {
                    console.error(text);
                }
            }
        };
    } else {
        opts.print = noop;
        opts.printErr = noop;
    }

    let runtimePath = new URL(`/${LIB_NAME}.js`, window.location.href).href;
    if ("znd_runtime_path" in window) {
        const url = new URL(`${znd_runtime_path}${LIB_NAME}.js`, window.location.href);
        runtimePath = url.href;
    }

    const mod = await import(/* @vite-ignore */ runtimePath);
    return mod.default(opts);
}

export default init;
