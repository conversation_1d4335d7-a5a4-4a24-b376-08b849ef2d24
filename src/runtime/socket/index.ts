import { Runtime } from "../Runtime";
import { idFactory } from "../util";
import type { int32_t, pointer_t, reference_t, uint16_t, uint32_t, uint8_t } from "../wasm";
import { Socket } from "./Socket";

const newId = idFactory();

const map = new Map<int32_t, Socket>();

export function create(iid: int32_t): int32_t {
    const runtime = Runtime.find(iid);
    const id = newId();
    map.set(id, new Socket(iid, runtime));
    console.log("socket create, iid: %d, id: %d", iid, id);
    return id;
}

export function del(iid: int32_t): void {
    if (!map.has(iid)) {
        throw new Error(`socket iid ${iid} not found`);
    }
    console.log(`socket delete ${iid}`);
    map.delete(iid);
}

export function isConnected(iid: int32_t): boolean {
    const socket = map.get(iid);
    if (socket === undefined) {
        throw new Error(`socket iid ${iid} not found`);
    }
    return socket.connected;
}

export function read(iid: int32_t, size: uint32_t, out_data: reference_t<uint8_t>, out_len: reference_t<uint32_t>) {
    const socket = map.get(iid);
    if (socket === undefined) {
        throw new Error(`socket iid ${iid} not found`);
    }
    return socket.read(size, out_data, out_len);
}

export function readable(iid: int32_t): boolean {
    const socket = map.get(iid);
    if (socket === undefined) {
        throw new Error(`socket iid ${iid} not found`);
    }
    return socket.readable();
}

export function setHost(iid: int32_t, ip: uint32_t, port: uint16_t): void {
    const socket = map.get(iid);
    if (socket === undefined) {
        throw new Error(`socket iid ${iid} not found`);
    }
    socket.setHost(ip, port);
}

export function write(iid: int32_t, ptr: pointer_t, len: uint32_t): void {
    const socket = map.get(iid);
    if (socket === undefined) {
        throw new Error(`socket iid ${iid} not found`);
    }
    socket.write(ptr, len);
}

export function connect(iid: int32_t): int32_t {
    const socket = map.get(iid);
    if (socket === undefined) {
        throw new Error(`socket iid ${iid} not found`);
    }
    if (socket.connected) {
        return 0;
    }
    return 1;
}
