import { Runtime } from "../Runtime";
import type { int32_t, pointer_t, reference_t, uint16_t, uint32_t, uint8_t } from "../wasm";

type MaybeUndefine<T> = T | undefined;

interface IHandshake {
    readonly major: number;
    readonly minor: number;
    readonly patch: number;
    readonly build: number;
}

const CLIENT_VERSION = [0, 0, 0, 1] as const;

function parseHandshake(buffer: Uint8Array): IHandshake {
    if (buffer.length !== 4) {
        throw new Error("250125224224267, invalid handshake");
    }
    const major = buffer[0]!;
    const minor = buffer[1]!;
    const patch = buffer[2]!;
    const build = buffer[3]!;
    return {
        major,
        minor,
        patch,
        build,
    };
}

interface IPromiseInfo<T> {
    readonly promise: Promise<T>;
    readonly resolve: (value: T) => void;
    readonly reject: (reason: unknown) => void;
}

function createPromise<T>(): IPromiseInfo<T> {
    let resolve: (value: T) => void;
    let reject: (reason: unknown) => void;
    const p = new Promise<T>((res, rej) => {
        resolve = res;
        reject = rej;
    });
    return { promise: p, resolve: resolve!, reject: reject! };
}

function isArrayBuffer(data: unknown): data is ArrayBuffer {
    return data instanceof ArrayBuffer;
}

export class Socket {
    readonly id: int32_t;

    readonly #runtime: Runtime;

    /**
     * WebSocket 地址
     * undefine 表示未设置
     * null     表示设计模式不需要连接
     * string   表示已设置
     */
    #url: string | undefined | null;

    /** 是否正在建立连接中 */
    #connecting: IPromiseInfo<boolean> | undefined;

    /** WebSocket 实例 */
    #ws: WebSocket | undefined;

    /** 连接失败的次数 */
    #failedCount;

    readonly #buffers: Uint8Array[];

    constructor(id: number, runtime: Runtime) {
        this.id = id;
        this.#runtime = runtime;
        this.#buffers = [];
        this.#failedCount = 0;
    }

    /**
     * 是否已连接
     */
    get connected() {
        return this.#ws !== undefined;
    }

    connect(): Promise<boolean> {
        if (this.#url === null) {
            return Promise.resolve(true);
        }

        if (this.#url === undefined) {
            throw new Error("url is undefined");
        }
        if (this.#ws !== undefined) {
            return Promise.resolve(true);
        }
        if (this.#connecting !== undefined) {
            return this.#connecting.promise;
        }
        this.#connecting = createPromise();
        const ws = new WebSocket(this.#url);
        ws.binaryType = "arraybuffer";

        ws.onerror = (event) => {
            console.log("连接出错", event);
            this.#failedCount++;
            this.#connecting!.resolve(false);
            this.#connecting = undefined;

            if (this.#failedCount > 3) {
                window.setTimeout(() => {
                    this.connect();
                }, 1000);
                return;
            } else {
                window.requestIdleCallback(() => {
                    this.connect();
                });
            }
        };

        ws.onopen = () => {
            ws.onerror = null;
            this.#handshake(ws);
        };
        return this.#connecting.promise;
    }

    #handshake(ws: WebSocket) {
        ws.onclose = () => {
            if (this.#connecting === undefined) {
                throw new Error("250125224224267, 未知错误");
            }
            this.#connecting.reject(new Error("连接意外关闭"));
            this.#connecting = undefined;
        };
        ws.onerror = (event) => {
            if (this.#connecting === undefined) {
                throw new Error("250125224224267, 未知错误");
            }
            this.#connecting.reject(event);
            this.#connecting = undefined;
        };
        ws.onmessage = (event) => {
            ws.onclose = null;
            ws.onerror = null;
            ws.onmessage = null;
            if (this.#connecting === undefined) {
                throw new Error("250125224224267, 未知错误");
            }

            if (!isArrayBuffer(event.data)) {
                ws.close();
                this.#connecting.reject(new Error("非法数据包"));
                this.#connecting = undefined;
                return;
            }

            const buffer = new Uint8Array(event.data);
            try {
                const handshake = parseHandshake(buffer);
                console.log("握手成功", handshake);
                ws.onmessage = this.#messageHandler;
                ws.onclose = this.#closeHandler;
                this.#ws = ws;
                this.#connecting.resolve(true);
                this.#connecting = undefined;
            } catch (e) {
                if (this.#connecting === undefined) {
                    throw new Error("250125224224267, 未知错误");
                }
                console.error(e);
                ws.close();
                this.#connecting.reject(e);
                this.#connecting = undefined;
            }
        };
        ws.send(new Uint8Array([...CLIENT_VERSION]));
        console.log("发送握手包");
    }

    #closeHandler = () => {
        this.#ws = undefined;
        console.log("%c连接已关闭", "color: red");
        if (this.onclose !== undefined) {
            this.onclose();
        }
        this.connect();
    };

    #messageHandler = (event: MessageEvent<unknown>) => {
        if (!isArrayBuffer(event.data)) {
            console.log("非法数据包", event);
            return;
        }
        const buffer = new Uint8Array(event.data);
        this.#buffers.push(buffer);
        // console.log("on message:\n%s", bufToHex(buffer));
    };

    onclose: MaybeUndefine<() => void>;

    read(size: uint32_t, out_data: reference_t<uint8_t>, out_len: reference_t<uint32_t>): number {
        let len = 0;
        while (true) {
            let buf = this.#buffers.shift();
            if (buf === undefined) {
                this.#runtime.writeU32(out_len, len);
                return 0;
            }
            const need = size - len;
            if (need > buf.length) {
                this.#runtime.writeBuffer(out_data + len, buf);
                len += buf.length;
                continue;
            }
            const right = new Uint8Array(buf, need);
            this.#buffers.unshift(right);
            buf = new Uint8Array(buf, 0, need);
            this.#runtime.writeBuffer(out_data + len, buf);
            this.#runtime.writeU32(out_len, size);
            return 0;
        }
    }

    readable(): boolean {
        return this.#buffers.length > 0;
    }

    setHost(ip: uint32_t, port: uint16_t) {
        if (ip === 0 && port === 0) {
            this.#url = null;
            return;
        }
        this.#url = `ws://${(ip >> 24) & 0xff}.${(ip >> 16) & 0xff}.${(ip >> 8) & 0xff}.${ip & 0xff}:${port}`;
        if (this.#ws !== undefined) {
            this.#ws.close();
            this.#ws = undefined;
        }
        this.connect();
    }

    write(ptr: pointer_t, len: uint32_t) {
        if (this.#ws === undefined) {
            if (this.#url === null) {
                return;
            }
            console.warn("连接已断开");
            return;
        }
        const data = this.#runtime.readBuffer(ptr, len);
        if (data.length === 0) {
            console.warn("发送数据为空");
            return;
        }

        let str = "";
        for (const v of data) {
            str += v.toString(16).padStart(2, "0") + " ";
        }
        // console.log("send data: ", str);
        this.#ws.send(data);
    }
}
