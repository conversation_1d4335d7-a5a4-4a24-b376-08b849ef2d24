import { Runtime } from "./Runtime";
import type { int32_t, pointer_t } from "./wasm";

interface PointerData {
    x: number;
    y: number;
    pressure: boolean;
}

const map: WeakMap<Runtime, PointerData> = new WeakMap();

export function init(urq: Runtime, canvas: HTMLCanvasElement) {
    const data: PointerData = {
        x: 0,
        y: 0,
        pressure: false,
    };

    canvas.addEventListener("mousedown", (e) => {
        const rect = canvas.getBoundingClientRect();
        data.x = e.clientX - rect.left;
        data.y = e.clientY - rect.top;
        data.pressure = true;
    });

    canvas.addEventListener("mouseup", (e) => {
        const rect = canvas.getBoundingClientRect();
        data.x = e.clientX - rect.left;
        data.y = e.clientY - rect.top;
        data.pressure = false;
    });

    map.set(urq, data);
}

const X = 0;
const Y = 1;
const PRESSURE = 2;
const NEXT = 3;

export function read(id: int32_t, ptr: pointer_t) {
    const urq = Runtime.find(id);
    const data = map.get(urq)!;
    const event = new Int32Array(urq.wasm.HEAP8.buffer, ptr, 4);

    event[X] = data.x;
    event[Y] = data.y;
    event[PRESSURE] = data.pressure ? 1 : 0;
    event[NEXT] = 0;
}
