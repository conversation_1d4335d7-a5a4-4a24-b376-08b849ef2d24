import { Runtime } from "./Runtime";
import type { int32_t, pointer_t } from "./wasm";

export interface KeyInfo {
    key: number;
    pressure: boolean;
}

const map: WeakMap<Runtime, KeyInfo> = new WeakMap();

const KEY = 0;
const pressure = 1;
const NEXT = 2;

export function init(urq: Runtime) {
    const info: KeyInfo = {
        key: 0,
        pressure: false,
    };
    map.set(urq, info);
}

export function read(id: int32_t, ptr: pointer_t) {
    const urq = Runtime.find(id);
    const event = new Int32Array(urq.wasm.HEAP8.buffer, ptr, 3);
    const info = map.get(urq)!;

    event[KEY] = info.key;
    event[pressure] = info.pressure ? 1 : 0;
    event[NEXT] = 0;
}
