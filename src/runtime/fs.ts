import { Errno, isErr } from "./Errno";
import { FileState } from "./FsHook";
import { Runtime } from "./Runtime";
import { to_pointer } from "./util";
import { NULL, type char_t, type int32_t, type pointer_t, type reference_t, type uint32_t } from "./wasm";

export function loadFile(id: int32_t, path_ptr: reference_t<char_t>) {
    const rt = Runtime.find(id);
    let path = rt.readString(path_ptr);
    if (isErr(path)) {
        throw new Error("250113183116014, path error");
    }
    return rt.fs.loadFile(path) ? 0 : -1;
}

export function exists(id: int32_t, path_ptr: reference_t<char_t>, out_size: reference_t<uint32_t>) {
    const rt = Runtime.find(id);
    let path = rt.readString(path_ptr);
    if (isErr(path)) {
        throw new Error("250113183116014, path error");
    }
    const data = rt.fs.readFile(path);
    if (!(data instanceof Uint8Array)) {
        if (data === FileState.Pending) {
            return rt.setErrno(Errno.Pending);
        }
        return rt.setErrno(Errno.Unexpected);
    }
    rt.writeU32(out_size, data.length);
    return 0;
}

/**
 * 读取文件
 * @param id        wasm id
 * @param path_ptr  文件路径
 * @param out_file  输出文件
 * @returns
 */
export function readFile(id: int32_t, path_ptr: reference_t<char_t>, out_file: pointer_t) {
    const rt = Runtime.find(id);
    let path = rt.readString(path_ptr);
    if (isErr(path)) {
        console.log("urq_web_fs_read, path error: %s", path.ecode);
        return rt.setErrno(path.ecode);
    }
    console.log("urq_web_fs_read, path: %s", path);
    const data = rt.fs.readFile(path);
    if (!(data instanceof Uint8Array)) {
        switch (data) {
            case FileState.Pending:
                return rt.setErrno(Errno.Pending);
            case FileState.Error:
                return rt.setErrno(Errno.FileNotFound);
            default:
                return rt.setErrno(Errno.Unexpected);
        }
    }
    const ptr = rt.malloc(4 + data.length);
    if (ptr === NULL) {
        return rt.setErrno(Errno.ENOMEM);
    }
    rt.writeU32(ptr, data.length);
    rt.writeBuffer(ptr + 4, data);
    rt.writePtr(out_file, ptr);
    return 0;
}

export function open(id: int32_t, path_ptr: reference_t<char_t>, out_fd: reference_t<int32_t>) {
    const rt = Runtime.find(id);
    let path = rt.readString(path_ptr);
    if (isErr(path)) {
        console.log("urq_web_fs_open, path error: %s", path.ecode);
        return rt.setErrno(path.ecode);
    }
    const data = rt.fs.readFile(path);
    if (!(data instanceof Uint8Array)) {
        switch (data) {
            case FileState.Pending:
                return rt.setErrno(Errno.Pending);
            case FileState.Error:
                return rt.setErrno(Errno.FileNotFound);
            default:
                return rt.setErrno(Errno.Unexpected);
        }
    }
    const fp = rt.malloc(8 + data.length);
    if (fp === NULL) {
        return rt.setErrno(Errno.ENOMEM);
    }
    console.log("urq_web_fs_open, file: %s, fp: %s, size: %s", path, to_pointer(fp), data.length);
    rt.writeU32(fp, data.length);
    rt.writeU32(fp + 4, 0);
    rt.writeBuffer(fp + 8, data);
    if (rt.writePtr(out_fd, fp) !== Errno.Ok) {
        rt.free(fp);
        rt.writePtr(out_fd, NULL);
        return rt.setErrno(Errno.Unexpected);
    }
    return 0;
}
