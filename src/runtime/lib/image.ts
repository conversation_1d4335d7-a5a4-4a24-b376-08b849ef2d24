export const enum ColorFormat {
    CF_TRUE_COLOR = 4,
    CF_TRUE_COLOR_ALPHA = 5,
    CF_TRUE_COLOR_CHROMA = 6,
    CF_INDEXED_1_BIT = 7,
    CF_INDEXED_2_BIT = 8,
    CF_INDEXED_4_BIT = 9,
    CF_INDEXED_8_BIT = 10,
    CF_ALPHA_1_BIT = 11,
    CF_ALPHA_2_BIT = 12,
    CF_ALPHA_4_BIT = 13,
    CF_ALPHA_8_BIT = 14,
}

/**
 * 将图片转为 LVGL 的图像格式
 * @param image 需要转换的图片
 * @returns
 */
function convert(image: HTMLImageElement) {
    const format = ColorFormat.CF_TRUE_COLOR_ALPHA;
    const canvas = new OffscreenCanvas(image.width, image.height);
    const context = canvas.getContext("2d");
    if (!context) {
        throw new Error("250119102757107, Failed to get 2D context");
    }
    context.drawImage(image, 0, 0);
    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    const output = new Uint8Array(4 + canvas.width * canvas.height * 4);

    const x = (format | (canvas.width << 10) | (canvas.height << 21)) >>> 0;
    output[0] = 255 & x;
    output[1] = (65280 & x) >> 8;
    output[2] = (0xff0000 & x) >> 16;
    output[3] = (0xff000000 & x) >> 24;

    const out = new Uint8Array(output.buffer, 4);
    let idx = 0;
    for (let i = 0; i < canvas.width * canvas.height; i++) {
        idx = i << 2;
        out[idx] = data[idx + 2]!;
        out[idx + 1] = data[idx + 1]!;
        out[idx + 2] = data[idx]!;
        out[idx + 3] = data[idx + 3]!;
    }
    return output;
}

/**
 * 将图像转换为 lvgl 格式的数据
 *
 * @param path - 图像路径
 * @returns
 */
export function convertImage(path: string): Promise<Uint8Array> {
    const image = new Image();
    image.src = path;
    return new Promise((resolve, reject) => {
        image.onload = () => {
            try {
                const output = convert(image);
                resolve(output);
            } catch (e) {
                reject(e);
            }
        };
        image.onerror = (e) => {
            reject(e);
        };
    });
}
