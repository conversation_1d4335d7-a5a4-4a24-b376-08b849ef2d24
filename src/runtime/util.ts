import type { pointer_t } from "./wasm";

export function to_pointer(ptr: pointer_t) {
    const vl = ptr.toString(16);
    return `0x${vl}`;
}

let idCount = 0;

export function newUId() {
    idCount++;
    if (idCount > 0x7fffffff) {
        idCount = 0;
    }
    return idCount;
}

/**
 * 什么也不做的函数
 */
export function noop(...args: any[]): void;
export function noop(): void {}

/**
 * id 工厂类
 * @param max id 最大值
 * @returns
 */
export function idFactory(max?: number | undefined) {
    let counter = 0;
    if (max === undefined) {
        return () => {
            return ++counter;
        };
    }
    return () => {
        let id = ++counter;
        if (counter > max) {
            counter = 0;
        }
        return id;
    };
}

// /**
//  * 打印一段 wasm 内存的内容
//  *
//  * @param obj  需要打印的对象
//  * @param size 需要打印的长度
//  * @param wasm wasm 实例
//  */
// export function logHex(obj: object_t, size: number, wasm: Wasm): void {
//     const buf = new Uint8Array(wasm.HEAPU8.buffer, obj.ptr, size);
//     let str = "";
//     let count = 0;
//     for (const v of buf) {
//         str += v < 0xf ? "0" + v.toString(16) : v.toString(16);
//         count++;
//         if (count === 16) {
//             str += "\n";
//             count = 0;
//             continue;
//         }

//         if (count % 4 === 0) {
//             str += ", ";
//             continue;
//         }
//         str += " ";
//     }
//     console.log(str);
// }

/**
 * 将一个 buffer 转换为 hex 字符串
 * @param buf 需要转换的 buffer
 * @returns
 */
export function bufToHex(buf: Uint8Array): string {
    let str = "";
    let count = 0;
    for (const v of buf) {
        str += v < 0xf ? "0" + v.toString(16) : v.toString(16);
        count++;
        if (count === 16) {
            str += "\n";
            count = 0;
            continue;
        }

        if (count % 4 === 0) {
            str += ", ";
            continue;
        }
        str += " ";
    }
    return str;
}
