import { Runtime } from "./Runtime";
import type { int32_t, pointer_t } from "./wasm";

interface Display {
    readonly ctx: CanvasRenderingContext2D;
    /** 颜色缓冲 */
    readonly rgba: Uint8Array;
}

const map: WeakMap<Runtime, Display> = new WeakMap();

/**
 * 创建 canvas
 * @param urq
 * @returns
 */
export function create(id: int32_t, width: int32_t, height: int32_t, _display: pointer_t) {
    const urq = Runtime.find(id);
    const ctx = urq.canvas.getContext("2d");
    if (ctx === null) {
        throw new Error("240904104557591, canvas context is null: " + urq.id);
    }
    const rgba = new Uint8Array(width * height * 4);
    const display: Display = { ctx, rgba };
    map.set(urq, display);

    urq.canvas.width = width;
    urq.canvas.height = height;
}

export function find(urq: Runtime): HTMLCanvasElement | undefined {
    return map.get(urq)?.ctx.canvas;
}

export function flush(id: int32_t, x1: int32_t, y1: int32_t, x2: int32_t, y2: int32_t, bufferP: pointer_t) {
    const urq = Runtime.find(id);
    const display = map.get(urq)!;
    const width = x2 - x1 + 1;
    const height = y2 - y1 + 1;

    const u8: Uint8Array = urq.wasm.HEAPU8;
    const size = width * height;
    const bgra = new Uint8Array(u8.buffer, bufferP, size * 4);
    const rgba = new Uint8Array(display.rgba.buffer, 0, size * 4);

    let idx = 0;
    for (let i = 0; i < size; i++) {
        idx = i << 2;
        rgba[idx] = bgra[idx + 2]!;
        rgba[idx + 1] = bgra[idx + 1]!;
        rgba[idx + 2] = bgra[idx]!;
        rgba[idx + 3] = bgra[idx + 3]!;
    }

    const data: Uint8ClampedArray = new Uint8ClampedArray(rgba.length);
    data.set(rgba);
    const imageData = new ImageData(data, width, height);
    display.ctx.putImageData(imageData, x1, y1);
}
