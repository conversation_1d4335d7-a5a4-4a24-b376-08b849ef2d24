export enum Errno {
    Ok = 0,
    ENOMEM = 48,
    EINVAL = 28,

    Min = 10000,
    /** 意外错误 */
    Unexpected,
    /** 未实现 */
    NotImplement,
    /** 等待中 */
    Pending,

    // 参数
    /** 参数非法 */
    ArgIllegal,
    /** 参数过长 */
    ArgTooLong,

    // 文件
    /** 找不到文件 */
    FileNotFound,

    // 连接
    /** 连接已关闭 */
    ConnClosed,

    /** 最大错误码 */
    Max,
}

/** 错误类 */
export class Error {
    readonly ecode: Errno;
    constructor(ecode: Errno) {
        this.ecode = ecode;
    }
}

export type Result<T> = Error | T;

export function ok<T>(data: T): Result<T> {
    return data;
}

export function err<T>(ecode: Errno): Result<T> {
    return new Error(ecode);
}

export function isErr<T>(result: Result<T>): result is Error {
    return result instanceof Error;
}
