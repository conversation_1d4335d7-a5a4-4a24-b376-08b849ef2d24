export type { EditorRuntime } from "./EditorRuntime.js";
export { FileState, type FileStateNoData, type FsHook } from "./FsHook.js";

import * as display from "./display";
import type { EditorRuntime } from "./EditorRuntime";
import * as fs from "./fs";
import type { FsHook } from "./FsHook";
import * as keyboard from "./keyboard";
import * as pointer from "./pointer";
import { Runtime } from "./Runtime";
import * as socket from "./socket/index";
import init from "./wasm";

Reflect.defineProperty(window, "urq_web_fs_load_file", { value: fs.loadFile });
Reflect.defineProperty(window, "urq_web_fs_exists", { value: fs.exists });
Reflect.defineProperty(window, "urq_web_fs_read_file", { value: fs.readFile });
Reflect.defineProperty(window, "urq_web_fs_open", { value: fs.open });
Reflect.defineProperty(window, "urq_web_display_flush", { value: display.flush });
Reflect.defineProperty(window, "urq_web_create_display", { value: display.create });
Reflect.defineProperty(window, "urq_web_read_mouse", { value: pointer.read });
Reflect.defineProperty(window, "urq_web_read_keyboard", { value: keyboard.read });

// socket
Reflect.defineProperty(window, "urq_web_socket_create", { value: socket.create });
Reflect.defineProperty(window, "urq_web_socket_connect", { value: socket.connect });
Reflect.defineProperty(window, "urq_web_socket_delete", { value: socket.del });
Reflect.defineProperty(window, "urq_web_socket_readable", { value: socket.readable });
Reflect.defineProperty(window, "urq_web_socket_read", { value: socket.read });
Reflect.defineProperty(window, "urq_web_socket_set_host", { value: socket.setHost });
Reflect.defineProperty(window, "urq_web_socket_write", { value: socket.write });

/**
 * 创建一个运行
 * @param w  画布宽度
 * @param h  画布高度
 * @param pw 工程宽度
 * @param ph 工程高度
 * @param fs 文件系统钩子
 * @param parentNode 父节点
 * @returns
 */
export async function create(w: number, h: number, fs: FsHook, parentNode: Node): Promise<EditorRuntime> {
    const wasm = await init({ noInitialRun: true });
    // for (let k in wasm) {
    //     k.startsWith("_urq_") && console.log(k);
    // }
    const rt = new Runtime(wasm, fs);
    rt.main(rt.id, -1, 0, w, h);
    keyboard.init(rt);
    pointer.init(rt, rt.canvas);
    parentNode.appendChild(rt.canvas);
    return rt;
}
