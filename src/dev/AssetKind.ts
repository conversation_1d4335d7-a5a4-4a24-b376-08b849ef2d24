export const enum AssetKind {
    Unknown = 0,
    Project = 1,
    Display = 2,
    Widget = 3,
    Texts = 7,
    Font = 8,
    Image = 9,
}

export function toAssetKind(value: unknown): AssetKind {
    let v: number = 0;
    switch (typeof value) {
        case "string":
            v = Number(value);
            break;
        case "number":
            v = value;
            break;
        default:
            return AssetKind.Unknown;
    }
    if (!Number.isInteger(v)) {
        return AssetKind.Unknown;
    }
    return v as AssetKind;
}
