import { toAssetKind, type <PERSON>set<PERSON><PERSON> } from "./AssetKind";

function isRouteParts(parts: string[]): parts is [string, string, string, string] {
    return parts.length === 4 && parts[0] === "p";
}

export class Route {
    readonly kind: AssetKind;
    readonly project: string;
    readonly id: number;

    constructor(kind: AssetKind, project: string, id: number) {
        this.kind = kind;
        this.project = project;
        this.id = id;
    }

    static parse(path: string): Route {
        const parts = path.split("/");
        if (!isRouteParts(parts)) {
            throw new Error(`Invalid route: ${path}`);
        }
        if (parts.length < 3) {
            throw new Error(`Invalid route: ${path}`);
        }
        const project = parts[1];
        let idOrBin = parts[3];
        if (idOrBin.endsWith(".bin")) {
            idOrBin = idOrBin.substring(0, -4);
        }
        const id = Number(idOrBin);
        if (!Number.isInteger(id)) {
            throw new Error("250203192938118, invalid id");
        }
        return new Route(toAssetKind(parts[2]), project, id);
    }
}
