import { project1 } from "../project-1/index";
import { texts } from "../project-1/texts";
import type { FileStateNoData } from "../runtime/FsHook";
import { FileState, FsHook } from "../runtime/index";
import { AssetKind } from "./AssetKind";
import { Route } from "./Route";

interface FileCache {
    state: FileState;
    data: Uint8Array;
}

const MOCK: Uint8Array = new Uint8Array(0);

/**
 * 文件路径和文件的表
 */
const FILE_DATA_MAP: Map<string, FileCache> = new Map();

function load(path: string) {
    if (FILE_DATA_MAP.has(path)) {
        return;
    }
    const cache: FileCache = {
        state: FileState.Pending,
        data: MOCK,
    };
    FILE_DATA_MAP.set(path, cache);
    fetch(path)
        .then((res) => {
            if (res.status !== 200) {
                console.log("fetch: %s, path: %s", res.status, path);
            }

            if (res.status === 200) {
                return res.arrayBuffer();
            } else {
                return undefined;
            }
        })
        .then(function (data) {
            return new Promise<ArrayBuffer | undefined>(function (resolve) {
                window.setTimeout(function () {
                    resolve(data);
                }, 1000);
            });
        })
        .then(function (data) {
            let fileData = FILE_DATA_MAP.get(path);
            if (fileData === undefined) {
                throw new Error("250113183116013, file not found");
            }
            if (fileData.state !== FileState.Pending) {
                throw new Error("250113183116012, unexpected file state");
            }
            if (data === undefined) {
                fileData.state = FileState.Error;
                fileData.data = MOCK;
            } else {
                fileData.state = FileState.Loaded;
                fileData.data = new Uint8Array(data);
            }
        })
        .catch((e) => {
            console.log("urq_web_fs_read, error: %s, file: %s", e, path);
        });
}

const HRL_KIND = new Set([AssetKind.Project, AssetKind.Display, AssetKind.Widget, AssetKind.Texts]);

function buildField(route: Route) {
    switch (route.kind) {
        case AssetKind.Project:
            return project1.project();
        case AssetKind.Display:
            return project1.display();
        case AssetKind.Widget: {
            const fty = project1.pages[route.id];
            if (fty === undefined) {
                return FileState.Error;
            }
            const [widgets] = fty();
            return widgets;
        }
        case AssetKind.Texts:
            return texts();
    }
    return undefined;
}

export function loadFile(path: string) {
    const route = Route.parse(path);
    if (HRL_KIND.has(route.kind)) {
        try {
            return buildField(route) !== undefined;
        } catch (error) {
            return false;
        }
    }
    const file = FILE_DATA_MAP.get(path);
    if (file === undefined) {
        load(path);
        return false;
    }
    return file.state !== FileState.Pending;
}

export function readFile(path: string): FileStateNoData | Uint8Array {
    const route = Route.parse(path);
    switch (route.kind) {
        // case AssetKind.Unknown:
        case AssetKind.Project:
            return project1.project();
        case AssetKind.Display:
            return project1.display();
        case AssetKind.Widget: {
            const fty = project1.pages[route.id];
            if (fty === undefined) {
                return FileState.Error;
            }
            return fty();
        }
        case AssetKind.Texts:
            return texts();
    }

    const file = FILE_DATA_MAP.get(path);
    if (file === undefined) {
        load(path);
        return FileState.Pending;
    }
    switch (file.state) {
        case FileState.Pending:
            return FileState.Pending;
        case FileState.Error:
            return FileState.Error;
        case FileState.Loaded:
            return file.data;
    }
}

export const fetchFs: FsHook = {
    loadFile,
    readFile,
};
