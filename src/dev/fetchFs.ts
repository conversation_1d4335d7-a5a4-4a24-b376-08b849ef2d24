import type { FileStateNoData } from "../runtime/FsHook";
import { FileState, FsHook } from "../runtime/index";

interface FileCache {
    state: FileState;
    data: Uint8Array;
}

const MOCK: Uint8Array = new Uint8Array(0);

/**
 * 文件路径和文件的表
 */
const FILE_DATA_MAP: Map<string, FileCache> = new Map();

function load(path: string) {
    if (FILE_DATA_MAP.has(path)) {
        return;
    }
    const cache: FileCache = {
        state: FileState.Pending,
        data: MOCK,
    };
    FILE_DATA_MAP.set(path, cache);
    fetch(path)
        .then((res) => {
            if (res.status !== 200) {
                console.log("fetch: %s, path: %s", res.status, path);
            }

            if (res.status === 200) {
                return res.arrayBuffer();
            } else {
                return undefined;
            }
        })
        // .then(function (data) {
        //     return new Promise<ArrayBuffer | undefined>(function (resolve) {
        //         window.setTimeout(function () {
        //             resolve(data);
        //         }, 1000);
        //     });
        // })
        .then(function (data) {
            let fileData = FILE_DATA_MAP.get(path);
            if (fileData === undefined) {
                throw new Error("250113183116013, file not found");
            }
            if (fileData.state !== FileState.Pending) {
                throw new Error("250113183116012, unexpected file state");
            }
            if (data === undefined) {
                fileData.state = FileState.Error;
                fileData.data = MOCK;
            } else {
                fileData.state = FileState.Loaded;
                fileData.data = new Uint8Array(data);
            }
        })
        .catch((e) => {
            console.log("urq_web_fs_read, error: %s, file: %s", e, path);
        });
}

export function loadFile(path: string) {
    const file = FILE_DATA_MAP.get(path);
    if (file === undefined) {
        load(path);
        return false;
    }
    return file.state !== FileState.Pending;
}

export function readFile(path: string): FileStateNoData | Uint8Array {
    const file = FILE_DATA_MAP.get(path);
    if (file === undefined) {
        load(path);
        return FileState.Pending;
    }
    switch (file.state) {
        case FileState.Pending:
            return FileState.Pending;
        case FileState.Error:
            return FileState.Error;
        case FileState.Loaded:
            return file.data;
    }
}

export const fetchFs: FsHook = {
    loadFile,
    readFile,
};

Reflect.defineProperty(window, "deleteConfig", {
    value: function () {
        for (const key of FILE_DATA_MAP.keys()) {
            FILE_DATA_MAP.delete(key);
        }
    },
    writable: false,
    configurable: false,
});
