export { ActionList, WidgetPropertyEnum } from "../proto/dev/active";

export { Action, ActionTiming, BitSetMode, PageActionType } from "../proto/dev/action";

export { AlignType, Location, ScrollDirection, Size } from "../proto/dev/common";

export { Device, ProtocolType, Tunnel, TunnelDeviceList } from "../proto/dev/device";

export { Display, DisplayList, PageInfo, PageType } from "../proto/dev/display";

export { HardwareType, ProjectInfo, ProjectObjectType, Version } from "../proto/dev/project";

export {
    ColorReference,
    GraphicReference,
    GraphicType,
    ProjectStyleConfig,
    StyleBorder,
    StyleProperties,
    StyleShadow,
} from "../proto/dev/style";

export { FontDefinition, FontFileType, FontStyle } from "../proto/dev/style";
export { TextReference, TextTag, TextTagLibrary } from "../proto/dev/text";

export {
    DataFormatType,
    DataReferenceUInt8,
    StructType,
    StructTypeField,
    Variable,
    VariableLibrary,
    VariableReference,
} from "../proto/dev/variable";

export { PageWidgets, Widget, WidgetVariableReference } from "../proto/dev/widget";
