// 刻度尺组件模拟数据
// import { Widget } from '../../gen/ts/znd/project/v1/widget';

// 水平刻度尺模拟数据
// export const horizontalRulerMockData: Widget = {
//   idU16: 1001,
//   name: "水平刻度尺",
//   memo: "用于测量水平距离的刻度尺",
//   visible: true,
//   pin: false,
//   lockScale: false,
//   rwMode: 0, // READ_WRITE_MODE_UNSPECIFIED
//   location: {
//     leftI16: 50,
//     topI16: 100,
//   },
//   size: {
//     widthI16: 300,
//     heightI16: 50,
//   },
//   enableControl: undefined,
//   stateProperty: undefined,
//   actionsU16: [],
//   minPressTimeU16: 0,
//   minPressIntervalU16: 0,
//   confirmParam: undefined,
//   soundFeedback: false,
//   graphic: [],
//   style: [],
//   styleSyncU8: 0,
//   text: [],
//   widget: {
//     $case: "widgetRuler",
//     value: {
//       rulerType: 1, // RULER_TYPE_HORIZONTAL
//       direction: 2, // RULER_DIRECTION_BOTTOM_RIGHT
//       labelPosition: 1, // RULER_LABEL_POSITION_OUTSIDE
//       scaleConfig: {
//         show: true,
//         scaleShowType: 1, // SCALE_SHOW_TYPE_POINT
//         gridShowType: 1, // GRID_SHOW_TYPE_MAIN
//         scaleMain: {
//           scaleCountU8: 10,
//           scaleWidthU8: 2,
//           scaleDrawLen: 15,
//           color: {
//             type: 1, // COLOR_TYPE_RGBA
//             rgba: {
//               r: 0,
//               g: 0,
//               b: 0,
//               a: 255,
//             },
//           },
//         },
//         scaleSec: {
//           scaleCountU8: 50,
//           scaleWidthU8: 1,
//           scaleDrawLen: 8,
//           color: {
//             type: 1, // COLOR_TYPE_RGBA
//             rgba: {
//               r: 128,
//               g: 128,
//               b: 128,
//               a: 255,
//             },
//           },
//         },
//         scaleRadiusU8: 0,
//         minValue: {
//           from: { $case: "valueI32", value: 0 },
//         },
//         maxValue: {
//           from: { $case: "valueI32", value: 100 },
//         },
//         gridLineConfig: {
//           lineType: 1, // LINE_TYPE_LINE
//           roundStart: false,
//           roundEnd: false,
//           dashGapU8: 0,
//           dashWidthU8: 0,
//           widthU8: 1,
//           lengthU8: 0,
//           color: {
//             type: 1, // COLOR_TYPE_RGBA
//             rgba: {
//               r: 200,
//               g: 200,
//               b: 200,
//               a: 255,
//             },
//           },
//         },
//       },
//       mainLine: {
//         lineType: 1, // LINE_TYPE_LINE
//         roundStart: false,
//         roundEnd: false,
//         dashGapU8: 0,
//         dashWidthU8: 0,
//         widthU8: 2,
//         lengthU8: 0,
//         color: {
//           type: 1, // COLOR_TYPE_RGBA
//           rgba: {
//             r: 0,
//             g: 0,
//             b: 0,
//             a: 255,
//           },
//         },
//       },
//       backgroundColor: {
//         type: 1, // COLOR_TYPE_RGBA
//         rgba: {
//           r: 255,
//           g: 255,
//           b: 255,
//           a: 255,
//         },
//       },
//       labelColor: {
//         type: 1, // COLOR_TYPE_RGBA
//         rgba: {
//           r: 0,
//           g: 0,
//           b: 0,
//           a: 255,
//         },
//       },
//       labelOffsetU8: 5,
//       startAngleU16: 0,
//       endAngleU16: 0,
//       radiusU16: 0,
//       centerOffsetXI16: 0,
//       centerOffsetYI16: 0,
//       showLabels: true,
//       showMainLine: true,
//       showGridLines: false,
//       autoSpacing: true,
//       currentValue: {
//         from: { $case: "valueI32", value: 25 },
//       },
//       customMarks: [
//         {
//           value: {
//             from: { $case: "valueI32", value: 25 },
//           },
//           label: "目标值",
//           color: {
//             type: 1, // COLOR_TYPE_RGBA
//             rgba: {
//               r: 255,
//               g: 0,
//               b: 0,
//               a: 255,
//             },
//           },
//           widthU8: 3,
//           lengthU8: 20,
//         },
//       ],
//     },
//   },
// };

// // 垂直刻度尺模拟数据（量杯刻度）
// export const verticalRulerMockData: Widget = {
//   idU16: 1002,
//   name: "垂直刻度尺",
//   memo: "用于量杯等垂直测量的刻度尺",
//   visible: true,
//   pin: false,
//   lockScale: false,
//   rwMode: 0,
//   location: {
//     leftI16: 400,
//     topI16: 50,
//   },
//   size: {
//     widthI16: 60,
//     heightI16: 300,
//   },
//   enableControl: undefined,
//   stateProperty: undefined,
//   actionsU16: [],
//   minPressTimeU16: 0,
//   minPressIntervalU16: 0,
//   confirmParam: undefined,
//   soundFeedback: false,
//   graphic: [],
//   style: [],
//   styleSyncU8: 0,
//   text: [],
//   widget: {
//     $case: "widgetRuler",
//     value: {
//       rulerType: 2, // RULER_TYPE_VERTICAL
//       direction: 1, // RULER_DIRECTION_TOP_LEFT
//       labelPosition: 1, // RULER_LABEL_POSITION_OUTSIDE
//       scaleConfig: {
//         show: true,
//         scaleShowType: 1, // SCALE_SHOW_TYPE_POINT
//         gridShowType: 1, // GRID_SHOW_TYPE_MAIN
//         scaleMain: {
//           scaleCountU8: 10,
//           scaleWidthU8: 2,
//           scaleDrawLen: 20,
//           color: {
//             type: 1,
//             rgba: {
//               r: 0,
//               g: 0,
//               b: 0,
//               a: 255,
//             },
//           },
//         },
//         scaleSec: {
//           scaleCountU8: 20,
//           scaleWidthU8: 1,
//           scaleDrawLen: 10,
//           color: {
//             type: 1,
//             rgba: {
//               r: 128,
//               g: 128,
//               b: 128,
//               a: 255,
//             },
//           },
//         },
//         scaleRadiusU8: 0,
//         minValue: {
//           from: { $case: "valueI32", value: 0 },
//         },
//         maxValue: {
//           from: { $case: "valueI32", value: 500 },
//         },
//       },
//       mainLine: {
//         lineType: 1,
//         roundStart: false,
//         roundEnd: false,
//         dashGapU8: 0,
//         dashWidthU8: 0,
//         widthU8: 3,
//         lengthU8: 0,
//         color: {
//           type: 1,
//           rgba: {
//             r: 0,
//             g: 100,
//             b: 200,
//             a: 255,
//           },
//         },
//       },
//       backgroundColor: {
//         type: 1,
//         rgba: {
//           r: 240,
//           g: 248,
//           b: 255,
//           a: 255,
//         },
//       },
//       labelColor: {
//         type: 1,
//         rgba: {
//           r: 0,
//           g: 0,
//           b: 0,
//           a: 255,
//         },
//       },
//       labelOffsetU8: 8,
//       startAngleU16: 0,
//       endAngleU16: 0,
//       radiusU16: 0,
//       centerOffsetXI16: 0,
//       centerOffsetYI16: 0,
//       showLabels: true,
//       showMainLine: true,
//       showGridLines: true,
//       autoSpacing: true,
//       currentValue: {
//         from: { $case: "valueI32", value: 150 },
//       },
//       customMarks: [
//         {
//           value: {
//             from: { $case: "valueI32", value: 100 },
//           },
//           label: "100ml",
//           color: {
//             type: 1,
//             rgba: {
//               r: 0,
//               g: 150,
//               b: 0,
//               a: 255,
//             },
//           },
//           widthU8: 2,
//           lengthU8: 25,
//         },
//         {
//           value: {
//             from: { $case: "valueI32", value: 250 },
//           },
//           label: "250ml",
//           color: {
//             type: 1,
//             rgba: {
//               r: 255,
//               g: 165,
//               b: 0,
//               a: 255,
//             },
//           },
//           widthU8: 2,
//           lengthU8: 25,
//         },
//       ],
//     },
//   },
// };
