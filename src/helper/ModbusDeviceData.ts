import {
    DataFormatType,
    ProtocolType,
    type DataReferenceUInt8,
    type Device,
    type Variable,
    type VariableReference,
} from "../proto";
import { defineIpPort, ModbusRegister } from "./define";
import type { IDeviceData } from "./IDeviceData";
import type { Format, StructMap } from "./StructMap";
import type { PartOf } from "./types";

/**
 * Modbus 设备变量表
 */
export class ModbusDeviceData implements IDeviceData {
    /**
     * 设备配置
     */
    readonly config: PartOf<Device>;
    /**
     * 设备ID
     */
    readonly deviceId: number;

    /**
     * 结构体列表
     */
    readonly structs: StructMap;

    /**
     * 变量列表
     */
    #variables: Record<number, PartOf<Variable>>;

    constructor(deviceId: number, ip: `${number}.${number}.${number}.${number}`, port: number, structs: StructMap) {
        const config: PartOf<Device> = {
            name: "modbus",
            protocol: ProtocolType.PROTOCOL_TYPE_MODBUS_MASTER,
            tunnelIdU8: 0,
            deviceParams: defineIpPort(ip, port),
        };

        this.deviceId = deviceId;
        this.config = config;
        this.#variables = {};
        this.structs = structs;
    }

    /**
     * 线圈变量
     * @param address 地址
     * @param count 数量，默认为 0 个，表示不是数组
     * @returns 变量
     */
    #coil(address: number, count: number): PartOf<Variable> {
        return {
            definition: {
                $case: "registerAddress",
                value: {
                    deviceIdU8: this.deviceId,
                    registerAddressU32: address,
                    registerTypeU8: ModbusRegister.Coil,
                    addressLengthU16: 1,
                    array1dLengthU8: count,
                },
            },
            dataFormat: DataFormatType.DATA_FORMAT_TYPE_BOOL,
        };
    }

    /**
     * 寄存器变量
     * @param address 地址
     * @param count 数量，默认为 0 个，表示不是数组
     * @returns 变量
     */
    #register(address: number, count: number, format: Format): PartOf<Variable> {
        return {
            definition: {
                $case: "registerAddress",
                value: {
                    deviceIdU8: this.deviceId,
                    registerAddressU32: address,
                    registerTypeU8: ModbusRegister.HoldingRegister,
                    array1dLengthU8: count,
                },
            },
            dataFormat: this.structs.getFormat(format),
        };
    }

    #dynamic(index: PartOf<VariableReference>, ...refs: Array<PartOf<VariableReference>>): PartOf<Variable> {
        return {
            definition: {
                $case: "dynamicVariable",
                value: {
                    subVariableIndex: index,
                    subVariables: refs,
                },
            },
        };
    }

    /**
     * 添加变量
     */
    #add(id: number, variable: PartOf<Variable>): void {
        if (this.#variables[id]) {
            throw new Error(`变量ID ${id} 已存在`);
        }
        variable.varDeviceIdU8 = this.deviceId;
        this.#variables[id] = variable;
    }

    /**
     * 添加线圈变量
     * @param id 变量ID
     * @param address 地址
     * @param count 数量，默认为 0 个，表示不是数组
     */
    addCoil(id: number, address: number, count: number = 0): void {
        return this.#add(id, this.#coil(address, count));
    }

    /**
     * 添加寄存器变量
     * @param id 变量ID
     * @param address 地址
     * @param count 数量，默认为 0 个，表示不是数组
     */
    addRegister(id: number, address: number, count: number = 0, format: Format = "u16"): void {
        return this.#add(id, this.#register(address, count, format));
    }

    addDynamic(id: number, index: PartOf<VariableReference>, ...refs: Array<PartOf<VariableReference>>): void {
        return this.#add(id, this.#dynamic(index, ...refs));
    }

    /**
     * 线圈变量
     * @param address 地址
     * @returns 变量引用
     */
    useCoil(address: number) {
        const reference: PartOf<VariableReference> = {
            from: {
                $case: "varInput",
                value: this.#coil(address, 0),
            },
        };
        return reference;
    }

    /**
     * 寄存器变量
     * @param address 地址
     * @param count 数量，默认为 0 个，表示不是数组
     * @returns 变量引用
     */
    useRegister(address: number, format: Format = "u16") {
        const reference: PartOf<VariableReference> = {
            from: {
                $case: "varInput",
                value: this.#register(address, 0, format),
            },
        };
        return reference;
    }

    /**
     * 动态变量
     * @param index 索引
     * @param refs 子变量
     * @returns 变量引用
     */
    useDynamic(index: PartOf<VariableReference>, ...refs: Array<PartOf<VariableReference>>): PartOf<VariableReference> {
        return {
            from: {
                $case: "varInput",
                value: this.#dynamic(index, ...refs),
            },
        };
    }

    /**
     * 变量引用，或是数组元素之1
     *
     * @param id    变量ID
     * @param index 如果为 0 表示正常使用，其它值表示对数组变量的索引
     * @param field 如果为 0 表示正常使用，其它值表示对结构体字段的引用
     * @returns 变量引用
     */
    ref(id: number, ...indexs: Array<number | PartOf<DataReferenceUInt8>>) {
        const variable = this.#variables[id];
        if (!variable) {
            throw new Error(`变量ID ${id} 不存在`);
        }

        const reference: PartOf<VariableReference> = {
            from: {
                $case: "varDefined",
                value: {
                    deviceIdU8: this.deviceId,
                    varIdU16: id,
                    varSubscripts: indexs.map((value): PartOf<DataReferenceUInt8> => {
                        if (typeof value === "number") {
                            return {
                                from: {
                                    $case: "constantU8",
                                    value,
                                },
                            };
                        } else {
                            return value;
                        }
                    }),
                },
            },
        };
        return reference;
    }

    /**
     * 获取变量列表
     */
    get variables() {
        return this.#variables;
    }
}
