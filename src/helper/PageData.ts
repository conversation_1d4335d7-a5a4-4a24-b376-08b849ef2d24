import { ActionList, PageWidgets, Widget, type WidgetVariableReference } from "../proto";
import { encode } from "./encode";
import type { PartOf } from "./types";
import type { FullWidget } from "./utils";

export class PageData {
    /** 页面ID */
    readonly id: number;

    #widgets: PartOf<Widget>[];

    /** 属性绑定地址(key是元件ID，0表示页面本身) */
    #widgetVariableReference: { [key: number]: PartOf<WidgetVariableReference> };

    /** 动作列表，key是元件ID，0表示页面本身事件 */
    #widgetActionList: { [key: number]: PartOf<ActionList> };

    #counter: number;

    constructor(id: number) {
        this.id = id;
        this.#widgets = [];
        this.#widgetVariableReference = {};
        this.#widgetActionList = {};
        this.#counter = 1;
    }

    addWidget(widget: FullWidget) {
        let id = widget?.ui?.idU16;
        if (id === undefined) {
            id = this.#counter++;
        }

        const ui = widget.ui;
        if (ui !== undefined) {
            if (ui.idU16 === undefined) {
                ui.idU16 = id;
            }
            this.#widgets.push(ui);
        }

        const attributes = widget.attributes;
        if (attributes !== undefined) {
            const widgetVariableReference: PartOf<WidgetVariableReference> = {
                variable: {},
            };
            for (const [attr, value] of Object.entries(attributes)) {
                widgetVariableReference.variable![Number(attr)] = value;
            }
            this.#widgetVariableReference[id] = widgetVariableReference;
        }
        if (widget.actions !== undefined) {
            this.#widgetActionList[id] = { actions: widget.actions };
        }
    }

    getBuf() {
        const page: PartOf<PageWidgets> = {
            widgets: this.#widgets,
            widgetVariableReference: this.#widgetVariableReference,
            widgetActionList: this.#widgetActionList,
        };
        return encode(PageWidgets, page);
    }
}
