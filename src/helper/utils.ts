import type { Action, ColorReference, Location, Size, VariableReference, Widget } from "../proto";
import type { PartOf } from "./types";

export function toColor(hex: `#${string}` | number): ColorReference {
    if (typeof hex === "number") {
        return {
            color: {
                $case: "colorValueU32",
                value: hex,
            },
        };
    }

    let vl = hex.replace("#", "");
    if (vl.length === 3) {
        vl = vl
            .split("")
            .map((c) => c + c)
            .join("");
    }
    if (vl.length === 4) {
        vl = vl
            .split("")
            .map((c) => c + c)
            .join("");
    }
    if (vl.length === 6) {
        vl = "FF" + vl;
    }
    const value = parseInt(vl, 16);
    const $case = "colorValueU32";
    return {
        color: {
            $case,
            value,
        },
    };
}

export function randomColor() {
    return toColor((Math.floor(Math.random() * 0xffffff) | 0xff000000) >>> 0);
}

export function posOf(left: number, top: number): Location {
    return {
        leftI16: left,
        topI16: top,
    };
}

export function sizeOf(width: number, height: number): Size {
    return {
        widthI16: width,
        heightI16: height,
    };
}

export function tagOf(deviceId: number, tagId: number): PartOf<VariableReference> {
    return {
        from: {
            $case: "varDefined",
            value: {
                deviceIdU8: deviceId,
                varIdU16: tagId,
            },
        },
    };
}

export function ip(ip1: number, ip2: number, ip3: number, ip4: number): number {
    const u8 = new Uint8Array(4);
    u8[3] = ip1;
    u8[2] = ip2;
    u8[1] = ip3;
    u8[0] = ip4;

    const i32 = new Uint32Array(u8.buffer);
    return i32[0]!;
}

/** 完整的组件 */
export interface FullWidget {
    /** 界面配置 */
    ui?: PartOf<Widget>;
    /** 地址配置 */
    attributes?: { [key: number]: PartOf<VariableReference> };
    /** 单个动作 */
    actions?: PartOf<Action>[];
}
