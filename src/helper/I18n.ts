import { TextTagLibrary, type TextTag } from "../proto";
import { encode } from "./encode";

type Tag = TextTag & { id: number };

export class I18n {
    /**
     * 自增ID
     */
    #counter = 0;

    /**
     * 开始使用生成的 ID
     */
    #autoGenerated = false;

    /**
     * tag content 与 tag 的表
     */
    #textTagMap: Map<string, Tag> = new Map();

    /**
     * tag id 与 tag 的表
     */
    #idTagMap: Map<number, Tag> = new Map();

    readonly id: number;

    constructor(id: number) {
        this.id = id;
    }

    build(): Uint8Array {
        const map = this.#idTagMap;
        const tags: Record<number, TextTag> = {};
        for (const tag of map.values()) {
            tags[tag.id] = tag;
        }
        return encode(TextTagLibrary, { tags });
    }

    #create(id: number, langs: string[]): number {
        const tag = {
            // TODO
            $case: "singleLang",
            value: langs[0],
            multiLine: false,
            classifyIdU16: 1,
            id,
        };
        this.#idTagMap.set(id, tag);
        for (const lang of langs) {
            this.#textTagMap.set(lang, tag);
        }
        return id;
    }

    /**
     * 创建一个新的字符呺
     * @param idOrString ID或者第一个语言的字符串
     * @param langs 语言列表
     * @returns
     */
    find(idOrString: number | string, ...langs: string[]): number {
        let id = 0;
        if (typeof idOrString === "number") {
            if (this.#autoGenerated) {
                throw new Error("不能在自动生成ID的情况下使用数字ID");
            }
            id = idOrString;
            this.#counter = Math.max(this.#counter, id + 1);
        } else {
            this.#autoGenerated = true;
            id = this.#newId();
            langs.unshift(idOrString);
        }
        for (const lang of langs) {
            const tag = this.#textTagMap.get(lang);
            if (tag !== undefined) {
                return this.#update(langs, tag);
            }
        }
        return this.#create(id, langs);
    }

    #newId() {
        return this.#counter++;
    }

    #update(langs: string[], tag: Tag): number {
        for (const lang of langs) {
            console.log("update tag: ", tag.id, lang);
            // TODO
            // if (!tag.content.includes(lang)) {
            //     tag.content.push(lang);
            // }
        }
        return tag.id;
    }
}

export class ProjectI18n {
    readonly #all: I18n[];

    readonly #project: I18n;

    constructor() {
        this.#project = new I18n(0);
        this.#all = [this.#project];
    }

    /**
     * 查找字符串获取语言 id，如果找不到则建一个新的
     * @param strings 字符串列表
     * @returns
     */
    project(...strings: string[]): number;
    /**
     * 更新 project 的语言
     * @param id      字符串 ID
     * @param strings 字符串列表
     * @returns
     */
    project(id: number, ...strings: string[]): number;
    project(idOrString: number | string, ...langs: string[]): number {
        return this.#project.find(idOrString, ...langs);
    }

    page(pageId: number, ...langs: string[]): number;
    page(pageId: number, id: number, ...langs: string[]): number;
    page(id: number, idOrString: number | string, ...langs: string[]): number {
        let i18n = this.#all[id];
        if (i18n === undefined) {
            i18n = new I18n(id);
            this.#all[id] = i18n;
        }
        return i18n.find(idOrString, ...langs);
    }

    get all(): readonly I18n[] {
        return this.#all;
    }

    build(id: number): Uint8Array {
        let i18n = this.#all[id];
        if (i18n === undefined) {
            throw new Error("250204150639314, invalid id: " + id);
        }
        return i18n.build();
    }
}
