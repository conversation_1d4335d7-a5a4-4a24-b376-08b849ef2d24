import { DataFormatType, ProtocolType, type Device, type Variable, type VariableReference } from "../proto";
import { ModbusRegister } from "./define";
import type { IDeviceData } from "./IDeviceData";
import type { PartOf } from "./types";

export class ModbusGroupDeviceData implements IDeviceData {
    readonly deviceId: number;

    readonly config: PartOf<Device>;

    #variables: Record<number, PartOf<Variable>>;

    constructor(deviceId: number, name: string, children: number[]) {
        this.deviceId = deviceId;
        this.config = {
            name,
            protocol: ProtocolType.PROTOCOL_TYPE_MODBUS_MASTER,
            tunnelIdU8: 0,
            deviceParams: {
                $case: "multiDeviceParam",
                value: {
                    subDeviceIds: children,
                    subDeviceIndexRegisterU16: 1,
                },
            },
        };
        this.#variables = {};
    }

    get variables(): Record<number, PartOf<Variable>> {
        return this.#variables;
    }

    /**
     * 寄存器变量
     * @param address 地址
     * @param count 数量，默认为 0 个，表示不是数组
     * @returns 变量
     */
    #register(address: number, count: number): PartOf<Variable> {
        return {
            definition: {
                $case: "registerAddress",
                value: {
                    deviceIdU8: this.deviceId,
                    registerAddressU32: address,
                    registerTypeU8: ModbusRegister.HoldingRegister,
                    array1dLengthU8: count,
                },
            },
            dataFormat: DataFormatType.DATA_FORMAT_TYPE_U16,
        };
    }

    /**
     * 添加变量
     */
    #add(id: number, variable: PartOf<Variable>): void {
        if (this.#variables[id]) {
            throw new Error(`变量ID ${id} 已存在`);
        }
        variable.varDeviceIdU8 = this.deviceId;
        this.#variables[id] = variable;
    }

    /**
     * 添加寄存器变量
     * @param id 变量ID
     * @param address 地址
     * @param count 数量，默认为 0 个，表示不是数组
     */
    addRegister(id: number, address: number, count: number = 0): void {
        return this.#add(id, this.#register(address, count));
    }

    /**
     * 变量引用，或是数组元素之1
     * @param id    变量ID
     * @param index 如果为 0 表示正常使用，其它值表示对数组变量的索引
     * @param field 如果为 0 表示正常使用，其它值表示对结构体字段的引用
     * @returns 变量引用
     */
    ref(id: number, ...indexs: number[]) {
        const variable = this.#variables[id];
        if (!variable) {
            throw new Error(`变量ID ${id} 不存在`);
        }
        const reference: PartOf<VariableReference> = {
            from: {
                $case: "varDefined",
                value: {
                    deviceIdU8: this.deviceId,
                    varIdU16: id,
                    varSubscripts: indexs.map((index) => ({
                        from: {
                            $case: "constantU8",
                            value: index,
                        },
                    })),
                },
            },
        };
        return reference;
    }
}
