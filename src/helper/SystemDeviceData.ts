import { ProtocolType, type Device, type Variable } from "../proto";
import type { IDeviceData } from "./IDeviceData";
import type { PartOf } from "./types";

export class SystemDeviceData implements IDeviceData {
    /**
     * 设备配置
     */
    readonly config: PartOf<Device>;

    /**
     * 设备ID
     */
    readonly deviceId: number;

    #variables: Record<number, PartOf<Variable>>;

    constructor(deviceId: number) {
        this.deviceId = deviceId;
        this.config = {
            name: "system",
            protocol: ProtocolType.PROTOCOL_TYPE_LOCAL_HMI,
        };
        this.#variables = {};
    }

    get variables(): Record<number, PartOf<Variable>> {
        return this.#variables;
    }
}
