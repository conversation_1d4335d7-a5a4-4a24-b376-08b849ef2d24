import {
    DataFormatType,
    Device,
    PageWidgets,
    Variable,
    type VariableReference,
    type WidgetPropertyEnum,
} from "../proto";
import { encode } from "./encode";
import type { PartOf } from "./types";
import { ip, type FullWidget } from "./utils";

/**
 * 完整的设备定义
 */
export interface DeviceDefinition {
    /** 设备参数 */
    device: PartOf<Device>;
    /** 设备标签 */
    tags: PartOf<Variable>[];
}

export function defineDevice(device: PartOf<Device>, ...tag_list: PartOf<Variable>[]) {
    let tags: Record<number, PartOf<Variable>> = {};
    for (let i = 0; i < tag_list.length; i++) {
        const tag = tag_list[i]!;
        tags[i] = tag;
    }
    return { device, tags };
}

export function oneOf<K extends keyof any, C extends string, V>(
    key: K,
    $case: C,
    value: V
): { [key in K]: { $case: C; value: V } } {
    return { [key]: { $case, value } } as any;
}

export function defineIpPort(
    uri: `${number}.${number}.${number}.${number}`,
    port = 502
): PartOf<Device["deviceParams"]> {
    const arr = uri.split(".").map(Number);
    if (arr.length !== 4) {
        throw new Error("Invalid IP address");
    }
    const ipAddress = ip(...(arr as [number, number, number, number]));

    return {
        $case: "ethernetParam",
        value: {
            ipAddress: oneOf("from", "constantU32", ipAddress),
            ipPort: oneOf("from", "constantU16", port),
            isUdp: false,
        },
    };
}

export const enum ZhenDianRegister {
    /** 用户位寄存器 bit */
    Ub = 0,
    /** 用户字寄存器 word */
    Uw = 1,
    /** 保持位寄存器 bit */
    Hb = 2,
    /** 保持字寄存器 word */
    Hw = 3,
    /** 系统位寄存器 bit */
    Sb = 4,
    /** 系统字寄存器 word */
    Sw = 5,
    /** 索引寄存器 word */
    Id = 6,
}

export const enum ModbusRegister {
    /** 线圈寄存器 bit */
    Coil = 0,
    /** 离散输入寄存器 bit */
    DiscreteInput = 1,
    /** 保持寄存器 word */
    HoldingRegister = 2,
    /** 输入寄存器 word */
    InputRegister = 3,
}

/**
 * 系统寄存器地址
 */
export function defineZndAddress(
    addr: number,
    reg: ZhenDianRegister = ZhenDianRegister.Hb,
    format: DataFormatType = DataFormatType.DATA_FORMAT_TYPE_I16
): PartOf<VariableReference> {
    return {
        from: {
            $case: "varInput",
            value: {
                definition: {
                    $case: "registerAddress",
                    value: {
                        deviceIdU8: 0,
                        registerAddressU32: addr,
                        registerTypeU8: reg,
                        addressLengthU16: 1,
                    },
                },
                dataFormat: format,
            },
        },
    };
}

interface ModbusAddressOpts {
    reg?: ModbusRegister | undefined;
    format?: DataFormatType | undefined;
    len?: number | undefined;
}

/**
 * 定义一个 modbus 地址
 * @param deviceId 设备id
 * @param addr 地址
 * @param reg 寄存器类型
 * @param format 数据格式
 * @returns
 */
export function defineModbusAddress(
    deviceId: number,
    addr: number,
    opts?: ModbusAddressOpts
): PartOf<VariableReference> {
    opts = opts ?? {};

    return {
        from: {
            $case: "varInput",
            value: {
                definition: {
                    $case: "registerAddress",
                    value: {
                        deviceIdU8: deviceId,
                        registerAddressU32: addr,
                        registerTypeU8: opts.reg ?? ModbusRegister.HoldingRegister,
                        addressLengthU16: opts.len ?? 1,
                    },
                },
                dataFormat: opts.format ?? DataFormatType.DATA_FORMAT_TYPE_U16,
            },
        },
    };
}

/**
 * 定义一个索引变量
 * @returns
 */
export function defineIndexVariable(index: number): PartOf<VariableReference> {
    return {
        from: {
            $case: "varInput",
            value: {
                definition: {
                    $case: "registerAddress",
                    value: {
                        deviceIdU8: 1,
                        registerAddressU32: index,
                        registerTypeU8: ZhenDianRegister.Id,
                        addressLengthU16: 1,
                    },
                },
                dataFormat: DataFormatType.DATA_FORMAT_TYPE_U16,
            },
        },
    };
}

/**
 * 定义一个页面
 * @param f
 * @returns
 */
export function definePage(f: () => FullWidget[]): () => Uint8Array {
    return function () {
        const pageWidgetList = f();

        const page: PartOf<PageWidgets> = {
            widgets: [],
            widgetVariableReference: {},
            widgetActionList: {},
        };
        for (let i = 0; i < pageWidgetList.length; i++) {
            const fullWidget = pageWidgetList[i]!;
            const id = i + 1;
            const ui = fullWidget.ui ?? {};
            ui.idU16 = ui.idU16 ?? id;

            page.widgets!.push(ui);

            const attributes = fullWidget.attributes ?? {};
            // const widgetVariableReference: PartOf<WidgetVariableReference> = {
            //     variable: {},
            // };

            for (const key in attributes) {
                if (attributes[key] === undefined) {
                    continue;
                }
                // const tag = attributes[key];
                // widgetVariableReference.variable![key] = tag;
            }

            // page.widgetVariableReference![id] = widgetVariableReference;
            page.widgetActionList![id] = { actions: fullWidget.actions ?? [] };
        }

        return encode(PageWidgets, page);
    };
}

/**
 * 定义地址的键值
 *
 * @param prop 元件属性ID
 * @param index 数组属性的下标
 * @returns 地址的键值，前8位表示元件属性ID，后8位表示数组属性的下标
 */
export function defineAddressKey(prop: WidgetPropertyEnum, index: number): number {
    return prop * 0x100 + index;
}
