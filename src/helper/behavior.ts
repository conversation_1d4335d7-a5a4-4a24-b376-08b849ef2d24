// import {ActionType, PageActionType} from '../proto/behavior';

// function showPage(
//     pageId: number,
//     trigger: TriggerType = TriggerType.TRIGGER_TYPE_PRESS): Behavior {
//   return {
//     triggerType: trigger,
//     actions: [
//       {
//         actionType: ActionType.ACTION_TYPE_PAGE,
//         page: {
//           actionType: PageActionType.PAGE_ACTION_TYPE_SHOW,
//           pageId,
//         },
//       },
//     ],
//   };
// }

// export const behaviors = {
//   showPage,
// };
