import type { ColorReference, StyleBorder, StyleProperties, StyleShadow } from "../proto";
import { toColor } from "./utils";

export class StyleBuilder implements StyleProperties {
    styleId?: number | undefined;
    /** 背景色 */
    backgroundColor?: ColorReference | undefined;
    /** 边框 */
    borderProps?: StyleBorder | undefined;
    /** 阴影 */
    shadowProps?: StyleShadow | undefined;
    constructor() {}

    bg(color: `#${string}` | number): this {
        if (typeof color === "string") {
            // this.backgroundColor = { colorVarId: 0,  toColor(color) };
            this.backgroundColor = toColor(color);
        } else {
            this.backgroundColor = {
                color: {
                    $case: "colorVarIdU8",
                    value: color,
                },
            };
        }
        return this;
    }

    border(width: number, color: `#${string}` | number): this {
        if (typeof color === "number") {
            this.borderProps = {
                widthU8: width,
                color: {
                    color: {
                        $case: "colorValueU32",
                        value: color,
                    },
                },
                left: true,
                top: true,
                right: true,
                bottom: true,
                internal: false,
            };
        } else {
            this.borderProps = {
                widthU8: width,
                color: {
                    color: {
                        $case: "colorVarIdU8",
                        value: 1,
                    },
                },
                left: true,
                top: true,
                right: true,
                bottom: true,
                internal: false,
            };
        }

        return this;
    }
}

export function style() {
    return new StyleBuilder();
}
