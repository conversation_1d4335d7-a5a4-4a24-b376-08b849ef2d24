import type { BinaryWriter } from "@bufbuild/protobuf/wire";
import type { PartOf } from "./types";

export interface Encoder<T> {
    encode(message: T, writer?: BinaryWriter): BinaryWriter;
    create(base: PartOf<T>): T;
}

export function encode<T>(lib: Encoder<T>, conf: PartOf<T>): Uint8Array {
    return lib.encode(lib.create(conf)).finish();
}

export interface Provider {
    (): Uint8Array;
}

export interface ProviderRecord {
    [key: string | number]: Provider;
}
