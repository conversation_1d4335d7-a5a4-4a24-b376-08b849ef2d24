import { DataFormatType, StructType, type StructType<PERSON>ield } from "../proto";
import type { PartOf } from "./types";

const formatMap = {
    u8: DataFormatType.DATA_FORMAT_TYPE_U8,
    u16: DataFormatType.DATA_FORMAT_TYPE_U16,
    u32: DataFormatType.DATA_FORMAT_TYPE_U32,
    u64: DataFormatType.DATA_FORMAT_TYPE_U64,
    i8: DataFormatType.DATA_FORMAT_TYPE_I8,
    i16: DataFormatType.DATA_FORMAT_TYPE_I16,
    i32: DataFormatType.DATA_FORMAT_TYPE_I32,
    i64: DataFormatType.DATA_FORMAT_TYPE_I64,
} as const;

export type Format = number | keyof typeof formatMap;

export class StructMap {
    /**
     * 结构体列表
     */
    readonly structs: Record<number, PartOf<StructType>>;

    constructor() {
        this.structs = {};
    }

    getFormat(format: Format): DataFormatType {
        if (typeof format === "number") {
            if (this.structs[format]) {
                return format;
            }
            throw new Error(`结构体 ${format} 不存在`);
        }
        if (!formatMap[format]) {
            throw new Error(`结构体 ${format} 不存在`);
        }
        return formatMap[format];
    }

    addStruct(id: number, ...fields: { format: Format; len: number }[]) {
        if (id < 100 || id > 255) {
            throw new Error(`结构体ID必须在100-255之间, 当前为 ${id}`);
        }
        if (this.structs[id]) {
            throw new Error(`结构体ID ${id} 已存在`);
        }

        let offset = 0;
        const field_list: PartOf<StructTypeField>[] = fields.map((field, index) => {
            const r: PartOf<StructTypeField> = {
                fieldIdU8: index,
                dataFormat: this.getFormat(field.format),
                lengthU8: field.len,
                startU16: offset,
            };
            offset += field.len;
            return r;
        });

        this.structs[id] = {
            name: `struct_${id}`,
            fields: field_list,
            lengthU16: fields.reduce((sum, field) => sum + field.len, 0),
        };
    }
}
