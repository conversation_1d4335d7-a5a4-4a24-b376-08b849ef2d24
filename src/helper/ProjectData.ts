import { TunnelDeviceList, VariableLibrary, type Device, type Tunnel } from "../proto";
import { encode } from "./encode";
import { ModbusDeviceData } from "./ModbusDeviceData";
import { ModbusGroupDeviceData } from "./ModbusGroupDeviceData";
import { PageData } from "./PageData";
import { StructMap, type Format } from "./StructMap";
import { SystemDeviceData } from "./SystemDeviceData";
import type { PartOf } from "./types";

export type DeviceData = ModbusDeviceData | SystemDeviceData | ModbusGroupDeviceData;

export class ProjectData {
    #tunnels: Record<number, PartOf<Tunnel>>;
    #struct_map: StructMap;
    #deviceVariables: Record<number, DeviceData>;
    #pages: Record<number, PageData>;

    constructor() {
        this.#tunnels = {};
        this.#struct_map = new StructMap();
        this.#deviceVariables = {};
        this.#pages = {};
    }

    getTunnelDeviceList() {
        const devices: PartOf<{ [key: number]: Device }> = {};

        for (const [id, dev] of Object.entries(this.#deviceVariables)) {
            devices[id as unknown as number] = dev.config;
        }

        const dt: PartOf<TunnelDeviceList> = {
            devices,
            tunnels: this.#tunnels,
            structDataTypes: this.#struct_map.structs,
        };
        return encode(TunnelDeviceList, dt);
    }

    *getDeviceVariables(): Generator<[number, Uint8Array]> {
        for (const [id, device] of Object.entries(this.#deviceVariables)) {
            yield [
                Number(id),
                encode(VariableLibrary, {
                    variables: device.variables,
                }),
            ];
        }
    }

    createPage(id: number) {
        if (this.#pages[id]) {
            throw new Error(`页面ID ${id} 已存在`);
        }
        this.#pages[id] = new PageData(id);
        return this.#pages[id];
    }

    getPage(id: number) {
        const page = this.#pages[id];
        if (page === undefined) {
            throw new Error(`页面ID ${id} 不存在`);
        }
        return page;
    }

    pages(): number[] {
        return Object.keys(this.#pages).map(Number);
    }

    /**
     * 添加一个结构体
     * @param id
     */
    addStruct(id: number, ...fields: { format: Format; len: number }[]) {
        this.#struct_map.addStruct(id, ...fields);
    }

    #checkDeviceExists(...ids: number[]) {
        for (const id of ids) {
            if (!this.#deviceVariables[id]) {
                throw new Error(`设备ID ${id} 不存在`);
            }
        }
    }

    #addDevice<T extends DeviceData>(id: number, device: T): T {
        if (id < 0 || id > 255) {
            throw new Error(`设备ID必须在0-255之间, 当前为 ${id}`);
        }
        if (this.#deviceVariables[id]) {
            throw new Error(`设备ID ${id} 已存在`);
        }
        this.#deviceVariables[id] = device;
        return device;
    }

    #getDevice<T extends DeviceData>(id: number, ctor: new (...args: any[]) => T): T {
        const device = this.#deviceVariables[id];
        if (!device) {
            throw new Error(`设备ID ${id} 不存在`);
        }
        if (!(device instanceof ctor)) {
            throw new Error(`设备ID ${id} 不是 ${ctor.name} 设备`);
        }
        return device;
    }

    /**
     * 添加系统设备
     * @param id 设备ID
     * @param device 设备配置
     */
    addSystemDevice(id: number) {
        const device = new SystemDeviceData(id);
        return this.#addDevice(id, device);
    }

    /**
     * 获取系统设备变量表
     * @param deviceId 设备ID
     * @returns 系统设备变量表
     */
    getSystemDevice(deviceId: number): SystemDeviceData {
        return this.#getDevice(deviceId, SystemDeviceData);
    }

    /**
     * 添加 Modbus 设备
     * @param id 设备ID
     * @param ip 设备IP
     * @param port 设备端口
     */
    addModbusDevice(id: number, ip: `${number}.${number}.${number}.${number}`, port: number = 502) {
        const deviceData = new ModbusDeviceData(id, ip, port, this.#struct_map);
        return this.#addDevice(id, deviceData);
    }

    /**
     * 获取 Modbus 设备变量表
     * @param deviceId 设备ID
     * @returns Modbus 设备变量表
     */
    getModbusDevice(deviceId: number): ModbusDeviceData {
        return this.#getDevice(deviceId, ModbusDeviceData);
    }

    addModbusGroupDevice(id: number, name: string, ...children: number[]) {
        this.#checkDeviceExists(...children);
        const device = new ModbusGroupDeviceData(id, name, children);
        return this.#addDevice(id, device);
    }

    getModbusGroupDevice(deviceId: number): ModbusGroupDeviceData {
        return this.#getDevice(deviceId, ModbusGroupDeviceData);
    }
}
