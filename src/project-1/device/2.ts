import type { ProjectData } from "../../helper/ProjectData";

export function setModbusDevice(project: ProjectData) {
    const dev = project.addModbusDevice(2, "127.0.0.1", 1502);
    // project.addModbusDevice(2, "192.168.0.105", 502);
    // project.addModbusDevice(2, "192.168.1.111", 502);

    // 添加一个常规变量
    dev.addRegister(1, 100);
    // 添加一个常规数组
    dev.addRegister(2, 200, 10);
    // 添加一个单层结构体
    dev.addRegister(3, 300, 1, 100);
    // 添加一个结构体数组
    dev.addRegister(4, 400, 10, 100);
}
