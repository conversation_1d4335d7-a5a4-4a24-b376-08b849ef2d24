import type { ProjectData } from "../../helper/ProjectData";
import { setSystemDevice } from "./1";
import { setModbusDevice } from "./2";
import { setModbusDevice2 } from "./3";
import { setModbusGroup } from "./4";
import { setStruct } from "./structures";

export function setDevices(project: ProjectData) {
    setStruct(project);
    setSystemDevice(project);
    setModbusDevice(project);
    setModbusDevice2(project);
    setModbusGroup(project);
}
