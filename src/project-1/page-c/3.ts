import { ActionTiming, DataFormatType } from "../../proto";
import { defineModbusAddress, definePage } from "../../helper/define";
import { posOf, sizeOf, type FullWidget } from "../../helper/utils";
import { MatrixButtonLayout } from "../../../proto/dev/widget";

// 输入框
export const w1: FullWidget = {
    ui: {
        idU16: 1,
        location: posOf(300, 0),
        size: sizeOf(100, 50),
        widget: {
            $case: "widgetString",
            value: {
                supportInput: true,
                keyboardPageIdU16: 1,
                dataFormat: DataFormatType.DATA_FORMAT_TYPE_UTF8,
                maxLengthU16: 10000,
                minLengthU16: 1,
                showScrollbar: true,
                passwordMode: false,
            },
        },
    },
    attributes: {
        // [0x0101]: tagOf(1, 0),
        // [0x0102]: tagOf(1, 1),
        [0x0101]: defineModbusAddress(1, 256),
    },
    actions: [
        {
            actionTiming: ActionTiming.ACTION_TIMING_PRESS,
            // page: {
            //     actionType: PageActionType.PAGE_ACTION_TYPE_BASE_CHANGE,
            //     pageId: {
            //         valueU16: 2,
            //     },
            // },
            waitResult: false,
        },
    ],
};

// 键盘
export const w2: FullWidget = {
    ui: {
        idU16: 2,
        location: posOf(100, 150),
        size: sizeOf(200, 200),
        widget: {
            $case: "widgetMatrixButton",
            value: {
                layout: MatrixButtonLayout.MATRIX_BUTTON_LAYOUT_UNSPECIFIED,
             buttons: [
                 {
                     buttonFunctions: [
                        {
                            func: {
                                $case: "unicode",
                                value: "1",
                            }
                        }
                     ],
                     buttonWidthU16: 1,
                     style: [
                         {
                             borderProps: {
                                 widthU8: 5,
                                 top: true,
                                 right: true,
                                 color: {
                                     color : {
                                        $case: "colorValueU32",
                                        value: 0xff0000ff,
                                     }
                                 },
                             },
                             shadowProps: {
                                 enable: true,
                                 widthI16: 5,
                                 color: {
                                     color : {
                                        $case: "colorValueU32",
                                        value: 0xff0000ff,
                                     }
                                 },
                             },
                             backgroundColor: {
                                 color : {
                                    $case: "colorValueU32",
                                    value: 0xff0000ff,
                                 }
                             },
                         },
                     ],
                 },
                 {
                     buttonFunctions: [
                        {
                            func: {
                                $case: "unicode",
                                value: "2",
                            }
                        }
                     ],
                     buttonWidthU16: 2,
                 },
                 {
                     buttonFunctions: [
                        {
                            func: {
                                $case: "unicode",
                                value: "3",
                            }
                        }
                     ],
                     buttonWidthU16: 3,
                 },
                 {
                     buttonFunctions: [
                        {
                            func: {
                                $case: "unicode",
                                value: "4",
                            }
                        }
                     ],
                     buttonWidthU16: 4,
                     style: [
                         {
                             //borderProps: {
                             //    width: 5,
                             //    top: true,
                             //    right: true,
                             //    color: {
                             //         0xff0000ff,
                             //    },
                             //},
                             shadowProps: {
                                 enable: true,
                                 widthI16: 20,
                                 color: {
                                     color : {
                                        $case: "colorValueU32",
                                        value: 0xff0000ff,
                                     }
                                 },
                             },
                             backgroundColor: {
                                 color : {
                                    $case: "colorValueU32",
                                    value: 0x00ff00ff,
                                 }
                             },
                         },
                     ],
                 },
             ],
            },
        },
        // widgetMatrixButton: {
        //     layout: MatrixButtonLayout.MATRIX_BUTTON_LAYOUT_UNSPECIFIED,
        // },
    },
    attributes: {
        // [0x0201]: defineModbusAddress(1, 1),
        // [0x0202]: tagOf(1, 2),
    },
    actions: [],
};

export const stringInput1: FullWidget = {
    ui: {
        idU16: 4,
        location: posOf(300, 0),
        size: sizeOf(100, 50),
        widget: {
            $case: "widgetString",
            value: {
       //          supportInput: true,
       //          keyboardPageIdU16: 1,
       //          dataFormat: DataFormatType.DATA_FORMAT_TYPE_UTF8,
             supportInput: true,
             // currentValue: "1234567890",
             maxLengthU16: 20,
             minLengthU16: 1,
             showScrollbar: true,
             passwordMode: false,
             // keyboardPageIdU16: 3,
             dataFormat: DataFormatType.DATA_FORMAT_TYPE_UTF8,
            },
        },
        // widgetString: {
        //     supportInput: true,
        //     // currentValue: "1234567890",
        //     maxLengthU16: 20,
        //     minLengthU16: 1,
        //     showScrollbar: true,
        //     passwordMode: false,
        //     // keyboardPageIdU16: 3,
        //     dataFormat: DataFormatType.DATA_FORMAT_TYPE_STRING_UTF8,
        // },
    },
    attributes: {},
    actions: [],
};

export const numberMax: FullWidget = {
    ui: {
        idU16: 5,
        location: posOf(300, 50),
        size: sizeOf(100, 50),
        widget: {
            $case: "widgetNumber",
            value: {
                supportInput: true,
             maxValue: {
                 from: {
                    $case: "valueI16",
                    value: 1120,
                 }
             },
             minValue: {
                 from: {
                    $case: "valueI16",
                    value: 1,
                 }
             },
             // showScrollbar: true,
             // passwordMode: false,
             // keyboardPageIdU16: 3,
             dataFormat: DataFormatType.DATA_FORMAT_TYPE_UTF8,
            },
        },
        // widgetNumber: {
        //     supportInput: true,
        //     // currentValue: "1234567890",
        // },
    },
    attributes: {},
    actions: [],
};
export const numberMin: FullWidget = {
    ui: {
        idU16: 6,
        location: posOf(300, 100),
        size: sizeOf(100, 50),
        widget: {
            $case: "widgetNumber",
            value: {
                supportInput: true,
             maxValue: {
                 from: {
                    $case: "valueI16",
                    value: 1120,
                 }
             },
             minValue: {
                 from: {
                    $case: "valueI16",
                    value: 1,
                 }
             },
             // showScrollbar: true,
             // passwordMode: false,
             // keyboardPageIdU16: 3,
             dataFormat: DataFormatType.DATA_FORMAT_TYPE_UTF8,
            },
        },
    },
    attributes: {},
    actions: [],
};

// 键盘
export const w3: FullWidget = {
    ui: {
        idU16: 3,
        location: posOf(300, 150),
        size: sizeOf(200, 200),
        // widgetMatrixButton: {
        //     layout: MatrixButtonLayout.MATRIX_BUTTON_LAYOUT_NUMBER,
        //     buttons: [
        //         {
        //             unicode: "1",
        //             buttonWidthU16: 1,
        //             style: [
        //                 {
        //                     borderProps: {
        //                         widthU8: 5,
        //                         top: true,
        //                         right: true,
        //                         color: {
        //                             colorValueU32: 0xff0000ff,
        //                         },
        //                     },
        //                     shadowProps: {
        //                         enable: true,
        //                         widthI16: 5,
        //                         color: {
        //                             colorValueU32: 0xff0000ff,
        //                         },
        //                     },
        //                     backgroundColor: {
        //                         colorValueU32: 0xff0000ff,
        //                     },
        //                 },
        //             ],
        //         },
        //         {
        //             unicode: "2",
        //             buttonWidthU16: 2,
        //         },
        //         {
        //             unicode: "3",
        //             buttonWidthU16: 3,
        //         },
        //         {
        //             unicode: "4",
        //             buttonWidthU16: 4,
        //             style: [
        //                 {
        //                     //borderProps: {
        //                     //    width: 5,
        //                     //    top: true,
        //                     //    right: true,
        //                     //    color: {
        //                     //         0xff0000ff,
        //                     //    },
        //                     //},
        //                     shadowProps: {
        //                         enable: true,
        //                         widthI16: 20,
        //                         color: {
        //                             colorValueU32: 0xff0000ff,
        //                         },
        //                     },
        //                     backgroundColor: {
        //                         colorValueU32: 0x00ff00ff,
        //                     },
        //                 },
        //             ],
        //         },
        //     ],
        // },
    },
    attributes: {
        // [0x0201]: defineModbusAddress(1, 1),
        // [0x0202]: tagOf(1, 2),
    },
    actions: [],
};

export default definePage(() => {
    return [stringInput1, numberMax, numberMin, w3];
});
