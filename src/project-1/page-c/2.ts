// import { ButtonFunctionCode } from "../../../proto/dev/widget";
import { ButtonKeyCode } from "../../../proto/dev/widget";
import { definePage } from "../../helper/define";
import { posOf, sizeOf, type FullWidget } from "../../helper/utils";
import { DataFormatType } from "../../proto";
import { ImageTemplates } from "../property/image";
import { StyleTemplates } from "../property/style";
// import { texts } from "../texts";

export const message: FullWidget = {
    ui: {
        idU16: 1,
        location: posOf(50, 300),
        size: sizeOf(100, 100),
        style: [StyleTemplates.card()],
        widget: {
            $case: "widgetText",
            value: {
                text: {
                    // tagIdU16: texts.project("This is a messagebox", "测试"),
                },
            },
        },
    },
    actions: [
        // {
        //     actionTiming: ActionTiming.ACTION_TIMING_PRESS,
        //     page: {
        //         actionType: PageActionType.PAGE_ACTION_TYPE_BASE_CHANGE,
        //         pageId: {
        //             valueU16: 3,
        //         },
        //     },
        //     waitResult: false,
        // },
        // {
        //     actionTiming: ActionTiming.ACTION_TIMING_RELEASE,
        //     page: {
        //         actionType: PageActionType.PAGE_ACTION_TYPE_BASE_CHANGE,
        //         pageId: {
        //             valueU16: 1,
        //         },
        //     },
        //     waitResult: false,
        // },
    ],
    attributes: {},
};

export const okBtn: FullWidget = {
    ui: {
        idU16: 2,
        location: posOf(50, 300),
        size: sizeOf(80, 80),
        // style: [{}],
        graphic: [ImageTemplates.image2(), ImageTemplates.image3()],
        widget : {
            $case: "widgetButton",
            value: {
                buttonKeyCode: ButtonKeyCode.BUTTON_KEY_CODE_OK
            },
        },
    },
    actions: [],
    attributes: {},
};

export const cancelBtn: FullWidget = {
    ui: {
        idU16: 2,
        location: posOf(100, 300),
        size: sizeOf(80, 80),
        style: [StyleTemplates.card()],
        graphic: [ImageTemplates.image0(), ImageTemplates.image1()],
        widget: {
            $case: "widgetButton",
            value: {
                buttonKeyCode: ButtonKeyCode.BUTTON_KEY_CODE_CANCEL,
            },
        },
    },
    actions: [],
    attributes: {},
};

export const stringInput1: FullWidget = {
    ui: {
        idU16: 3,
        location: posOf(100, 400),
        size: sizeOf(200, 200),
        widget: {
            $case: "widgetString",
            value: {
                supportInput: true,
                currentValue: "1234567890",
                maxLengthU16: 10,
                minLengthU16: 1,
                showScrollbar: true,
                passwordMode: false,
                keyboardPageIdU16: 3,
                dataFormat: DataFormatType.DATA_FORMAT_TYPE_UTF8,
            },
        },
    },
    attributes: {},
    actions: [],
};

export default definePage(() => {
    return [okBtn];
});
