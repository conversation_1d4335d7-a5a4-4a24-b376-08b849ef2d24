import { definePage } from "../../helper/define";
import { posOf, sizeOf, type FullWidget } from "../../helper/utils";
import { StyleTemplates } from "../property/style";

// 字符串输入
export const stringInput1: FullWidget = {
    ui: {
        idU16: 3,
        location: posOf(100, 400),
        size: sizeOf(200, 200),
        style: [StyleTemplates.card()],
        // widgetString: {
        //     supportInput: true,
        //     currentValue: "1234567890",
        //     maxLengthU16: 10,
        //     minLengthU16: 1,
        //     showScrollbar: true,
        //     passwordMode: false,
        //     keyboardPageIdU16: 3,
        // },
    },
    attributes: {},
    actions: [],
};

export const confirmButton1: FullWidget = {
    ui: {
        idU16: 4,
        location: posOf(100, 400),
        size: sizeOf(200, 200),
        style: [StyleTemplates.card()],
        // widgetButton: {
        //     buttonDefine: {
        //         functionCode: ButtonFunctionCode.BUTTON_FUNCTION_CODE_OK,
        //         actionsU16: [1],
        //         text: [TextTemplates.font1()],
        //     },
        // },
    },
    attributes: {},
    actions: [],
};

export default definePage(() => {
    return [stringInput1, confirmButton1];
});
