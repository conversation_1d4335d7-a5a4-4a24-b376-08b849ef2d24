import { DataFormatType, ScrollDirection, WidgetPropertyEnum } from "../../proto";
import { defineAddress<PERSON>ey, defineModbusAddress, definePage, ModbusRegister } from "../../helper/define";
import { posOf, sizeOf, type FullWidget } from "../../helper/utils";
import { ImageTemplates } from "../property/image";
import { StyleTemplates } from "../property/style";
// import { texts } from "../texts";
import * as widget from "../../../proto/dev/widget";
// import { WidgetCalendar_CalendarType } from "../../../proto/dev/widget";
import { DateFormatType, TimeFormatType,ListDataResourceType, SymbolType, Direction } from "../../../proto/dev/common";
import { TextTemplates } from "../property/text";
import { ButtonKeyCode } from "../../../proto/dev/widget";

// 文本显示
export const label1: FullWidget = {
    ui: {
        idU16: 1,
        location: posOf(0, 0),
        size: sizeOf(50, 50),
        confirmParam: {
            confirmPageIdU16: 3,
            //confirmWaitTimeU16: 1000,
            //confirmTimeoutRun: true,
        },
        style: [StyleTemplates.fixedColorID1(), StyleTemplates.card()],
        text: [
            {
                from: {
                    $case: "tagIdU16",
                    value: 2,
                },
            },
        ],
        actionsU16: [1, 2, 3],
        widget: {
            $case: "widgetText",
            value: {
            },
        },
    },
    attributes: {
        [defineAddressKey(WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_BIT_WRITE_ADDRESS, 1)]: defineModbusAddress(
            1,
            0x601,
            { reg: ModbusRegister.Coil }
        ),
    },
    actions: [],
};

// 图片
export const image1: FullWidget = {
    ui: {
        idU16: 2,
        location: posOf(100, 300),
        size: sizeOf(200, 200),
        // style: [StyleTemplates.card()],
        graphic: [ImageTemplates.image3(), ImageTemplates.image1()],
        stateProperty: {
            noStateWay: widget.NoStateWay.NO_STATE_WAY_USE_BLANK
        },
        widget: {
            $case: "widgetGraphic",
            value: {},
        },
    },
    attributes: {
        // [0x0201]: defineModbusAddress(1, 258, ModbusRegister.Coil),
        // [0x0202]: defineModbusAddress(1, 259, ModbusRegister.Coil),
        // [0x0102]: defineZndAddress(1, ZhenDianRegister.Hb),
        // [0x0103]: defineZndAddress(0, ZhenDianRegister.Hw),
        // [0x0104]: defineZndAddress(1, ZhenDianRegister.Hw),
        // [0x0203]: defineModbusAddress(1, 256, ModbusRegister.HoldingRegister),
        // [0x0204]: defineModbusAddress(1, 257, ModbusRegister.HoldingRegister),
        // [defineAddressKey(WidgetPropertyEnum.WIDGET_PROPERTY_ENUM_ACTION_WORD_WRITE_ADDRESS, 0)]: defineModbusAddress(
        //     1,
        //     258
        // ),
    },
    actions: [
        // {
        //     actionTiming: ActionTiming.ACTION_TIMING_PRESS,
        //     wordAddValue: {
        //         addValue: {
        //             valueU16: 1,
        //         },
        //     },
        //     waitResult: false,
        // },
    ],
};

// 字符串输入
export const stringInput1: FullWidget = {
    ui: {
        idU16: 3,
        location: posOf(100, 400),
        size: sizeOf(200, 200),
        widget: {
            $case: "widgetString",
            value: {
                supportInput: true,
                currentValue: "1234567890",
                maxLengthU16: 10,
                minLengthU16: 1,
                showScrollbar: true,
                passwordMode: false,
                keyboardPageIdU16: 3,
                dataFormat: DataFormatType.DATA_FORMAT_TYPE_UTF8,
            },
        },
    },
    attributes: {},
    actions: [],
};

// 位显示
export const bit1: FullWidget = {
    ui: {
        idU16: 5,
        location: posOf(100, 0),
        size: sizeOf(150, 200),
        minPressTimeU16: 0,
        minPressIntervalU16: 0,
        stateProperty: {
            stateCountU8: 2,
            noStateWay: widget.NoStateWay.NO_STATE_WAY_USE_BLANK
        },
        graphic: [ImageTemplates.image0(), ImageTemplates.image1(), ImageTemplates.image2()],
        style: [StyleTemplates.card(), StyleTemplates.transparent()],
        widget: {
            $case: "widgetBit",
            value: {
                text: [TextTemplates.font_textpad()],
            },
        },
    },
    attributes: {},
    actions: [],
};

// 数值输入
export const numberInput1: FullWidget = {
    ui: {
        idU16: 4,
        location: posOf(300, 200),
        size: sizeOf(150, 200),
        confirmParam: {
            confirmPageIdU16: 2,
            //confirmWaitTimeU16: 1000,
            //confirmTimeoutRun: true,
        },
        widget: {
            $case: "widgetNumber",
            value: {
                supportInput: true,
                // currentValue: {
                currentValue: {
                    from: {
                        $case: "valueDouble",
                        value: 100.12,
                    },
                },
                maxValue: {
                    from: {
                        $case: "valueI32",
                        value: 255,
                    },
                },
                minValue: {
                    from: {
                        $case: "valueI32",
                        value: 0,
                    },
                },
                integerDigitsU8: 3,
                decimalDigitsU8: 2,
                hideLeadingZero: true,
                hideTrailingZero: true,
                showPlusSign: true,
                lessThanMinColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0xff0000ff,
                    },
                },
                greaterThanMaxColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x00ff00ff,
                    },
                },
                lessThanMinFlashTimeU8: 10000,
                greaterThanMaxFlashTimeU8: 10000,
                passwordMode: false,
                keyboardPageIdU16: 3,
                dataFormat: DataFormatType.DATA_FORMAT_TYPE_UTF8,
            },
        },
    },
    attributes: {},
    actions: [],
};

// 克隆
export const clone1: FullWidget = {
    ui: {
        idU16: 6,
        location: posOf(400, 200),
        size: sizeOf(150, 200),
        style: [StyleTemplates.transparent()],
        widget: {
            $case: "widgetClone",
            value: {
                pageIdU16: 1,
                widgetIdU16: 8,
            },
        },
        confirmParam: {
            confirmPageIdU16: 4,
            //confirmWaitTimeU16: 1000,
            //confirmTimeoutRun: true,
        },
    },
    attributes: {},
    actions: [],
};

export const curve1: widget.WidgetCurve = {
    curveType: widget.CurveType.CURVE_TYPE_BAR,
    seriesCountU8: 1,
    xMode: {
        $case: "pointCountU16",
        value: 10,
    },
    drawDirection: ScrollDirection.SCROLL_DIRECTION_LEFT_TO_RIGHT,
    enableCursor: true,
    cursorWidthU8: 5,
    cursorColor: {
        color: {
            $case: "colorValueU32",
            value: 0xff0000ff,
        },
    },
    seriesConfig: [
        {
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
            pointConfig: {
                pointType: widget.PointType.POINT_TYPE_CIRCLE,
                radiusU8: 0,
                widthU8: 10,
                heightU8: 10,
                style: {
                    backgroundColor: {
                        color: {
                            $case: "colorValueU32",
                            value: 0x00ff00ff,
                        },
                    },
                },
            },
            lineConfig: {
                lineType: widget.LineType.LINE_TYPE_LINE,
                roundEnd: false,
                roundStart: false,
                dashWidthU8: 1,
                dashGapU8: 1,
                widthU8: 5,
                color: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x000ff0ff,
                    },
                },
                lengthU8: 5,
            },
            supportScale: false,
            showFadeMask: true,
        },
        // 2
        {
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0x0ff00ff,
                },
            },
            pointConfig: {
                pointType: widget.PointType.POINT_TYPE_CIRCLE,
                radiusU8: 0,
                widthU8: 2,
                heightU8: 2,
                style: {
                    backgroundColor: {
                        color: {
                            $case: "colorValueU32",
                            value: 0xf0f000ff,
                        },
                    },
                },
            },
            lineConfig: {
                lineType: widget.LineType.LINE_TYPE_LINE,
                roundEnd: false,
                roundStart: false,
                dashWidthU8: 2,
                dashGapU8: 3,
                widthU8: 3,
                color: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x1f0f00ff,
                    },
                },
                lengthU8: 6,
            },
            supportScale: false,
            showFadeMask: false,
        },
    ],
    scaleValueConfigY2: {
        show: true,
        scaleShowType: widget.ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_POINT,
        gridShowType: widget.ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
        scaleMain: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
        },
        scaleSec: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
        },
        maxValue: {
            from: {
                $case: "valueI32",
                value: 100,
            },
        },
        minValue: {
            from: {
                $case: "valueI32",
                value: 0,
            },
        },
    scaleRadiusU8: 10,
    },
    scaleValueConfigY: {
        show: true,
        scaleShowType: widget.ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_POINT,
        gridShowType: widget.ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
        scaleMain: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
        },
        scaleSec: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
        },
        maxValue: {
            from: {
                $case: "valueI32",
                value: 100,
            },
        },
        minValue: {
            from: {
                $case: "valueI32",
                value: 0,
            },
        },
        gridLineConfig: {
            lineType: widget.LineType.LINE_TYPE_LINE,
            roundEnd: false,
            roundStart: false,
            dashWidthU8: 5,
            dashGapU8: 3,
            widthU8: 2,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xf02f00ff,
                },
            },
            lengthU8: 6,
        },
        scaleRadiusU8: 0,
    },
    scaleValueConfigX: {
        show: true,
        scaleShowType: widget.ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_POINT,
        gridShowType: widget.ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
        scaleMain: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
        },
        scaleSec: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
        },
        scaleRadiusU8: 0,
        gridLineConfig: {
            lineType: widget.LineType.LINE_TYPE_LINE,
            dashWidthU8: 5,
            dashGapU8: 3,
            widthU8: 2,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff2000ff,
                },
            },
            roundStart: false,
            roundEnd: false,
            lengthU8: 9
        },
    },
    useCurrentTime: false,
    timeFormat: TimeFormatType.TIME_FORMAT_TYPE_HM,
    dateFormat: DateFormatType.DATE_FORMAT_TYPE_YMD,
};
export const curve2: widget.WidgetCurve = {
    curveType: widget.CurveType.CURVE_TYPE_BAR,
    seriesCountU8: 1,
    xMode: {
        $case: "pointCountU16",
        value: 10,
    },
    drawDirection: ScrollDirection.SCROLL_DIRECTION_LEFT_TO_RIGHT,
    enableCursor: true,
    cursorWidthU8: 5,
    cursorColor: {
        color: {
            $case: "colorValueU32",
            value: 0xff0000ff,
        },
    },
    seriesConfig: [
        {
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
            //pointConfig: {
            //    pointType: PointType.POINT_TYPE_CIRCLE,
            //    radiusU8: 0,
            //    widthU8: 10,
            //    heightU8: 10,
            //    style: {
            //        backgroundColor: {
            //            color: {
            //                $case: "colorValueU32",
            //                value: 0x00ff00ff,
            //            },
            //        },
            //    },
            //},
            lineConfig: {
                lineType: widget.LineType.LINE_TYPE_LINE,
                roundEnd: false,
                roundStart: false,
                dashWidthU8: 1,
                dashGapU8: 1,
                widthU8: 5,
                lengthU8: 5,
                color: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x000ff0ff,
                    },
                },
            },
            supportScale: false,
            showFadeMask: false,
        },
        // 2
        {
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0x0ff00ff,
                },
            },
            // pointConfig: {
            //     pointType: PointType.POINT_TYPE_CIRCLE,
            //     radiusU8: 0,
            //     widthU8: 2,
            //     heightU8: 2,
            //     style: {
            //         backgroundColor: {
            //             color: {
            //                 $case: "colorValueU32",
            //                 value: 0xf0f000ff,
            //             },
            //         },
            //     },
            // },
            // lineConfig: {
            //     lineType: LineType.LINE_TYPE_LINE,
            //     roundEnd: false,
            //     roundStart: false,
            //     dashWidthU8: 2,
            //     dashGapU8: 3,
            //     widthU8: 3,
            //     color: {
            //         color: {
            //             $case: "colorValueU32",
            //             value: 0x1f0f00ff,
            //         },
            //     },
            // },
            supportScale: false,
            showFadeMask: false,
        },
    ],
    scaleValueConfigY2: {
        show: true,
        scaleShowType: widget.ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_POINT,
        gridShowType: widget.ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
        scaleMain: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
        },
        scaleSec: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
        },
        scaleRadiusU8: 0,
    },
    scaleValueConfigY: {
        show: true,
        scaleShowType: widget.ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_POINT,
        gridShowType: widget.ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
        scaleMain: {
            scaleCountU8: 10,   
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
        },
        scaleSec: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
        },
        scaleRadiusU8: 0,
        maxValue: {
            from: {
                $case: "valueI32",
                value: 100,
            },
        },
        minValue: {
            from: {
                $case: "valueI32",
                value: 0,
            },
        },
        gridLineConfig: {
            lineType: widget.LineType.LINE_TYPE_LINE,
            roundEnd: false,
            roundStart: false,
            dashWidthU8: 5,
            dashGapU8: 3,
            widthU8: 2,
            lengthU8: 6,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xf02f00ff,
                },
            },
        },
    },
    scaleValueConfigX: {
        show: true,
        scaleShowType: widget.ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_POINT,
        gridShowType: widget.ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
        scaleMain: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
        },
        scaleSec: {
            scaleCountU8: 10,
            scaleWidthU8: 5,
            scaleDrawLen: 10,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff0000ff,
                },
            },
        },
        scaleRadiusU8: 0,
        gridLineConfig: {
            lineType: widget.LineType.LINE_TYPE_LINE,
            dashWidthU8: 5,
            dashGapU8: 3,
            widthU8: 2,
            lengthU8: 6,
            color: {
                color: {
                    $case: "colorValueU32",
                    value: 0xff2000ff,
                },
            },
            roundStart: false,
            roundEnd: false,
        },
    },
    useCurrentTime: false,
    timeFormat: TimeFormatType.TIME_FORMAT_TYPE_HM,
    dateFormat: DateFormatType.DATE_FORMAT_TYPE_YMD,
};

export const trendCurve1: FullWidget = {
    ui: {
        idU16: 17,
        location: posOf(100, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetTrendCurve",
            value: {
                curve: curve1,
                pauseResumeTimeU8: 10,
                isHistory: false,
                useRelativeTime: false,
                useTimeLabel: false,
                timeFormat: TimeFormatType.TIME_FORMAT_TYPE_HM,
                dateFormat: DateFormatType.DATE_FORMAT_TYPE_YMD,
            },
        },
    },
    attributes: {},
    actions: [],
};


export const label2: FullWidget = {
    ui: {
        idU16: 8,
        location: posOf(200, 200),
        size: sizeOf(120, 100),
        style: [StyleTemplates.card()],
        text: [
            {
                from: {
                    $case: "input",
                    value: {
                        from: {
                            $case: "singleLang",
                            value: "label2",
                        },
                    },
                },
            },
        ],
        widget: {
            $case: "widgetText",
            value: {},
        },
    },
    attributes: {},
    actions: [],
};

export const calendar1: FullWidget = {
    ui: {
        idU16: 18,
        location: posOf(100, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetCalendar",
            value: {
                calendarType: widget.WidgetCalendar_CalendarType.CALENDAR_TYPE_DROPDOWN,
                weekBgColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0xff0000ff,
                    },
                },
                weekFontColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x00ff00ff,
                    },
                },
                todayBgColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x0000ffff,
                    },
                },
                todayFontColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x0000ffff,
                    },
                },
                highlightColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x0000ffff,
                    },
                },
                highlightFontColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x0000ffff,
                    },
                },
                highlightDateConfig: [
                    {
                        yearU16: 2025,
                        monthU8: 5,
                        dayU8: 1,
                    },
                ],
            },
        },
    },
};

export const xyCurve1: FullWidget = {
    ui: {
        idU16: 19,
        location: posOf(100, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetXyCurve",
            value: {
                curve: curve1,
            },
        },
    },
    attributes: {},
    actions: [],
};


// export const roller1: FullWidget = {    
//     ui: {
//         idU16: 20,
//         location: posOf(100, 100),
//         size: sizeOf(60, 120),
//         widget: {
//             $case: "widgetRoller",
//             value: {
//                 options: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"],
//                 defIndexU8: 0,
//                 viewCountU8: 5,
//                 font: {
//                     fontIdU8: 16,
//                     textColor: {
//                         color: {
//                             $case: "colorValueU32",
//                             value: 0x0000ffff,
//                         },
//                     },
//                 },
//             },
//         },
//     },
//     attributes: {},
//     actions: [],
// };

export const group: FullWidget = {
    ui: {
        idU16: 21,
        location: posOf(100, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetGroup",
            value: {
                subWidgets: [label1.ui!, label2.ui!]
            },
        },
    },
    attributes: {},
    actions: [],
};

export const okBtn: FullWidget = {
    ui: {
        idU16: 22,
        location: posOf(50, 300),
        size: sizeOf(80, 80),
        // style: [{}],
        graphic: [ImageTemplates.image2(), ImageTemplates.image3()],
        widget : {
            $case: "widgetButton",
            value: {
                buttonKeyCode: ButtonKeyCode.BUTTON_KEY_CODE_OK,
            },
        },
    },
    actions: [],
    attributes: {},
};

export const trendCurve2: FullWidget = {
    ui: {
        idU16: 23,
        location: posOf(200, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetTrendCurve",
            value: {
                curve: curve1,
                pauseResumeTimeU8: 10,
                isHistory: false,
                useRelativeTime: false,
                useTimeLabel: false,
                timeFormat: TimeFormatType.TIME_FORMAT_TYPE_HM,
                dateFormat: DateFormatType.DATE_FORMAT_TYPE_YMD,
            },
        },
    },
    attributes: {},
    actions: [],
};

export const options: widget.WidgetOptionList = {
    dataResourceType: ListDataResourceType.LIST_DATA_RESOURCE_TYPE_UNSPECIFIED,
    options: ["1", "2", "3"],
    optionsValue: ["1", "2", "3"],
    selectedColor: {
        color: {
            $case: "colorValueU32",
            value: 0x0000ffff,
        },
    },
    rowSpacingU8: 5,
}
export const roller1: FullWidget = {
    ui: {
        idU16: 24,
        location: posOf(200, 100),
        size: sizeOf(80, 80),
        widget: {
            $case: "widgetRollerList",
            value: {
                rollerListMode: widget.WidgetRollerList_RollerListMode.ROLLER_LIST_MODE_LIMITED,
                list: options,
                viewCountU8: 5,
                defIndexU8: 0,
            },
        },
    },
    attributes: {},
    actions: [],
};

export const dropDown: FullWidget = {
    ui: {
        idU16: 25,
        location: posOf(200, 300),
        size: sizeOf(80, 40),
        widget: {
            $case: "widgetDropList",
            value: {
                list: options,
                direction: Direction.DIRECTION_UP,
                alwaysOpen: true,
                listBgColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x0000ffff,
                    },
                },
                dropListSymbol: SymbolType.SYMBOL_TYPE_UP,
            },
        },
    },
    attributes: {},
    actions: [],
};

export const barCurve: FullWidget = {
    ui: {
        idU16: 26,
        location: posOf(100, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetBarCurve",
            value: {
                curve: curve1,
            },
        },
    },
    attributes: {},
    actions: [],
};

export const scatterCurve: FullWidget = {
    ui: {
        idU16: 27,
        location: posOf(100, 100),
        size: sizeOf(500, 350),
        widget: {
            $case: "widgetScatterCurve",
            value: {
                curve: curve1,
            },
        },
    },
    attributes: {},
    actions: [],
};

export const meter: FullWidget = {
    ui: {
        idU16: 28,
        location: posOf(100, 100),
        size: sizeOf(200, 200),
        widget: {
            $case: "widgetMeter",
            value: {
                startU16: 0,
                endU16: 180,
                lowerLimitU16: 20,
                upperLimitU16: 80,
                limitWidthU8: 5,
                limitRadiusU8: 10,
                lowerLimitColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x0000ffff,
                    } 
                },
                upperLimitColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x00f0ffff,
                    } 
                },
                middleColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0xff00ffff,
                    } 
                },
                scaleValueConfig: {
                    scaleShowType: widget.ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_UNSPECIFIED,
                    gridShowType: widget.ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_UNSPECIFIED,
                    scaleMain: {
                        scaleCountU8: 4,
                        scaleWidthU8: 5,
                        scaleDrawLen: 10,
                        color: {
                            color: {
                                $case: "colorValueU32",
                                value: 0x00ff00ff,
                            },
                        },
                    },
                    scaleSec: {
                        scaleCountU8: 10,
                        scaleWidthU8: 5,
                        scaleDrawLen: 10,
                        color: {
                            color: {
                                $case: "colorValueU32",
                                value: 0xff000fff,
                            },
                        },
                    },
                    scaleRadiusU8: 10,
                    minValue: {
                        from: {
                            $case: "valueI32",
                            value: 0,
                        },
                    },
                    maxValue: {
                        from: {
                            $case: "valueI32",
                            value: 100,
                        },
                    },
                },
                scaleLabelColor: {
                    color: {
                        $case: "colorValueU32",
                        value: 0x0000ffff,
                    } 
                },
                decimalPlacesU8: 0,
                labelRadiusU8: 10,
                pointerConfig: {
                    color: {
                        color: {
                            $case: "colorValueU32",
                            value: 0x0000ffff,
                        } 
                    },
                    widthU8: 5,
                    lengthU8: 10,
                },
                pointerPointConfig: {
                    radiusU8: 6,
                    style: {
                        backgroundColor: {
                            color: {
                                $case: "colorValueU32",
                                value: 0x00f0ffff,
                            } 
                        },

                    }  
                },
            },
        },
    },
    attributes: {},
    actions: [],
};


export const rules: FullWidget = {
    ui: {
        idU16: 29,
        location: posOf(100, 100),
        size: sizeOf(200, 200),
        widget: {
            $case: "widgetRuler",
            value: {
                direction: widget.RulerDirection.RULER_DIRECTION_TOP_LEFT,
                scaleConfig: {
                    scaleMain: {
                        scaleCountU8: 4,
                        scaleWidthU8: 5,
                        scaleDrawLen: 10,
                        color: {
                            color: {
                                $case: "colorValueU32", 
                                value: 0x0000ffff,
                            },
                        },
                    },
                    maxValue: {
                        from: {
                            $case: "valueI32",
                            value: 100,
                        },
                    },
                    minValue: {
                        from: {
                            $case: "valueI32",
                            value: 0,
                        },
                    },
                    scaleSec: {
                        scaleCountU8: 10,
                        scaleWidthU8: 5,
                        scaleDrawLen: 10,
                        color: {
                            color: {
                                $case: "colorValueU32",
                                value: 0x00f0ffff,
                            },
                        },
                    },
                },
                mainLine: {
                    widthU8: 5,
                    lengthU8: 10,
                    color: {
                        color: {
                            $case: "colorValueU32",
                            value: 0x0000ffff,
                        },
                    },
                }
            },
        },
    }
};

export default definePage(() => {
    // return [image1]
    // return [stringInput1, numberInput1];
    
    // return [label1, image1, stringInput1, bit1, numberInput1, clone1, calendar1];
    // return [image1, stringInput1, bit1, numberInput1]
    // return [meter]

    // return [bit1, okBtn];
    // return [clone1,label1, label2];
    return [createHorizontalRuler];
    // return [dropDown];
    // return [trendCurve1];
    // return [bit1];
    // return [label1, trendCurve1]
});
