# 刻度尺组件在1.ts中的使用方法

本文档展示了如何在`1.ts`文件中使用刻度尺组件的各种方式。

## 1. 基础使用方式

### 直接定义刻度尺组件

在`1.ts`文件中，我们已经定义了两个基础的刻度尺组件：

```typescript
// 水平刻度尺
export const horizontalRuler: FullWidget = {
    ui: {
        idU16: 29,
        location: posOf(50, 100),
        size: sizeOf(300, 50),
        widget: {
            $case: "widgetRuler",
            value: {
                rulerType: widget.RulerType.RULER_TYPE_HORIZONTAL,
                // ... 其他配置
            },
        },
    },
    attributes: {},
    actions: [],
};

// 垂直刻度尺
export const verticalRuler: FullWidget = {
    // ... 类似配置
};
```

### 在页面中使用

```typescript
export default definePage(() => {
    return [horizontalRuler, verticalRuler];
});
```

## 2. 使用工厂函数动态创建

### 导入工厂函数

```typescript
import { 
    createHorizontalRuler,
    createVerticalRuler,
    createArcRuler,
    rulerExamples
} from "./ruler-examples";
```

### 创建动态刻度尺

```typescript
// 创建水平刻度尺：ID, x, y, width, height, minValue, maxValue, currentValue
const dynamicHorizontalRuler = createHorizontalRuler(4001, 50, 400, 300, 40, 0, 100, 35);

// 创建垂直刻度尺：ID, x, y, width, height, minValue, maxValue, currentValue
const dynamicVerticalRuler = createVerticalRuler(4002, 400, 400, 50, 200, 0, 500, 200);

// 创建弧形刻度尺：ID, x, y, size, minValue, maxValue, startAngle, endAngle, currentValue
const dynamicArcRuler = createArcRuler(4003, 500, 400, 120, 0, 120, 135, 405, 75);
```

### 在页面中使用动态创建的刻度尺

```typescript
export default definePage(() => {
    return [dynamicHorizontalRuler, dynamicVerticalRuler, dynamicArcRuler];
});
```

## 3. 使用预定义示例

### 预定义的示例组件

`rulerExamples`对象包含了几个预配置的刻度尺示例：

- `thermometer`: 温度计（垂直刻度尺，-10°C到50°C）
- `pressureGauge`: 压力表（弧形刻度尺，0-10压力单位）
- `lengthRuler`: 长度测量尺（水平刻度尺，0-50长度单位）
- `liquidMeasure`: 液体容量刻度（垂直刻度尺，0-1000ml）

### 使用预定义示例

```typescript
export default definePage(() => {
    return [
        rulerExamples.thermometer, 
        rulerExamples.pressureGauge, 
        rulerExamples.lengthRuler
    ];
});
```

## 4. 混合使用方式

你可以混合使用不同方式创建的刻度尺：

```typescript
export default definePage(() => {
    return [
        horizontalRuler,                    // 基础定义的组件
        rulerExamples.thermometer,          // 预定义示例
        dynamicArcRuler                     // 动态创建的组件
    ];
});
```

## 5. 实际应用场景示例

### 温度监控界面

```typescript
const temperatureRuler = createVerticalRuler(
    5001,           // ID
    100, 50,        // 位置
    40, 200,        // 大小
    -20, 60,        // 温度范围 -20°C 到 60°C
    25              // 当前温度 25°C
);
```

### 液体容量测量

```typescript
const cupMeasure = createVerticalRuler(
    5002,           // ID
    200, 50,        // 位置
    60, 300,        // 大小
    0, 500,         // 容量范围 0-500ml
    150             // 当前液位 150ml
);
```

### 压力表显示

```typescript
const pressureMeter = createArcRuler(
    5003,           // ID
    300, 100,       // 位置
    150,            // 大小
    0, 10,          // 压力范围 0-10 bar
    135, 405,       // 角度范围 135°-405°
    6.5             // 当前压力 6.5 bar
);
```

### 长度测量工具

```typescript
const ruler = createHorizontalRuler(
    5004,           // ID
    50, 300,        // 位置
    400, 30,        // 大小
    0, 50,          // 长度范围 0-50cm
    12.5            // 当前测量值 12.5cm
);
```

## 6. 配置参数说明

### 工厂函数参数

#### createHorizontalRuler / createVerticalRuler
- `id`: 组件唯一ID
- `x, y`: 组件位置坐标
- `width, height`: 组件尺寸
- `minValue, maxValue`: 刻度范围
- `currentValue`: 当前值（可选）

#### createArcRuler
- `id`: 组件唯一ID
- `x, y`: 组件位置坐标
- `size`: 组件尺寸（正方形）
- `minValue, maxValue`: 刻度范围
- `startAngle, endAngle`: 起始和结束角度（默认135°-405°）
- `currentValue`: 当前值（可选）

### 刻度尺类型

- `RULER_TYPE_HORIZONTAL`: 水平刻度尺
- `RULER_TYPE_VERTICAL`: 垂直刻度尺
- `RULER_TYPE_ARC`: 弧形刻度尺
- `RULER_TYPE_CIRCLE`: 圆形刻度尺

### 刻度方向

- `RULER_DIRECTION_TOP_LEFT`: 刻度在上方/左侧
- `RULER_DIRECTION_BOTTOM_RIGHT`: 刻度在下方/右侧
- `RULER_DIRECTION_BOTH`: 刻度在两侧

### 标签位置

- `RULER_LABEL_POSITION_OUTSIDE`: 标签在刻度外侧
- `RULER_LABEL_POSITION_INSIDE`: 标签在刻度内侧
- `RULER_LABEL_POSITION_CENTER`: 标签在刻度中心

## 7. 当前页面配置

当前`1.ts`文件配置为显示三个动态创建的刻度尺：

```typescript
export default definePage(() => {
    return [dynamicHorizontalRuler, dynamicVerticalRuler, dynamicArcRuler];
});
```

你可以通过修改这个返回数组来切换显示不同的刻度尺组合。

## 8. 注意事项

1. **ID唯一性**: 确保每个组件的ID是唯一的
2. **位置布局**: 注意组件之间的位置不要重叠
3. **数值范围**: 确保currentValue在minValue和maxValue范围内
4. **角度设置**: 弧形刻度尺的角度范围要合理设置
5. **尺寸适配**: 根据实际需求调整组件尺寸

通过这些方式，你可以灵活地在项目中使用刻度尺组件，满足各种测量显示需求。
