import { defineModbusAdd<PERSON>, define<PERSON><PERSON>, ModbusRegister } from "../../helper/define";
import type { PartOf } from "../../helper/types";
import { type FullWidget } from "../../helper/utils";
import type { Action } from "../../proto";
import { ActionTiming, BitSetMode, PageActionType } from "../../proto";
import { ImageTemplates } from "../property/image";
import { StyleTemplates } from "../property/style";

function ui() {
    return {
        style: [StyleTemplates.card()],
        graphic: [ImageTemplates.image0(), ImageTemplates.image1()],
        widgetGraphic: {},
    };
}

function act(op: PartOf<Action["action"]>): PartOf<Action>[] {
    return [
        {
            actionTiming: ActionTiming.ACTION_TIMING_PRESS,
            waitResult: false,
            ...op,
        },
    ];
}

export const c1: FullWidget = {
    ui: ui(),
    attributes: {},
    actions: act({
        $case: "bitOpera",
        value: {
            bitWriteMode: BitSetMode.BIT_SET_MODE_TOGGLE,
            writeVariable: defineModbusAddress(1, 0x601, { reg: ModbusRegister.Coil }),
        },
    }),
};

export const c2: FullWidget = {
    ui: ui(),
    attributes: {},
    actions: act({
        $case: "page",
        value: {
            actionType: PageActionType.PAGE_ACTION_TYPE_BASE_CHANGE,
            pageId: {
                from: {
                    $case: "constantU16",
                    value: 2,
                },
            },
        },
    }),
};

export const c3: FullWidget = {
    ui: ui(),
    attributes: {},
    actions: act({
        $case: "page",
        value: {
            actionType: PageActionType.PAGE_ACTION_TYPE_BASE_CHANGE,
            pageId: {
                from: {
                    $case: "constantU16",
                    value: 1,
                },
            },
        },
    }),
};

export const c4: FullWidget = {
    ui: ui(),
    attributes: {},
    actions: act({
        $case: "wordAddValue",
        value: {
            readVariable: defineModbusAddress(1, 0x104),
            writeVariable: defineModbusAddress(1, 0x104),
            addValue: {
                from: {
                    $case: "constantNumber",
                    value: {
                        from: {
                            $case: "valueI32",
                            value: 1,
                        },
                    },
                },
            },
        },
    }),
};

export default definePage(() => {
    return [c1, c2, c3, c4];
});
