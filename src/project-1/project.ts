import { encode } from "../helper/encode";
import type { PartOf } from "../helper/types";
import { HardwareType, ProjectInfo, Version } from "../proto";

export function project() {
    const builderVersion = Version.create({
        majorU8: 0,
        minorU8: 1,
        patchU16: 0,
        buildU16: 0,
    });

    const project: PartOf<ProjectInfo> = {
        hardwareType: HardwareType.HARDWARE_TYPE_PC,
        createBuilderVersion: builderVersion,
        // projectIdU32: 12345,
        updateBuilderVersion: builderVersion,
        minFirmwareVersion: builderVersion,
    };

    return encode(ProjectInfo, project);
}
