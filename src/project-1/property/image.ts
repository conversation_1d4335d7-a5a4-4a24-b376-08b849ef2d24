import { GraphicReference, GraphicType } from "../../proto";
import { toColor } from "../../helper/utils";

export const ImageTemplates = {
    image0: (): GraphicReference => ({
        graphicType: GraphicType.GRAPHIC_TYPE_IMAGE,
        src: 0,
        widthU16: 200,
        heightU16: 200,
        opacityU8: 50,
        recolor: toColor("#ff0000"),
    }),
    image1: (): GraphicReference => ({
        graphicType: GraphicType.GRAPHIC_TYPE_IMAGE,
        src: 1,
        widthU16: 200,
        heightU16: 200,
        opacityU8: 50,
        recolor: toColor("#ff0000"),
    }),
    image2: (): GraphicReference => ({
        graphicType: GraphicType.GRAPHIC_TYPE_IMAGE,
        src: 2,
        widthU16: 200,
        heightU16: 200,
    }),

    image3: (): GraphicReference => ({
        graphicType: GraphicType.GRAPHIC_TYPE_IMAGE,
        src: 3,
        widthU16: 200,
        heightU16: 200,
    }),
};
