import { StyleProperties } from "../../proto";
import { toColor } from "../../helper/utils";

// 常用样式模板
export const StyleTemplates = {
    // 透明无边框样式
    transparent: (): StyleProperties => ({
        backgroundColor: toColor("#00000000"), // 完全透明
        shadowProps: {
            enable: true,
            color: toColor("#ff0000"),
            offsetXI16: 10,
            offsetYI16: 10,
            spreadI16: 5,
            widthI16: 5,
        },
        borderProps: {
            color: toColor("#000000"),
            widthU8: 0,
            left: false,
            top: false,
            right: false,
            bottom: false,
            internal: false,
        },
    }),

    random: (options?: { bgColor?: number; borderColor?: number; shadowColor?: number }): StyleProperties => ({
        backgroundColor: {
            color: {
                $case: "colorValueU32",
                value: 0x00FF00FF,
            },
        },
        borderProps: {
            color: toColor(options?.borderColor ?? "#e0e0e0"), // 默认浅灰色
            widthU8: 1,
            left: true,
            top: true,
            right: true,
            bottom: true,
            internal: false,
        },
        shadowProps: {
            enable: true,
            color: toColor(options?.shadowColor ?? "#000000"), // 默认半透明黑色
            offsetXI16: 2,
            offsetYI16: 2,
            spreadI16: 0,
            widthI16: 4,
        },
    }),

    fixedColorID1: (): StyleProperties => ({
        backgroundColor: {
            color: {
                $case: "colorVarIdU8",
                value: 1,
            },
        },
        borderProps: {
            color: {
                color: {
                    $case: "colorVarIdU8",
                    value: 1,
                },
            },
            widthU8: 1,
            left: true,
            top: true,
            right: true,
            bottom: true,
            internal: false,
        },
        shadowProps: {
            enable: true,
            color: {
                color : {
                    $case: "colorVarIdU8",
                    value: 1,
                }
            },
            offsetXI16: 2,
            offsetYI16: 2,
            spreadI16: 0,
            widthI16: 4,
        },
    }),

    // 卡片样式
    card: (options?: { bgColor?: number; borderColor?: number; shadowColor?: number }): StyleProperties => ({
       // backgroundColor: {
       //     //color: {
       //     //    $case: "colorVarIdU8",
       //     //    value: 1,
       //     //},
       // },
        backgroundColor: toColor(options?.bgColor ?? "#3399ff"), // 默认蓝色
        borderProps: {
            color: toColor(options?.borderColor ?? "#e0e0e0"), // 默认浅灰色
            widthU8: 1,
            left: true,
            top: true,
            right: true,
            bottom: true,
            internal: false,
        },
        shadowProps: {
            enable: true,
            color: toColor(options?.shadowColor ?? "#000000"), // 默认半透明黑色
            offsetXI16: 2,
            offsetYI16: 2,
            spreadI16: 0,
            widthI16: 4,
        },
    }),

    // 按钮样式
    button: (options?: { bgColor?: number; borderColor?: number; hoverColor?: number }): StyleProperties => ({
        backgroundColor: toColor(options?.bgColor ?? "#3399ff"), // 默认蓝色
        borderProps: {
            color: toColor(options?.borderColor ?? "#2266cc"), // 默认深蓝色
            widthU8: 1,
            left: true,
            top: true,
            right: true,
            bottom: true,
            internal: false,
        },
        shadowProps: {
            enable: true,
            color: toColor(options?.hoverColor ?? "#000000"), // 默认半透明黑色
            offsetXI16: 0,
            offsetYI16: 2,
            spreadI16: 0,
            widthI16: 2,
        },
    }),

    // 输入框样式
    input: (options?: { bgColor?: number; borderColor?: number; focusColor?: number }): StyleProperties => ({
        backgroundColor: toColor(options?.bgColor ?? "#ffffff"), // 默认白色
        borderProps: {
            color: toColor(options?.borderColor ?? "#cccccc"), // 默认灰色
            widthU8: 1,
            left: true,
            top: true,
            right: true,
            bottom: true,
            internal: false,
        },
        shadowProps: {
            enable: true,
            color: toColor(options?.focusColor ?? "#0099ff"), // 默认蓝色阴影
            offsetXI16: 0,
            offsetYI16: 0,
            spreadI16: 0,
            widthI16: 2,
        },
    }),

    // 分割线样式
    divider: (options?: { color?: number; width?: number }): StyleProperties => ({
        backgroundColor: toColor(options?.color ?? "#e0e0e0"), // 默认浅灰色
        borderProps: {
            color: toColor(options?.color ?? "#e0e0e0"),
            widthU8: options?.width ?? 1,
            left: false,
            top: true,
            right: false,
            bottom: false,
            internal: false,
        },
    }),

    // 警告样式
    warning: (): StyleProperties => ({
        backgroundColor: toColor("#ffff00"), // 半透明黄色
        borderProps: {
            color: toColor("#ffcc00"), // 橙色边框
            widthU8: 1,
            left: true,
            top: true,
            right: true,
            bottom: true,
            internal: false,
        },
    }),

    // 错误样式
    error: (): StyleProperties => ({
        backgroundColor: toColor("#ff0000"), // 半透明红色
        borderProps: {
            color: toColor("#cc0000"), // 深红色边框
            widthU8: 1,
            left: true,
            top: true,
            right: true,
            bottom: true,
            internal: false,
        },
    }),
};
