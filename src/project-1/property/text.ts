import { TextReference } from "../../proto";

export const TextTemplates = {
//    font1: (): TextReference => ({
        //tagIdU16: texts.project("font1", "font-1"),
        //autoGenerated: false,
        //font: [
            //{
                //fontIdU8: 16,
                //textAlign: AlignType.ALIGN_TYPE_CENTER,
                //textPadding: {
                    //leftI16: 0,
                    //topI16: 0,
                    //rightI16: 0,
                    //bottomI16: 0,
                //},
                //textColor: {
                    //color: {
                        //$case: "colorValueU32",
                        //value: 0x000000ff,
                    //},
                //},
                //blinkIntervalU8: 0,
            //},
        //],
    //}),

    //font2: (): TextReference => ({
        //tagIdU16: texts.project("font2", "font-2"),
        //autoGenerated: false,
        //font: [
            //{
                //fontIdU8: 16,
                //textAlign: AlignType.ALIGN_TYPE_CENTER,
                //textColor: {
                    //color: {
                        //$case: "colorValueU32",
                        //value: 0x000000ff,
                    //},
                //},
                //blinkIntervalU8: 0,
            //},
        //],
    //}),

    //font_marquee: (): TextReference => ({
        //from: {     
            //$case: "input",
            //value: {
                //from: {
                    //$case: "singleLang",
                    //value: "",
                //},
                //font: [
                    //{
                        //fontIdU8: 16,
                        //textAlign: AlignType.ALIGN_TYPE_CENTER,
                        //textPadding: {
                            //leftI16: 0,
                    //topI16: 0,
                    //rightI16: 0,
                    //bottomI16: 0,
                //},
                //textColor: {
                    //color: {
                        //$case: "colorValueU32",
                        //value: 0x000000ff,
                    //},
                //},
                //blinkIntervalU8: 0,
                //marquee: {
                    //enable: true,
                    //intervalTimeU8: 1000,
                    //scrollDistanceU16: 10,
                    //scrollDirection: ScrollDirection.SCROLL_DIRECTION_LEFT_TO_RIGHT,
                    //loop: true,
                //},
            //},
        //],
    //}),

    font_textpad: (): TextReference => ({
        from: {
            $case: "input",
            value: {
                from: {
                    $case: "singleLang",
                    value: "text",
                },
                classifyIdU16: 0,
                multiLine: false,
            },
        },
    }),
};
