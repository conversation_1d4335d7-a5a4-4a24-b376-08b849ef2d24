// import { ProjectI18n } from "../helper/I18n";
import type { PartOf } from "../helper/types";
import { encode } from "../helper/encode";
import { FontStyle, TextTagLibrary, type FontDefinition } from "../proto";
// import { TextTagLibrary } from "../../../proto/dev/text";

// export const texts = new ProjectI18n();

export const fontDefinitions: PartOf<FontDefinition>[] = [
    {
        name: "simsun",
        fonts: {
            0: {
                fontSize: 12,
                fontStyle: FontStyle.FONT_STYLE_UNSPECIFIED,
                fontName: "simsun",
            }
        },
    },
];

function createTextTag() {
    const tag: PartOf<TextTagLibrary> = {
        tags: {
            0: {
                from: {
                    $case: "singleLang",
                    value: "text0",
                },
            },
            1: {
                from: {
                    $case: "singleLang",
                    value: "text1"
                    // value: {
                    //     content: {
                    //         1: "text1",
                    //     },
                    // },
                },
            },
            2: {
                from: {
                    $case: "multiLang",
                    value: {
                        content: {
                            0: "mutLang1",
                            1: "mutLang2",
                        },
                    },
                },
            },
        },
    };
    return tag;
}

export function texts() {
    return encode(TextTagLibrary, createTextTag());
}
