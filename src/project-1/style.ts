import { ProjectStyleConfig} from "../proto";
import { encode } from "../helper/encode";
import type { PartOf } from "../helper/types";

function createStyle() {
    const conf: PartOf<ProjectStyleConfig> = {
        themes: [
            {
                name: "default",
                memo: "默认主题",
            },
        ],
        defaultThemeNoU8: 1,
        defaultLanguageNoU8: 0,
        styleTemplates: {
            0: {
                name: "0",
                properties: {
                    backgroundColor: {
                        color: {
                            $case: "colorValueU32",
                            value: 0xffff00ff,
                        },
                    },
                    borderProps: {
                        color: {
                            color: {
                                $case: "colorValueU32",
                                value: 0xff0000ff,
                            },
                        },
                    },
                },
            },
            1: {
                name: "1",
                properties: {
                    backgroundColor: {
                        color: {
                            $case: "colorValueU32",
                            value: 0x00ff00ff,
                        },
                    },
                    borderProps: {
                        color: {
                            color: {
                                $case: "colorValueU32",
                                value: 0x00ff00ff,
                            },
                        },
                    },
                },
            },
        },
        themeColorValues: {
            0x0001: 0xFF0000FF,
            0x0101: 0xFF0000FF,
            0x0201: 0x00FF00FF,
            0x0301: 0x0000FFFF,

            0x0002: 0xFF0000FF,
            0x0102: 0xFF0000FF,
            0x0202: 0x00FF00FF,
            0x0302: 0x0000FFFF,

            0x0003: 0xFF0000FF,
            0x0103: 0xFF0000FF,
            0x0203: 0x00FF00FF,
            0x0303: 0x0000FFFF,
        },
        languages: [
            {
                languageType: 1,
                defaultFontName: "Arial",
            },
            {
                languageType: 2,
                defaultFontName: "Arial",
            },
            {
                languageType: 1,
                defaultFontName: "Arial",
            },
            {
                languageType: 2,
                defaultFontName: "Arial",
            },
        ],
    };
    return conf;
}

export function style() {
    return encode(ProjectStyleConfig, createStyle());
}
