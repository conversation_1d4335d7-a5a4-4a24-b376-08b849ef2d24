import { FontIdOptionMap } from "../gen/font";

export const fontMap: FontIdOptionMap = {
    "-1": {
        size: 12,
        fonts: {
            simsun: [
                { symbols: "你好，世界！" },
                { symbols: "中国福建厦门" },
                { symbols: "文本边距" },
                { symbols: "字体" },
                { symbols: "文本颜色" },
                { symbols: "文本对齐" },
                { symbols: "文本闪烁" },
                { symbols: "文本跑马灯" },
                { symbols: "文本跑马灯" },
                { symbols: "一二三四五六七八九十" },
                { symbols: "壹贰叁肆伍陆柒捌玖拾" },
                { symbols: "！@#￥%……&*（）——+{}【】：“”；'：？《》，。、；：" },
                { range: [0, 0xff] },
            ],
        },
    },
    16: {
        size: 16,
        fonts: {
            simsun: [
                { symbols: "你好，世界！" },
                { symbols: "中国福建厦门" },
                { symbols: "文本边距" },
                { symbols: "字体" },
                { symbols: "文本颜色" },
                { symbols: "文本对齐" },
                { symbols: "文本闪烁" },
                { symbols: "文本跑马灯" },
                { symbols: "文本跑马灯" },
                { symbols: "一二三四五六七八九十" },
                { symbols: "壹贰叁肆伍陆柒捌玖拾" },
                { symbols: "！@#￥%……&*（）——+{}【】：“”；'：？《》，。、；：" },
                { range: [0, 0xff] },
            ],
        },
    },
};
