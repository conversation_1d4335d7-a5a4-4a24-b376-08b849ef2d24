import { getConfig } from "../gen/conf";
import { ProjectData } from "../helper/ProjectData";
import { setDevices } from "./device";
import { display } from "./display";
import pagesC from "./page-c";
import { setServerPage } from "./page-s";
import { project } from "./project";
import { style } from "./style";
import { texts } from "./texts";

const conf = getConfig();

export const projectData = new ProjectData();

setDevices(projectData);
setServerPage(projectData);

const pages = (function () {
    if (conf.server) {
        const pages: Record<number, () => Uint8Array> = {};
        for (const id of projectData.pages()) {
            pages[id] = () => projectData.getPage(id).getBuf();
        }
        return pages;
    } else {
        return pagesC;
    }
})();

export const project1 = {
    project,
    display,
    devices: () => projectData.getTunnelDeviceList(),
    style,
    texts,
    pages,
} as const;
