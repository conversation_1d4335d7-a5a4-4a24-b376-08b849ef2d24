import type { PartOf } from "../helper/types";
import { posOf, sizeOf } from "../helper/utils";
import { Widget } from "../proto";

/**
 * 模拟的组件
 */
export const widget1: PartOf<Widget> = {
    idU16: 1,
    location: posOf(0, 0),
    size: sizeOf(100, 100),
};

/**
 * 模拟的组件2
 */
export const widget2: PartOf<Widget> = {
    idU16: 2,
    location: posOf(100, 0),
    size: sizeOf(100, 100),
};

/**
 * 模拟的组件3
 */
export const widget3: PartOf<Widget> = {
    idU16: 3,
    location: posOf(300, 300),
    size: sizeOf(100, 100),
};
