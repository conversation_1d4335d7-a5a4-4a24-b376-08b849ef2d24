import type { ProjectData } from "../../helper/ProjectData";
import { setActions } from "./setActions";
import { setAttributes } from "./setAttributes";
import { setCollect } from "./setCollect";
import { setCollect2 } from "./setCollect2";
import { setDebug } from "./setDebug";

export function setServerPage(project: ProjectData) {
    if (false) {
        setDebug(1, project);
    } else {
        setAttributes(1, project);
        setActions(2, project);
        setCollect(3, project);
        setCollect2(4, project);
    }
}
