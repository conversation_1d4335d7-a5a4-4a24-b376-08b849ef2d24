import type { ProjectData } from "../../helper/ProjectData";
import type { PartOf } from "../../helper/types";
import type { DataReferenceUInt8 } from "../../proto";

export function setDebug(pageId: number, project: ProjectData) {
    const page = project.createPage(pageId);
    const device = project.getModbusDevice(2);

    const index: PartOf<DataReferenceUInt8> = {
        from: {
            $case: "variable",
            value: device.useRegister(0),
        },
    };

    page.addWidget({
        attributes: {
            1: device.ref(1),
            2: device.ref(2, 2),
            3: device.ref(3, 1),
            4: device.ref(4, 1, 1),
            5: device.ref(4, index, 1),
            11: device.useCoil(100),
            12: device.useDynamic(device.useRegister(0), device.ref(2, 1), device.ref(2, 2), device.ref(2, 3)),
        },
    });
}
