import type { ProjectData } from "../../helper/ProjectData";
import { PageActionType } from "../../proto";
import { defineAction } from "./helper";

export function setAttributes(pageId: number, project: ProjectData) {
    const page = project.createPage(pageId);
    const device = project.getModbusDevice(2);

    page.addWidget({
        attributes: {
            1: device.ref(0x1),
            2: device.ref(0x1),

            11: device.useRegister(0x1),
            12: device.useCoil(0x1),
        },
        actions: defineAction({
            $case: "page",
            value: {
                actionType: PageActionType.PAGE_ACTION_TYPE_BASE_CHANGE,
                pageId: {
                    from: {
                        $case: "constantU16",
                        value: 1,
                    },
                },
            },
        }),
    });
}
