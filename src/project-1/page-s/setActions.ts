import type { ProjectData } from "../../helper/ProjectData";
import { PageActionType } from "../../proto";
import { defineAction } from "./helper";

export function setActions(pageId: number, project: ProjectData) {
    const page = project.createPage(pageId);

    page.addWidget({
        actions: defineAction({
            $case: "page",
            value: {
                actionType: PageActionType.PAGE_ACTION_TYPE_BASE_CHANGE,
                pageId: {
                    from: {
                        $case: "constantU16",
                        value: 2,
                    },
                },
            },
        }),
    });
}
