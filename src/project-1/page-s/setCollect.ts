import type { ProjectData } from "../../helper/ProjectData";
import { PageActionType } from "../../proto";
import { defineAction } from "./helper";

export function setCollect(pageId: number, project: ProjectData) {
    const page = project.createPage(pageId);
    const vm = project.getModbusDevice(2);

    page.addWidget({
        actions: defineAction({
            $case: "page",
            value: {
                actionType: PageActionType.PAGE_ACTION_TYPE_BASE_CHANGE,
                pageId: {
                    from: {
                        $case: "constantU16",
                        value: 3,
                    },
                },
            },
        }),
    });

    for (let i = 0; i < 3; i++) {
        page.addWidget({
            attributes: {
                11: vm.useRegister(i),
                12: vm.useCoil(i),
            },
        });
    }
}
