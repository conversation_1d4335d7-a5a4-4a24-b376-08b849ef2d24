#include "urq/var/table.h"
#include "urq/atag/atag.h"
#include "urq/atag/id.h"
#include "urq/var/var.h"
#include "urq_test_c.h"
#include <stdbool.h>
#include <stdint.h>
#include <urq_test_c/assert_array_equal.h>

struct _ctx {
    int count;
    int value;
};

void cb1(void *arg1, const urq_var_t *const value)
{
    struct _ctx *ctx = arg1;
    ctx->count++;
    printf("cb1: %d\n", value->value[0]);
    // ctx->value = NULL;
}

test_unit(normal)
{
    struct _ctx ctx = {0};
    const size_t SIZE = 2;
    const urq_atag_id_t TAG_ID = 99;

    urq_atag_t atag = (urq_atag_t){
        .device_id = 1,
        .tag_id = TAG_ID,
        .raw_size = (uint16_t)SIZE,
        .byte_size = (uint16_t)SIZE,
    };

    urq_var_table_t *table = urq_var_table_new();
    assert_ne(table, NULL);

    uint8_t data[10] = {0};
    int is_change = 0;

    assert_eq(urq_var_table_add(table, &atag), 0, "添加变量应该成功");
    assert_eq(urq_var_table_add(table, &atag), -1, "重复添加变量应该失败");
    assert_eq((int32_t)urq_var_table_size(table), 1, "变量数量应该为1");

    assert_eq(
        urq_var_table_add_cb(table, TAG_ID - 1, cb1, &ctx), -1,
        "给不存在的变量添加回调应该失败");
    assert_eq(
        urq_var_table_add_cb(table, TAG_ID, cb1, &ctx), 0, "添加回调应该成功");

    assert_eq(
        urq_var_table_set(table, TAG_ID, SIZE, data, &is_change), 0,
        "设置变量应该成功");
    assert_eq(ctx.count, 0, "虽然设置成功了，但值没有变化，所以回调不会被调用");

    assert_eq(
        urq_var_table_set(table, TAG_ID, SIZE + 1, data, &is_change), -1,
        "内容大小不匹配，设置应该失败");
    assert_eq(is_change, false, "设置失败，所以没有变化");

    assert_eq(
        urq_var_table_set(table, TAG_ID, SIZE - 1, data, &is_change), -1,
        "内容大小不匹配，设置应该失败");
    assert_eq(is_change, false, "设置失败，所以没有变化");

    data[0] = 1;
    data[1] = 2;
    // 设置成功，并且有变化
    assert_eq(
        urq_var_table_set(table, TAG_ID, SIZE, data, &is_change), 0,
        "对变量设置值应该成功");
    assert_eq(is_change, true, "值应该有变化");

    // 释放变量表
    urq_var_table_free(table);
}

run_tests(normal)
