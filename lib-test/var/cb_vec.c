#include "urq/var/cb_vec.h"
#include "urq/var/var.h"
#include "urq_test_c.h"
#include "urq_test_c/test.h"
#include <stdint.h>

void cb1(void *arg1, const urq_var_t *value)
{
    if (arg1 == NULL || value == NULL) {
        return;
    }
}

test_unit(normal)
{
    urq_var_cb_vec_t vec;
    urq_var_cb_vec_init_inplace(&vec);
    assert_eq((int32_t)vec.size, 0);
    assert_eq((int32_t)vec.capacity, 0);
    assert_eq(vec.data, NULL);

    // push
    urq_var_cb_vec_push(&vec, cb1, NULL);
    assert_eq((int32_t)vec.size, 1);
    assert_eq((int32_t)vec.capacity, 16);
    assert_ne(vec.data, NULL);

    urq_var_cb_vec_free_inplace(&vec);
}

run_tests(normal)
