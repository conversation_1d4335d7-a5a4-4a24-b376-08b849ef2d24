# 添加测试
function(urq_add_test name files)
    set(URQ_CURRENT_BUILD "urq-lib-test-${name}")
    message(STATUS "[CPM] build test, name: ${URQ_CURRENT_BUILD}")

    add_executable("${URQ_CURRENT_BUILD}" "${files}")
    target_include_directories(${URQ_CURRENT_BUILD} PRIVATE "${PROJECT_SOURCE_DIR}/src")
    target_link_libraries(${URQ_CURRENT_BUILD} PRIVATE urq_lib)

    set(urq_test_c_DIR "${CMAKE_PREFIX_PATH}/urq_test_c")
    find_package(urq_test_c 0.0.7 REQUIRED)
    target_include_directories(${URQ_CURRENT_BUILD} SYSTEM PUBLIC "${urq_test_c_INCLUDE_DIRS}")
    target_link_libraries(${URQ_CURRENT_BUILD} PRIVATE ${urq_test_c_LIBRARIES})

    #  添加测试
    add_test(NAME "${URQ_CURRENT_BUILD}" COMMAND "${URQ_CURRENT_BUILD}")
endfunction()

enable_testing()
urq_add_test(var-cb-vec "var/cb_vec.c")
urq_add_test(var-table "var/table.c")
