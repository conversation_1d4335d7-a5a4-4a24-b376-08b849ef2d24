---
Language: Cpp
# BasedOnStyle:  LLVM
AccessModifierOffset: -4
AlignAfterOpenBracket: AlwaysBreak
AlignArrayOfStructures: None
AlignConsecutiveMacros: true
AlignConsecutiveAssignments: None
AlignConsecutiveBitFields: None
AlignConsecutiveDeclarations: None
AlignEscapedNewlines: Right
AlignOperands: Align
AlignTrailingComments: true
AllowAllArgumentsOnNextLine: true
AllowAllParametersOfDeclarationOnNextLine: true
AllowShortEnumsOnASingleLine: true
AllowShortBlocksOnASingleLine: Never
AllowShortCaseLabelsOnASingleLine: false
AllowShortFunctionsOnASingleLine: All
AllowShortLambdasOnASingleLine: All
AllowShortIfStatementsOnASingleLine: Never
AllowShortLoopsOnASingleLine: false
AlwaysBreakAfterDefinitionReturnType: None
AlwaysBreakAfterReturnType: None
# 是否在多行字符串前换行
AlwaysBreakBeforeMultilineStrings: false
AlwaysBreakTemplateDeclarations: MultiLine
AttributeMacros:
    - __capability
    - __attribute__

# 是否将函数参数打包在一行
# true: 尽可能多的参数放在同一行
# false: 参数尽可能分行显示
BinPackArguments: true
# 是否将函数声明的参数打包在一行
BinPackParameters: true
BraceWrapping:
    AfterCaseLabel: false
    AfterClass: false
    AfterControlStatement: Never
    AfterEnum: false
    AfterFunction: false
    AfterNamespace: false
    AfterObjCDeclaration: false
    AfterStruct: false
    AfterUnion: false
    AfterExternBlock: false
    BeforeCatch: false
    BeforeElse: false
    BeforeLambdaBody: false
    BeforeWhile: false
    IndentBraces: false
    SplitEmptyFunction: true
    SplitEmptyRecord: true
    SplitEmptyNamespace: true
BreakBeforeBinaryOperators: None
BreakBeforeConceptDeclarations: true
BreakBeforeBraces: WebKit
BreakBeforeInheritanceComma: false
BreakInheritanceList: BeforeColon
BreakBeforeTernaryOperators: true
BreakConstructorInitializersBeforeComma: false
BreakConstructorInitializers: BeforeComma
BreakAfterJavaFieldAnnotations: false
BreakStringLiterals: true
ColumnLimit: 80
CommentPragmas: "^ IWYU pragma:"
QualifierAlignment: Leave
CompactNamespaces: false
ConstructorInitializerIndentWidth: 4
ConstructorInitializerAllOnOneLineOrOnePerLine: true
ContinuationIndentWidth: 4
Cpp11BracedListStyle: true
DeriveLineEnding: true
DerivePointerAlignment: false
DisableFormat: false
EmptyLineAfterAccessModifier: Never
EmptyLineBeforeAccessModifier: LogicalBlock
ExperimentalAutoDetectBinPacking: false
PackConstructorInitializers: BinPack
BasedOnStyle: ""
AllowAllConstructorInitializersOnNextLine: true
FixNamespaceComments: true
ForEachMacros:
    - foreach
    - Q_FOREACH
    - BOOST_FOREACH
IfMacros:
    - KJ_IF_MAYBE
IncludeBlocks: Preserve
IncludeCategories:
    - Regex: '^"(llvm|llvm-c|clang|clang-c)/'
      Priority: 2
      SortPriority: 0
      CaseSensitive: false
    - Regex: '^(<|"(gtest|gmock|isl|json)/)'
      Priority: 3
      SortPriority: 0
      CaseSensitive: false
    - Regex: ".*"
      Priority: 1
      SortPriority: 0
      CaseSensitive: false
IncludeIsMainRegex: "(Test)?$"
IncludeIsMainSourceRegex: ""
IndentAccessModifiers: false
IndentCaseLabels: false
IndentCaseBlocks: false
IndentGotoLabels: true
IndentPPDirectives: None
IndentExternBlock: AfterExternBlock
IndentRequires: false
IndentWidth: 4
IndentWrappedFunctionNames: false
InsertTrailingCommas: None
JavaScriptQuotes: Leave
JavaScriptWrapImports: true
KeepEmptyLinesAtTheStartOfBlocks: true
LambdaBodyIndentation: Signature
# 定义宏块的开始标记
MacroBlockBegin: "__attribute__"
# 定义宏块的结束标记
MacroBlockEnd: "__attribute__"
MaxEmptyLinesToKeep: 1
# 对命名空间进行缩进 None/Inner/All
NamespaceIndentation: None
ObjCBinPackProtocolList: Auto
ObjCBlockIndentWidth: 2
ObjCBreakBeforeNestedBlockParam: true
ObjCSpaceAfterProperty: false
ObjCSpaceBeforeProtocolList: true
# 破坏赋值的惩罚
PenaltyBreakAssignment: 2
# 在第一个调用参数前换行的惩罚
PenaltyBreakBeforeFirstCallParameter: 0
# 破坏注释格式的惩罚
PenaltyBreakComment: 300
# 破坏<<<的惩罚
PenaltyBreakFirstLessLess: 120
# 破坏括号的惩罚
PenaltyBreakOpenParenthesis: 0
# 破坏字符串的惩罚
PenaltyBreakString: 1000
# 破坏模板声明的惩罚
PenaltyBreakTemplateDeclaration: 10
# 破坏字符的惩罚
PenaltyExcessCharacter: 1000000
# 破坏返回类型的惩罚
PenaltyReturnTypeOnItsOwnLine: 10000
# 破坏缩进的惩罚
PenaltyIndentedWhitespace: 0
# 指针对齐
PointerAlignment: Right
PPIndentWidth: -1
ReferenceAlignment: Pointer
# 是否重新格式化注释
ReflowComments: true
RemoveBracesLLVM: false
SeparateDefinitionBlocks: Leave
ShortNamespaceLines: 1
SortIncludes: CaseSensitive
SortJavaStaticImport: Before
SortUsingDeclarations: true
SpaceAfterCStyleCast: false
SpaceAfterLogicalNot: false
SpaceAfterTemplateKeyword: true
SpaceBeforeAssignmentOperators: true
SpaceBeforeCaseColon: false
SpaceBeforeCpp11BracedList: false
SpaceBeforeCtorInitializerColon: true
SpaceBeforeInheritanceColon: true
SpaceBeforeParens: ControlStatements
SpaceBeforeParensOptions:
    AfterControlStatements: true
    AfterForeachMacros: true
    AfterFunctionDefinitionName: false
    AfterFunctionDeclarationName: false
    AfterIfMacros: true
    AfterOverloadedOperator: false
    BeforeNonEmptyParentheses: false
SpaceAroundPointerQualifiers: Default
SpaceBeforeRangeBasedForLoopColon: true
SpaceInEmptyBlock: false
SpaceInEmptyParentheses: false
SpacesBeforeTrailingComments: 1
SpacesInAngles: Never
SpacesInConditionalStatement: false
SpacesInContainerLiterals: true
SpacesInCStyleCastParentheses: false
SpacesInLineCommentPrefix:
    Minimum: 1
    Maximum: -1
SpacesInParentheses: false
SpacesInSquareBrackets: false
SpaceBeforeSquareBrackets: false
BitFieldColonSpacing: Both
Standard: Latest
StatementAttributeLikeMacros:
    - Q_EMIT
StatementMacros:
    - Q_UNUSED
    - QT_REQUIRE_VERSION
TabWidth: 8
UseCRLF: false
UseTab: Never
WhitespaceSensitiveMacros:
    - STRINGIZE
    - PP_STRINGIZE
    - BOOST_PP_STRINGIZE
    - NS_SWIFT_NAME
    - CF_SWIFT_NAME
---

